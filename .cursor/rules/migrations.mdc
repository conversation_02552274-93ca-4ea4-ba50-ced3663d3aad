---
description: 
globs: 
alwaysApply: false
---
# Rule: Creating Database Migrations

Database migrations are used to manage changes to the database schema.

1.  **Location:** Migration files reside in the [lcars/src/db/scripts/migration/migration-files/](mdc:lcars/src/db/scripts/migration/migration-files) directory.
2.  **Naming:** Files should be named sequentially, followed by an underscore and a descriptive name or ticket ID (e.g., `00296_ENG1032_gong_date_filter.js`).
3.  **Format:** Each migration file is a JavaScript module exporting the following:
    *   `allCustomers` (boolean): Set to `true` if the migration should run on all client schemas. Use the `{schema}` placeholder in your SQL.
    *   `justTribble` (boolean): Set to `true` if the migration *only* affects the central `tribble` schema.
    *   `justTheseSchema` (string[]): An array of specific schema names if the migration targets only a subset of clients (usually empty).
    *   `up` (string): A template literal containing the SQL query/queries to apply the migration.
    *   `down` (string): A template literal containing the SQL query/queries to revert the migration.

4.  **Example:** See [00296_ENG1032_gong_date_filter.js](mdc:lcars/src/db/scripts/migration/migration-files/00296_ENG1032_gong_date_filter.js) for an example of adding a setting to the `tribble.setting` table.
