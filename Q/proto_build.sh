#!/bin/bash
set -e

echo "Building proto files..."

# chat completion
protoc-gen-grpc-ts \
  --ts_out=grpc_js:../packages/chat-completion-service/src \
  --proto_path ./ \
  --proto_path ../node_modules/grpc-tools/bin \
chat_completion_service.proto 

grpc_tools_node_protoc \
  --js_out=import_style=commonjs,binary:../packages/chat-completion-service/src \
  --grpc_out=grpc_js:../packages/chat-completion-service/src \
  --plugin=protoc-gen-grpc=`which grpc_tools_node_protoc_plugin` \
  --proto_path=. \
  --proto_path ../node_modules/grpc-tools/bin \
chat_completion_service.proto

# Brain ingest
protoc-gen-grpc-ts \
  --ts_out=grpc_js:../packages/brain-ingest-service/src \
  --proto_path ./ \
  --proto_path ../node_modules/grpc-tools/bin \
brain_ingest_service.proto

grpc_tools_node_protoc \
  --js_out=import_style=commonjs,binary:../packages/brain-ingest-service/src \
  --grpc_out=grpc_js:../packages/brain-ingest-service/src \
  --plugin=protoc-gen-grpc=`which grpc_tools_node_protoc_plugin` \
  --proto_path=. \
  --proto_path ../node_modules/grpc-tools/bin \
brain_ingest_service.proto

echo "Proto files built successfully!"

turbo build --filter=chat-completion-service
turbo build --filter=brain-ingest-service
npx prettier --write ../packages/chat-completion-service/
npx prettier --write ../packages/brain-ingest-service/


# Build Python packages
echo "Building Python packages..."
cd ../packages/chat-completion-service-python

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    echo "Error: Python virtual environment not found at .venv"
    exit 1
fi

# Activate the virtual environment
source .venv/bin/activate

# Run the proto generation script
python generate_protos.py

# Copy the generated files to positronic-nlp
echo "Copying generated files to positronic-nlp..."
cp -r dist/* ../../positronic-nlp/

echo "Python proto files built and copied successfully!"

