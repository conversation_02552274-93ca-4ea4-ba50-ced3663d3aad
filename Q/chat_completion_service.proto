syntax = "proto3";

import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

package chatcompletion;

message TokenUsage {
  int32 completion_tokens = 1;
  int32 prompt_tokens = 2;
  float total_cost = 3; // in proto3, can use 'float' for monetary values
  int32 total_tokens = 4;
}

message Duration {
  google.protobuf.Timestamp start = 1; // Use Timestamp instead of string
  google.protobuf.Timestamp end = 2; // Use Timestamp instead of string
}

message Statistics {
  TokenUsage token_usage = 1;
  Duration duration = 2; // in proto3 by default
}

message ActivityLogData {
  int32 client_id = 1;
  int32 user_id = 2;
  string details = 3; 
  string event = 4;
  string source_table = 5;
  string source_id = 6;
  string type = 7;
  bool exclude_from_interaction_count = 8;
}


//These are OPENAI spec, unless otherwise specified
message CallLlmRequest {
  string schema = 1;
  bool needs_redaction = 2;
  bool use_chat_completion = 3;
  string input_messages_raw = 4;
  string input_text = 5;
  int32 timeout = 6;
  ActivityLogData activity_log_data = 7;
  string correlation_id = 8;
  string model = 9;
}

// Represents a tool call.
message ToolCall {
  // The ID of the tool call.
  string id = 1; 
  // The type of the tool, e.g., "function".
  string type = 2; 
  // funtion_name is the name of the function being called.
  string function_name = 3; 
  // Input arguments for the tool call.
  google.protobuf.Struct arguments = 4;
}

// Text content type
message Text {
  string type = 1;
  string text = 2;
}

// Image URL content type
message ImageURL {
  string type = 1;
  ContentPartImageUrl image_url = 2;
}

message ContentPartImageUrl {
  string url = 1;
}

// Defines a content part, which can be either text or an image URL
message ContentPart {
  oneof content_type {
    Text text_content = 1;
    ImageURL image_url_content = 2;
  }
}

message RepeatingContentParts {
  repeated ContentPart content_part = 1;
}

// ChatMessage is used to represent an entire message in a chat.
message ChatMessage {
  
  // Content can be text or ContentParts (latter if image data)
  oneof content {
    string text_content = 1;
    RepeatingContentParts content_parts = 6;
  } 

  // Role can be "system", "assistant", "user" or "tool".
  string role = 2;
  // Name is used to differentiate between multiple users in one chat.
  string name = 3;
  // List of tool calls requested in this message.
  repeated ToolCall tool_calls = 4;
  // The ID of the tool call being responded to, used only for "tool" messages. 
  string tool_call_id = 5;
}

// ChatMessageDelta is used to represent a streamed update to a chat message.
message ChatMessageDelta {
  // Incremental text content for the message.
  string content_chunk = 1;
  // Role can be "system", "assistant", "user" or "tool".
  string role = 2;
  // Name is used to differentiate between multiple users in one chat.
  string name = 3;
  // List of tool calls requested in this message.
  repeated ToolCall tool_calls = 4;
  // The ID of the tool call being responded to, used only for "tool" messages. 
  string tool_call_id = 5;
}

// Represents a tool that the model may call
message Tool {
  // The type of the tool, currently only "function" is supported.
  string type = 1; 
  // Details of the function
  Function function = 2; 

  // Represents the detailed structure of a function tool
  message Function {
    // Description of the function, what it does, when/how to call, etc.
    string description = 1;
    // The name of the function, will be used in a resulting tool call.
    string name = 2; 
    // The parameters the function accepts, described as a JSON Schema object.
    google.protobuf.Struct parameters = 3; 
    // Force strict mode for this function call. Only a subset of parameters are allowed. See https://platform.openai.com/docs/guides/function-calling
    bool strict = 4;
  }
}

// ToolChoice represents an instruction for the model to call a specific tool.
message ToolChoice {
  oneof choice {
    // The type of the tool, e.g., "function"
    string choice_string = 1;
    ToolChoiceDetails choice_details = 2;
  }
}

// ToolChoiceDetails represents the detailed structure of a tool choice.
message ToolChoiceDetails {
  // The type of the tool, e.g., "function"
  string type = 1; 
  // The name of the tool to use.
  string function = 2; 
}

message ResponseFormat {
  // Potential fields here, e.g., specifying JSON format
  string type = 1; // Example: "json_object"
  string json_schema = 2; // Example: "https://json-schema.org/draft/2020-12/schema"
}

// ChatCompletionParams include all options available in the OpenAI API. 
message ChatCompletionParams {
  repeated ChatMessage messages = 1;
  string model = 2;
  float frequency_penalty = 3;
  map<int32, float> logit_bias = 4;
  bool logprobs = 5;
  int32 top_logprobs = 6;
  int32 max_tokens = 7;
  int32 n = 8;
  float presence_penalty = 9;
  ResponseFormat response_format = 10;
  int32 seed = 11;
  repeated string stop = 12;
  bool stream = 13;
  float temperature = 14;
  float top_p = 15;
  repeated Tool tools = 16;
  ToolChoice tool_choice = 17;
  string user = 18;
  bool parallel_tool_calls = 19;
  string reasoning_effort = 20; //Only available in reasoning models like o1 models
}

message ChatCompletionRequest {
  string schema = 1;
  bool needs_redaction = 2;
  int32 timeout = 3;
  ActivityLogData activity_log_data = 4;
  ChatCompletionParams params = 5;
  Resource resource = 6;
  string correlation_id = 7;
}

// ResourceBaseModel is an enumeration of available Large Language Models.
enum ResourceBaseModel {  
  RESOURCE_BASE_MODEL_UNSPECIFIED = 0; // Default will be gpt-4/32k, NOT any special models
  GPT_4 = 1;
  GPT_3_5_TURBO = 2;
  GPT_4_TURBO = 3;
  GPT_4_OH = 4;
  GPT_4_OH_MINI = 5;
  GPT_4_OH_1 = 6;
  GPT_4_OH_3_MINI = 7;
  CLAUDE_3_7_SONNET = 8;
  GPT_4_1 = 9;
  GPT_4_1_MINI = 10;
  GEMINI_2_5_FLASH = 11;
  CLAUDE_4_SONNET = 12;
  CLAUDE_4_OPUS = 13;
  ANY_CLAUDE = 14;
}

// Resource describes the LLM resource desired for the request.
message Resource {
  string url = 1;
  string api_version = 2;
  ResourceBaseModel base_model = 3;
}

message CallLlmResponse {
  string content = 1;
  Statistics stats = 2;
}

// Represents a choice given by the model. Will contain either a message or a
// message delta depending on whether this response was streamed or not.
message Choice {
  string finish_reason = 1;
  int32 index = 2;
  ChatMessage message = 3;
  ChatMessageDelta message_delta = 4;
}

// Usage statistics for the completion request
message Usage {
  int32 completion_tokens = 1;
  int32 prompt_tokens = 2;
  int32 total_tokens = 3;
}

message ChatCompletionResponse {
  string id = 1;
  repeated Choice choices = 2;
  int64 created = 3;
  string model = 4;
  string system_fingerprint = 5;
  string object = 6;
  Usage usage = 7;
  Statistics stats = 8;
  string refusal = 9; // Only used when structured output is specified, and the LLM decided not do it. 
}

message GenerateEmbeddingRequest {
  string text = 1;
  string model = 2;
}

message GenerateEmbeddingResponse {
  repeated float embedding = 1;
  string provider = 2;
}

// Version-related messages
message VersionRequest {
  // Empty request - no parameters needed
}

message VersionResponse {
  string service_name = 1;
  string version = 2;
  string commit_sha = 3;
  string commit_short = 4;
  string commit_date = 5;
  string build_date = 6;
  string build_id = 7;
  string branch = 8;
  string environment = 9;
  string build_url = 10; // optional
}

//Claude definitions:
message ClaudeChatCompletionRequest {
  string schema = 1;
  int32 timeout = 2;
  ActivityLogData activity_log_data = 3;
  ClaudeMessageRequest params = 4;
  Resource resource = 5;
  string correlation_id = 6;
}


message ClaudeMessageRequest {
  // The model that will complete your prompt
  string model = 1;
  // Maximum number of tokens to generate before stopping
  int32 max_tokens = 2;
  // Messages between the human and Claude
  repeated ClaudeRequestMessage messages = 3;
  // System prompt
  string system = 4;
  // Controls randomness (0-1)
  float temperature = 5;
  // Like nucleus sampling, sets a threshold of most likely tokens to consider
  float top_p = 6;
  // Stops generation at a specified sequence
  repeated string stop_sequences = 7;
  // Stream partial message deltas
  bool stream = 8;
  // Metadata about the request
  ClaudeMessageMetadata metadata = 9;
  // Tools available to the model
  repeated ClaudeTool tools = 10;
  // The tool to use.
  ClaudeToolChoice tool_choice = 11;
  // Whether to enable thinking.
  ClaudeThinking thinking = 12;
  // timeout
  int32 timeout = 13;
}

message ClaudeToolChoice {
  string type = 1; // "any", "auto", "none", "tool",
  string name = 2; // required if type is "tool"
  bool disable_parallel_tool_use = 3; // default: false
}

message ClaudeThinking {
  string type = 1; // "enabled" or "disabled"
  int32 budget_tokens = 2; // x > 1024. required if type is "enabled"
}

// Message object representing a single exchange
message ClaudeRequestMessage {
  // Role of the message sender: "user" or "assistant"
  string role = 1;
  // Content of the message
  repeated ClaudeContentBlock content = 2;
}

// Content block that can be text or other media
message ClaudeContentBlock {
  // Type of content
  string type = 1;
  
  // Fields used when type is "text"
  string text = 2;
  
  // Fields used when type is "image"
  ClaudeImageSource source = 3;
  
  // Fields used when type is "tool_use"
  ClaudeToolUse tool_use = 4;
  
  // Fields used when type is "tool_result"
  ClaudeToolResult tool_result = 5;
}

// Image source information
message ClaudeImageSource {
  // Type of image source: "base64" or "url"
  string type = 1;
  // Media type of the image
  string media_type = 2;
  // The data - either base64 encoded or URL
  string data = 3;
}

// Tool use definition
message ClaudeToolUse {
  // ID of the tool use
  string id = 1;
  // Name of the tool to use
  string name = 2;
  // Input parameters for the tool
  google.protobuf.Struct input = 3;
}

// Tool result after execution
message ClaudeToolResult {
  // ID of the tool use
  string tool_use_id = 1;
  // Content of the result
  string content_string = 2;
  // Content of the result 
  repeated ClaudeContentBlock content_block = 3; //either string or block but not both  
}

// Metadata for the message request
message ClaudeMessageMetadata {
  // User identifier 
  string user_id = 1;
}

// Individual tool definition
message ClaudeTool {
  // Name of the tool
  string name = 1;
  // Description of the tool
  string description = 2;
  // Tool input schema
  google.protobuf.Struct input_schema = 3;
}

// Usage tracking
message ClaudeUsage {
  // Input token count
  int32 input_tokens = 1;
  // Output token count
  int32 output_tokens = 2;
}

// Message response object
message ClaudeChatCompletionResponse {
  // ID of the message
  string id = 1;
  // The model that processed the request
  string model = 2;
  // Type of response
  string type = 3;
  // Role (always "assistant")
  string role = 4;
  // Content blocks in the response
  repeated ClaudeMessageResponseContentBlock content = 5;
  // Stop reason if applicable
  string stop_reason = 6;
  // Stop sequence that triggered the stop if applicable
  string stop_sequence = 7;
  // Usage statistics
  Statistics stats = 8;
  // If streaming, this will be the delta for the message. 
  repeated ClaudeMessageResponseContentBlock delta = 9;
}

message ClaudeMessageResponseContentBlock {
  string text = 1;
  string text_delta = 2;
  string type = 3; //"text", "tool_use", "thinking"
  //if Tooluse:
  string id = 4;
  string name = 5;
  google.protobuf.Struct input = 6;
  repeated ClaudeMessageResponseCitation citations = 7;
  int32 index = 8;
}

message ClaudeMessageResponseCitation {
  string cited_text = 1;
  int32 document_index = 2;
  string document_title = 3;
  int32 end_char_index = 4;
  int32 start_char_index = 5;
  int32 start_page_number = 6;
  int32 end_page_number = 7;
  string type = 8; //"char_location" "page_location"
}

// Define the service
service ChatCompletionService {
  rpc CallLLM (CallLlmRequest) returns (CallLlmResponse);
  rpc ChatCompletion (ChatCompletionRequest) returns (ChatCompletionResponse);
  rpc ChatCompletionStream (ChatCompletionRequest) returns (stream ChatCompletionResponse);
  rpc ClaudeChatCompletion (ClaudeChatCompletionRequest) returns (ClaudeChatCompletionResponse);
  rpc ClaudeChatCompletionStream (ClaudeChatCompletionRequest) returns (stream ClaudeChatCompletionResponse);
  rpc GeminiChatCompletion (ChatCompletionRequest) returns (ChatCompletionResponse);
  rpc GeminiChatCompletionStream (ChatCompletionRequest) returns (stream ChatCompletionResponse);
  rpc GenerateEmbedding (GenerateEmbeddingRequest) returns (GenerateEmbeddingResponse);
  rpc GetVersion (VersionRequest) returns (VersionResponse);
}
