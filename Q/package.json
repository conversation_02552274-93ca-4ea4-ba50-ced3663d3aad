{"name": "Q", "version": "1.0.0", "description": "", "private": true, "main": "index.js", "scripts": {"build": "tsc && npm run copy-version", "copy-version": "if [ -f version.json ]; then cp version.json dist/; fi", "test": "jest", "start": "node dist/app.js", "dev": "nodemon --watch 'src/**/*.ts' --exec 'ts-node' src/app.ts"}, "keywords": [], "author": "Tribble gang", "license": "ISC", "devDependencies": {"@tribble/types-shared": "^0.0.1", "@types/google-protobuf": "^3.15.12", "@types/jest": "^29.5.5", "@types/node": "^20.7.1", "jest": "^29.7.0", "nodemon": "^3.0.1", "prettier": "3.2.4", "prettier-plugin-organize-imports": "^3.2.4", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.2.2"}, "dependencies": {"@anthropic-ai/sdk": "^0.39.0", "@anthropic-ai/vertex-sdk": "^0.7.0", "@azure/eventgrid": "^4.14.0", "@azure/identity": "^3.3.0", "@azure/keyvault-secrets": "^4.7.0", "@azure/openai": "^2.0.0", "@azure/service-bus": "^7.9.1", "@google/genai": "^1.0.1", "@grpc/grpc-js": "^1.9.14", "@tribble/chat-completion-service": "^0.0.1", "@tribble/common-utils": "^0.0.1", "@tribble/tribble-db": "^0.0.0", "applicationinsights": "^2.8.0", "brain-ingest-service": "*", "dotenv": "^16.3.1", "google-auth-library": "^9.15.1", "google-protobuf": "^3.21.2", "grpc-tools": "^1.12.4", "openai": "^4.77.0", "pg": "^8.11.3", "tiktoken": "^1.0.13", "turbo": "^1.13.2"}}