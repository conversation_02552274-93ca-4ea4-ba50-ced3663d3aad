ENVIRONMENT='development' # Ensures function calls to localhost
PORT=

# Populate for local dev
DATABASEURL=

#For azure credential
AZURE_CLIENT_ID=
AZURE_TENANT_ID=
AZURE_CLIENT_SECRET=
AZURE_KEY_VAULT_NAME=kv-tribble-test

APPLICATION_INSIGHTS_DISABLE=true
# If you want application_insights turned on, have these guys:
APPLICATIONINSIGHTS_CONNECTION_STRING=
APPLICATION_INSIGHTS_URL=

ENV_PREFIX=test # test | staging | blank for prod
AZURE_KEY_VAULT_NAME=kv-tribble-test

#For API keys.
# Key can be generated using: crypto.randomBytes(32).toString('base64')
# IV can be generated using: crypto.randomBytes(12).toString('base64')
# But if you're working with the test db, you need the same values that are found
# in the tribble-test-q
ENCRYPTION_KEY=
ENCRYPTION_IV=

# For the grpc server
GRPC_SERVER_ADDRESS=localhost:50051

# These topic connection strings can be found in LLMQUEUE_test --> topics --> [topic name] --> shared access policies --> send_listen
# or you can use ds9/scripts/setupServiceBusEventGrid.sh, which will add these env vars for you
# TOPIC_ANSWERED_CONNECTION_STRING=
# TOPIC_NEW_REQUEST_CONNECTION_STRING=
# TOPIC_CNC_CONNECTION_STRING=

# LOCAL_TOPIC_ANSWERED=
# LOCAL_TOPIC_SUBSCRIPTION_ANSWERED=
# ANSWERED_ACCESS_KEY=
# LOCAL_ANSWERED_TOPIC_ENDPOINT=
# LOCAL_TOPIC_NEW_REQUEST=
# LOCAL_TOPIC_SUBSCRIPTION_NEW_REQUEST=
# NEW_REQUEST_ACCESS_KEY=
# LOCAL_NEW_REQUEST_TOPIC_ENDPOINT=
# LOCAL_TOPIC_CNC=
# LOCAL_TOPIC_SUBSCRIPTION_CNC=
# CNC_ACCESS_KEY=
# LOCAL_CNC_TOPIC_ENDPOINT=
