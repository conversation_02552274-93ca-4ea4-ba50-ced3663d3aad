import { AzureKeyCredential, EventGridPublisherClient } from '@azure/eventgrid';
import { DefaultAzureCredential } from '@azure/identity';
import { SecretClient } from '@azure/keyvault-secrets';
import { ServerUnaryCall, sendUnaryData } from '@grpc/grpc-js';
import {
  AddDocumentRequest,
  AddQuestionnaireRequest,
  IBrainIngestServer,
  MimeType,
  PrivacyStatus,
} from 'brain-ingest-service';
import { Empty } from 'google-protobuf/google/protobuf/empty_pb';
import { constants } from './constants';

interface FunctionRequest<T = any> {
  function_name: 'ingest' | 'digest';
  job_id: number;
  schema: string;
  client_id: number;
  user_id: number;
  request_id?: string;
  is_structured?: boolean;
  payload: T;
}

let NEW_REQUEST_ACCESS_KEY: string;

const docsContainer = 'documents';

export function getBlobUrl(schema: string) {
  //get client id / blob url / container name.
  const blobPrefix = process.env.BLOB_STORAGE_PREFIX + process.env.ENV_PREFIX; //e.g. tribbletest
  const storageId = blobPrefix + schema;
  const blobUrl = `https://${storageId}.blob.core.windows.net/`;
  return blobUrl;
}

if (process.env.NEW_REQUEST_ACCESS_KEY) {
  NEW_REQUEST_ACCESS_KEY = process.env.NEW_REQUEST_ACCESS_KEY;
} else {
  const credential = new DefaultAzureCredential();
  const vaultName = process.env.AZURE_KEY_VAULT_NAME;
  const vaultUrl = `https://${vaultName}.vault.azure.net`;
  const secretClient = new SecretClient(vaultUrl, credential);
  secretClient
    .getSecret(constants.SECRET_NEW_REQUEST_ACCESS_KEY)
    .then((secret) => {
      NEW_REQUEST_ACCESS_KEY = secret.value;
    });
}

const getNewRequestClient = () => {
  let endpoint = process.env.LOCAL_NEW_REQUEST_TOPIC_ENDPOINT;
  if (!endpoint) {
    const env_prefix = process.env.ENV_PREFIX,
      env_string = env_prefix ? `-${env_prefix}` : '';
    endpoint = `https://new-request${env_string}.eastus2-1.eventgrid.azure.net/api/events`;
  }
  return new EventGridPublisherClient(
    endpoint,
    'EventGrid',
    new AzureKeyCredential(NEW_REQUEST_ACCESS_KEY),
  );
};

//Use event grid to send a function request to Q2.
const send_function_request = async (
  func_request: Partial<FunctionRequest>,
) => {
  const { EVENT_GRID_TYPE_FUNC_NEW_REQUEST, NEW_REQUEST } = constants;
  const eg_client = getNewRequestClient();
  await eg_client.send([
    {
      subject: NEW_REQUEST,
      eventType: EVENT_GRID_TYPE_FUNC_NEW_REQUEST,
      data: func_request,
      dataVersion: '1.0',
    },
  ]);
};

function getMimeTypeString(request: AddDocumentRequest): string {
  switch (request.getMimeType()) {
    case MimeType.MIME_TYPE_CSV:
      return 'text/csv';
    case MimeType.MIME_TYPE_PDF:
      return 'application/pdf';
    case MimeType.MIME_TYPE_DOCX:
      return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    case MimeType.MIME_TYPE_XLSX:
      return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    case MimeType.MIME_TYPE_PPTX:
      return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
    case MimeType.MIME_TYPE_UNKNOWN:
    default:
      return 'application/octet-stream';
  }
}

export const brainIngestServer: IBrainIngestServer = {
  addDocument: async (
    call: ServerUnaryCall<AddDocumentRequest, Empty>,
    callback: sendUnaryData<Empty>,
  ) => {
    const req = call.request;

    const clientId = req.getClientId(),
      schema = req.getSchema(),
      userId = req.getUserId(),
      jobId = req.getJobId(),
      filename = req.getFilename(),
      mimeType = getMimeTypeString(req),
      blobName = req.getBlobName();

    let privacy: string;
    switch (req.getPrivacy()) {
      case PrivacyStatus.PRIVACY_STATUS_PUBLIC:
        privacy = 'public';
        break;
      case PrivacyStatus.PRIVACY_STATUS_PRIVATE:
        privacy = 'private';
        break;
    }

    const alwaysTag = (req.toObject().alwaysTagMetadataList || []).map(
      (mdf) => {
        return {
          id: mdf.id,
          type_id: mdf.typeId,
          value: mdf.value,
        };
      },
    );

    const neverTag = (req.toObject().neverTagMetadataList || []).map((mdf) => {
      return {
        id: mdf.id,
        type_id: mdf.typeId,
        value: mdf.value,
      };
    });

    // Extract source dates from request if present
    const sourceCreatedDate = req.hasSourceCreatedDate()
      ? req.getSourceCreatedDate().toDate().toISOString()
      : null;
    const sourceModifiedDate = req.hasSourceModifiedDate()
      ? req.getSourceModifiedDate().toDate().toISOString()
      : null;

    send_function_request({
      schema: schema,
      client_id: clientId,
      user_id: userId,
      function_name: 'ingest',
      job_id: jobId,
      payload: {
        type: 'DOC',
        job_id: jobId,
        user_id: userId,
        client_id: clientId,
        storage_url: getBlobUrl(schema),
        database_id: schema,
        folder: docsContainer,
        files: [
          {
            document_id: req.getDocumentId(),
            file_name: blobName,
            file_name_original: filename,
            file_mimetype: mimeType,
            meta: {
              expiry_date: req.getExpiryDate(),
              source_created_date: sourceCreatedDate,
              source_modified_date: sourceModifiedDate,
              // Default values below.
              privacy: privacy,
              auto_tag_metadata: req.getAutoTagMetadata(),
              always_tag_metadata: alwaysTag,
              never_auto_tag_metadata: neverTag,
            },
          },
        ],
        is_update: req.getIsUpdate(),
      },
    });

    const response = new Empty();
    callback(null, response);
  },

  addQuestionnaire: async (
    call: ServerUnaryCall<AddQuestionnaireRequest, Empty>,
    callback: sendUnaryData<Empty>,
  ) => {
    const req = call.request;
    console.log(`[addQuestionaire] ${JSON.stringify(req.toObject())}`);

    const clientId = req.getClientId(),
      schema = req.getSchema(),
      userId = req.getUserId(),
      jobId = req.getJobId(),
      filename = req.getFilename(),
      mimeType = getMimeTypeString(req),
      blobName = req.getBlobName();

    // Extract source dates from request if present
    const sourceCreatedDate = req.hasSourceCreatedDate()
      ? req.getSourceCreatedDate().toDate().toISOString()
      : null;
    const sourceModifiedDate = req.hasSourceModifiedDate()
      ? req.getSourceModifiedDate().toDate().toISOString()
      : null;

    let privacy: string;
    switch (req.getPrivacy()) {
      case PrivacyStatus.PRIVACY_STATUS_PUBLIC:
        privacy = 'public';
        break;
      case PrivacyStatus.PRIVACY_STATUS_PRIVATE:
        privacy = 'private';
        break;
    }

    const alwaysTag = (req.toObject().alwaysTagMetadataList || []).map(
      (mdf) => {
        return {
          id: mdf.id,
          type_id: mdf.typeId,
          value: mdf.value,
        };
      },
    );

    const neverTag = (req.toObject().neverTagMetadataList || []).map((mdf) => {
      return {
        id: mdf.id,
        type_id: mdf.typeId,
        value: mdf.value,
      };
    });

    send_function_request({
      schema: schema,
      client_id: clientId,
      user_id: userId,
      function_name: 'ingest',
      job_id: jobId,
      payload: {
        type: 'RFP',
        job_id: jobId,
        user_id: userId,
        client_id: clientId,
        storage_url: getBlobUrl(schema),
        database_id: schema,
        folder: docsContainer,
        files: [
          {
            document_id: req.getDocumentId(),
            file_name: blobName,
            file_name_original: filename,
            file_mimetype: mimeType,
            meta: {
              expiry_date: req.getExpiryDate(),
              source_created_date: sourceCreatedDate,
              source_modified_date: sourceModifiedDate,
              // Default values below.
              privacy: privacy,
              auto_tag_metadata: req.getAutoTagMetadata(),
              always_tag_metadata: alwaysTag,
              never_auto_tag_metadata: neverTag,

              // RFP specific values below.
              first_row_is_header: req.getFirstRowIsHeader(),
              header_row_idx: req.getHeaderRowIndex(),
              question_columns_idx: req.getQuestionColumnsIndicesList(),
              answer_columns_idx: req.getAnswerColumnsIndicesList(),

              // Insight specific values below.
              insight_columns_idx: req.getInsightColumnsIndicesList(),
            },
          },
        ],
        is_update: req.getIsUpdate(),
      },
    });

    const response = new Empty();
    callback(null, response);
  },
};
