import * as grpc from '@grpc/grpc-js';
import {
  ServerUnaryCall,
  ServerWritableStream,
  sendUnaryData,
} from '@grpc/grpc-js';
import {
  ActivityLogData as ActivityLogDataProto,
  CallLlmRequest,
  CallLlmResponse,
  ChatCompletionRequest,
  ChatCompletionResponse,
  ChatMessage,
  ChatMessageDelta,
  Choice,
  ClaudeChatCompletionRequest,
  ClaudeChatCompletionResponse,
  ClaudeMessageResponseCitation,
  ClaudeMessageResponseContentBlock,
  Duration as DurationProto,
  GenerateEmbeddingResponse,
  IChatCompletionServiceServer,
  ResourceBaseModel,
  Statistics as StatisticsProto,
  TokenUsage as TokenUsageProto,
  ToolCall,
  VersionRequest,
  VersionResponse,
  getModelFromInput,
  toClaudeRequestObject,
  toOpenAIRequestObject,
} from '@tribble/chat-completion-service';
import { isReasoningModel, readVersionFile } from '@tribble/common-utils';
import * as structpb from 'google-protobuf/google/protobuf/struct_pb';
import { Timestamp } from 'google-protobuf/google/protobuf/timestamp_pb';
import OpenAI, { AzureOpenAI } from 'openai';
import {
  ChatCompletion,
  ChatCompletionChunk,
  ChatCompletionMessageParam,
} from 'openai/resources/chat';
import { Stream } from 'openai/streaming';
import { callAnthropic, callAnthropicStream } from './callers/anthropic';
import { call as azureCall } from './callers/azureOpenAi';
import { getWrappedVertexClient } from './callers/GCP';
import { call as openAiCall } from './callers/openAi';
import { constants } from './constants';
import { generateEmbeddingsFromAzure } from './embeddings';
import {
  addTokensToCounter,
  getRightSizeLLM,
  setBusy,
  subtractTokensFromCounter,
  tooBigForEverything,
} from './llmResource';
import { ActivityLogData, IResponse, LLMResource, Statistics } from './model';
import {
  FAKE_RESPONSE_SCHEMA_TOOL,
  processCompletionsResponse,
  transformRequest,
} from './util/genai_translator';
import { insertActivityLog } from './utils/logging';
import { telemetryClient } from './utils/telemetry';
import {
  ChatMessage as ChatMessageObject,
  countTokens,
  countTokensInMessage,
  countTokensInMessages,
} from './utils/tokens';

const CALL_LLM_API_VERSION = process.env.CALL_LLM_API_VERSION || '2024-10-21';

export const chatCompletionServer: IChatCompletionServiceServer = {
  // callLLM is a streamlined API for the common case of just sending messages.
  callLLM: async (
    call: ServerUnaryCall<CallLlmRequest, CallLlmResponse>,
    callback: sendUnaryData<CallLlmResponse>,
  ) => {
    const req = call.request;
    const baseResource = getModelFromInput(req.getModel());

    try {
      console.log(`(${req.getCorrelationId()}): [callLLM] Received request`);
      const token_size = countTokens(req.getInputMessagesRaw()),
        llm = getRightSizeLLM(
          req.getNeedsRedaction(),
          token_size,
          baseResource,
        );

      if (!llm) {
        const tooBig = tooBigForEverything(token_size, baseResource);

        console.log(
          `(${req.getCorrelationId()}): [callLLM] Error: No LLM resource available.`,
        );

        const error = tooBig
          ? {
              code: grpc.status.RESOURCE_EXHAUSTED,
              message: `Request too large for any available LLM resource (${token_size} tokens).`,
            }
          : {
              code: grpc.status.RESOURCE_EXHAUSTED,
              message: 'All LLM resources are busy. Please try again later.',
            };
        callback(error, null);
        return;
      }
      setBusy(llm.index, token_size);

      let messages: ChatCompletionMessageParam[];
      try {
        messages = JSON.parse(req.getInputMessagesRaw());
      } catch (err) {
        messages = [{ role: 'system', content: req.getInputMessagesRaw() }];
      }

      console.log(
        `(${req.getCorrelationId()}): [callLLM] Servicing request on [${llm.resource.name}]`,
      );

      const start = new Date();
      let result: IResponse<ChatCompletion>;
      if (llm.resource.is_internal) {
        result = await azureCall({
          apiKey: llm.resource.api_key,
          deploymentId: llm.resource.name,
          endpoint: llm.resource.url,
          model: llm.resource.basemodel,
          messages,
          apiVersion: CALL_LLM_API_VERSION,
          timeout: req.getTimeout(),
        });
      } else {
        result = await openAiCall(
          llm.resource.basemodel,
          llm.resource.api_key,
          messages,
          req.getCorrelationId(),
        );
      }
      const end = new Date(),
        durationMs = end.getTime() - start.getTime(),
        activityLogData = fromActivityLogDataProto(req.getActivityLogData());

      if (result.success) {
        const resp = new CallLlmResponse(),
          stats = getStatsObject({
            start,
            end,
            durationMs,
            result: result.response,
            is_azure: llm.resource.is_internal,
            resourceUsed: llm.resource,
            activity_log_data: activityLogData,
          });
        insertActivityLog(activityLogData, stats);

        //Add completion tokens to resource-token-counter.
        addTokensToCounter(llm.index, stats.token_usage.completion_tokens);

        console.log(
          `(${req.getCorrelationId()}): [callLLM] Got a response (duration=${durationMs}ms, total tokens=${stats.token_usage.total_tokens})`,
        );

        resp.setStats(toStatsProto(stats));
        resp.setContent(result.response.choices[0].message.content);

        // Successful response
        callback(null, resp);
        return;
      }

      console.log(
        `(${req.getCorrelationId()}): [callLLM] Failed: ${llm.resource.name}`,
      );

      telemetryClient.trackEvent({
        name: constants.EVENT_FAILED_LLM_CALL,
        measurements: { duration: durationMs },
        properties: {
          schema: req.getSchema,
          ...activityLogData,
          error: result.error,
        },
      });

      callback(
        {
          code: grpc.status.INTERNAL,
          message: result.error,
        },
        null,
      );
    } catch (error) {
      console.error(`(${req.getCorrelationId()}): [callLLM] ${error.stack}`);
      callback(
        {
          code: grpc.status.INTERNAL,
          message: error.message,
        },
        null,
      );
    }
  },

  // chatCompletion supports the complete OpenAI create chat completion API.
  chatCompletion: async (
    call: ServerUnaryCall<ChatCompletionRequest, ChatCompletionResponse>,
    callback: sendUnaryData<ChatCompletionResponse>,
  ) => {
    const req = call.request;

    const nonStreamingModels = [ResourceBaseModel.GPT_4_OH_1];
    if (nonStreamingModels.includes(req.getResource().getBaseModel())) {
      return nonStreamingChatCompletion({ call, callback });
    }

    try {
      const openaiReq = toOpenAIRequestObject(req.getParams());
      let promptTokenEstimate = countTokensInMessages(
        openaiReq['messages'],
        openaiReq['tools'],
      );
      const maxAnswerTokens = openaiReq.max_tokens || constants.TOKEN_BUFFER,
        llm = getRightSizeLLM(
          req.getNeedsRedaction(),
          promptTokenEstimate + maxAnswerTokens,
          req.getResource().getBaseModel(),
        );

      console.log(
        `(${req.getCorrelationId()}): [chatCompletion] Received request`,
      );

      if (!llm) {
        console.log(
          `(${req.getCorrelationId()}): [chatCompletion] Error: No LLM resource available.`,
        );

        const tooBig = tooBigForEverything(
          promptTokenEstimate + maxAnswerTokens,
          req.getResource().getBaseModel(),
        );

        const error = tooBig
          ? {
              code: grpc.status.RESOURCE_EXHAUSTED,
              message: `Request too large for any available LLM resource (${promptTokenEstimate + maxAnswerTokens} tokens).`,
            }
          : {
              code: grpc.status.RESOURCE_EXHAUSTED,
              message: 'All LLM resources are busy. Please try again later.',
            };
        callback(error, null);
        return;
      }
      setBusy(llm.index, promptTokenEstimate);

      console.log(
        `(${req.getCorrelationId()}): [chatCompletion] Servicing request on [${llm.resource.name}][${llm.resource.url}]`,
      );

      const start = new Date();
      const client = new AzureOpenAI({
        apiKey: llm.resource.api_key,
        apiVersion: req.getResource().getApiVersion(),
        maxRetries: 3,
        deployment: llm.resource.name,
        endpoint: llm.resource.url,
        timeout: req.getTimeout(),
      });

      const chatMessages = openaiReq['messages'];
      delete openaiReq['messages'];

      let activityLogData: ActivityLogData, durationMs: number;
      let usage: ChatCompletionChunk['usage'];

      try {
        const stream = (await client.chat.completions.create({
          ...openaiReq,
          messages: chatMessages,
          model: llm.resource.basemodel,
          stream: true,
          stream_options: {
            include_usage: true,
          },
        })) as unknown as Stream<ChatCompletionChunk>;

        const resp = new ChatCompletionResponse(),
          respMessages: ChatMessageObject[] = [];

        let currentArgsBuffer: string = null,
          currentToolCall: ToolCall = null,
          currentlyStreamingArgs: Boolean = false,
          respMessage: ChatMessageObject = null;

        for await (const chunk of stream) {
          // Function arguments arrive as a string that we buffer and return to
          // the client as an easy-to-use proto Struct object.
          //
          // At the start of the loop, assume we're not streaming arguments until
          // we see evidence otherwise.
          currentlyStreamingArgs = false;

          if (chunk.id) {
            resp.setId(chunk.id);
          }
          if (chunk.choices) {
            for (const choiceChunk of chunk.choices) {
              const choices = resp.getChoicesList();
              let choice = choices[choiceChunk.index];
              if (typeof choice === 'undefined') {
                choice = new Choice();
                choice.setIndex(choiceChunk.index);
                resp.addChoices(choice, choiceChunk.index);
              }

              if (choiceChunk.delta) {
                let message: ChatMessage;
                if (choice.hasMessage()) {
                  message = choice.getMessage();
                } else {
                  message = new ChatMessage();
                  respMessage = {
                    role: 'assistant',
                    content: '',
                    tool_calls: [],
                  };
                  choice.setMessage(message);
                  respMessages.push(respMessage);
                }

                if (choiceChunk.delta.content) {
                  message.setTextContent(
                    message.getTextContent() + choiceChunk.delta.content,
                  );
                  respMessage.content += choiceChunk.delta.content;
                }

                if (choiceChunk.delta.role) {
                  message.setRole(message.getRole() + choiceChunk.delta.role);
                }

                for (const toolChunk of choiceChunk.delta.tool_calls || []) {
                  let toolCall = message.getToolCallsList()[toolChunk['index']];
                  if (typeof toolCall === 'undefined') {
                    toolCall = new ToolCall();
                    toolCall.setType('function');
                    message.addToolCalls(toolCall, toolChunk['index']);
                  }
                  if (toolChunk['id']) {
                    toolCall.setId(toolCall.getId() + toolChunk['id']);
                  }
                  if (toolChunk.function) {
                    const functionChunk = toolChunk.function;
                    if (functionChunk.name) {
                      toolCall.setFunctionName(
                        toolCall.getFunctionName() + functionChunk.name,
                      );
                    }
                    if (functionChunk.arguments) {
                      currentlyStreamingArgs = true;

                      if (currentArgsBuffer === null) {
                        // We just started streaming arguments.
                        currentArgsBuffer = functionChunk.arguments;
                        currentToolCall = toolCall;
                      } else {
                        // We're already streaming arguments.
                        currentArgsBuffer += functionChunk.arguments;
                      }
                    }
                  }
                }
              }

              if (choiceChunk.finish_reason) {
                choice.setFinishReason(choiceChunk.finish_reason);
              }
            }
          }

          // Now that we've processed tool calls, we need to check to see if
          // any ongoing args buffering has just completed.
          if (currentArgsBuffer !== null && !currentlyStreamingArgs) {
            const args = JSON.parse(currentArgsBuffer),
              argsStruct = structpb.Struct.fromJavaScript(args);
            currentToolCall.setArguments(argsStruct);

            // Add tool call to token-counting message.
            respMessage.tool_calls.push({
              id: currentToolCall.getId(),
              type: 'function',
              function: {
                name: currentToolCall.getFunctionName(),
                arguments: currentArgsBuffer,
              },
            });

            // Reset the buffer for another tool call.
            currentArgsBuffer = null;
            currentToolCall = null;
          }

          if (chunk['created']) {
            resp.setCreated(Math.round(chunk['created']));
          }
          if (chunk['model']) {
            resp.setModel(chunk['model']);
          }
          if (chunk['object']) {
            resp.setObject(chunk['object']);
          }
          if (chunk['systemFingerprint']) {
            resp.setSystemFingerprint(chunk['systemFingerprint']);
          }
          if (chunk['refusal']) {
            resp.setRefusal(chunk['refusal']);
          }
          if (chunk['usage']) {
            usage = chunk['usage'];
          }
        }

        const end = new Date();

        const completionTokens =
          usage?.completion_tokens ||
          respMessages.map(countTokensInMessage).reduce((a, b) => a + b, 0);
        const promptTokens = usage?.prompt_tokens || promptTokenEstimate;
        const totalTokens =
          usage?.total_tokens || promptTokens + completionTokens;

        //Use the api returned usage tokens, they're better than our estimates.
        subtractTokensFromCounter(llm.index, promptTokenEstimate);
        addTokensToCounter(llm.index, promptTokens + completionTokens);

        durationMs = end.getTime() - start.getTime();
        activityLogData = fromActivityLogDataProto(req.getActivityLogData());

        const promptCost =
            llm.resource.cost_per_1k_token_prompt * (promptTokens / 1000),
          completionCost =
            llm.resource.cost_per_1k_token_completion *
            (completionTokens / 1000),
          totalCost = sumAndRound(promptCost, completionCost),
          stats = {
            duration: {
              start: start,
              end: end,
              durationMs: durationMs,
            },
            enter_queue: activityLogData.enter_queue,
            leave_queue: activityLogData.leave_queue,
            token_usage: {
              total_cost: totalCost,
              completion_tokens: completionTokens,
              prompt_tokens: promptTokens,
              total_tokens: totalTokens,
              ...usage,
            },
            llm_name: llm.resource.name,
          } as Statistics;

        console.log(
          `(${req.getCorrelationId()}): [chatCompletion] Got a response (duration=${durationMs}ms, total tokens=${promptTokens + completionTokens}, total cost=${totalCost})`,
        );

        insertActivityLog(activityLogData, stats);
        resp.setStats(toStatsProto(stats));

        // Successful response
        callback(null, resp);
        return;
      } catch (error) {
        console.log(
          `(${req.getCorrelationId()}): [chatCompletion] Failed: ${llm.resource.name} ${JSON.stringify(error)}`,
        );

        telemetryClient.trackEvent({
          name: constants.EVENT_FAILED_LLM_CALL,
          measurements: { duration: durationMs },
          properties: {
            schema: req.getSchema(),
            ...activityLogData,
            error: error,
          },
        });

        let internalErrorMsg = error;
        if (internalErrorMsg['code'] && internalErrorMsg['message']) {
          internalErrorMsg = `${internalErrorMsg['code']}: ${internalErrorMsg['message']}`;
        } else if (internalErrorMsg['message'] && internalErrorMsg['type']) {
          internalErrorMsg = `${internalErrorMsg['type']}: ${internalErrorMsg['message']}`;
        }

        callback(
          {
            code: grpc.status.INTERNAL,
            message: internalErrorMsg,
          },
          null,
        );
      }
    } catch (error) {
      console.error(
        `(${req.getCorrelationId()}): [chatCompletion] ${error.stack}`,
      );
      callback(
        {
          code: grpc.status.INTERNAL,
          message: error.message,
        },
        null,
      );
    }
  },

  // chatCompletionStream returns a stream of chat completions as they are
  // generated by the LLM. Content updates are returned as they're generated
  // while tool calls are returned only after they're fully buffered and
  // actionable.
  chatCompletionStream: async (
    call: ServerWritableStream<ChatCompletionRequest, ChatCompletionResponse>,
  ) => {
    const req = call.request;

    try {
      const openaiReq = toOpenAIRequestObject(req.getParams()),
        promptTokenEstimate = countTokensInMessages(
          openaiReq['messages'],
          openaiReq['tools'],
        ),
        maxAnswerTokens = openaiReq.max_tokens || constants.TOKEN_BUFFER,
        llm = getRightSizeLLM(
          req.getNeedsRedaction(),
          promptTokenEstimate + maxAnswerTokens,
          req.getResource().getBaseModel(),
        );

      console.log(
        `(${req.getCorrelationId()}): [chatCompletionStream] Received request`,
      );

      if (!llm) {
        console.log(
          `(${req.getCorrelationId()}): [chatCompletionStream] Error: No LLM resource available.`,
        );
        const tooBig = tooBigForEverything(
          promptTokenEstimate + maxAnswerTokens,
          req.getResource().getBaseModel(),
        );

        const error = tooBig
          ? {
              code: grpc.status.RESOURCE_EXHAUSTED,
              message: `Request too large for any available LLM resource (${promptTokenEstimate + maxAnswerTokens} tokens).`,
            }
          : {
              code: grpc.status.RESOURCE_EXHAUSTED,
              message: 'All LLM resources are busy. Please try again later.',
            };

        call.emit('error', error);
        return;
      }
      setBusy(llm.index, promptTokenEstimate);

      console.log(
        `(${req.getCorrelationId()}): [chatCompletionStream] Servicing request on [${llm.resource.name}][${llm.resource.url}]`,
      );

      const start = new Date();

      const client = new AzureOpenAI({
        apiKey: llm.resource.api_key,
        apiVersion: req.getResource().getApiVersion(),
        maxRetries: 3,
        deployment: llm.resource.name,
        endpoint: llm.resource.url,
        timeout: req.getTimeout(),
      });

      // Extact messages for Azure OpenAIClient interface.
      const chatMessages = openaiReq['messages'];
      delete openaiReq['messages'];

      let activityLogData: ActivityLogData, durationMs: number;
      let usage: ChatCompletionChunk['usage'];

      try {
        const streamConfig = {
          temperature: 0,
          ...openaiReq,
          messages: chatMessages,
          model: llm.resource.basemodel,
          stream: true,
          stream_options: {
            include_usage: true,
          },
        };

        // Reasoning models (at least as of now) don't support temperature
        if (isReasoningModel(llm.resource.basemodel)) {
          delete streamConfig['temperature'];
        }

        const stream = (await client.chat.completions.create(
          streamConfig,
        )) as unknown as Stream<ChatCompletionChunk>;

        const respMessages: ChatMessageObject[] = [];

        let resp = new ChatCompletionResponse(),
          sendChunk: Boolean = false,
          currentArgsBuffer: string = null,
          currentToolCall: ToolCall = null,
          currentlyStreamingArgs: Boolean = false,
          respChoiceIndex: number = 0,
          respMessage: ChatMessageObject = {
            role: 'assistant',
            content: '',
            tool_calls: [],
          };

        respMessages.push(respMessage);
        const heartBeatFrequency = 1000;
        let nthToken = 1;

        for await (const chunk of stream) {
          //Send an empty message every nth token to keep the connection alive
          //during loooooong running tool argument generation.
          if (nthToken++ % heartBeatFrequency === 0) {
            // console.log(`(${req.getCorrelationId()}) Sending heartbeat...`);
            const heartBeat = new ChatCompletionResponse();
            call.write(heartBeat);
          }

          // Assume there's nothing to send until we see evidence otherwise.
          sendChunk = false;

          // Function arguments arrive as a string that we buffer and return to
          // the client as an easy-to-use proto Struct object.
          //
          // At the start of the loop, assume we're not streaming arguments until
          // we see evidence otherwise.
          currentlyStreamingArgs = false;

          if (chunk.choices) {
            for (const choiceChunk of chunk.choices) {
              const choices = resp.getChoicesList();
              let choice: Choice;
              if (choices.length) {
                choice = choices[0];
              } else {
                choice = new Choice();
                choice.setIndex(choiceChunk.index);
                resp.addChoices(choice);
              }
              if (choiceChunk.index !== respChoiceIndex) {
                // One assistant completion message per choice. Generally
                // there's only one choice but users can request multiple.
                respChoiceIndex = choiceChunk.index;
                respMessage = {
                  role: 'assistant',
                  content: '',
                  tool_calls: [],
                };
                respMessages.push(respMessage);
              }

              if (choiceChunk.delta) {
                let message: ChatMessageDelta;
                if (choice.hasMessageDelta()) {
                  message = choice.getMessageDelta();
                } else {
                  message = new ChatMessageDelta();
                  choice.setMessageDelta(message);
                }

                if (choiceChunk.delta.content) {
                  message.setContentChunk(choiceChunk.delta.content);
                  respMessage.content += choiceChunk.delta.content;
                  sendChunk = true;
                }

                if (choiceChunk.delta.role) {
                  message.setRole(choiceChunk.delta.role);
                }

                for (const toolChunk of choiceChunk.delta.tool_calls || []) {
                  const toolCalls = message.getToolCallsList();
                  let toolCall: ToolCall;
                  if (toolCalls.length) {
                    toolCall = toolCalls[0];
                  } else {
                    toolCall = new ToolCall();
                    toolCall.setType('function');
                    message.addToolCalls(toolCall);
                  }

                  if (toolChunk['id']) {
                    toolCall.setId(toolCall.getId() + toolChunk['id']);
                  }
                  if (toolChunk.function) {
                    const functionChunk = toolChunk.function;
                    if (functionChunk.name) {
                      toolCall.setFunctionName(
                        toolCall.getFunctionName() + functionChunk.name,
                      );
                    }
                    if (functionChunk.arguments) {
                      currentlyStreamingArgs = true;

                      if (currentArgsBuffer === null) {
                        // We just started streaming arguments.
                        currentArgsBuffer = functionChunk.arguments;
                        currentToolCall = toolCall;
                      } else {
                        // We're already streaming arguments.
                        currentArgsBuffer += functionChunk.arguments;
                      }
                    }
                  }
                }

                if (choiceChunk.finish_reason) {
                  choice.setFinishReason(choiceChunk.finish_reason);
                  sendChunk = true;
                  currentlyStreamingArgs = false;
                }

                // Now that we've processed tool chunks, we need to check to
                // see if any ongoing args buffering has just completed.
                if (currentArgsBuffer !== null && !currentlyStreamingArgs) {
                  const args = JSON.parse(currentArgsBuffer),
                    argsStruct = structpb.Struct.fromJavaScript(args);
                  currentToolCall.setArguments(argsStruct);
                  respMessage.tool_calls.push({
                    id: currentToolCall.getId(),
                    type: 'function',
                    function: {
                      name: currentToolCall.getFunctionName(),
                      arguments: currentArgsBuffer,
                    },
                  });

                  // Reset the buffer for another tool call.
                  currentArgsBuffer = null;
                  currentToolCall = null;

                  // This means the tool call is done, let's send the chunk.
                  sendChunk = true;
                }
              }
            }
          }

          // Below are present and constant on all chunks, good to have but not
          // enough to trigger sending a chunk to the client.
          if (chunk['created']) {
            resp.setCreated(Math.round(chunk['created']));
          }
          if (chunk['id']) {
            resp.setId(chunk['id']);
          }
          if (chunk['model']) {
            resp.setModel(chunk['model']);
          }
          if (chunk['object']) {
            resp.setObject(chunk['object']);
          }
          if (chunk['systemFingerprint']) {
            resp.setSystemFingerprint(chunk['systemFingerprint']);
          }

          if (sendChunk) {
            call.write(resp);
            resp = new ChatCompletionResponse();
          }

          if (chunk['usage']) {
            usage = chunk['usage'];
          }
        }

        const end = new Date();

        const completionTokens =
          usage?.completion_tokens ||
          respMessages.map(countTokensInMessage).reduce((a, b) => a + b, 0);
        const promptTokens = usage?.prompt_tokens || promptTokenEstimate;
        const totalTokens =
          usage?.total_tokens || promptTokens + completionTokens;

        //Use the api returned usage tokens, they're better than our estimates.
        subtractTokensFromCounter(llm.index, promptTokenEstimate);
        addTokensToCounter(llm.index, promptTokens + completionTokens);

        durationMs = end.getTime() - start.getTime();
        activityLogData = fromActivityLogDataProto(req.getActivityLogData());

        const promptCost =
            llm.resource.cost_per_1k_token_prompt * (promptTokens / 1000),
          completionCost =
            llm.resource.cost_per_1k_token_completion *
            (completionTokens / 1000),
          stats = {
            duration: {
              start: start,
              end: end,
              durationMs: durationMs,
            },
            enter_queue: activityLogData.enter_queue,
            leave_queue: activityLogData.leave_queue,
            token_usage: {
              total_cost: sumAndRound(promptCost, completionCost),
              completion_tokens: completionTokens,
              prompt_tokens: promptTokens,
              total_tokens: totalTokens,
              ...usage,
            },
            llm_name: llm.resource.name,
          } as Statistics;

        console.log(
          `(${req.getCorrelationId()}): [chatCompletionStream] Got a response (duration=${durationMs}ms, total tokens=${totalTokens}, total cost=${sumAndRound(promptCost, completionCost)})`,
        );

        insertActivityLog(activityLogData, stats);

        resp.setStats(toStatsProto(stats));
        call.write(resp);

        // Successful response
        call.end();
        return;
      } catch (error) {
        console.log(
          `(${req.getCorrelationId()}): [chatCompletionStream] Failed: ${llm.resource.name} ${JSON.stringify(error)}`,
        );

        telemetryClient.trackEvent({
          name: constants.EVENT_FAILED_LLM_CALL,
          measurements: { duration: durationMs },
          properties: {
            schema: req.getSchema(),
            ...activityLogData,
            error: error,
          },
        });

        let internalErrorMsg = error;
        if (internalErrorMsg['code'] && internalErrorMsg['message']) {
          internalErrorMsg = `${internalErrorMsg['code']}: ${internalErrorMsg['message']}`;
        }

        call.emit('error', {
          code: grpc.status.INTERNAL,
          message: internalErrorMsg,
        });
      }
    } catch (error) {
      console.error(
        `(${req.getCorrelationId()}): [chatCompletionStream] ${error.stack}`,
      );
      call.emit('error', {
        code: grpc.status.INTERNAL,
        message: error.message,
      });
    }
  },

  geminiChatCompletionStream: async (
    call: ServerWritableStream<ChatCompletionRequest, ChatCompletionResponse>,
  ) => {
    const req = call.request;

    try {
      const openaiReq = toOpenAIRequestObject(req.getParams()),
        promptTokenEstimate = countTokensInMessages(
          openaiReq['messages'],
          openaiReq['tools'],
        ),
        maxAnswerTokens = openaiReq.max_tokens || constants.TOKEN_BUFFER,
        llm = getRightSizeLLM(
          req.getNeedsRedaction(),
          promptTokenEstimate + maxAnswerTokens,
          req.getResource().getBaseModel(),
        );

      console.log(
        `(${req.getCorrelationId()}): [geminiChatCompletionStream] Received request`,
      );

      if (!llm) {
        console.log(
          `(${req.getCorrelationId()}): [geminiChatCompletionStream] Error: No LLM resource available.`,
        );
        const tooBig = tooBigForEverything(
          promptTokenEstimate + maxAnswerTokens,
          req.getResource().getBaseModel(),
        );

        const error = tooBig
          ? {
              code: grpc.status.RESOURCE_EXHAUSTED,
              message: `Request too large for any available LLM resource (${promptTokenEstimate + maxAnswerTokens} tokens).`,
            }
          : {
              code: grpc.status.RESOURCE_EXHAUSTED,
              message: 'All LLM resources are busy. Please try again later.',
            };

        call.emit('error', error);
        return;
      }
      setBusy(llm.index, promptTokenEstimate);

      console.log(
        `(${req.getCorrelationId()}): [geminiChatCompletionStream] Servicing request on [${llm.resource.name}][${llm.resource.url}]`,
      );

      let activityLogData: ActivityLogData, durationMs: number;
      let usage: ChatCompletionChunk['usage'];
      const start = new Date();

      try {
        const transformedRequest = await transformRequest(openaiReq);
        const vertextClient = await getWrappedVertexClient(llm.resource);
        const response = await vertextClient.models.generateContent({
          ...transformedRequest,
          model: llm.resource.basemodel,
        });

        const transformedResponse = processCompletionsResponse(
          response,
          llm.resource.basemodel,
          response.responseId,
        );

        const hasReponseSchema = Boolean(openaiReq['response_format']);
        const hasToolCalls = Boolean(openaiReq['tools']?.length);
        // Gemini doesn't support response_schema AND tools.
        // We skirt this by using a fake tool with the response schema, and asking gemini to call it last.
        const usedFakeTool = hasReponseSchema && hasToolCalls;
        if (usedFakeTool) {
          // Let's grab that fake tool output and shove it into "content"
          // so the downstream caller gets it as expected.

          const finishTool =
            transformedResponse.choices[0].message.tool_calls?.find(
              (tool) => tool.function?.name === FAKE_RESPONSE_SCHEMA_TOOL,
            );
          if (finishTool) {
            transformedResponse.choices[0].message.content =
              finishTool.function.arguments;
            transformedResponse.choices[0].message.tool_calls = [];
            transformedResponse.choices[0].finish_reason = 'stop';
          }
        }

        let resp = new ChatCompletionResponse();
        resp.setModel(llm.resource.basemodel);
        resp.setId(transformedResponse.id);

        for (const choice of transformedResponse.choices) {
          const choiceChunk = new Choice();
          resp.setChoicesList([choiceChunk]);

          choiceChunk.setIndex(0);
          const messageDelta = new ChatMessageDelta();
          choiceChunk.setMessageDelta(messageDelta);
          choiceChunk.setFinishReason(choice.finish_reason);

          messageDelta.setRole('assistant');
          messageDelta.setContentChunk(choice.message.content);

          if (choice.message.tool_calls?.length) {
            for (const toolCallChunk of choice.message.tool_calls) {
              const toolCall = new ToolCall();
              messageDelta.getToolCallsList().push(toolCall);

              toolCall.setType('function');
              toolCall.setId(toolCallChunk.id);

              const args = JSON.parse(toolCallChunk.function.arguments);
              const argsStruct = structpb.Struct.fromJavaScript(args);
              toolCall.setArguments(argsStruct);
              toolCall.setFunctionName(toolCallChunk.function.name);
            }
          }
        }

        // Below are present and constant on all chunks, good to have but not
        // enough to trigger sending a chunk to the client.
        if (transformedResponse['created']) {
          resp.setCreated(Math.round(transformedResponse['created']));
        }
        if (transformedResponse['id']) {
          resp.setId(transformedResponse['id']);
        }
        if (transformedResponse['model']) {
          resp.setModel(transformedResponse['model']);
        }
        if (transformedResponse['object']) {
          resp.setObject(transformedResponse['object']);
        }
        if (transformedResponse['systemFingerprint']) {
          resp.setSystemFingerprint(transformedResponse['systemFingerprint']);
        }

        call.write(resp);
        resp = new ChatCompletionResponse();

        if (transformedResponse['usage']) {
          usage = transformedResponse['usage'];
        }

        const end = new Date();

        const completionTokens = usage?.completion_tokens;
        const promptTokens = usage?.prompt_tokens || promptTokenEstimate;
        const totalTokens =
          usage?.total_tokens || promptTokens + completionTokens;

        //Use the api returned usage tokens, they're better than our estimates.
        subtractTokensFromCounter(llm.index, promptTokenEstimate);
        addTokensToCounter(llm.index, promptTokens + completionTokens);

        durationMs = end.getTime() - start.getTime();
        activityLogData = fromActivityLogDataProto(req.getActivityLogData());

        console.log(
          `(${req.getCorrelationId()}): [geminiChatCompletionStream] Got a response (duration=${durationMs}ms, total tokens=${totalTokens})`,
        );

        const promptCost =
            llm.resource.cost_per_1k_token_prompt * (promptTokens / 1000),
          completionCost =
            llm.resource.cost_per_1k_token_completion *
            (completionTokens / 1000),
          stats = {
            duration: {
              start: start,
              end: end,
              durationMs: durationMs,
            },
            enter_queue: activityLogData.enter_queue,
            leave_queue: activityLogData.leave_queue,
            token_usage: {
              total_cost: sumAndRound(promptCost, completionCost),
              completion_tokens: completionTokens,
              prompt_tokens: promptTokens,
              total_tokens: totalTokens,
              ...usage,
            },
            llm_name: llm.resource.name,
          } as Statistics;

        insertActivityLog(activityLogData, stats);

        resp.setStats(toStatsProto(stats));
        call.write(resp);

        // Successful response
        call.end();
        return;
      } catch (error) {
        console.log(
          `(${req.getCorrelationId()}): [geminiChatCompletionStream] Failed: ${llm.resource.name} ${JSON.stringify(error)}`,
        );

        telemetryClient.trackEvent({
          name: constants.EVENT_FAILED_LLM_CALL,
          measurements: { duration: durationMs },
          properties: {
            schema: req.getSchema(),
            ...activityLogData,
            error: error,
          },
        });

        let internalErrorMsg = error;
        if (internalErrorMsg['code'] && internalErrorMsg['message']) {
          internalErrorMsg = `${internalErrorMsg['code']}: ${internalErrorMsg['message']}`;
        }

        call.emit('error', {
          code: grpc.status.INTERNAL,
          message: internalErrorMsg,
        });
      }
    } catch (error) {
      console.error(
        `(${req.getCorrelationId()}): [geminiChatCompletionStream] ${error.stack}`,
      );
      call.emit('error', {
        code: grpc.status.INTERNAL,
        message: error.message,
      });
    }
  },

  geminiChatCompletion: async (
    call: ServerUnaryCall<ChatCompletionRequest, ChatCompletionResponse>,
    callback: sendUnaryData<ChatCompletionResponse>,
  ) => {
    const req = call.request;

    try {
      const openaiReq = toOpenAIRequestObject(req.getParams());
      let promptTokenEstimate = countTokensInMessages(
        openaiReq['messages'],
        openaiReq['tools'],
      );
      const maxAnswerTokens = openaiReq.max_tokens || constants.TOKEN_BUFFER,
        llm = getRightSizeLLM(
          req.getNeedsRedaction(),
          promptTokenEstimate + maxAnswerTokens,
          req.getResource().getBaseModel(),
        );

      console.log(
        `(${req.getCorrelationId()}): [geminiChatCompletion] Received request`,
      );

      if (!llm) {
        console.log(
          `(${req.getCorrelationId()}): [geminiChatCompletion] Error: No LLM resource available.`,
        );

        const tooBig = tooBigForEverything(
          promptTokenEstimate + maxAnswerTokens,
          req.getResource().getBaseModel(),
        );

        const error = tooBig
          ? {
              code: grpc.status.RESOURCE_EXHAUSTED,
              message: `Request too large for any available LLM resource (${promptTokenEstimate + maxAnswerTokens} tokens).`,
            }
          : {
              code: grpc.status.RESOURCE_EXHAUSTED,
              message: 'All LLM resources are busy. Please try again later.',
            };
        callback(error, null);
        return;
      }
      setBusy(llm.index, promptTokenEstimate);

      console.log(
        `(${req.getCorrelationId()}): [geminiChatCompletion] Servicing request on [${llm.resource.name}][${llm.resource.url}]`,
      );

      const start = new Date();
      let activityLogData: ActivityLogData, durationMs: number;
      let usage: ChatCompletionChunk['usage'];

      try {
        const transformedRequest = await transformRequest(openaiReq);
        const vertextClient = await getWrappedVertexClient(llm.resource);
        const response = await vertextClient.models.generateContent({
          ...transformedRequest,
          model: llm.resource.basemodel,
        });

        const transformedResponse = processCompletionsResponse(
          response,
          llm.resource.basemodel,
          response.responseId,
        );

        const hasReponseSchema = Boolean(openaiReq['response_format']);
        const hasToolCalls = Boolean(openaiReq['tools']?.length);
        // Gemini doesn't support response_schema AND tools.
        // We skirt this by using a fake tool with the response schema, and asking gemini to call it last.
        const usedFakeTool = hasReponseSchema && hasToolCalls;
        if (usedFakeTool) {
          // Let's grab that fake tool output and shove it into "content"
          // so the downstream caller gets it as expected.

          const finishTool =
            transformedResponse.choices[0].message.tool_calls?.find(
              (tool) => tool.function?.name === FAKE_RESPONSE_SCHEMA_TOOL,
            );
          if (finishTool) {
            transformedResponse.choices[0].message.content =
              finishTool.function.arguments;
            transformedResponse.choices[0].message.tool_calls = [];
            transformedResponse.choices[0].finish_reason = 'stop';
          }
        }

        let resp = new ChatCompletionResponse();
        resp.setModel(llm.resource.basemodel);
        resp.setId(transformedResponse.id);

        for (const choice of transformedResponse.choices) {
          const choiceChunk = new Choice();
          resp.setChoicesList([choiceChunk]);

          choiceChunk.setIndex(0);
          const messageDelta = new ChatMessageDelta();
          choiceChunk.setMessageDelta(messageDelta);
          choiceChunk.setFinishReason(choice.finish_reason);

          messageDelta.setRole('assistant');
          messageDelta.setContentChunk(choice.message.content);

          if (choice.message.tool_calls?.length) {
            for (const toolCallChunk of choice.message.tool_calls) {
              const toolCall = new ToolCall();
              messageDelta.getToolCallsList().push(toolCall);

              toolCall.setType('function');
              toolCall.setId(toolCallChunk.id);

              const args = JSON.parse(toolCallChunk.function.arguments);
              const argsStruct = structpb.Struct.fromJavaScript(args);
              toolCall.setArguments(argsStruct);
              toolCall.setFunctionName(toolCallChunk.function.name);
            }
          }
        }

        const end = new Date();

        const completionTokens = usage?.completion_tokens;
        const promptTokens = usage?.prompt_tokens || promptTokenEstimate;
        const totalTokens =
          usage?.total_tokens || promptTokens + completionTokens;

        //Use the api returned usage tokens, they're better than our estimates.
        subtractTokensFromCounter(llm.index, promptTokenEstimate);
        addTokensToCounter(llm.index, promptTokens + completionTokens);

        durationMs = end.getTime() - start.getTime();
        activityLogData = fromActivityLogDataProto(req.getActivityLogData());

        console.log(
          `(${req.getCorrelationId()}): [geminiChatCompletion] Got a response (duration=${durationMs}ms, total tokens=${totalTokens})`,
        );

        const promptCost =
            llm.resource.cost_per_1k_token_prompt * (promptTokens / 1000),
          completionCost =
            llm.resource.cost_per_1k_token_completion *
            (completionTokens / 1000),
          totalCost = sumAndRound(promptCost, completionCost),
          stats = {
            duration: {
              start: start,
              end: end,
              durationMs: durationMs,
            },
            enter_queue: activityLogData.enter_queue,
            leave_queue: activityLogData.leave_queue,
            token_usage: {
              total_cost: totalCost,
              completion_tokens: completionTokens,
              prompt_tokens: promptTokens,
              total_tokens: totalTokens,
              ...usage,
            },
            llm_name: llm.resource.name,
          } as Statistics;

        console.log(
          `(${req.getCorrelationId()}): [geminiChatCompletion] Got a response (duration=${durationMs}ms, total tokens=${promptTokens + completionTokens}, total cost=${totalCost})`,
        );

        insertActivityLog(activityLogData, stats);
        resp.setStats(toStatsProto(stats));

        // Successful response
        callback(null, resp);
        return;
      } catch (error) {
        console.log(
          `(${req.getCorrelationId()}): [geminiChatCompletion] Failed: ${llm.resource.name} ${JSON.stringify(error)}`,
        );

        telemetryClient.trackEvent({
          name: constants.EVENT_FAILED_LLM_CALL,
          measurements: { duration: durationMs },
          properties: {
            schema: req.getSchema(),
            ...activityLogData,
            error: error,
          },
        });

        let internalErrorMsg = error;
        if (internalErrorMsg['code'] && internalErrorMsg['message']) {
          internalErrorMsg = `${internalErrorMsg['code']}: ${internalErrorMsg['message']}`;
        } else if (internalErrorMsg['message'] && internalErrorMsg['type']) {
          internalErrorMsg = `${internalErrorMsg['type']}: ${internalErrorMsg['message']}`;
        }

        callback(
          {
            code: grpc.status.INTERNAL,
            message: internalErrorMsg,
          },
          null,
        );
      }
    } catch (error) {
      console.error(
        `(${req.getCorrelationId()}): [geminiChatCompletion] ${error.stack}`,
      );
      callback(
        {
          code: grpc.status.INTERNAL,
          message: error.message,
        },
        null,
      );
    }
  },

  generateEmbedding: async (call, callback) => {
    const req = call.request;
    const text = req.getText();
    const resp = new GenerateEmbeddingResponse();

    const embeddingResponse = await generateEmbeddingsFromAzure(text);
    if (!embeddingResponse || !embeddingResponse.embedding) {
      callback(
        {
          code: grpc.status.INTERNAL,
          message: 'Failed to generate embedding',
        },
        null,
      );
      return;
    }
    resp.setProvider(embeddingResponse.origin);
    resp.setEmbeddingList(embeddingResponse.embedding);
    callback(null, resp);
  },

  claudeChatCompletion: async (call, callback) => {
    const req = call.request;
    const params = req.getParams();
    const baseModel = getModelFromInput(params.getModel());
    const tokenEstimate = 20000; //TODO @dantribble: actually calculate this
    console.log(
      `(${req.getCorrelationId()}): [claudeChatCompletion] Received request`,
    );

    const llm = getRightSizeLLM(false, tokenEstimate, baseModel);

    if (!llm) {
      const tooBig = tooBigForEverything(tokenEstimate, baseModel);
      const error = tooBig
        ? {
            code: grpc.status.RESOURCE_EXHAUSTED,
            message: `Request too large for any available LLM resource (${tokenEstimate} tokens).`,
          }
        : {
            code: grpc.status.RESOURCE_EXHAUSTED,
            message: 'All LLM resources are busy. Please try again later.',
          };
      callback(error, null);
      return;
    }
    setBusy(llm.index, tokenEstimate);

    try {
      const claudeReq = toClaudeRequestObject(params);
      const start = new Date();
      const timeout = req.getTimeout() || 60000;

      console.log(
        `(${req.getCorrelationId()}): [claudeChatCompletion] Servicing request on [${llm.resource.name}][${llm.resource.url}] (with timeout ${timeout})`,
      );

      const response = await callAnthropic({
        ...claudeReq,
        model: llm.resource.basemodel,
        max_tokens: claudeReq.max_tokens || constants.TOKEN_BUFFER,
        resource: llm.resource,
        timeout,
      });
      const end = new Date();

      const resp = new ClaudeChatCompletionResponse();

      resp.setId(response.id);
      resp.setModel(response.model);
      resp.setType(response.type);
      resp.setRole(response.role);
      if (response.stop_reason) {
        resp.setStopReason(response.stop_reason);
      }
      if (response.stop_sequence) {
        resp.setStopSequence(response.stop_sequence);
      }
      if (response.content?.length > 0) {
        for (const content of response.content) {
          const contentBlock = new ClaudeMessageResponseContentBlock();
          contentBlock.setType(content.type);
          if (content.type === 'text') {
            contentBlock.setText(content.text);
            if (content.citations?.length > 0) {
              const citations: ClaudeMessageResponseCitation[] = [];
              for (const citation of content.citations) {
                const citationBlock = new ClaudeMessageResponseCitation();
                citationBlock.setCitedText(citation.cited_text);
                citationBlock.setDocumentIndex(citation.document_index);
                citationBlock.setDocumentTitle(citation.document_title);
                if (citation.type === 'page_location') {
                  citationBlock.setStartPageNumber(citation.start_page_number);
                  citationBlock.setEndPageNumber(citation.end_page_number);
                }
                if (citation.type === 'char_location') {
                  citationBlock.setStartCharIndex(citation.start_char_index);
                  citationBlock.setEndCharIndex(citation.end_char_index);
                }
                citations.push(citationBlock);
              }
              contentBlock.setCitationsList(citations);
            }
          }
          if (content.type === 'tool_use') {
            contentBlock.setId(content.id);
            contentBlock.setName(content.name);
            if (content.input) {
              contentBlock.setInput(
                structpb.Struct.fromJavaScript(content.input as any),
              );
            }
          }
          resp.addContent(contentBlock);
        }
      }

      let activityLogData = fromActivityLogDataProto(req.getActivityLogData());

      const completionTokens = response.usage?.output_tokens;
      const promptTokens = response.usage?.input_tokens;
      const promptCost =
        llm.resource.cost_per_1k_token_prompt * (promptTokens / 1000);
      const completionCost =
        llm.resource.cost_per_1k_token_completion * (completionTokens / 1000);
      const totalTokens = promptTokens + completionTokens;
      const totalCost = sumAndRound(promptCost, completionCost);

      subtractTokensFromCounter(llm.index, tokenEstimate);
      addTokensToCounter(llm.index, totalTokens);

      const stats = {
        duration: {
          start,
          end,
          durationMs: end.getTime() - start.getTime(),
        },
        token_usage: {
          total_cost: totalCost,
          completion_tokens: completionTokens,
          prompt_tokens: promptTokens,
          total_tokens: totalTokens,
          ...response.usage,
        },
        llm_name: llm.resource.name,
      } as Statistics;

      insertActivityLog(activityLogData, stats);
      resp.setStats(toStatsProto(stats));
      callback(null, resp);
      console.log(
        `(${req.getCorrelationId()}): [claudeChatCompletion] Got a response (duration=${stats.duration.durationMs}ms, total tokens=${totalTokens}, total cost=${totalCost})`,
      );
    } catch (error) {
      let internalErrorMsg = error;
      if (internalErrorMsg['code'] && internalErrorMsg['message']) {
        internalErrorMsg = `${internalErrorMsg['code']}: ${internalErrorMsg['message']}`;
      } else if (internalErrorMsg['message'] && internalErrorMsg['type']) {
        internalErrorMsg = `${internalErrorMsg['type']}: ${internalErrorMsg['message']}`;
      }
      console.log(
        `(${req.getCorrelationId()}): [claudeChatCompletion] Failed. There may be retries... ${llm.resource.name} ${JSON.stringify(error)}`,
      );

      callback(
        {
          code: grpc.status.INTERNAL,
          message: internalErrorMsg,
        },
        null,
      );
    }
  },
  claudeChatCompletionStream: async (
    call: ServerWritableStream<
      ClaudeChatCompletionRequest,
      ClaudeChatCompletionResponse
    >,
  ) => {
    const req = call.request;
    const params = req.getParams();
    const claudeReq = toClaudeRequestObject(params);
    const baseModel = getModelFromInput(params.getModel());
    const tokenEstimate = 20000; //TODO @dantribble: actually calculate this

    const llm = getRightSizeLLM(false, tokenEstimate, baseModel);

    if (!llm) {
      const tooBig = tooBigForEverything(tokenEstimate, baseModel);
      const error = tooBig
        ? {
            code: grpc.status.RESOURCE_EXHAUSTED,
            message: `Request too large for any available LLM resource (${tokenEstimate} tokens).`,
          }
        : {
            code: grpc.status.RESOURCE_EXHAUSTED,
            message: 'All LLM resources are busy. Please try again later.',
          };
      call.emit('error', error);
      call.end();
      return;
    }
    console.log(
      `(${req.getCorrelationId()}): [claudeChatCompletionStream] Servicing request on [${llm.resource.name}][${llm.resource.url}]`,
    );

    claudeReq['model'] = llm.resource.basemodel;
    setBusy(llm.index, tokenEstimate);

    try {
      const start = new Date();
      const stream = await callAnthropicStream({
        ...claudeReq,
        model: llm.resource.basemodel,
        max_tokens: 5000,
        resource: llm.resource,
      });

      let sendChunk: Boolean = false;
      let currentArgsBuffer: string = '';
      let currentlyStreamingArgs: Boolean = false;
      let currentTool = {
        type: null,
        id: null,
        name: null,
        input: null,
      };

      let inputTokens = 0;
      let outputTokens = 0;

      const heartBeatFrequency = 1000;
      let nthToken = 1;

      for await (const event of stream) {
        if (nthToken % heartBeatFrequency === 0) {
          const heartbeat = new ClaudeChatCompletionResponse();
          call.write(heartbeat);
        }

        let resp = new ClaudeChatCompletionResponse();
        if (event.type === 'message_start') {
          resp.setId(event.message.id);
          resp.setType('message_start');
          resp.setRole(event.message.role);
          resp.setModel(event.message.model);
          const usage = new TokenUsageProto();
          usage.setPromptTokens(event.message.usage.input_tokens);
          inputTokens += event.message.usage.input_tokens || 0;
          outputTokens += event.message.usage.output_tokens || 0;
          usage.setCompletionTokens(event.message.usage.output_tokens);
          const stats = new StatisticsProto();
          stats.setTokenUsage(usage);
        }

        if (event.type === 'content_block_start') {
          const contentBlock = event.content_block;
          if (contentBlock.type === 'text') {
            sendChunk = true;
            const delta = new ClaudeMessageResponseContentBlock();
            delta.setType('text');
            delta.setTextDelta(contentBlock.text);
            resp.addDelta(delta);
          }
          if (contentBlock.type === 'tool_use') {
            sendChunk = false;
            currentTool.type = 'tool_use';
            currentTool.id = contentBlock.id;
            currentTool.name = contentBlock.name;
            currentlyStreamingArgs = true;
          }
        }

        if (event.type === 'content_block_delta') {
          const deltaBlock = new ClaudeMessageResponseContentBlock();
          const eventDelta = event.delta;
          if (eventDelta.type === 'text_delta') {
            sendChunk = true;
            deltaBlock.setType('text');
            deltaBlock.setTextDelta(eventDelta.text);
            deltaBlock.setIndex(event.index);
            resp.addDelta(deltaBlock);
          }

          if (eventDelta.type === 'input_json_delta') {
            sendChunk = false;
            currentlyStreamingArgs = true;
            if (eventDelta.partial_json) {
              currentArgsBuffer += eventDelta.partial_json;
            }
          }
          //One day we will support citations and thinking...
        }

        if (event.type === 'content_block_stop') {
          currentlyStreamingArgs = false;
        }

        if (event.type === 'message_delta') {
          const eventDelta = event.delta;
          if (eventDelta.stop_reason === 'tool_use') {
            //Done streaming a tool
            currentlyStreamingArgs = false;
            if (event.usage) {
              outputTokens += event.usage.output_tokens || 0;
              const usage = new TokenUsageProto();
              usage.setCompletionTokens(event.usage.output_tokens);
              const stats = new StatisticsProto();
              stats.setTokenUsage(usage);
              resp.setStats(stats);
            }
          }
        }

        if (currentArgsBuffer != '' && !currentlyStreamingArgs) {
          //We are done streaming a tool. we can send it.
          const args = JSON.parse(currentArgsBuffer);
          const argsStruct = structpb.Struct.fromJavaScript(args);
          const deltaProto = new ClaudeMessageResponseContentBlock();
          deltaProto.setType('tool_use');
          deltaProto.setId(currentTool.id);
          deltaProto.setName(currentTool.name);
          deltaProto.setInput(argsStruct);
          resp.addDelta(deltaProto);
          //Reset the buffer and tool
          currentArgsBuffer = '';
          currentTool = {
            type: null,
            id: null,
            name: null,
            input: null,
          };

          sendChunk = true;
        }

        if (sendChunk) {
          call.write(resp);
          resp = new ClaudeChatCompletionResponse();
        }
      }

      const end = new Date();

      const promptCost =
        llm.resource.cost_per_1k_token_prompt * (inputTokens / 1000);
      const completionCost =
        llm.resource.cost_per_1k_token_completion * (outputTokens / 1000);
      const totalTokens = inputTokens + outputTokens;
      const totalCost = sumAndRound(promptCost, completionCost);

      subtractTokensFromCounter(llm.index, tokenEstimate);
      addTokensToCounter(llm.index, totalTokens);

      console.log(
        `(${req.getCorrelationId()}): [claudeChatCompletionStream] Got a response (duration=${end.getTime() - start.getTime()}ms, total_tokens=${totalTokens}, total_cost=${totalCost})`,
      );

      const stats = {
        duration: {
          start,
          end,
          durationMs: end.getTime() - start.getTime(),
        },
        token_usage: {
          total_cost: totalCost,
          completion_tokens: outputTokens,
          prompt_tokens: inputTokens,
          total_tokens: totalTokens,
        },
        llm_name: llm.resource.name,
      } as Statistics;
      let activityLogData = fromActivityLogDataProto(req.getActivityLogData());
      insertActivityLog(activityLogData, stats);
      const resp = new ClaudeChatCompletionResponse();
      resp.setStats(toStatsProto(stats));
      call.write(resp);
    } catch (error) {
      console.log(
        `(${req.getCorrelationId()}): [claudeChatCompletionStream] Failed. There may be retries... ${llm.resource.name}, ${error}`,
      );
      call.emit('error', {
        code: grpc.status.INTERNAL,
        message: error.message,
      });
    } finally {
      call.end();
    }
  },

  getVersion: async (
    call: ServerUnaryCall<VersionRequest, VersionResponse>,
    callback: sendUnaryData<VersionResponse>,
  ) => {
    try {
      const versionData = readVersionFile();
      const response = new VersionResponse();

      if (versionData) {
        response.setServiceName(versionData.serviceName || 'Q');
        response.setVersion(versionData.version || 'unknown');
        response.setCommitSha(versionData.commitSha || 'unknown');
        response.setCommitShort(versionData.commitShort || 'unknown');
        response.setCommitDate(versionData.commitDate || 'unknown');
        response.setBuildDate(versionData.buildDate || 'unknown');
        response.setBuildId(versionData.buildId || 'unknown');
        response.setBranch(versionData.branch || 'unknown');
      } else {
        response.setServiceName('Q');
        response.setVersion('unknown');
        response.setCommitSha('unknown');
        response.setCommitShort('unknown');
        response.setCommitDate('unknown');
        response.setBuildDate('unknown');
        response.setBuildId('unknown');
        response.setBranch('unknown');
      }

      callback(null, response);
    } catch (error) {
      console.error('Error in GetVersion:', error);
      callback(
        {
          code: grpc.status.INTERNAL,
          message: 'Failed to retrieve version information',
        },
        null,
      );
    }
  },
};

//This is just to support non-streaming models like o1
async function nonStreamingChatCompletion({
  call,
  callback,
}: {
  call: ServerUnaryCall<ChatCompletionRequest, ChatCompletionResponse>;
  callback: sendUnaryData<ChatCompletionResponse>;
}) {
  const req = call.request;

  try {
    const openaiReq = toOpenAIRequestObject(req.getParams());
    let promptTokenEstimate = countTokensInMessages(
      openaiReq['messages'],
      openaiReq['tools'],
    );
    const maxAnswerTokens = openaiReq.max_tokens || constants.TOKEN_BUFFER,
      llm = getRightSizeLLM(
        req.getNeedsRedaction(),
        promptTokenEstimate + maxAnswerTokens,
        req.getResource().getBaseModel(),
      );

    console.log(
      `(${req.getCorrelationId()}): [chatCompletion] Received request`,
    );

    if (!llm) {
      console.log(
        `(${req.getCorrelationId()}): [chatCompletion] Error: No LLM resource available.`,
      );

      const tooBig = tooBigForEverything(
        promptTokenEstimate + maxAnswerTokens,
        req.getResource().getBaseModel(),
      );

      const error = tooBig
        ? {
            code: grpc.status.RESOURCE_EXHAUSTED,
            message: `Request too large for any available LLM resource (${promptTokenEstimate + maxAnswerTokens} tokens).`,
          }
        : {
            code: grpc.status.RESOURCE_EXHAUSTED,
            message: 'All LLM resources are busy. Please try again later.',
          };
      callback(error, null);
      return;
    }
    setBusy(llm.index, promptTokenEstimate);

    console.log(
      `(${req.getCorrelationId()}): [chatCompletion] Servicing request on [${llm.resource.name}][${llm.resource.url}]`,
    );

    const start = new Date();
    const client = new AzureOpenAI({
      apiKey: llm.resource.api_key,
      apiVersion: req.getResource().getApiVersion(),
      maxRetries: 3,
      deployment: llm.resource.name,
      endpoint: llm.resource.url,
      timeout: req.getTimeout(),
    });

    // Extact messages for Azure OpenAIClient interface.
    const chatMessages = openaiReq['messages'];
    delete openaiReq['messages'];

    openaiReq['temperature'] = req.getParams().getTemperature() || 0;

    let activityLogData: ActivityLogData, durationMs: number;

    try {
      const completion = await client.chat.completions.create({
        ...openaiReq,
        messages: chatMessages,
        model: llm.resource.basemodel,
      });

      const resp = new ChatCompletionResponse(),
        respMessages: ChatMessageObject[] = [];

      let respMessage: ChatMessageObject = null;

      if (completion.id) {
        resp.setId(completion.id);
      }

      if (completion.choices) {
        for (const completionChoice of completion.choices) {
          const choices = resp.getChoicesList();
          let choice = choices[completionChoice.index];
          if (typeof choice === 'undefined') {
            choice = new Choice();
            choice.setIndex(completionChoice.index);
            resp.addChoices(choice, completionChoice.index);
          }

          let message: ChatMessage;
          if (choice.hasMessage()) {
            message = choice.getMessage();
          } else {
            message = new ChatMessage();
            respMessage = {
              role: 'assistant',
              content: '',
              tool_calls: [],
            };
            choice.setMessage(message);
            respMessages.push(respMessage);
          }

          if (completionChoice.message.content) {
            message.setTextContent(completionChoice.message.content);
          }

          if (completionChoice.message.role) {
            message.setRole(completionChoice.message.role);
          }

          if (completionChoice.message.refusal) {
            resp.setRefusal(completionChoice.message.refusal);
          }

          for (const toolCall of completionChoice.message.tool_calls || []) {
            let toolCallProto = new ToolCall();
            toolCallProto.setType('function');
            message.addToolCalls(toolCallProto);

            if (toolCall.id) {
              toolCallProto.setId(toolCall.id);
            }

            if (toolCall.function) {
              let functionCall = toolCall.function;
              if (functionCall.name) {
                toolCallProto.setFunctionName(functionCall.name);
              }

              if (functionCall.arguments) {
                let args = JSON.parse(functionCall.arguments),
                  argsStruct = structpb.Struct.fromJavaScript(args);
                toolCallProto.setArguments(argsStruct);
              }
              respMessage.tool_calls.push({
                id: toolCallProto.getId(),
                type: 'function',
                function: {
                  name: toolCallProto.getFunctionName(),
                  arguments: functionCall.arguments,
                },
              });
            }
          }
        }
      }

      if (completion.created) {
        resp.setCreated(Math.round(completion.created));
      }

      if (completion.model) {
        resp.setModel(completion.model);
      }

      if (completion.object) {
        resp.setObject(completion.object);
      }

      if (completion.system_fingerprint) {
        resp.setSystemFingerprint(completion.system_fingerprint);
      }

      const end = new Date();
      let completionTokens =
        completion.usage?.completion_tokens ||
        respMessages.map(countTokensInMessage).reduce((a, b) => a + b, 0);

      const promptTokens =
        completion.usage?.prompt_tokens || promptTokenEstimate;

      subtractTokensFromCounter(llm.index, promptTokenEstimate);
      addTokensToCounter(llm.index, promptTokens + completionTokens);

      durationMs = end.getTime() - start.getTime();
      activityLogData = fromActivityLogDataProto(req.getActivityLogData());

      const promptCost =
          llm.resource.cost_per_1k_token_prompt * (promptTokens / 1000),
        completionCost =
          llm.resource.cost_per_1k_token_completion * (completionTokens / 1000),
        totalCost = sumAndRound(promptCost, completionCost),
        stats = {
          duration: {
            start: start,
            end: end,
            durationMs: durationMs,
          },
          enter_queue: activityLogData.enter_queue,
          leave_queue: activityLogData.leave_queue,
          token_usage: {
            total_cost: totalCost,
            completion_tokens: completionTokens,
            prompt_tokens: promptTokens,
            total_tokens: promptTokens + completionTokens,
            ...completion.usage,
          },
          llm_name: llm.resource.name,
        } as Statistics;

      console.log(
        `(${req.getCorrelationId()}): [chatCompletion] Got a response (duration=${durationMs}ms, total tokens=${promptTokenEstimate + completionTokens}, total cost=${totalCost})`,
      );

      insertActivityLog(activityLogData, stats);

      resp.setStats(toStatsProto(stats));

      // Successful response
      callback(null, resp);
      return;
    } catch (err) {
      console.log(
        `(${req.getCorrelationId()}): [chatCompletion] Failed: ${llm.resource.name} ${JSON.stringify(err)}`,
      );

      telemetryClient.trackEvent({
        name: constants.EVENT_FAILED_LLM_CALL,
        measurements: { duration: durationMs },
        properties: {
          schema: req.getSchema(),
          ...activityLogData,
          error: err,
        },
      });

      let internalErrorMsg = err;
      if (internalErrorMsg['code'] && internalErrorMsg['message']) {
        internalErrorMsg = `${internalErrorMsg['code']}: ${internalErrorMsg['message']}`;
      } else if (internalErrorMsg['message'] && internalErrorMsg['type']) {
        internalErrorMsg = `${internalErrorMsg['type']}: ${internalErrorMsg['message']}`;
      }

      callback(
        {
          code: grpc.status.INTERNAL,
          message: internalErrorMsg,
        },
        null,
      );
    }
  } catch (error) {
    console.error(
      `(${req.getCorrelationId()}): [chatCompletion] ${error.stack}`,
    );
    callback(
      {
        code: grpc.status.INTERNAL,
        message: error.message,
      },
      null,
    );
  }
}

function toStatsProto(stats: Statistics): StatisticsProto {
  const statsProto = new StatisticsProto(),
    durationProto = new DurationProto();

  const tokenUsageProto = new TokenUsageProto();
  tokenUsageProto.setTotalCost(stats.token_usage.total_cost);
  tokenUsageProto.setCompletionTokens(stats.token_usage.completion_tokens);
  tokenUsageProto.setPromptTokens(stats.token_usage.prompt_tokens);
  tokenUsageProto.setTotalTokens(stats.token_usage.total_tokens);
  statsProto.setTokenUsage(tokenUsageProto);

  const startTS = new Timestamp(),
    endTS = new Timestamp();
  startTS.fromDate(stats.duration.start);
  durationProto.setStart(startTS);
  endTS.fromDate(stats.duration.end);
  durationProto.setEnd(endTS);
  statsProto.setDuration(durationProto);

  return statsProto;
}

function fromActivityLogDataProto(pb: ActivityLogDataProto): ActivityLogData {
  const result = {
    client_id: pb.getClientId() || undefined,
    user_id: pb.getUserId() || undefined,
    details: pb.getDetails() || undefined,
    event: pb.getEvent() || undefined,
    source_table: pb.getSourceTable() as
      | 'conversation'
      | 'conversation_detail'
      | 'content',
    source_id: pb.getSourceId(),
    type: pb.getType(),
    exclude_from_interaction_count: pb.getExcludeFromInteractionCount(),
  };

  return result;
}

interface StatsParams {
  start: Date;
  end: Date;
  durationMs: number;
  result: ChatCompletion;
  is_azure: boolean;
  resourceUsed: LLMResource;
  activity_log_data: ActivityLogData;
}

function sumAndRound(prompt: number, completion: number) {
  return Math.round((prompt + completion) * 1e5) / 1e5;
}

//Make the stats object that goes in db/activity_log
const getStatsObject = (params: StatsParams): Statistics => {
  const stats = {
    duration: {
      start: params.start,
      end: params.end,
      durationMs: params.durationMs,
    },
    token_usage: {},
    llm_name: params.resourceUsed.name,
  } as Statistics;

  const { enter_queue, leave_queue } = params.activity_log_data;
  if (enter_queue) {
    stats.enter_queue = enter_queue;
  }
  if (leave_queue) {
    stats.leave_queue = leave_queue;
  }

  if (params.is_azure) {
    const usage = (params.result as ChatCompletion).usage ?? {};

    // Azure OAI usage looks like: { prompt_tokens: 1650, completion_tokens: 1, total_tokens: 1651 }
    // But somehow the schema expecting promptTokens and completionTokens? Anyway...cover all bases
    const prompt_tokens = usage['promptTokens'] || usage['prompt_tokens'] || 0;
    const completion_tokens =
      usage['completionTokens'] || usage['completion_tokens'] || 0;

    const prompt_cost =
      params.resourceUsed.cost_per_1k_token_prompt * (prompt_tokens / 1000);
    const completion_cost =
      params.resourceUsed.cost_per_1k_token_completion *
      (completion_tokens / 1000);

    stats.token_usage = {
      total_cost: sumAndRound(prompt_cost, completion_cost),
      completion_tokens: completion_tokens,
      prompt_tokens: prompt_tokens,
      total_tokens:
        usage['totalTokens'] ||
        usage['total_tokens'] ||
        completion_tokens + prompt_tokens,
    };
  } else {
    const usage = (params.result as ChatCompletion).usage ?? {};
    const prompt_cost =
      params.resourceUsed.cost_per_1k_token_prompt *
      (usage['prompt_tokens'] / 1000);
    const completion_cost =
      params.resourceUsed.cost_per_1k_token_completion *
      (usage['completion_tokens'] / 1000);

    stats.token_usage = {
      total_cost: sumAndRound(prompt_cost, completion_cost),
      completion_tokens: usage['completion_tokens'],
      prompt_tokens: usage['prompt_tokens'],
      total_tokens: usage['total_tokens'],
    };
  }
  return stats as Statistics;
};
