//Command and control the queue

import { EventGridDeserializer } from '@azure/eventgrid';
import {
  ProcessErrorArgs,
  ServiceBusReceivedMessage,
  ServiceBusReceiver,
} from '@azure/service-bus';
import { initializeFunctionTimer, set_interval } from './functionQueue';
import { init as refreshLlms } from './llmResource';
import { FunctionRegistryEntry } from './model';
import {
  setFunctionExpiry,
  setHealthCheckInterval,
  set_function_map,
} from './utils/azure';
import {
  getSbClientCNC,
  handleMessageError,
  subscriptionName,
  topicName,
} from './utils/serviceBus';
import {
  initializeTelemetryTimer,
  setIncludeQ2Status,
} from './utils/telemetry';

type Command = //Matches what's in LCARS/routes/q_cnc

    | 'refresh_llm'
    | 'new_function_map'
    | 'set_function_interval'
    | 'activate_telemetry'
    | 'deactivate_telemetry'
    | 'set_function_expiry'
    | 'set_function_health_check_interval'
    | 'include_q2_telemetry'
    | 'exclude_q2_telemetry';

const consumer = new EventGridDeserializer();

let cncReceiver: ServiceBusReceiver;
let cncSubscription: ReturnType<ServiceBusReceiver['subscribe']>;

export const restartCNC = async () => {
  console.log('restarting cnc...');
  if (cncSubscription) await cncSubscription.close();
  if (cncReceiver) await cncReceiver.close();
  initializeCNC();
};

export const initializeCNC = () => {
  console.log('Starting CNC listener');
  cncReceiver = getSbClientCNC().createReceiver(
    topicName.cnc,
    subscriptionName.cnc,
    {
      receiveMode: 'receiveAndDelete',
    },
  );

  cncSubscription = cncReceiver.subscribe({
    processMessage: async (brokeredMessage: ServiceBusReceivedMessage) => {
      const events = await consumer.deserializeEventGridEvents(
        brokeredMessage.body,
      );
      for (const event of events) {
        console.log(`CNC: ${event.subject}`);

        switch (event.subject as Command) {
          case 'refresh_llm':
            handleRefreshLlms();
            break;

          case 'activate_telemetry':
            handleActivateTelemetry();
            break;

          case 'deactivate_telemetry':
            handleDeactivateTelemetry();
            break;

          case 'include_q2_telemetry':
            setIncludeQ2Status(true);
            break;

          case 'exclude_q2_telemetry':
            setIncludeQ2Status(false);
            break;

          case 'new_function_map':
            const new_function_map = event.data as Record<
              string,
              FunctionRegistryEntry
            >;
            if (new_function_map) handleRefreshFunctionLimits(new_function_map);
            break;

          case 'set_function_interval':
            const { new_interval } = event.data as any;
            if (new_interval) handleFunctionInterval(new_interval);
            break;

          case 'set_function_expiry':
            const { new_expiry } = event.data as any;
            if (new_expiry) handleNewFunctionExpiry(new_expiry);
            break;

          case 'set_function_health_check_interval':
            const { new_health_interval } = event.data as any;
            if (new_health_interval)
              handleHealthCheckInterval(new_health_interval);
            break;
        }
      }
    },
    processError: async (args: ProcessErrorArgs) => {
      handleMessageError(args, 'cncSubscription');
    },
  });
};

const handleFunctionInterval = (newInterval: number) => {
  set_interval(newInterval);
  clearInterval(initializeFunctionTimer());
  const forceRestart = true;
  initializeFunctionTimer(newInterval, forceRestart);
};

const handleRefreshFunctionLimits = (
  newFunctionMap: Record<string, FunctionRegistryEntry>,
) => {
  set_function_map(newFunctionMap);
};

const handleRefreshLlms = async () => {
  await refreshLlms();
};

const handleDeactivateTelemetry = () => {
  clearInterval(initializeTelemetryTimer());
};

const handleActivateTelemetry = () => {
  clearInterval(initializeTelemetryTimer());
  const forceRestart = true;
  initializeTelemetryTimer(forceRestart);
};

const handleNewFunctionExpiry = (new_expiry) => {
  setFunctionExpiry(new_expiry);
};

const handleHealthCheckInterval = (new_interval) => {
  setHealthCheckInterval(new_interval);
};
