import { constants } from './constants';
import {
  callAzureFunction,
  confirm_function_healthy,
  get_function_map,
} from './utils/azure';
import {
  getAllClientJobs,
  updateJobRecord,
  updateJobRecordStatus,
} from './utils/db';
import { telemetryClient } from './utils/telemetry';

// If a function fails, try once more before giving up
let client_job_failure_count = {};

const handleFunctionInvokeError = (schema, job_id, payload) => {
  // If the function call has failed before, then update job record to error
  const client_job_key = `${schema}_${job_id}`;
  if (client_job_failure_count[client_job_key]) {
    updateJobRecord(schema, job_id, {}, payload, constants.STATUS_ERROR);
    delete client_job_failure_count[client_job_key];
  } else {
    client_job_failure_count[client_job_key] = true;
  }
};

export const try_the_function_queue = async () => {
  let func_map = get_function_map();

  // Add some debugs so we know Q is working
  // console.log(`[function_queue] Trying the function queue...`);

  // We shouldn't care about completed/errored jobs
  const pendingOnly = true;
  const allJobs = await getAllClientJobs(pendingOnly);
  const jobsBySchema = Object.entries(allJobs);

  for (const [schema, jobs] of jobsBySchema) {
    const processing_count_by_type: Record<string, number> = {};

    Object.keys(func_map).forEach((func) => {
      processing_count_by_type[func] = 0;
    });

    //If jobs have been sitting in submitted for too long, mark them as queued
    const staleJobs = jobs.filter((job) => {
      return (
        job.status == constants.STATUS_SUBMITTED &&
        new Date().getTime() - job.updated_at.getTime() >
          constants.STALE_SUBMITTED_TIMER
      );
    });

    for (let job of staleJobs) {
      console.log(
        `[function_queue] job_id: ${job.id} is stale, marking as queued`,
      );
      await updateJobRecordStatus(schema, job.id, constants.STATUS_QUEUED);
    }

    // Count the in-progress jobs by type
    jobs
      .filter((job) => job.status == constants.STATUS_PROCESSING)
      .forEach((job) => {
        processing_count_by_type[job.type] =
          (processing_count_by_type[job.type] || 0) + 1;
      });

    // For each job type, if the processing count is below the limit,
    // look for and execute queued jobs.
    for (const [type, processing_count] of Object.entries(
      processing_count_by_type,
    )) {
      if (Object.keys(func_map).includes(type) == false) {
        //If this function type is not in our map, skip it
        continue;
      }

      const limit = func_map[type].limit_per_client;
      const timeout_ms = func_map[type].timeout_ms;

      if (processing_count < limit) {
        jobs
          .filter((j) => j.type == type && j.status == constants.STATUS_QUEUED)
          .slice(0, limit - processing_count)
          .forEach(async (job, jobIdx) => {
            const payload = job.request_body;

            let endpoint = `/api/invoke`;
            if (payload.is_structured && type == 'digest') {
              endpoint = `/api/process_structured_file`;
            }

            console.log(
              `[function_queue] [${schema}] (idx: ${jobIdx}) OK to invoke function ${type} for job_id (${job.id}): ${endpoint}`,
            );

            let response_json = {};
            try {
              const function_initiation = await callAzureFunction(
                type,
                payload,
                job.id,
                schema,
                false,
                endpoint,
              );
              if (function_initiation) {
                response_json = await function_initiation.json();
                updateJobRecord(
                  schema,
                  job.id,
                  response_json,
                  payload,
                  constants.STATUS_PROCESSING,
                );
                confirm_function_healthy(
                  job.id,
                  schema,
                  response_json,
                  timeout_ms,
                );
              } else {
                console.error(
                  `[try_the_function_queue] [${schema}] Could not call Azure function for job_id: ${job.id}`,
                );
                handleFunctionInvokeError(schema, job.id, payload);
              }
            } catch (err) {
              console.error(
                `[try_the_function_queue] [${schema}] Error with job_id: ${job.id}`,
              );
              console.error(err.message);
              telemetryClient.trackException({
                exception: err,
                contextObjects: job,
              });

              handleFunctionInvokeError(schema, job.id, payload);
            }
          });
      }
    }
  }
};

let interval =
  Number(process.env.FUNCTION_INTERVAL_TIMER) ||
  constants.FUNCTION_INTERVAL_TIMER;

export const set_interval = (new_interval: number) => {
  interval = new_interval;
};

const get_interval = () => {
  return interval;
};

let timerId;
export const initializeFunctionTimer = (
  interval = get_interval(),
  forceRestart = false,
) => {
  if (!timerId || forceRestart) {
    if (interval == -1) return null;

    timerId = setInterval(() => {
      try_the_function_queue();
    }, interval);
  }

  return timerId;
};
