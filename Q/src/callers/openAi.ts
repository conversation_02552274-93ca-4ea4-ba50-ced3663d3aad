import OpenAI from 'openai';
import {
  ChatCompletion,
  ChatCompletionMessage,
  ChatCompletionMessageParam,
} from 'openai/resources/chat';
import { IResponse } from '../model';

export const call = async (
  model: string,
  apiKey: string,
  messages: ChatCompletionMessageParam[],
  correlationId: string,
): Promise<IResponse<ChatCompletion>> => {
  try {
    const openai = new OpenAI({
      apiKey,
      maxRetries: 3,
    });

    const chatCompletion = await openai.chat.completions.create({
      messages: messages as ChatCompletionMessage[],
      model,
      temperature: 0,
    });

    return { success: true, response: chatCompletion };
  } catch (err) {
    console.error(`(${correlationId}): [openAiCall] ${err.message}`);
    return { success: false, error: err };
  }
};
