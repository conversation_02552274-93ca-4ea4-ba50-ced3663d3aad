import { GoogleGenAI } from '@google/genai';
import OpenAI from 'openai';
import { LLMResource } from '../model';
import {
  getCredentials as getGCPCredentials,
  getGoogleAuth,
} from '../utils/google';

export async function getWrappedOpenAIClientForGCP(
  resource: LLMResource,
): Promise<OpenAI> {
  try {
    const auth = await getGoogleAuth();
    const apiKey = await auth.getAccessToken();
    const { vertex_project_id, gcp_ml_region, url } = resource;
    const baseURL = `${url}/projects/${vertex_project_id}/locations/${gcp_ml_region}/endpoints/openapi/`;

    return new OpenAI({
      apiKey,
      baseURL,
      maxRetries: 3,
    });
  } catch (err) {
    console.error('Error in getWrappedOpenAIClientForGCP:', err);
    throw err;
  }
}

export async function getWrappedVertexClient(resource: LLMResource) {
  const creds = await getGCPCredentials();
  const { vertex_project_id, gcp_ml_region, url } = resource;
  const ai = new GoogleGenAI({
    location: gcp_ml_region,
    project: vertex_project_id,
    vertexai: true,
    googleAuthOptions: {
      credentials: creds,
      scopes: ['https://www.googleapis.com/auth/cloud-platform'],
    },
  });
  return ai;
}
