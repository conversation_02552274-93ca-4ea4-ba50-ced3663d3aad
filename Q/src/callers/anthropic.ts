// gcp_ml_region
// vertex_project_id
import Anthropic from '@anthropic-ai/sdk';
import {
  MessageParam,
  RawMessageStreamEvent,
  TextBlockParam,
  Tool,
  ToolChoice,
} from '@anthropic-ai/sdk/resources/messages/messages';
import { Stream } from '@anthropic-ai/sdk/streaming';
import { AnthropicVertex } from '@anthropic-ai/vertex-sdk';
import { constants } from '../constants';
import { LLMResource } from '../model';
import { getGoogleAuth } from '../utils/google';

interface ClaudeChatReqParams {
  resource: LLMResource;
  messages: MessageParam[];
  max_tokens: number;
  timeout?: number;
  model: string;
  tools?: Tool[];
  tool_choice?: ToolChoice;
  system: string | Array<TextBlockParam>;
}

export const callAnthropic = async (params: ClaudeChatReqParams) => {
  const {
    resource,
    messages,
    max_tokens,
    timeout,
    model,
    tools,
    tool_choice,
    system,
  } = params;

  const client = await getAnthropicClient(resource, timeout);
  const result = await client.messages.create({
    max_tokens,
    messages,
    model,
    tools,
    tool_choice,
    system,
  });

  return result;
};

export const callAnthropicStream = async (
  params: ClaudeChatReqParams,
): Promise<Stream<RawMessageStreamEvent>> => {
  const { resource, messages, max_tokens, model, tools, tool_choice, system } =
    params;

  const client = await getAnthropicClient(resource, params.timeout);

  const result = await client.messages.create({
    max_tokens,
    messages,
    model,
    tools,
    tool_choice,
    system,
    stream: true,
  });
  return result;
};

async function getAnthropicClient(resource: LLMResource, timeout?: number) {
  timeout = timeout && timeout > 0 ? timeout : constants.REQUEST_TIMEOUT;
  if (resource.provider === 'AWS') {
    //One day baby.
  }
  if (resource.provider === 'GCP') {
    const auth = await getGoogleAuth();
    return new AnthropicVertex({
      googleAuth: auth,
      region: resource.gcp_ml_region,
      projectId: resource.vertex_project_id,
      baseURL: resource.url,
      timeout: timeout,
    });
  }
  if (resource.provider === 'ANTHROPIC') {
    return new Anthropic({
      apiKey: resource.api_key,
      timeout: timeout,
    });
  }

  throw new Error(
    `Unsupported provider: ${resource.provider}. Supported providers are: ANTHROPIC, GCP.`,
  );
}
