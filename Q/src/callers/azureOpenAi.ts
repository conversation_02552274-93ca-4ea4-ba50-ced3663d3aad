import { AzureOpenAI } from 'openai';
import { ChatCompletion, ChatCompletionMessageParam } from 'openai/resources';
import { constants } from '../constants';
import { IResponse } from '../model';

interface AzureOIAParams {
  apiKey: string;
  model: string;
  endpoint: string;
  deploymentId: string;
  messages: ChatCompletionMessageParam[];
  apiVersion: string;
  timeout?: number;
}
export const call = async (
  params: AzureOIAParams,
): Promise<IResponse<ChatCompletion>> => {
  try {
    const { endpoint, apiKey, deploymentId, messages, apiVersion, timeout } =
      params;
    const client = new AzureOpenAI({
      apiKey: apiKey,
      apiVersion,
      maxRetries: 3,
      deployment: deploymentId,
      endpoint: endpoint,
      timeout: timeout || constants.REQUEST_TIMEOUT,
    });

    const result = await client.chat.completions.create({
      messages: messages,
      model: params.model,
      temperature: 0,
    });
    return { success: true, response: result };
  } catch (err) {
    console.error(`[azureOpenAi.call]`, err.message);
    return azureCompletionErrorHandler(err);
  }
};

const azureCompletionErrorHandler = (err: any): IResponse => {
  /**
Errors come in different shapes (thanks Azure!)
here's one for context length:
{
  code: 'context_length_exceeded'
  message: 'This model's maximum length is...'
  param: 'messages',
  type: 'invalid_request_error'
}

here's one for specified timeout has been hit:
{
  name: 'AbortError',
  message: 'The operation was aborted'
  stack: 'blah blah....'
}
**/
  if (err.code == constants.AZ_ERROR_MAX_TOKEN_LENGTH) {
    return { success: false, error: err.code };
  }

  if (err.name == constants.AZ_ERROR_ABORT) {
    return { success: false, error: 'Request timed out' };
  }

  return { success: false, error: err };
};
