export const constants = {
  SCHEMA_TRIBBLE: 'tribble',
  TABLE_ACTIVITY_LOG: 'activity_log',
  TABLE_JOB: 'job',
  TABLE_DOC: 'document',
  TABLE_DOCUMENT_TYPE: 'document_type',

  TOKEN_DENOMINATOR: 3, //1 token should be 3-4 chars, but use 3 to be safe
  TOKEN_BUFFER: process.env.TOKEN_BUFFER
    ? parseInt(process.env.TOKEN_BUFFER)
    : 500,
  REQUEST_TIMEOUT: 60000,
  Q_STATUS: 'q_status',
  EVENT_FAILED_LLM_CALL: 'failed_llm_call',
  EVENT_FUNCTION_MAP_ERROR: 'function_map_error',
  EVENT_JOB_ERROR: 'job_error',
  ANSWERED: 'answered',
  AZ_ERROR_MAX_TOKEN_LENGTH: 'context_length_exceeded',
  AZ_ERROR_ABORT: 'AbortError',

  NEW_REQUEST: 'new_request',
  SECRET_NEW_REQUEST_ACCESS_KEY: 'TOPIC-NEW-REQUEST-ACCESS-KEY',

  EVENT_GRID_TYPE_FUNC_NEW_REQUEST: 'function_new_request',

  EVENT_GRID_TYPE_LLM_NEW_REQUEST: 'llm_new_request',
  EVENT_GRID_TYPE_FUNCTION_CALL_NEW_REQUEST: 'function_new_request',

  EVENT_GRID_TYPE_LLM_ANSWERED: 'llm_answered',
  EVENT_GRID_TYPE_FUNCTION_ANSWERED: 'function_answered',

  STATUS_PROCESSING: 'processing',
  STATUS_QUEUED: 'queued',
  STATUS_ERROR: 'error',
  STATUS_PROCESSED: 'processed',
  STATUS_INITIAL: 'initial',
  STATUS_SUBMITTED: 'submitted',

  Q_TELEMETRY_INTERVAL: 30000,
  FUNCTION_INTERVAL_TIMER: 15000,
  CHECK_FUNCTION_TIMER: 60000,
  STALE_SUBMITTED_TIMER: 30000,
};
