// Borrowed from https://github.com/PublicAffairs/openai-gemini
// Used for translation of OpenAI API to and from GenAI API requests
// Added updated types and some fixes for latest sdks.

import {
  Content,
  FunctionCallingConfigMode,
  FunctionDeclaration,
  GenerateContentConfig,
  GenerateContentParameters,
  GenerateContentResponse,
  HarmBlockThreshold,
  HarmCategory,
  Part,
  SafetySetting,
  Schema,
  SchemaUnion,
  Tool,
  ToolConfig,
  Type,
} from '@google/genai';
import { Buffer } from 'node:buffer';
import {
  ChatCompletion,
  ChatCompletionContentPart,
  ChatCompletionCreateParams,
  ChatCompletionMessageParam,
  ChatCompletionTool,
  ChatCompletionToolMessageParam,
  ResponseFormatJSONSchema,
} from 'openai/resources';

const safetySettings: SafetySetting[] = Object.values(HarmCategory).map(
  (category) => ({
    category,
    threshold: HarmBlockThreshold.BLOCK_NONE,
  }),
);
const fieldsMap: Record<string, keyof GenerateContentConfig> = {
  frequency_penalty: 'frequencyPenalty',
  max_completion_tokens: 'maxOutputTokens',
  max_tokens: 'maxOutputTokens',
  n: 'candidateCount', // not for streaming
  presence_penalty: 'presencePenalty',
  seed: 'seed',
  stop: 'stopSequences',
  temperature: 'temperature',
  top_k: 'topK', // non-standard
  top_p: 'topP',
};

const getGenAIType = (type: string): Type => {
  switch (type) {
    case 'string':
      return Type.STRING;
    case 'number':
      return Type.NUMBER;
    case 'integer':
      return Type.INTEGER;
    case 'boolean':
      return Type.BOOLEAN;
    case 'array':
      return Type.ARRAY;
    case 'object':
      return Type.OBJECT;
    default:
      return Type.STRING;
  }
};

const convertSchemaObject = (obj: any): Schema => {
  const result: Schema = {};

  if (obj?.type) {
    if (Array.isArray(obj.type)) {
      // Handle union types like ['string', 'null']
      if (obj.type.includes('null') && obj.type.length === 2) {
        const nonNullType = obj.type.find((t) => t !== 'null');
        result.nullable = true;
        result.type = getGenAIType(nonNullType);
      } else {
        // For other union types, use the first type
        result.type = getGenAIType(obj.type[0]);
      }
    } else {
      result.type = getGenAIType(obj.type);
    }
  }

  if (obj?.description) {
    result.description = obj.description;
  }

  if (obj?.properties) {
    result.properties = {};
    for (const [key, value] of Object.entries(obj.properties)) {
      result.properties[key] = convertSchemaObject(value);
    }
  }

  if (obj?.items) {
    result.items = convertSchemaObject(obj.items);
  }

  if (obj?.required && Array.isArray(obj.required)) {
    result.required = obj.required;
  }

  if (obj?.enum) {
    result.enum = obj.enum;
  }

  return result;
};

const adjustSchema = (schema: ResponseFormatJSONSchema): SchemaUnion => {
  if (!schema || !schema.json_schema || !schema.json_schema.schema) {
    return schema;
  }

  const genaiSchema = convertSchemaObject(schema.json_schema.schema);
  return genaiSchema;
};

const transformConfig = (
  req: ChatCompletionCreateParams,
): Partial<GenerateContentConfig> => {
  let cfg: Partial<GenerateContentConfig> = {};
  //if (typeof req.stop === "string") { req.stop = [req.stop]; } // no need
  for (const key in req) {
    const matchedKey = fieldsMap[key as keyof typeof fieldsMap];
    if (matchedKey && key in req) {
      const value = req[key as keyof ChatCompletionCreateParams];
      (cfg as any)[matchedKey] = value;
    }
  }
  if (req.response_format) {
    switch (req.response_format.type) {
      case 'json_schema':
        const translatedSchema = adjustSchema(req.response_format);
        cfg.responseSchema = translatedSchema;
        cfg.responseMimeType = 'application/json';
        if (
          cfg.responseSchema &&
          typeof cfg.responseSchema === 'object' &&
          cfg.responseSchema !== null &&
          'enum' in cfg.responseSchema
        ) {
          cfg.responseMimeType = 'text/x.enum';
          break;
        }
      // eslint-disable-next-line no-fallthrough
      case 'json_object':
        cfg.responseMimeType = 'application/json';
        break;
      case 'text':
        cfg.responseMimeType = 'text/plain';
        break;
      default:
        throw new Error('Unsupported response_format.type');
    }
  }
  return cfg;
};

const parseImg = async (url: string): Promise<Part> => {
  let mimeType: string | null, data: string;
  if (url.startsWith('http://') || url.startsWith('https://')) {
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`${response.status} ${response.statusText} (${url})`);
      }
      mimeType = response.headers.get('content-type');
      data = Buffer.from(await response.arrayBuffer()).toString('base64');
    } catch (err) {
      throw new Error('Error fetching image: ' + err.toString());
    }
  } else {
    const match = url.match(/^data:(?<mimeType>.*?)(;base64)?,(?<data>.*)$/);
    if (!match) {
      throw new Error('Invalid image data: ' + url);
    }
    ({ mimeType, data } = match.groups);
  }
  return {
    inlineData: {
      mimeType,
      data,
    },
  };
};

const transformFnResponse = (
  { content, tool_call_id }: ChatCompletionToolMessageParam,
  parts: any,
): void => {
  if (!parts.calls) {
    throw new Error('No function calls found in the previous message');
  }
  let response;
  try {
    response = JSON.parse(content as string);
  } catch (err) {
    response = content; // keep as is
  }
  if (
    typeof response !== 'object' ||
    response === null ||
    Array.isArray(response)
  ) {
    response = { result: response };
  }
  if (!tool_call_id) {
    throw new Error('tool_call_id not specified');
  }
  const { i, name } = parts.calls[tool_call_id] ?? {};
  if (!name) {
    throw new Error('Unknown tool_call_id: ' + tool_call_id);
  }
  if (parts[i]) {
    throw new Error('Duplicated tool_call_id: ' + tool_call_id);
  }
  parts[i] = {
    functionResponse: {
      id: tool_call_id.startsWith('call_') ? null : tool_call_id,
      name,
      response,
    },
  };
};

const transformFnCalls = ({ tool_calls }: any): any => {
  const calls = {};
  const parts = tool_calls.map(
    ({ function: { arguments: argstr, name }, id, type }, i) => {
      if (type !== 'function') {
        throw new Error(`Unsupported tool_call type: "${type}"`);
      }
      let args;
      try {
        args = JSON.parse(argstr);
      } catch (err) {
        console.error('Error parsing function arguments:', err);
        throw new Error('Invalid function arguments: ' + argstr);
      }
      calls[id] = { i, name };
      return {
        functionCall: {
          id: id.startsWith('call_') ? null : id,
          name,
          args,
        },
      };
    },
  );
  parts.calls = calls;
  return parts;
};

const transformMsg = async ({
  content,
}: ChatCompletionMessageParam): Promise<Part[]> => {
  const parts: Part[] = [];
  if (!Array.isArray(content)) {
    // system, user: string
    // assistant: string or null (Required unless tool_calls is specified.)
    parts.push({ text: content });
    return parts;
  }
  // user:
  // An array of content parts with a defined type.
  // Supported options differ based on the model being used to generate the response.
  // Can contain text, image, or audio inputs.
  for (const item of content) {
    switch (item.type) {
      case 'text':
        parts.push({ text: item.text });
        break;
      case 'image_url':
        parts.push(await parseImg(item.image_url.url));
        break;
      case 'input_audio':
        parts.push({
          inlineData: {
            mimeType: 'audio/' + item.input_audio.format,
            data: item.input_audio.data,
          },
        });
        break;
      default:
        throw new Error(`Unknown "content" item type: "${item.type}"`);
    }
  }
  if (
    content.every(
      (item: ChatCompletionContentPart) => item.type === 'image_url',
    )
  ) {
    parts.push({ text: '' }); // to avoid "Unable to submit request because it must have a text parameter"
  }
  return parts;
};

const transformMessages = async (
  messages: ChatCompletionMessageParam[],
): Promise<{
  systemInstruction: GenerateContentConfig['systemInstruction'];
  contents: GenerateContentParameters['contents'];
}> => {
  if (!messages) {
    return { systemInstruction: undefined, contents: [] };
  }
  const contents: Content[] = [];
  let systemInstruction: GenerateContentConfig['systemInstruction'];
  for (const item of messages) {
    switch (item.role) {
      case 'system':
        systemInstruction = { parts: await transformMsg(item) };
        continue;
      case 'tool':
        // eslint-disable-next-line no-case-declarations
        let lastContent = contents[contents.length - 1];
        let { role, parts } = lastContent ?? {
          role: undefined,
          parts: undefined,
        };
        if (role !== 'function') {
          const calls = (parts as any)?.calls;
          const newParts: any[] = [];
          (newParts as any).calls = calls;
          contents.push({
            role: 'function', // ignored
            parts: newParts,
          });
          parts = newParts;
        }
        transformFnResponse(item, parts);
        continue;
      case 'assistant':
        // Don't modify the original item
        break;
      case 'user':
        break;
      default:
        throw new Error(`Unknown message role: "${item.role}"`);
    }

    // Type guard for assistant messages with tool_calls
    const hasToolCalls = 'tool_calls' in item && item.tool_calls;

    contents.push({
      role: item.role,
      parts: hasToolCalls
        ? transformFnCalls(item as any)
        : await transformMsg(item as any),
    });
  }
  if (systemInstruction) {
    if (!contents[0]?.parts?.some((part) => part.text)) {
      contents.unshift({ role: 'user', parts: [{ text: ' ' }] });
    }
  }

  return { systemInstruction, contents };
};

const adjustTool = (tool: ChatCompletionTool): FunctionDeclaration => {
  if (tool.type !== 'function') {
    throw new Error(`Unsupported tool type: ${tool.type}`);
  }

  const funcDeclaration: FunctionDeclaration = {
    name: tool.function.name,
    description: tool.function.description || '',
  };

  if (tool.function.parameters) {
    funcDeclaration.parameters = convertSchemaObject(tool.function.parameters);
  }

  return funcDeclaration;
};

const transformTools = (
  req: ChatCompletionCreateParams,
): { tools?: Tool[]; toolConfig?: ToolConfig } => {
  let tools: Tool[], toolConfig: ToolConfig;
  if (req.tools) {
    const funcs = req.tools.filter((tool) => tool.type === 'function');
    const translated = funcs.map(adjustTool);
    tools = [{ functionDeclarations: translated }];

    let geminiToolMode = FunctionCallingConfigMode.AUTO;
    let allowedFunctionNames: string[] | undefined;

    if (typeof req.tool_choice === 'string') {
      switch (req.tool_choice) {
        case 'none':
          geminiToolMode = FunctionCallingConfigMode.NONE;
          break;
        case 'required':
          geminiToolMode = FunctionCallingConfigMode.ANY;
          break;
        default:
          geminiToolMode = FunctionCallingConfigMode.AUTO;
          break;
      }
    }

    if (typeof req.tool_choice === 'object') {
      geminiToolMode = FunctionCallingConfigMode.ANY;
      allowedFunctionNames = [req.tool_choice.function?.name];
    }

    if (geminiToolMode == FunctionCallingConfigMode.ANY) {
      allowedFunctionNames = tools[0].functionDeclarations?.map(
        (tool) => tool.name,
      );
    }

    if (allowedFunctionNames || geminiToolMode) {
      toolConfig = {
        functionCallingConfig: {
          mode: geminiToolMode,
          ...(allowedFunctionNames ? { allowedFunctionNames } : {}),
        },
      };
    }
  }

  return { tools, ...(toolConfig ? { toolConfig } : {}) };
};

export const FAKE_RESPONSE_SCHEMA_TOOL = 'complete_response';

export const transformRequest = async (
  req: ChatCompletionCreateParams,
): Promise<GenerateContentParameters> => {
  const res: GenerateContentParameters = {
    ...(await transformMessages(req.messages)),
    config: {
      safetySettings,
      ...transformConfig(req),
      ...transformTools(req),
    },
    model: req.model,
  };

  if (req['response_format'] && req.tools) {
    //Gemini doesn't allow response_format AND tools!
    //So instead we create a dummy tool to get the response_format
    const tool: FunctionDeclaration = {
      name: FAKE_RESPONSE_SCHEMA_TOOL,
      description:
        'Format response for the user. This tool is a placeholder that forces a JSON version of what the user asked for. It should be the last function called.',
      parameters: res.config.responseSchema,
    };
    (res.config.tools[0] as Tool).functionDeclarations.push(tool);

    const allowedFunctionNames = (
      res.config.tools[0] as Tool
    ).functionDeclarations.map((f) => f.name);

    res.config.toolConfig.functionCallingConfig = {
      mode: FunctionCallingConfigMode.ANY,
      allowedFunctionNames,
    };
    delete res.config.responseSchema;
    delete res.config.responseMimeType;
  }
  if (res['systemInstruction']) {
    //Move systemInstruction to config
    res.config.systemInstruction = res['systemInstruction'];
    delete res['systemInstruction'];
  }
  return res;
};

const generateId = () => {
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const randomChar = () =>
    characters[Math.floor(Math.random() * characters.length)];
  return Array.from({ length: 29 }, randomChar).join('');
};

const reasonsMap = {
  //https://ai.google.dev/api/rest/v1/GenerateContentResponse#finishreason
  //"FINISH_REASON_UNSPECIFIED": // Default value. This value is unused.
  STOP: 'stop',
  MAX_TOKENS: 'length',
  SAFETY: 'content_filter',
  RECITATION: 'content_filter',
  //"OTHER": "OTHER",
};
const SEP = '\n\n|>';
const transformCandidates = (key, cand) => {
  const message = { role: 'assistant', content: [] } as any;
  for (const part of cand.content?.parts ?? []) {
    if (part.functionCall) {
      const fc = part.functionCall;
      message.tool_calls = message.tool_calls ?? [];
      message.tool_calls.push({
        id: fc.id ?? 'call_' + generateId(),
        type: 'function',
        function: {
          name: fc.name,
          arguments: JSON.stringify(fc.args),
        },
      });
    } else {
      message.content.push(part.text);
    }
  }
  message.content = message.content.join(SEP) || null;
  return {
    index: cand.index || 0, // 0-index is absent in new -002 models response
    [key]: message,
    logprobs: null,
    finish_reason: message.tool_calls
      ? 'tool_calls'
      : reasonsMap[cand.finishReason] || cand.finishReason,
    //original_finish_reason: cand.finishReason,
  };
};
const transformCandidatesMessage = transformCandidates.bind(null, 'message');
const transformCandidatesDelta = transformCandidates.bind(null, 'delta');

const transformUsage = (data) => ({
  completion_tokens: data.candidatesTokenCount,
  prompt_tokens: data.promptTokenCount,
  total_tokens: data.totalTokenCount,
});

const checkPromptBlock = (choices, promptFeedback, key) => {
  if (choices.length) {
    return;
  }
  if (promptFeedback?.blockReason) {
    console.log('Prompt block reason:', promptFeedback.blockReason);
    if (promptFeedback.blockReason === 'SAFETY') {
      promptFeedback.safetyRatings
        .filter((r) => r.blocked)
        .forEach((r) => console.log(r));
    }
    choices.push({
      index: 0,
      [key]: null,
      finish_reason: 'content_filter',
      //original_finish_reason: data.promptFeedback.blockReason,
    });
  }
  return true;
};

export const processCompletionsResponse = (
  data: GenerateContentResponse,
  model: string,
  id: string,
): ChatCompletion => {
  const obj = {
    id,
    choices: data.candidates.map(transformCandidatesMessage),
    created: Math.floor(Date.now() / 1000),
    model: data.modelVersion ?? model,
    //system_fingerprint: "fp_69829325d0",
    object: 'chat.completion',
    usage: data.usageMetadata && transformUsage(data.usageMetadata),
  };
  if (obj.choices.length === 0) {
    checkPromptBlock(obj.choices, data.promptFeedback, 'message');
  }
  return obj as ChatCompletion;
};
