import { AzureOpenAI } from 'openai';
import { decryptText } from './utils/auth';
const Embedders: LLMResource[] = [];
const EMBEDDING_API_VERSION = process.env.EMBEDDING_API_VERSION || '2023-05-15';

export async function generateEmbeddingsFromAzure(
  text: string,
): Promise<{ embedding: number[]; origin: string } | null> {
  let embedder: LLMResource;
  try {
    embedder = await getNextEmbedder();
    const endpoint = embedder.url;

    const client = new AzureOpenAI({
      apiKey: embedder.api_key,
      deployment: embedder.name,
      endpoint,
      timeout: 10000,
      maxRetries: 3,
      apiVersion: EMBEDDING_API_VERSION,
    });

    const embeddings = await client.embeddings.create({
      model: embedder.basemodel,
      input: [text],
    });
    return {
      embedding: embeddings.data[0].embedding,
      origin: embedder.provider,
    };
  } catch (err) {
    console.error(
      `[generateEmbeddings] failed on [${embedder?.name ?? 'undefined'}]: `,
      err,
    );

    return null;
  }
}

let lastIndexUsed = 0;

//Just roll along round robin.
const getNextEmbedder = async (): Promise<LLMResource> => {
  // In rare cases, a race condition on LCARS startup can cause lastUsedIndex to
  // be NaN which would hamstring this logic forever.
  if (isNaN(lastIndexUsed)) {
    lastIndexUsed = 0;
  }
  //The race condition happens if the Embedders array is empty. So let's check for that.
  if (Embedders.length === 0) {
    await initializeEmbedders();
  }

  const nextIndex = (lastIndexUsed + 1) % Embedders.length;
  lastIndexUsed = nextIndex;

  return Embedders[nextIndex];
};

export const initializeEmbedders = async () => {
  //Initialize the embedder constant with stuff from the db
  clearEmbedders();

  const embeddingResources =
    (await getEmbeddingResources()) as LLMResourceBootstrap[];

  for (const embeddingResource of embeddingResources) {
    if (!embeddingResource.api_key) continue;
    const decryptedApiKey = decryptText(embeddingResource.api_key);
    Embedders.push({
      ...embeddingResource,
      api_key: decryptedApiKey!, // Todo: need to handle null from decryptText
      current: {
        requests_this_minute: 0,
        tokens_this_minute: 0,
      },
      limits: {
        requests_per_minute: embeddingResource.requests_per_minute,
        tokens_per_minute: embeddingResource.tokens_per_minute,
        tokens_per_request: embeddingResource.tokens_per_request,
      },
    });
  }
  console.log(`Initialize embedding models (${Embedders.length})`);
};

const clearEmbedders = () => {
  while (Embedders.length) {
    Embedders.pop();
  }
};

type WithoutNullableKeys<Type> = {
  [Key in keyof Type]-?: WithoutNullableKeys<NonNullable<Type[Key]>>;
};

export type LLMResourceBootstrap = WithoutNullableKeys<EmbeddingResoucre>;

export type NewLLMResource = Omit<LLMResourceBootstrap, 'id'>;

//Note: We're not currently using any smart rate limit stuff, but just in case we ever want to,
//here it is.
export interface LLMResource
  extends Omit<
    LLMResourceBootstrap,
    'tokens_per_minute' | 'requests_per_minute' | 'tokens_per_request'
  > {
  current: {
    requests_this_minute: number;
    tokens_this_minute: number;
  };
  limits: LLMResourceLimit;
}

interface LLMResourceLimit {
  // Needs to be changed in lcars/src/agents/embedder.ts
  concurrenc?: number;
  requests_per_minute: number;
  tokens_per_minute: number;
  tokens_per_request: number;
}

import { getDB } from '@tribble/tribble-db/db_ops';

export type EmbeddingResoucre = Awaited<
  ReturnType<typeof getEmbeddingResources>
>[number];

export async function getEmbeddingResources() {
  const db = await getDB();

  return await db
    .selectFrom('tribble.llm_resource')
    .where('use_for_embedding', '=', true)
    .where('is_active', '=', true)
    .select([
      'id',
      'name',
      'provider',
      'basemodel',
      'tokens_per_minute',
      'requests_per_minute',
      'tokens_per_request',
      'cost_per_1k_token_prompt',
      'url',
      'api_key',
      'is_internal',
      'is_active',
    ])
    .execute();
}
