import { ResourceBaseModel } from '@tribble/chat-completion-service';
import { constants } from './constants';
import { LLMResource, LLMResourceBootstrap } from './model';
import { decryptText } from './utils/auth';
import { query } from './utils/db';

export let LlmResources: LLMResource[] = [];

type LLMResourceWithIndex = { resource: LLMResource; index: number };

function filterResourcesByBaseModel(
  resources: LLMResourceWithIndex[],
  baseModel?: ResourceBaseModel,
) {
  if (baseModel === ResourceBaseModel.GPT_4_TURBO) {
    resources = resources.filter(includesTurbo).filter(includesGPT4);
  }

  if (baseModel === ResourceBaseModel.GPT_3_5_TURBO) {
    resources = resources.filter(onlyGPT35);
  }

  if (baseModel === ResourceBaseModel.GPT_4_OH) {
    resources = resources.filter(onlyGPT4o);
  }

  if (baseModel === ResourceBaseModel.GPT_4_OH_MINI) {
    resources = resources.filter(only4oMini);
    resources = resources.sort(prioritizeGPT41Mini);
  }

  if (baseModel === ResourceBaseModel.GPT_4_OH_1) {
    resources = resources.filter(only_o1);
  }

  if (baseModel === ResourceBaseModel.GPT_4_OH_3_MINI) {
    resources = resources.filter(only_o3Mini);
  }
  if (baseModel === ResourceBaseModel.CLAUDE_3_7_SONNET) {
    resources = resources.filter(onlyClaude37Sonnet);
  }
  if (baseModel === ResourceBaseModel.CLAUDE_4_SONNET) {
    resources = resources.filter(onlyClaude4Sonnet);
  }
  if (baseModel === ResourceBaseModel.CLAUDE_4_OPUS) {
    resources = resources.filter(onlyClaude4Opus);
  }
  if (baseModel === ResourceBaseModel.ANY_CLAUDE) {
    resources = resources.filter(anyClaude);
  }
  if (baseModel === ResourceBaseModel.GPT_4_1) {
    resources = resources.filter(onlyGPT41);
  }
  if (baseModel === ResourceBaseModel.GPT_4_1_MINI) {
    resources = resources.filter(onlyGPT41Mini);
  }
  if (baseModel === ResourceBaseModel.GEMINI_2_5_FLASH) {
    resources = resources.filter(onlyGemini25Flash);
  }
  if (
    baseModel === ResourceBaseModel.GPT_4 ||
    baseModel === ResourceBaseModel.RESOURCE_BASE_MODEL_UNSPECIFIED ||
    !baseModel
  ) {
    //Default
    resources = resources
      .filter(no_Turbo)
      .filter(no_GPT35)
      .filter(no_GPT32K) //use 4o as the bigger buddy
      .filter(no_o1)
      .filter(no_4oMini)
      .filter(no_GPT41Mini)
      .filter(no_o3Mini)
      .filter(no_Claude)
      .filter(no_GPT41)
      .filter(no_Google);
  }

  return resources;
}

export const tooBigForEverything = (
  tokenSize: number,
  baseModel: ResourceBaseModel,
) => {
  const resources = LlmResources.map((resource, index) => ({
    resource,
    index,
  }));
  const filtered = filterResourcesByBaseModel(resources, baseModel);
  return !filtered.some(
    (r) =>
      r.resource.limits.tokens_per_request > tokenSize + constants.TOKEN_BUFFER,
  );
};

//Get a free LLM. Consider token limits and requests per minute.
export const getRightSizeLLM = (
  forceInternalOnly: boolean = false,
  tokenSize: number,
  baseModel?: ResourceBaseModel,
): LLMResourceWithIndex | undefined => {
  let freeResources = LlmResources.map((resource, index) => ({
    resource,
    index,
  })).filter((r) => {
    return _isFree(r.resource, tokenSize);
  });

  if (!freeResources.length) return;

  freeResources = filterResourcesByBaseModel(freeResources, baseModel);

  freeResources
    .sort(lastUsedAscending) //Round robin
    .sort(dePriortize4o);

  const resource = forceInternalOnly
    ? freeResources.find((r) => {
        return r.resource.is_internal;
      })
    : freeResources.find((r) => {
        return r.resource != undefined;
      });
  return resource;
};

export const setBusy = (index: number, tokens: number) => {
  LlmResources[index].current.requests_this_minute += 1;
  setLastUsed(index);
  addTokensToCounter(index, tokens);
};

const setLastUsed = (index: number) => {
  LlmResources[index].last_used = Date.now();
};

export const addTokensToCounter = (index: number, tokens: number) => {
  LlmResources[index].current.tokens_this_minute += tokens;
};

export const subtractTokensFromCounter = (index: number, tokens: number) => {
  LlmResources[index].current.tokens_this_minute -= tokens;
};

//This used to update a concurrent requests counter, but we don't do that anymore.
//I'm leaving it in, in case we need to do some setFree operations at some point.
export const setFree = (index: number) => {};

const setLLMResources = (newResources: LLMResource[]) => {
  LlmResources = newResources;
};

const _isFree = (resource: LLMResource, tokenSize: number): boolean => {
  const { limits, current } = resource;
  return (
    current.requests_this_minute < limits.requests_per_minute &&
    current.tokens_this_minute + tokenSize <
      limits.tokens_per_minute - constants.TOKEN_BUFFER &&
    tokenSize + constants.TOKEN_BUFFER < limits.tokens_per_request
  );
};

//Fetch bootstrapped llm resources from db,
//and merge them with any currently in the resource library;
export const init = async () => {
  const queryString = `
    SELECT 
          id, 
          name, 
          provider, 
          basemodel, 
          tokens_per_minute, 
          requests_per_minute, 
          tokens_per_request, 
          cost_per_1k_token_prompt,
          cost_per_1k_token_completion,
          url, 
          api_key, 
          is_internal,
          gcp_ml_region,
          vertex_project_id
    FROM  tribble.llm_resource
    WHERE use_for_embedding = false
    AND is_active = true
    ORDER BY id ASC
    `;

  const bootstrapped = await query(queryString, []);
  const bootstrappedRows: LLMResourceBootstrap[] = bootstrapped.rows || [];
  //merge into llmResource
  const newResources: LLMResource[] = [];

  for (const bootStrappedRow of bootstrappedRows) {
    const decryptedApiKey = decryptText(bootStrappedRow.api_key);
    const match = LlmResources.find((r) => r.id == bootStrappedRow.id);

    if (match) {
      newResources.push({
        ...bootStrappedRow,
        api_key: decryptedApiKey,
        current: match.current,
        limits: {
          requests_per_minute: bootStrappedRow.requests_per_minute,
          tokens_per_minute: bootStrappedRow.tokens_per_minute,
          tokens_per_request: bootStrappedRow.tokens_per_request,
        },
        last_used: match.last_used || Date.now(),
      });
    } else {
      //Initialize new resource
      newResources.push({
        ...bootStrappedRow,
        api_key: decryptedApiKey,
        current: {
          requests_this_minute: 0,
          tokens_this_minute: 0,
        },
        limits: {
          requests_per_minute: bootStrappedRow.requests_per_minute,
          tokens_per_minute: bootStrappedRow.tokens_per_minute,
          tokens_per_request: bootStrappedRow.tokens_per_request,
        },
        last_used: Date.now(),
      });
    }
  }
  console.log(`Initializing ${newResources.length} LLM models`);
  setLLMResources(newResources);
  initializeLLMTimer();
};

let timerId: ReturnType<typeof setTimeout>;

export const initializeLLMTimer = (forceRestart: boolean = false) => {
  if (!timerId || forceRestart) {
    timerId = setInterval(() => {
      for (const resource of LlmResources) {
        resource.current.requests_this_minute = 0;
        resource.current.tokens_this_minute = 0;
      }
    }, 60000);
  }

  return timerId;
};

type Comparator = (a: LLMResourceWithIndex, b: LLMResourceWithIndex) => number;

// Prioritize resources that have a keyword in some field.
export const prioritizeBy =
  <T extends keyof LLMResource>(
    field: T,
    keyword: string,
    direction: 'ASC' | 'DESC' = 'ASC',
  ): Comparator =>
  (a, b) => {
    const hasKeywordA = String(a.resource[field]).includes(keyword);
    const hasKeywordB = String(b.resource[field]).includes(keyword);
    if (hasKeywordA === hasKeywordB) {
      return 0; // If both have or neither have the keyword, they are considered equal.
    }
    if (direction === 'ASC') {
      return hasKeywordA ? -1 : 1; // Prioritize resources with the keyword.
    }
    return hasKeywordA ? 1 : -1; // Deprioritize resources with the keyword.
  };

//Filter by a field in the resource object.
export const filterBy =
  <T extends keyof LLMResource>(field: T, keywords: string[]) =>
  (r) => {
    return keywords.includes(String(r.resource[field]));
  };

export const strFieldContains =
  <T extends keyof LLMResource>(field: T, keywords: string[]) =>
  (r) => {
    return keywords.some((kw) => {
      return (r.resource[field] as string).includes(kw);
    });
  };

export const strFieldEquals =
  <T extends keyof LLMResource>(field: T, keywords: string[]) =>
  (r) => {
    return keywords.some((kw) => {
      return (r.resource[field] as string) == kw;
    });
  };

export const strFieldExcludes =
  <T extends keyof LLMResource>(field: T, keywords: string[]) =>
  (r) => {
    return !keywords.some((kw) => {
      return (r.resource[field] as string).includes(kw);
    });
  };

// Sort by a field in the resource object.
export const sortBy =
  <T extends keyof LLMResource>(
    field: T,
    direction: 'ASC' | 'DESC' = 'ASC',
  ): Comparator =>
  (a, b) => {
    const valueA = a.resource[field];
    const valueB = b.resource[field];
    if (valueA === valueB) {
      return 0; // If both values are equal, they are considered equal.
    }
    if (direction === 'ASC') {
      return valueA < valueB ? -1 : 1; // Sort in ascending order.
    }
    return valueA > valueB ? -1 : 1; // Sort in descending order.
  };

const lastUsedAscending = sortBy('last_used');
const dePriortize32K = prioritizeBy('basemodel', 'gpt-4-32k', 'DESC');
const dePriortize4o = prioritizeBy('basemodel', 'gpt-4o', 'DESC');
const prioritizeGPT4 = prioritizeBy('basemodel', 'gpt-4');
const prioritizeGPT41Mini = prioritizeBy('basemodel', 'gpt-4.1-mini');

const noGpt4o = strFieldExcludes('basemodel', ['gpt-4o']);
const no_Turbo = strFieldExcludes('name', ['turbo']);
const no_GPT35 = strFieldExcludes('basemodel', ['gpt-35']);
const no_GPT32K = strFieldExcludes('basemodel', ['gpt-4-32k']);
const no_o1 = strFieldExcludes('basemodel', ['o1']);
const no_4oMini = strFieldExcludes('basemodel', ['gpt-4o-mini']);
const no_o3Mini = strFieldExcludes('basemodel', ['o3-mini']);
const no_Claude = strFieldExcludes('basemodel', ['claude']);
const no_GPT41 = strFieldExcludes('basemodel', ['gpt-4.1']);
const no_GPT41Mini = strFieldExcludes('basemodel', ['gpt-4.1-mini']);
const no_Google = strFieldExcludes('provider', ['GOOGLE']);
const no_Gemini = strFieldExcludes('basemodel', ['google/gemini']);

const onlyGPT4o = strFieldEquals('basemodel', ['gpt-4o']);
const includesTurbo = strFieldContains('name', ['turbo']);
const includesGPT4 = strFieldContains('basemodel', ['gpt-4', 'gpt-4-32k']);
const onlyGPT35 = strFieldContains('basemodel', ['gpt-35']);
const only4oMini = strFieldEquals('basemodel', ['gpt-4o-mini', 'gpt-4.1-mini']);
const only_o1 = strFieldContains('basemodel', ['o1']);
const only_o3Mini = strFieldEquals('basemodel', ['o3-mini']);
const onlyClaude37Sonnet = strFieldEquals('basemodel', ['claude-3-7-sonnet']);
const onlyClaude4Sonnet = strFieldContains('basemodel', ['claude-sonnet-4']);
const onlyClaude4Opus = strFieldContains('basemodel', ['claude-opus-4']);
const onlyGPT41 = strFieldEquals('basemodel', ['gpt-4.1']);
const onlyGPT41Mini = strFieldContains('basemodel', ['gpt-4.1-mini']);
const onlyGemini25Flash = strFieldContains('basemodel', [
  'google/gemini-2.5-flash',
]);

const anyClaude = strFieldContains('basemodel', ['claude-']);
