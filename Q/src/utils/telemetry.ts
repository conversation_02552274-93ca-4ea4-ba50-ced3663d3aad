import TelemetryClient from 'applicationinsights/out/Library/TelemetryClient';

import { constants } from '../constants';
import { getAllClientJobs } from './db';

let appInsights = require('applicationinsights');
appInsights.setup(process.env.APPLICATIONINSIGHTS_CONNECTION_STRING).start();
let telemetryClient: TelemetryClient = appInsights.defaultClient;
const disabled = process.env.APPLICATION_INSIGHTS_DISABLE;
if (disabled && disabled == 'true') {
  telemetryClient.config.disableAppInsights = true;
}

export { telemetryClient };

let timerId: ReturnType<typeof setTimeout>;
//Every 30 seconds, send q status
export const initializeTelemetryTimer = (forceRestart: boolean = false) => {
  if (!timerId || forceRestart) {
    const interval =
      Number(process.env.Q_TELEMETRY_INTERVAL) ||
      constants.Q_TELEMETRY_INTERVAL;
    if (interval == -1) return null;

    timerId = setInterval(async () => {
      if (telemetryClient) {
        const status = await getStatus();

        if (shouldSendStatus(status)) {
          telemetryClient.trackEvent({
            name: 'q_status',
            properties: status,
          });
        }
      }
    }, interval);
  }

  return timerId;
};

interface Status {
  max_wait_time: number;
  function_jobs?: {
    [key: string]: any;
  };
}

let includeQ2Status = false;
const shouldIncludeQ2Status = () => {
  return includeQ2Status;
};

export const setIncludeQ2Status = (status: boolean) => {
  includeQ2Status = status;
};

const getStatus = async (): Promise<Status> => {
  let max_wait_time = 0;

  max_wait_time > 0
    ? (max_wait_time = new Date().getTime() - max_wait_time)
    : 0;

  if (shouldIncludeQ2Status()) {
    const functionJobsStatus = await getFunctionJobsStatus();
    return {
      max_wait_time,
      function_jobs: functionJobsStatus,
    };
  }

  return {
    max_wait_time,
  };
};

const getFunctionJobsStatus = async () => {
  const allJobs = await getAllClientJobs();
  const jobsStatusObject = {};

  Object.keys(allJobs).map((schema) => {
    const jobs = allJobs[schema];
    jobsStatusObject[schema] = jobs.map((j) => {
      delete j.request_body;
      delete j.response_body;
      return j;
    });
  });
  return jobsStatusObject;
};

//Only send status if we have something to say.
function shouldSendStatus(status: Status) {
  if (status.function_jobs) return true;
  if (status.max_wait_time != 0) return true;
  return false;
}
