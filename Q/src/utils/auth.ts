import crypto from 'crypto';

export const encryptText = (text: string): string => {
  const encryptionKey = Buffer.from(process.env.ENCRYPTION_KEY, 'base64');
  const encryptionIV = process.env.ENCRYPTION_IV;

  if (!encryptionKey || !encryptionIV) {
    // This shouldn't happen
    return null;
  }

  const cipher = crypto.createCipheriv(
    'aes-256-ctr',
    encryptionKey,
    encryptionIV,
  );
  let cipheredText = cipher.update(text, 'utf8', 'hex');
  cipheredText += cipher.final('hex');
  return cipheredText;
};

export const decryptText = (encryptedText: string): string => {
  const encryptionKey = Buffer.from(process.env.ENCRYPTION_KEY, 'base64');
  const encryptionIV = process.env.ENCRYPTION_IV;

  if (!encryptionKey || !encryptionIV) {
    // This shouldn't happen
    return null;
  }

  const decipher = crypto.createDecipheriv(
    'aes-256-ctr',
    encryptionKey,
    encryptionIV,
  );
  let decipheredText = decipher.update(encryptedText, 'hex', 'utf8');
  decipheredText += decipher.final('utf8');
  return decipheredText;
};
