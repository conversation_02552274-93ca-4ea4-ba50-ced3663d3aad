import { initializePool } from '@tribble/tribble-db/db_ops';
import pg from 'pg';
import { constants } from '../constants';
import { Job } from '../model';
import { telemetryClient } from './telemetry';

// This is a workaround to make sure that our timestamps (without timezone) are properly returned as UTC
pg.types.setTypeParser(1114, (val) => {
  if (!val) return val;
  const temp = new Date(val);
  return new Date(
    Date.UTC(
      temp.getFullYear(),
      temp.getMonth(),
      temp.getDate(),
      temp.getHours(),
      temp.getMinutes(),
      temp.getSeconds(),
      temp.getMilliseconds(),
    ),
  );
});

export async function getClient() {
  const pool = await initializePool();
  const client = await pool.connect();
  return client;
}

export async function query(text, params) {
  try {
    const poolLocal = await initializePool();
    const res = await poolLocal.query(text, params);
    return res;
  } catch (err) {
    console.error(`[DB.query] `, err);
    console.error(err.stack);
    return err;
  }
}

export async function getClientIdFromSchema(schema: string): Promise<number> {
  const queryString = `
      SELECT id
      FROM tribble.client
      WHERE database_schema_id = $1
    `;
  try {
    const result = await query(queryString, [schema]);
    if (result.rows && result.rows.length) {
      return result.rows[0].id;
    }
    return -1;
  } catch (err) {
    console.error(`[getClientJobsBy] ${err.message}`);
    return -1;
  }
}

export async function getClientJobsByFunc(
  schema: string,
  function_name: string,
  status: string,
  limit: number = 100,
) {
  const queryString = `
      SELECT
        id,
        request_body,
        type,
        status,
        updated_at
      FROM ${schema}.${constants.TABLE_JOB}
      WHERE type = $1
      AND status = $2
      AND updated_at > NOW() - INTERVAL '24 HOURS'
      ORDER BY updated_at ASC
      LIMIT $3
    `;
  try {
    const result = await query(queryString, [function_name, status, limit]);
    if (result.rows && result.rows.length) {
      return result.rows;
    }
    return [];
  } catch (err) {
    console.error(`[getClientJobsByFunc] ${err.message}`);
    return [];
  }
}

export async function getClientJobs(
  schema: string,
  pendingOnly: boolean,
  limit: number = 1000,
) {
  // Only apply limit if not pendingOnly.
  // Else, need to ensure we get all pending.

  const queryString = `
      SELECT
        id,
        request_body,
        type,
        status,
        updated_at
      FROM ${schema}.${constants.TABLE_JOB}
      WHERE updated_at > NOW() - INTERVAL '24 HOURS'
      ${pendingOnly ? 'AND status = ANY($1::text[]) ' : ''}
      ORDER BY updated_at ASC
      ${pendingOnly ? '' : 'LIMIT $1'}
    `;
  try {
    const result = await query(
      queryString,
      pendingOnly
        ? [
            [
              constants.STATUS_QUEUED,
              constants.STATUS_SUBMITTED,
              constants.STATUS_PROCESSING,
            ],
          ]
        : [limit],
    );
    if (result.rows && result.rows.length) {
      return result.rows;
    }
    return [];
  } catch (err) {
    console.error(`[getClientJobsBy] ${err.message}`);
    return [];
  }
}

export async function getClientJobsByStatus(
  schema: string,
  status: string,
  limit: number = 100,
) {
  const queryString = `
      SELECT
        id,
        request_body,
        type,
        status,
        updated_at
      FROM ${schema}.${constants.TABLE_JOB}
      WHERE status = $1
      AND updated_at > NOW() - INTERVAL '24 HOURS'
      ORDER BY updated_at ASC
      LIMIT $2
    `;
  try {
    const result = await query(queryString, [status, limit]);
    if (result.rows && result.rows.length) {
      return result.rows;
    }
    return [];
  } catch (err) {
    console.error(`[getClientJobsByStatus] ${err.message}`);
    return [];
  }
}

export async function getAllClientJobs(
  pendingOnly: boolean = false,
): Promise<Record<string, Job[]>> {
  let return_jobs = {};
  try {
    const all_schemas_query = `
          SELECT nspname 
          FROM pg_namespace 
          WHERE nspname ~ '^c[0-9]*$' 
          ORDER BY nspname`;
    const schema_results = await query(all_schemas_query, []);

    if (schema_results.rows && schema_results.rows.length) {
      for (const schema_row of schema_results.rows) {
        const schema = schema_row.nspname;

        const jobs = await getClientJobs(schema, pendingOnly);
        if (!jobs.length) {
          continue;
        }
        return_jobs[schema] = jobs;
      }
    }
    return return_jobs;
  } catch (err) {
    console.error(`[getAllClientJobs] ${err.message}`);
    return return_jobs;
  }
}

export async function updateJobRecordStatus(
  schema: string,
  job_id: number,
  status: string,
) {
  let success = true;
  const queryString = `
    UPDATE ${schema}.${constants.TABLE_JOB} 
      SET status = $1
    WHERE id = $2
    RETURNING id;
  `;
  try {
    const result = await query(queryString, [status, job_id]);
    if (!result || !result.rows) {
      success = false;
    }
  } catch (err) {
    console.error(`[updateJobRecordStatus] ${err.message}`);
    success = false;
  } finally {
    return success;
  }
}

export async function updateJobRecord(
  schema: string,
  job_id: number,
  response_body: Record<string, any>,
  request_body: Record<string, any>,
  status: string,
) {
  let success = true;
  const queryString = `
    UPDATE ${schema}.${constants.TABLE_JOB} 
      SET 
      status = $1,
      response_body = $2,
      request_body = $3
    WHERE id = $4
    RETURNING id;
  `;
  try {
    const result = await query(queryString, [
      status,
      response_body,
      request_body,
      job_id,
    ]);
    if (!result || !result.rows) {
      success = false;
    }
  } catch (err) {
    console.error(`[updateJobRecord] ${err.message}`);
    success = false;
  } finally {
    return success;
  }
}

export async function updateJobRequest(
  schema: string,
  job_id: number,
  request_body: Record<string, any>,
  status: string,
) {
  let success = true;
  const queryString = `
    UPDATE ${schema}.${constants.TABLE_JOB} 
    SET request_body = $1, status = $2 
    WHERE id = $3
    RETURNING id;
  `;
  try {
    const result = await query(queryString, [request_body, status, job_id]);
    if (!result || !result.rows) {
      success = false;
    }
  } catch (err) {
    console.error(`[updateJobRequest] ${err.message}`);
    success = false;
  } finally {
    return success;
  }
}

export async function set_job_status_error(schema: string, job_id: number) {
  await updateJobRecordStatus(schema, job_id, constants.STATUS_ERROR);

  telemetryClient.trackEvent({
    name: constants.EVENT_JOB_ERROR,
    properties: {
      schema,
      job_id,
    },
  });
}
