import { constants } from '../constants';
import { ActivityLogData, Statistics } from '../model';
import { query } from './db';

export async function insertActivityLog(
  activity_log_data: ActivityLogData,
  stats: Statistics,
) {
  const {
    client_id,
    user_id,
    source_table,
    source_id,
    type,
    event,
    details,
    exclude_from_interaction_count,
  } = activity_log_data;

  const combinedDetails = mergeObjects(details, stats);

  const queryString = `
      INSERT INTO ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_ACTIVITY_LOG} 
      (client_id, user_id, date_timestamp, source_table, source_id, type, event, token_usage, details, exclude_from_interaction_count)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING id;`;
  const res = await query(queryString, [
    client_id,
    user_id,
    new Date(),
    source_table,
    source_id,
    type,
    event,
    stats.token_usage,
    combinedDetails,
    exclude_from_interaction_count,
  ]);
  if (res && res.rows) {
    return res.rows[0];
  }
  console.error(`[insertActivityLog] Activity log failed to insert ${res}`);
  return {};
}

//Merge two objects together. The objects might be json strings, null or blank or whatever.
export function mergeObjects(
  obj1: Record<string, any> | null | '' | string,
  obj2: Record<string, any> | null | '' | string,
): Record<string, any> {
  obj1 = obj1 ?? {};
  obj2 = obj2 ?? {};

  //json parse
  try {
    if (typeof obj1 === 'string') obj1 = JSON.parse(obj1);
  } catch (err) {
    obj1 = {};
  }

  try {
    if (typeof obj2 === 'string') obj2 = JSON.parse(obj2);
  } catch (err) {
    obj2 = {};
  }

  // Merge the two objects
  const mergedObj: Record<string, any> = { ...(obj1 as object) };
  for (const [key, value] of Object.entries(obj2)) {
    if (mergedObj.hasOwnProperty(key)) {
      if (Array.isArray(mergedObj[key]) && Array.isArray(value)) {
        mergedObj[key] = [...mergedObj[key], ...value];
      } else if (
        typeof mergedObj[key] === 'object' &&
        typeof value === 'object'
      ) {
        mergedObj[key] = mergeObjects(mergedObj[key], value);
      } else {
        mergedObj[key] = [mergedObj[key], value];
      }
    } else {
      mergedObj[key] = value;
    }
  }
  return mergedObj;
}
