import { AzureKeyCredential, EventGridPublisherClient } from '@azure/eventgrid';
import { DefaultAzureCredential } from '@azure/identity';
import { SecretClient } from '@azure/keyvault-secrets';
import {
  ProcessErrorArgs,
  ServiceBusClient,
  delay,
  isServiceBusError,
} from '@azure/service-bus';

let ANSWERED_ACCESS_KEY;
let client;

export const initSecret = async () => {
  if (process.env.ANSWERED_ACCESS_KEY) {
    ANSWERED_ACCESS_KEY = process.env.ANSWERED_ACCESS_KEY;
  } else {
    const credential = new DefaultAzureCredential();
    const vaultName = process.env.AZURE_KEY_VAULT_NAME;
    const vaultUrl = `https://${vaultName}.vault.azure.net`;
    const secretClient = new SecretClient(vaultUrl, credential);

    secretClient.getSecret('TOPIC-ANSWERED-ACCESS-KEY').then((secret) => {
      ANSWERED_ACCESS_KEY = secret.value;
    });
  }
};

export const getSbClientNewRequest = () => {
  const connectionString = process.env.TOPIC_NEW_REQUEST_CONNECTION_STRING;
  const sbClient = new ServiceBusClient(connectionString);
  return sbClient;
};

// export const getSbClientAnswered = () => {
//   const connectionString = process.env.TOPIC_ANSWERED_CONNECTION_STRING;
//   const sbClient = new ServiceBusClient(connectionString);
//   return sbClient;
// };

export const getSbClientCNC = () => {
  const connectionString = process.env.TOPIC_CNC_CONNECTION_STRING;
  const sbClient = new ServiceBusClient(connectionString);
  return sbClient;
};

export const getEGAnsweredClient = (): EventGridPublisherClient<'Custom'> => {
  if (client) return client;
  let endpoint = '';

  if (process.env.LOCAL_ANSWERED_TOPIC_ENDPOINT) {
    endpoint = process.env.LOCAL_ANSWERED_TOPIC_ENDPOINT;
  } else {
    const env_prefix = process.env.ENV_PREFIX;
    const env_string = env_prefix ? `-${env_prefix}` : '';
    endpoint = `https://answered${env_string}.eastus2-1.eventgrid.azure.net/api/events`;
  }
  client = new EventGridPublisherClient(
    endpoint,
    'EventGrid',
    new AzureKeyCredential(ANSWERED_ACCESS_KEY),
  );
  return client;
};

export const handleMessageError = async (
  args: ProcessErrorArgs,
  source: string,
) => {
  console.warn(`[${source}] ${args.errorSource} occurred: `, args.error);

  // The `subscribe() call will not stop trying to receive messages without explicit intervention.
  // The handler will continue to retry if `close()` is not called on the subscription.
  if (isServiceBusError(args.error)) {
    switch (args.error.code) {
      case 'MessagingEntityDisabled':
      case 'MessagingEntityNotFound':
      case 'UnauthorizedAccess':
        console.error(
          `[${source}] An unrecoverable error occurred. Stopping processing. ${args.error.code}`,
          args.error,
        );
        //TODO: more stuff
        break;
      case 'MessageLockLost':
        //keep trying
        console.log(`Message lock lost for message`, args.error);
        break;
      case 'ServiceBusy':
        // wait an arbitrary amount of time.
        await delay(1000);
        break;
    }
  }
};

export const topicName = {
  // answered: process.env.LOCAL_TOPIC_ANSWERED ? process.env.LOCAL_TOPIC_ANSWERED : 'answered',
  new_request: process.env.LOCAL_TOPIC_NEW_REQUEST
    ? process.env.LOCAL_TOPIC_NEW_REQUEST
    : 'new_request',
  cnc: process.env.LOCAL_TOPIC_CNC ? process.env.LOCAL_TOPIC_CNC : 'cnc',
};

export const subscriptionName = {
  // answered: process.env.LOCAL_TOPIC_SUBSCRIPTION_ANSWERED ? process.env.LOCAL_TOPIC_SUBSCRIPTION_ANSWERED : 'answered_subscription',
  new_request: process.env.LOCAL_TOPIC_SUBSCRIPTION_NEW_REQUEST
    ? process.env.LOCAL_TOPIC_SUBSCRIPTION_NEW_REQUEST
    : 'new_request_subscription',
  cnc: process.env.LOCAL_TOPIC_SUBSCRIPTION_CNC
    ? process.env.LOCAL_TOPIC_SUBSCRIPTION_CNC
    : 'cnc_subscription',
};
