export const getClient = jest.fn();
export const initializePool = jest.fn(() => {
  return {
    connect: jest.fn(),
    query: jest.fn(() => {
      return { rows: [] };
    }),
    end: jest.fn(),
    on: jest.fn(),
  };
});
export const query = jest.fn(() => {
  return { rows: [] };
});

export const getClientJobsByFunc = jest.fn();
export const getClientJobs = jest.fn();
export const getClientJobsByStatus = jest.fn();
export const getAllClientJobs = jest.fn(() => {
  return {};
});
export const updateJobRecordStatus = jest.fn();
export const updateJobRecord = jest.fn();
export const updateJobRequest = jest.fn();
export const set_job_status_error = jest.fn();
