//Mock the Azure service bus subscription
module.exports = {
  getSbClientAnswered: jest.fn().mockImplementation(() => {
    return {
      createReceiver: jest.fn().mockImplementation(() => {
        return {
          receiveMessages: jest.fn(),
          subscribe: jest.fn(),
          close: jest.fn(),
        };
      }),
      close: jest.fn(),
    };
  }),
  getSbClientCNC: jest.fn().mockImplementation(() => {
    return {
      createReceiver: jest.fn().mockImplementation(() => {
        return {
          receiveMessages: jest.fn(),
          subscribe: jest.fn(),
          close: jest.fn(),
        };
      }),
      close: jest.fn(),
    };
  }),
  getSbClientNewRequest: jest.fn().mockImplementation(() => {
    return {
      createReceiver: jest.fn().mockImplementation(() => {
        return {
          receiveMessages: jest.fn(),
          subscribe: jest.fn(),
          close: jest.fn(),
        };
      }),
      close: jest.fn(),
    };
  }),
  topicName: {
    answered: 'answered',
    cnc: 'cnc',
    newRequest: 'new_request',
  },
  subscriptionName: {
    answered: 'answered',
    cnc: 'cnc',
    newRequest: 'new_request',
  },
  initSecret: jest.fn(),
};
