import { get_encoding } from 'tiktoken';

const encoding = get_encoding('cl100k_base');

export function countTokens(text: string): number {
  return encoding.encode(text).length;
}

const tokensPerMessage = 3,
  tokensPerName = 1;

// Counting message tokens is not straight-forward when tool use needs to be
// taken into account. Tools are rendered into the system message using a
// format that had to be reverse-engineered by the community with each model
// update.
//
// This is the primary forum thread on the topic:
//   https://community.openai.com/t/how-to-calculate-the-tokens-when-using-function-call/266573/58
//
// This function's implementation is derived from the one in openai-java:
// https://github.com/forestwanglin/openai-java/blob/main/jtokkit/src/main/java/xyz/felh/openai/jtokkit/utils/TikTokenUtils.java
export function countTokensForMessages(messages: any[]): number {
  let numTokens = 0;
  for (const message of messages) {
    numTokens += tokensPerMessage;
    for (const [key, value] of Object.entries(message)) {
      numTokens += encoding.encode(String(value)).length;
      if (key === 'name') {
        numTokens += tokensPerName;
      }
    }
  }
  numTokens += 3; // every reply is primed with <|start|>assistant<|message|>
  return numTokens;
}

type ToolFunction = {
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<
      string,
      {
        type: string;
        description: string;
      }
    >;
    required: string[];
  };
};

type Tool = {
  type: 'function';
  function: ToolFunction;
};

export type ChatMessage = {
  role: 'assistant' | 'tool' | 'system' | 'user';
  content?: string;
  name?: string;
  tool_calls?: ToolCall[];
  tool_call_id?: string;
};

type ToolCall = {
  id: string;
  type: string;
  function: {
    name: string;
    arguments: string;
  };
};

export function countTokensInMessages(
  messages: ChatMessage[],
  tools: Tool[] = [],
): number {
  let tokens = 0;
  const toolMessageSize = messages.filter((it) => it.role === 'tool').length;

  if (toolMessageSize > 1) {
    tokens += toolMessageSize * 2 + 1;

    const jsonContentToolSize = messages.filter(
      (it) => it.role === 'tool' && isJSONString(it.content),
    ).length;

    if (jsonContentToolSize > 0) {
      tokens += 1 - jsonContentToolSize;
    }
  }

  let paddedSystem = false;
  messages.forEach((message) => {
    const msg = { ...message };
    if (msg.role === 'system' && tools.length > 0 && !paddedSystem) {
      if (isNotBlank(msg.content)) {
        msg.content += '\n';
        const formatted = formatFunctionDefinitions(tools);
        const includingHeader = `## Tools\n\n## functions\n\n${formatted}`;
        msg.content += includingHeader;
        paddedSystem = true;
      }
    }
    tokens += estimateTokensInMessage(msg, toolMessageSize);
  });

  // Each completion (vs message) seems to carry a 3-token overhead
  tokens += 3;
  return tokens;
}

export function countTokensInMessage(message: ChatMessage): number {
  return estimateTokensInMessage(message, 1);
}

function estimateTokensInMessage(
  message: ChatMessage,
  toolMessageSize: number,
): number {
  let tokens = 0;

  // role
  tokens += countTokens(message.role);

  // content
  if (message.role === 'tool') {
    if (toolMessageSize === 1) {
      tokens += countTokens(message.content ?? '');
    } else {
      // tokens += countTokens(format(message.content ?? ''));
      tokens += countTokens(message.content ?? '');
      const contentJSON = tryFormat(message.content ?? '');
      if (isNotBlank(contentJSON)) {
        tokens -= Object.keys(contentJSON).length;
      }
    }
  } else {
    if (typeof message.content === 'string') {
      tokens += countTokens(message.content);
    } else {
      // TODO: support array of content, eg image URLs.
    }
  }

  // name (not calculated for tool)
  if (message.role !== 'tool' && message.name) {
    tokens += countTokens(message.name) + 1; // +1 for the name
  }

  // Processing tool_calls if role is assistant
  if (message.role === 'assistant' && message.tool_calls) {
    for (const toolCall of message.tool_calls) {
      tokens += 3; // Base tokens for tool call
      tokens += countTokens(toolCall.type);
      // Assuming 'function' type needs to be checked and 'function' is always present
      if (toolCall.function.name) {
        const nameToken = countTokens(toolCall.function.name);
        tokens += nameToken * 2; // Name tokens doubled
      }
      if (toolCall.function.arguments) {
        tokens += countTokens(toolCall.function.arguments);
      }
    }
    if (message.tool_calls.length > 1) {
      tokens += 15 - message.tool_calls.length * 5 - 6;
    } else {
      tokens -= 2;
    }
  }

  // Role-based token adjustment
  tokens += message.role === 'tool' ? 2 : 3;

  return tokens;
}

type JsonType = Record<string, any>;

export function formatFunctionDefinitions(tools: Tool[]): string {
  const lines: string[] = ['namespace functions {', ''];
  for (const tool of tools) {
    const func = tool.function; // Adjusted to directly access the function property
    if (isNotBlank(func.description)) {
      lines.push(`// ${func.description}`);
    }

    const parameters = func.parameters as JsonType;
    const properties = parameters.properties as JsonType;
    const paramDescription = parameters.description;
    const paramTitle = parameters.title;

    if (isNotBlank(properties) && isNotBlank(Object.keys(properties))) {
      const paramLines: string[] = [];
      const hasParamDescription = isNotBlank(paramDescription),
        hasParamTitle = isNotBlank(paramTitle);

      let header: string;
      if (hasParamDescription && hasParamTitle) {
        header = `type ${func.name} = (_: // ${paramTitle}\n//\n// ${paramDescription}\n{`;
      } else if (hasParamDescription) {
        header = `type ${func.name} = (_: // ${paramDescription}\n{`;
      } else if (hasParamTitle) {
        header = `type ${func.name} = (_: // ${paramTitle}\n{`;
      } else {
        header = `type ${func.name} = (_: {`;
      }

      paramLines.push(header);
      const formattedProperties = formatObjectProperties(parameters);
      const formattedPropertiesHasNewLine = formattedProperties.includes('\n');

      if (!formattedPropertiesHasNewLine) {
        paramLines.push(formattedProperties);
        paramLines.push(`}) => any;'`);
        const joinedParams = paramLines.join(' ');
        lines.push(joinedParams);
      } else {
        paramLines.push(formattedProperties.trim());
        const joinedParams = paramLines.join('\n');
        lines.push(joinedParams);
        lines.push('}) => any;');
      }
    } else {
      lines.push(`type ${func.name} = () => any;`);
    }
    lines.push('');
  }
  lines.push(`} // namespace functions`);
  return lines.join('\n');
}

function formatObjectProperties(p: JsonType): string {
  const properties = p.properties as JsonType;
  if (isBlank(properties)) {
    return '';
  }
  const anyParameterHasDescription = Object.values(properties).some((element) =>
    isNotBlank(element['description']),
  );

  const requiredParams = p.required as string[];
  const lines: string[] = [];

  let index = 0;
  for (const key of Object.keys(properties)) {
    const prop = properties[key] as JsonType;
    const description = prop.description;

    if (isNotBlank(description)) {
      lines.push(`\n// ${description}`);
    }
    const isRequired = requiredParams && requiredParams.includes(key);
    const isRequiredArray = isRequired && prop.type === 'array';
    const hasDefault = prop.hasOwnProperty('default');
    const isLast = index === Object.keys(properties).length - 1;

    lines.push(
      `${key}${isRequired ? '' : '?'}: ${isRequiredArray ? 'Array<\n' : ''}${formatType(prop, isRequiredArray)}${!isLast || hasDefault ? ',' : ''}${hasDefault ? ` // default: ${prop['default']}` : ''}`,
    );
    index++;
  }
  const joined = lines.join(anyParameterHasDescription ? '\n' : ' ');
  return `${joined}${anyParameterHasDescription ? '\n' : ''}`;
}

function formatType(props: JsonType, isRequiredArray: boolean): string {
  const type = props.type;
  const isEnum = props.hasOwnProperty('enum');

  switch (type) {
    case 'string':
      if (isEnum) {
        const enumValues: string[] = props.enum;
        const enumStringified = enumValues.map((e) => `"${e}"`).join(' | ');
        return `${enumStringified},`;
      } else {
        return 'string';
      }

    case 'array':
      const hasItems = props.hasOwnProperty('items');
      if (hasItems) {
        const items = props.items as JsonType;
        const terminator = isRequiredArray ? '>' : '[]';
        return `${formatType(items, false)}${terminator}`;
      }
      return `any[]`;

    case 'object':
      return `{\n${formatObjectProperties(props)}\n}`;

    case 'integer':
      if (isEnum) {
        const enumValues: number[] = props.enum;
        return `${enumValues.join(' | ')},`;
      } else {
        return 'number';
      }

    case 'number':
      if (isEnum) {
        const enumValues: number[] = props.enum;
        return `${enumValues.join(' | ')},`;
      } else {
        return 'number';
      }

    case 'boolean':
      return 'boolean';
    case 'null':
      return 'null';
    default:
      return 'any';
  }
}

function isJSONString(content: string): boolean {
  try {
    JSON.parse(content);
    return true;
  } catch (ignored) {
    return false;
  }
}

function tryFormat(content: string): object | null {
  try {
    return JSON.parse(content);
  } catch (ignored) {
    return null;
  }
}

function format(content: any): string {
  try {
    const obj = JSON.parse(content.toString());
    return formatArguments(JSON.stringify(obj));
  } catch (ex) {
    // error
    return content.toString();
  }
}

function formatArguments(args: string): string {
  const jsonObject = JSON.parse(args);
  const lines: string[] = ['{'];
  const properties: string[] = [];

  Object.keys(jsonObject).forEach((fieldName) => {
    properties.push(`"${fieldName}":${formatValue(jsonObject[fieldName])}`);
  });

  lines.push(properties.join(',\n'));
  lines.push('}');
  return lines.join('\n');
}

function formatValue(value: any): string {
  if (typeof value === 'string') {
    return `"${value}"`;
  } else if (typeof value === 'number') {
    return `${value}`;
  } else if (Array.isArray(value)) {
    const arrayContents = value
      .map((item) => {
        if (typeof item === 'string') {
          return `"${item}"`;
        } else if (typeof item === 'number') {
          return `${item}`;
        } else {
          return '""';
        }
      })
      .join(',');
    return `[${arrayContents}]`;
  }
  return '""';
}

function isBlank<T>(t: T): boolean {
  if (t === null || t === undefined) {
    return true;
  }
  if (Array.isArray(t)) {
    return t.length === 0;
  } else if (typeof t === 'object' && !Array.isArray(t)) {
    return Object.keys(t).length === 0;
  } else if (typeof t === 'string') {
    return t.trim().length == 0;
  }
  return false;
}

function isNotBlank<T>(t: T): boolean {
  return !isBlank(t);
}
