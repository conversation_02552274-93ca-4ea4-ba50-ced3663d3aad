import { getAzureKeyValues } from '@tribble/azure-helper';
import { GoogleAuth } from 'google-auth-library';

let vertexCredential: any;

export const getCredentials = async () => {
  if (!vertexCredential) {
    const secrets = await getAzureKeyValues(['GOOGLE-AI-SA-CREDENTIAL']);
    vertexCredential = secrets['GOOGLE-AI-SA-CREDENTIAL'];
  }
  return vertexCredential;
};

export async function getGoogleAuth() {
  const credentials = await getCredentials();
  const auth = new GoogleAuth({
    credentials,
    scopes: ['https://www.googleapis.com/auth/cloud-platform'],
  });
  return auth;
}
