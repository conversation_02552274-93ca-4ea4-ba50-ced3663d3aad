import { DefaultAzureCredential } from '@azure/identity';
import { SecretClient } from '@azure/keyvault-secrets';
import { FunctionName, functionPorts } from '@tribble/types-shared';
import { constants } from '../constants';
import { FunctionRegistryEntry } from '../model';
import { set_job_status_error } from './db';

const RunningJobs = {};

export let function_map: Record<string, FunctionRegistryEntry> = {};

export function set_function_map(new_function_map) {
  function_map = new_function_map;
}

export const get_function_map = (): Record<string, FunctionRegistryEntry> => {
  return function_map;
};

function set_default_function_map() {
  const default_map = {
    ingest: {
      secret_name: 'FUNCKEY-INGEST',
      limit_per_client: 1,
    },
    ingest_url: {
      secret_name: 'FUNCKEY-INGEST-URL',
      limit_per_client: 1,
    },
    generate_content: {
      secret_name: 'FUNCKEY-GENERATE-CONTENT',
      limit_per_client: 1,
    },
    ingest_integration: {
      secret_name: 'FUNCKEY-INGEST-INTEGRATION',
      limit_per_client: 1,
      timeout_ms: 60000 * 60 * 24 * 3, // 3 days. The first ingests can be long
    },
    digest: {
      secret_name: 'FUNCKEY-DIGEST',
      limit_per_client: 1,
    },
  };

  const map = process.env.FUNCTION_MAP;
  if (map) {
    try {
      set_function_map(JSON.parse(map));
    } catch (err) {
      set_function_map(default_map);
    }
  } else {
    set_function_map(default_map);
  }
}

set_default_function_map();

export async function getFunctionKey(functionName) {
  //Get x-functions-key header from secrets
  const credential = new DefaultAzureCredential();
  const secret = get_function_map()[functionName].secret_name;
  const vaultName = process.env.AZURE_KEY_VAULT_NAME;
  const vaultUrl = `https://${vaultName}.vault.azure.net`;
  const secretClient = new SecretClient(vaultUrl, credential);
  const headerKey = await secretClient.getSecret(secret);

  return headerKey.value;
}

export async function callAzureFunction(
  functionName: string,
  data,
  job_id: number,
  schema: string,
  forceAzure = false,
  endpoint?: string,
) {
  const jobKey = `${job_id}, ${schema}`;
  if (RunningJobs[jobKey]) return; //If job_id is in progress, don't re run it.
  const env_prefix = process.env.ENV_PREFIX ? process.env.ENV_PREFIX : '';
  const functionPrefix = `functribble${env_prefix}${functionName.replace('_', '')}`; // e.g. functribbletestingest
  const azureUrl =
    process.env.ENVIRONMENT == 'development' && !forceAzure
      ? `http://127.0.0.1:${functionPorts[functionName]}`
      : `https://${functionPrefix}.azurewebsites.net`;
  endpoint = endpoint || `/api/invoke`;
  const functionUrl = azureUrl + endpoint;

  let headers = {
    'Content-Type': 'application/json',
  };

  const functionHeaderKey = await getFunctionKey(functionName);
  if (
    functionHeaderKey &&
    (process.env.ENVIRONMENT != 'development' || forceAzure)
  ) {
    headers['x-functions-key'] = functionHeaderKey;
  }

  try {
    console.log(
      `[callAzureFunction] [${schema}] Calling function: ${functionName} [${functionUrl}]`,
    );

    const response = await fetch(functionUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify(data),
    });

    RunningJobs[jobKey] = true; //Keep track of which jobs have been started.

    return response;
  } catch (err) {
    console.error(
      `[callAzureFunction] Error calling Azure Function [${functionName}]: `,
      `data: ${JSON.stringify(data, null, 2)}`,
      err,
    );
    return null;
  }
}

interface FunctionCallResponseJson {
  id?: string;
  statusQueryGetUri?: string;
  sendEventPostUri?: string;
  terminatePostUri?: string;
  rewindPostUri?: string;
  purgeHistoryDeleteUri?: string;
  restartPostUri?: string;
  suspendPostUri?: string;
  resumePostUri?: string;
}

interface FunctionStatus {
  //from azure function statusQueryGetUri
  name?: FunctionName;
  runtimeStatus?:
    | 'Pending'
    | 'Running'
    | 'Completed'
    | 'ContinuedAsNew'
    | 'Failed'
    | 'Terminated'
    | 'Suspended';
  input?: Record<string, any>; //function call body
  output?: Record<string, any>;
  createdTime?: Date;
  lastUpdatedTime?: Date;
}

let health_check_interval =
  parseInt(process.env.HEALTH_CHECK_INTERVAL) || constants.CHECK_FUNCTION_TIMER;

export const getHealthCheckInterval = (): number => {
  return health_check_interval;
};

export const setHealthCheckInterval = (new_interval: number) => {
  health_check_interval = new_interval;
};
//Continually check function status to confirm it's healthy.
//If it fails, update the job record with error status.
export async function confirm_function_healthy(
  job_id: number,
  schema: string,
  function_json: FunctionCallResponseJson,
  timeout_ms?: number,
) {
  const jobKey = `${job_id}, ${schema}`;
  async function job_error() {
    await set_job_status_error(schema, job_id);
    delete RunningJobs[jobKey];
  }

  if (!function_json || !function_json.statusQueryGetUri) {
    await job_error();
    return;
  }

  const max_fetch_retries = 2;
  let fetch_retry = 1;
  const start = new Date();

  //Keep checking function status until it's completed (or failed) (or expired).
  const timerId = setInterval(async () => {
    if (fetch_retry >= max_fetch_retries) {
      job_error();

      clearInterval(timerId);

      return;
    }

    if (isExpired(start, timeout_ms)) {
      job_error();
      clearInterval(timerId);
      console.error(
        `[Function timeout] Function id: ${function_json.id}, Job: ${job_id}`,
      );
      return;
    }

    try {
      const func_status = await get_function_status(
        function_json.statusQueryGetUri,
      );
      if (!func_status || !func_status.runtimeStatus) {
        fetch_retry++;
        return;
      }

      const status = func_status.runtimeStatus;
      if (
        status == 'Failed' ||
        status == 'Suspended' ||
        status == 'Terminated'
      ) {
        console.error(`[Job ${job_id} - ${status}]: `, func_status);

        await job_error();
        clearInterval(timerId);
      }

      if (status == 'Completed') {
        clearInterval(timerId);
        delete RunningJobs[jobKey]; //clear from RunningJobs
      }
    } catch (err) {
      fetch_retry++;
    }
  }, getHealthCheckInterval());
}

export async function get_function_status(queryUrl): Promise<FunctionStatus> {
  try {
    const result = await fetch(queryUrl);
    const json = await result.json();
    return json;
  } catch (err) {
    console.error(
      `[get_function_status] unable to check azure function [url=${queryUrl}]: ${err.message}`,
    );
    return null;
  }
}

let FUNCTION_MAX_RUN_TIME_HOURS = parseInt(
  process.env.FUNCTION_MAX_RUN_TIME_HOURS || '6',
);
let expiry = FUNCTION_MAX_RUN_TIME_HOURS * 60 * 60 * 1000; // [x] hours * 60 minutes * 60 seconds * 1000 milliseconds;

function getFunctionExpiry() {
  return expiry;
}
export function setFunctionExpiry(new_expiry) {
  expiry = new_expiry;
}

export function isExpired(date: Date, timeout_ms?: number) {
  const now = new Date();
  const expiry = timeout_ms || getFunctionExpiry();
  return now.getTime() - date.getTime() > expiry;
}
