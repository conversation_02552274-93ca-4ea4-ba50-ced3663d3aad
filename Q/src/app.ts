require('dotenv').config();

import { EventGridDeserializer } from '@azure/eventgrid';
import {
  ProcessErrorArgs,
  ServiceBusReceivedMessage,
  ServiceBusReceiver,
} from '@azure/service-bus';
import { Server, ServerCredentials } from '@grpc/grpc-js';
import { ChatCompletionServiceService } from '@tribble/chat-completion-service';
import {
  acquireDatabaseLock,
  releaseDatabaseLock,
} from '@tribble/common-utils';
import { initializePool } from '@tribble/tribble-db/db_ops';
import { BrainIngestService } from 'brain-ingest-service';
import http from 'http';
import { brainIngestServer } from './brain_ingest';
import { chatCompletionServer } from './chat_completion';
import { initializeCNC } from './cnc';
import { constants } from './constants';
import { initializeEmbedders } from './embeddings';
import { initializeFunctionTimer } from './functionQueue';
import { init as initLlmResources, initializeLLMTimer } from './llmResource';
import { FunctionRequest } from './model';
import {
  callAzureFunction,
  confirm_function_healthy,
  get_function_map,
} from './utils/azure';
import {
  getClient,
  getClientIdFromSchema,
  getClientJobsByFunc,
  set_job_status_error,
  updateJobRecord,
  updateJobRequest,
} from './utils/db';
import {
  getSbClientNewRequest,
  handleMessageError,
  initSecret,
  subscriptionName,
  topicName,
} from './utils/serviceBus';
import { initializeTelemetryTimer, telemetryClient } from './utils/telemetry';

initializeCNC();

export const initialize = async () => {
  await initializePool();
  await initLlmResources();
  await initializeEmbedders();
  await initSecret();
  initializeTelemetryTimer();
  initializeFunctionTimer();
};

const consumer = new EventGridDeserializer();

let answerReceiver: ServiceBusReceiver;
let answerSubscription: ReturnType<ServiceBusReceiver['subscribe']>;
let newRequestReceiver: ServiceBusReceiver;
let newRequestSubscription: ReturnType<ServiceBusReceiver['subscribe']>;

export const startServiceBusListeners = () => {
  console.log('Starting function call request listeners');

  newRequestReceiver = getSbClientNewRequest().createReceiver(
    topicName.new_request,
    subscriptionName.new_request,
    {
      receiveMode: 'peekLock',
    },
  );

  newRequestSubscription = newRequestReceiver.subscribe(
    {
      //New request message processor
      processMessage: async (brokeredMessage: ServiceBusReceivedMessage) => {
        const events = await consumer.deserializeEventGridEvents(
          brokeredMessage.body,
        );
        for (const event of events) {
          if (
            event.eventType ==
            constants.EVENT_GRID_TYPE_FUNCTION_CALL_NEW_REQUEST
          ) {
            let request: FunctionRequest = event.data as FunctionRequest;

            console.log(
              `[newRequestSubscription] Received a [${constants.EVENT_GRID_TYPE_FUNCTION_CALL_NEW_REQUEST}] message (Schema: ${request.schema}, JobId: ${request.job_id})`,
            );
            processFunctionRequest(request);
          }
        }
        newRequestReceiver.completeMessage(brokeredMessage);
      },

      processError: async (args: ProcessErrorArgs) => {
        console.error(`[newRequestSubscription] processError triggered.`);
        handleMessageError(args, 'newRequestSubscription');
      },
    },
    {
      autoCompleteMessages: false,
    },
  );
};

const processFunctionRequest = async (request: FunctionRequest) => {
  const { schema, function_name, job_id, payload, user_id } = request;
  const function_map = get_function_map();
  const function_registry = function_map[function_name],
    forceAzure = false;

  if (!function_registry) {
    await set_job_status_error(schema, job_id);
    telemetryClient.trackEvent({
      name: constants.EVENT_FUNCTION_MAP_ERROR,
      properties: {
        job_id,
        schema,
        function_name,
      },
    });
    return;
  }

  // There can be a race condition from bursty bunch of requests.
  // Try to acquire a db lock to properly check # of running jobs.
  const clientId = await getClientIdFromSchema(schema);
  const dbClient = await getClient();

  try {
    const lock = await acquireDatabaseLock(
      clientId,
      user_id,
      function_name,
      dbClient,
    );

    // If can't get a lock, just make the job queued
    if (!lock) {
      try {
        console.log(
          `[processFunctionRequest] Could not acquire lock. Marking job [${job_id}] as queued.`,
        );
        await updateJobRequest(
          schema,
          job_id,
          payload,
          constants.STATUS_QUEUED,
        );
      } catch (err) {
        // Something very wrong w DB if this happens.
        console.error(
          `[processFunctionRequest] Lock not acquired. Error updating job request: ${err.message}`,
        );
      }
      return;
    }

    try {
      const running_jobs = await getClientJobsByFunc(
        schema,
        function_name,
        constants.STATUS_PROCESSING,
        function_registry.limit_per_client,
      );

      if (running_jobs.length >= function_registry.limit_per_client) {
        console.info(
          `[processFunctionRequest] ` +
            `Running jobs (${running_jobs.length}) limit (${function_registry.limit_per_client}) reached. ` +
            `Marking job [${job_id}] as queued. (schema: ${schema})`,
        );
        //We will not run the job now. Make sure the request body is set and return.
        //the timer at initializeFunctionTimer will pick this up later.
        const success = await updateJobRequest(
          schema,
          job_id,
          payload,
          constants.STATUS_QUEUED,
        );
        if (!success) {
          //Job may not run.
          telemetryClient.trackEvent({
            name: constants.EVENT_JOB_ERROR,
            properties: {
              job_id,
              schema,
              function_name,
              has_request_body: false,
              payload,
            },
          });
        }
        return;
      }

      let response_json = {},
        isStructuredDigest =
          request.payload.is_structured && function_name == 'digest';
      try {
        let endpoint = `/api/invoke`;

        if (isStructuredDigest) {
          endpoint = `/api/process_structured_file`;
          callAzureFunction(
            function_name,
            payload,
            job_id,
            schema,
            forceAzure,
            endpoint,
          );

          await updateJobRecord(
            schema,
            job_id,
            {}, // sync function, no response
            payload,
            constants.STATUS_PROCESSING,
          );
          return;
          //Digest is a synchronous function. Don't await it.
        }

        const function_initiation = await callAzureFunction(
          function_name,
          payload,
          job_id,
          schema,
          forceAzure,
          endpoint,
        );
        if (function_initiation) {
          response_json = await function_initiation.json();
          confirm_function_healthy(
            job_id,
            schema,
            response_json,
            function_registry.timeout_ms,
          );
          await updateJobRecord(
            schema,
            job_id,
            response_json,
            payload,
            constants.STATUS_PROCESSING,
          );
        } else {
          //Job did not start leave it in queue.
          await updateJobRecord(
            schema,
            job_id,
            response_json,
            payload,
            constants.STATUS_QUEUED,
          );
        }
      } catch (err) {
        console.error(
          `[processFunctionRequest] Error calling Azure Function: ${err.message} (Schema: ${schema}, JobId: ${job_id})`,
        );
      }
    } finally {
      await releaseDatabaseLock(clientId, user_id, function_name, dbClient);
    }
  } finally {
    dbClient.release();
  }
};

const startGrpcServer = async () => {
  const server = new Server();

  // Add the service implementations to the server
  server.addService(BrainIngestService, brainIngestServer);
  server.addService(ChatCompletionServiceService, chatCompletionServer);

  // Specify the server address and port
  // TODO: Remove the fallback to CHAT_COMPLETION_SERVER_ADDRESS once all
  // prod configurations have been updated.
  const serverAddress =
    process.env.GRPC_SERVER_ADDRESS ||
    process.env.CHAT_COMPLETION_SERVER_ADDRESS;

  // Start the server
  server.bindAsync(
    serverAddress,
    ServerCredentials.createInsecure(),
    (error, port) => {
      if (error) {
        console.error(`Server error: ${error.message}`);
      } else {
        console.log(`Server listening on port ${port}`);
      }
    },
  );
};

const startAzureHCServer = async () => {
  // Run a simple HTTP 1.1 server that response to / with 200.
  const server = http.createServer((req, res) => {
    // Default response for all paths
    res.writeHead(200);
    res.end();
  });

  const port = process.env.PORT || 8080;

  server.listen(port, () => {
    console.log(`Azure HC server listening on port ${port}`);
  });
};

initialize().then(() => {
  startGrpcServer();
  startAzureHCServer();
  startServiceBusListeners();
});

export const closeAll = async () => {
  if (answerSubscription) answerSubscription.close();
  if (answerReceiver) answerReceiver.close();
  if (newRequestReceiver) newRequestReceiver.close();
  if (newRequestSubscription) newRequestSubscription.close();

  const pool = await initializePool();
  if (pool) {
    pool.end();
  }

  clearInterval(initializeLLMTimer());

  clearInterval(initializeTelemetryTimer());
  clearInterval(initializeFunctionTimer());
};
