import { FunctionName } from '@tribble/types-shared';

export interface LLMResourceBootstrap {
  id: number;
  name: string;
  provider: string;
  basemodel: 'gpt4' | 'gpt4-32k' | string;
  tokens_per_minute: number;
  requests_per_minute: number;
  tokens_per_request: number;
  cost_per_1k_token_prompt: number;
  cost_per_1k_token_completion: number;
  url: string;
  api_key: string;
  is_internal: boolean;
  gcp_ml_region: string;
  vertex_project_id: string;
}

export interface LLMResource
  extends Omit<
    LLMResourceBootstrap,
    'tokens_per_minute' | 'requests_per_minute' | 'tokens_per_request'
  > {
  current: {
    requests_this_minute: number;
    tokens_this_minute: number;
  };
  limits: LLMResourceLimit;
  last_used?: number;
}

interface LLMResourceLimit {
  requests_per_minute: number;
  tokens_per_minute: number;
  tokens_per_request: number;
}

export type ActivityLogData = {
  client_id?: number;
  user_id?: number;
  details?: string;
  event?: string;
  source_table: 'conversation' | 'conversation_detail' | 'content';
  source_id: string;
  type: string;
  enter_queue?: Date;
  leave_queue?: Date;
  exclude_from_interaction_count?: boolean;
};

export interface Job {
  id: number;
  type: string;
  status?: 'processed' | 'processing' | 'queued' | 'error' | 'submitted';
  request_body?: { [key: string]: string };
  response_body?: { [key: string]: string };
  error?: string;
  created_date: Date;
  updated_at?: Date; //timestamp without timezone;
  created_by_id: number;
}

export interface FunctionRequest<T = any> {
  function_name: FunctionName;
  job_id: number;
  schema: string;
  client_id: number;
  user_id: number;
  request_id?: string;
  payload: T;
}

export interface FunctionRegistryEntry {
  secret_name: string;
  limit_per_client: number;
  timeout_ms?: number;
}

export interface Statistics {
  token_usage: TokenUsage;
  duration?: { start: Date; end: Date; durationMs: number };
  enter_queue?: Date;
  leave_queue?: Date;
  llm_name?: string;
}

export interface TokenUsage {
  completion_tokens: number;
  prompt_tokens: number;
  total_cost?: number;
  total_tokens: number;
}

export interface IResponse<T = any> {
  success: boolean;
  error?: string;
  response?: T;
}
