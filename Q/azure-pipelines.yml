# Node.js Express Web App to Linux on Azure
# Build a Node.js Express app and deploy it to Azure as a Linux web app.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript

trigger:
  branches:
    include:
      - main
      - dev
  # This is important in order to trigger the fild only for the current folder. See root-level README.
  paths:
    include:
      - 'Q/*'
      - 'packages/*'

pr: none

variables:
  # Agent VM image name
  vmImageName: 'ubuntu-latest'

  # Working Directory
  workingDirectory: '$(Build.SourcesDirectory)/Q'

stages:
  - stage: Build
    displayName: Build
    jobs:
      - job: Build
        displayName: Build
        pool:
          vmImage: $(vmImageName)

        steps:
          - task: NodeTool@0
            inputs:
              versionSpec: '20.19'
            displayName: 'Install Node.js'

          - script: |
              node $(Build.SourcesDirectory)/scripts/generate-version.js $(Build.SourcesDirectory)/Q
            displayName: 'Generate Version Info'

          - script: |
              npx -yes turbo@1.13.3 prune Q
            displayName: "Separate Q from the monorepo"
            workingDirectory: $(Build.SourcesDirectory)

          - script: |
              npm install
              npm run build
            displayName: 'npm install and build'
            failOnStderr: false
            workingDirectory: '$(Build.SourcesDirectory)/out'

          - script: |
              find . -type d \( -name dist -o -name node_modules \) -exec zip -r $(Build.ArtifactStagingDirectory)/$(Build.BuildId)_ds9.zip {} +;
              find . -type f \( -name package.json -o -name package-lock.json \) -exec zip -ur $(Build.ArtifactStagingDirectory)/$(Build.BuildId)_ds9.zip {} +;
              [ -f turbo.json ] && zip -ur $(Build.ArtifactStagingDirectory)/$(Build.BuildId)_ds9.zip turbo.json;
            displayName: 'Create release zip'
            workingDirectory: $(Build.SourcesDirectory)/out

          - upload: $(Build.ArtifactStagingDirectory)/$(Build.BuildId)_ds9.zip
            artifact: release
