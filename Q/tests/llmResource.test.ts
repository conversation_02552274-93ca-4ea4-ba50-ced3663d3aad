jest.mock('../src/utils/serviceBus');
jest.mock('../src/utils/db');
jest.mock('../src/utils/telemetry');
jest.useFakeTimers();
import { ResourceBaseModel } from '@tribble/chat-completion-service/src';
import { closeAll } from '../src/app';
import {
  LlmResources,
  getRightSizeLLM,
  setBusy,
  setFree,
  sortBy,
} from '../src/llmResource';

describe('LLM Resource tests', () => {
  beforeEach(() => {
    // Reset the LLM resources array
    while (LlmResources.length) {
      LlmResources.pop();
    }
  });
  afterAll(async () => {
    await closeAll();
    jest.clearAllTimers();
    jest.useRealTimers();
  });
  it('should return nothing when there are no free resources', () => {
    //Add gpt-3 resource
    LlmResources.push({
      id: 1,
      name: 'Resource1',
      provider: 'openai',
      basemodel: 'gpt-3',
      current: {
        requests_this_minute: 21,
        tokens_this_minute: 4000,
      },
      limits: {
        requests_per_minute: 20,
        tokens_per_minute: 4000,
        tokens_per_request: 10000,
      },
      is_internal: true,
      url: 'https://www.llmresource1.com',
      api_key: 'key123',
      cost_per_1k_token_prompt: 0,
      cost_per_1k_token_completion: 0,
    });
    //Add gpt-4 resource
    LlmResources.push({
      id: 2,
      name: 'Resource2',
      provider: 'openai',
      basemodel: 'gpt-4',
      current: {
        requests_this_minute: 19,
        tokens_this_minute: 4000,
      },
      limits: {
        requests_per_minute: 20,
        tokens_per_minute: 4000,
        tokens_per_request: 10000,
      },
      is_internal: true,
      url: 'https://www.llmresource2.com',
      api_key: 'key456',
      cost_per_1k_token_prompt: 0,
      cost_per_1k_token_completion: 0,
    });
    expect(getRightSizeLLM(false, 25)).toBeUndefined();
  });
  it('should return free resource with priority to gpt4 model', () => {
    //Add gpt-3 resource
    LlmResources.push({
      id: 1,
      name: 'Resource1',
      provider: 'openai',
      basemodel: 'gpt-3',
      current: {
        requests_this_minute: 10,
        tokens_this_minute: 100,
      },
      limits: {
        requests_per_minute: 20,
        tokens_per_minute: 4000,
        tokens_per_request: 10000,
      },
      is_internal: true,
      url: 'https://www.llmresource1.com',
      api_key: 'key123',
      cost_per_1k_token_prompt: 0,
      cost_per_1k_token_completion: 0,
    });
    //Add gpt-4 resource
    LlmResources.push({
      id: 2,
      name: 'Resource2',
      provider: 'openai',
      basemodel: 'gpt-4',
      current: {
        requests_this_minute: 10,
        tokens_this_minute: 100,
      },
      limits: {
        requests_per_minute: 20,
        tokens_per_minute: 4000,
        tokens_per_request: 10000,
      },
      is_internal: true,
      url: 'https://www.llmresource2.com',
      api_key: 'key456',
      cost_per_1k_token_prompt: 0,
      cost_per_1k_token_completion: 0,
    });
    const freeLLM = getRightSizeLLM(false, 20);
    expect(freeLLM).toBeDefined();
    expect(freeLLM?.resource.basemodel).toEqual('gpt-4');
  });
  it('should return first free resource if no gpt4 model is free', () => {
    LlmResources.push({
      id: 1,
      name: 'Resource1',
      provider: 'openai',
      basemodel: 'gpt-3',
      current: {
        requests_this_minute: 10,
        tokens_this_minute: 100,
      },
      limits: {
        requests_per_minute: 20,
        tokens_per_minute: 4000,
        tokens_per_request: 10000,
      },
      is_internal: true,
      url: 'https://www.llmresource1.com',
      api_key: 'key123',
      cost_per_1k_token_prompt: 0,
      cost_per_1k_token_completion: 0,
    });
    //Add gpt-4 resource
    LlmResources.push({
      id: 2,
      name: 'Resource2',
      provider: 'openai',
      basemodel: 'gpt-4',
      current: {
        requests_this_minute: 20,
        tokens_this_minute: 100,
      },
      limits: {
        requests_per_minute: 20,
        tokens_per_minute: 4000,
        tokens_per_request: 10000,
      },
      is_internal: true,
      url: 'https://www.llmresource2.com',
      api_key: 'key456',
      cost_per_1k_token_prompt: 0,
      cost_per_1k_token_completion: 0,
    });
    const freeLLM = getRightSizeLLM(false, 40);
    expect(freeLLM).toBeDefined();
    expect(freeLLM?.resource.api_key).toEqual('key123');
  });
  it('should set resources busy or free correctly', () => {
    LlmResources.push({
      id: 1,
      name: 'Resource1',
      provider: 'openai',
      basemodel: 'gpt-3',
      current: {
        requests_this_minute: 10,
        tokens_this_minute: 100,
      },
      limits: {
        requests_per_minute: 20,
        tokens_per_minute: 4000,
        tokens_per_request: 10000,
      },
      is_internal: true,
      url: 'https://www.llmresource1.com',
      api_key: 'key123',
      cost_per_1k_token_prompt: 0,
      cost_per_1k_token_completion: 0,
    });
    //Add gpt-4 resource
    LlmResources.push({
      id: 2,
      name: 'Resource2',
      provider: 'openai',
      basemodel: 'gpt-4',
      current: {
        requests_this_minute: 10,
        tokens_this_minute: 100,
      },
      limits: {
        requests_per_minute: 20,
        tokens_per_minute: 4000,
        tokens_per_request: 10000,
      },
      is_internal: true,
      url: 'https://www.llmresource2.com',
      api_key: 'key456',
      cost_per_1k_token_prompt: 0,
      cost_per_1k_token_completion: 0,
    });
    const currentTokens = LlmResources[0].current.tokens_this_minute;
    setBusy(0, 100);
    expect(LlmResources[0].current.tokens_this_minute).toEqual(
      currentTokens + 100,
    );
    setFree(0);
  });
  it('setbusy should impact getRightSizeLLM', () => {
    LlmResources.push({
      id: 1,
      name: 'Resource1',
      provider: 'openai',
      basemodel: 'gpt-3',
      current: {
        requests_this_minute: 10,
        tokens_this_minute: 100,
      },
      limits: {
        requests_per_minute: 20,
        tokens_per_minute: 4000,
        tokens_per_request: 10000,
      },
      is_internal: true,
      url: 'https://www.llmresource1.com',
      api_key: 'key123',
      cost_per_1k_token_prompt: 0,
      cost_per_1k_token_completion: 0,
    });
    //Add gpt-4 resource
    LlmResources.push({
      id: 2,
      name: 'Resource2',
      provider: 'openai',
      basemodel: 'gpt-4',
      current: {
        requests_this_minute: 10,
        tokens_this_minute: 100,
      },
      limits: {
        requests_per_minute: 20,
        tokens_per_minute: 4000,
        tokens_per_request: 10000,
      },
      is_internal: true,
      url: 'https://www.llmresource2.com',
      api_key: 'key456',
      cost_per_1k_token_prompt: 0,
      cost_per_1k_token_completion: 0,
    });
    const llm = getRightSizeLLM(false, 10);
    expect(llm).toBeDefined();
    expect(llm?.resource.basemodel).toBe('gpt-4');
    setBusy(llm!.index, 20000);
    const newLlm = getRightSizeLLM(false, 10);
    expect(newLlm?.resource.basemodel).toBe('gpt-3');
  });

  it('should reset requests and tokens per minute every 60 seconds', () => {
    LlmResources.push({
      id: 1,
      name: 'Resource1',
      provider: 'openai',
      basemodel: 'gpt-4',
      current: {
        requests_this_minute: 20,
        tokens_this_minute: 500,
      },
      limits: {
        requests_per_minute: 1000,
        tokens_per_minute: 10000,
        tokens_per_request: 10000,
      },
      is_internal: true,
      url: 'https://www.llmresource1.com',
      api_key: 'key123',
      cost_per_1k_token_prompt: 10,
      cost_per_1k_token_completion: 5,
    });
    jest.advanceTimersByTime(60000);
    expect(LlmResources[0].current.requests_this_minute).toBe(0);
    expect(LlmResources[0].current.tokens_this_minute).toBe(0);
  });
});

import { prioritizeBy } from '../src/llmResource';

describe('prioritizeByName', () => {
  it('should prioritize resources with the keyword in ascending order', () => {
    const resources = [
      { index: 1, resource: { name: 'Resource1' } },
      { index: 2, resource: { name: 'Resource2' } },
      { index: 3, resource: { name: 'Resource3' } },
      { index: 4, resource: { name: 'Resource312' } },
    ] as any;
    const sortByName = prioritizeBy('name', '2');

    const sortedResources = resources.sort(sortByName);
    expect(sortedResources[0].resource.name).toEqual('Resource2');
    expect(sortedResources[1].resource.name).toEqual('Resource312');
    expect(sortedResources[2].resource.name).toEqual('Resource1');
    expect(sortedResources[3].resource.name).toEqual('Resource3');
  });

  it('should deprioritize resources with the keyword in descending order', () => {
    const resources = [
      { index: 1, resource: { name: 'Resource1' } },
      { index: 2, resource: { name: 'Resource2' } },
      { index: 3, resource: { name: 'Resource3' } },
      { index: 4, resource: { name: 'Resource42' } },
    ] as any;

    const sortedResources = resources.sort(prioritizeBy('name', '2', 'DESC'));
    expect(sortedResources[0].resource.name).toEqual('Resource1');
    expect(sortedResources[1].resource.name).toEqual('Resource3');
    expect(sortedResources[2].resource.name).toEqual('Resource2');
    expect(sortedResources[3].resource.name).toEqual('Resource42');
  });

  it('should consider resources with the keyword equal if both have or neither have the keyword', () => {
    const resources = [
      { index: 1, resource: { name: 'Resource1' } },
      { index: 2, resource: { name: 'Resource2' } },
      { index: 3, resource: { name: 'Resource3' } },
    ] as any;

    const sortedResources = resources.sort(prioritizeBy('name', '4'));
    expect(sortedResources[0].resource.name).toEqual('Resource1');
    expect(sortedResources[1].resource.name).toEqual('Resource2');
    expect(sortedResources[2].resource.name).toEqual('Resource3');
  });
});
describe('sortBy', () => {
  const resources = [
    { index: 1, resource: { name: 'Resource1', last_used: 10 } },
    { index: 2, resource: { name: 'Resource2', last_used: 5 } },
    { index: 3, resource: { name: 'Resource3', last_used: 15 } },
  ] as any[];

  it('should sort resources in ascending order based on the specified field', () => {
    const sortByNameAsc = sortBy('name');
    const sortedResources = resources.sort(sortByNameAsc);
    expect(sortedResources[0].resource.name).toEqual('Resource1');
    expect(sortedResources[1].resource.name).toEqual('Resource2');
    expect(sortedResources[2].resource.name).toEqual('Resource3');
  });

  it('should sort resources in descending order based on the specified field', () => {
    const sortByValueDesc = sortBy('last_used', 'DESC');
    const sortedResources = resources.sort(sortByValueDesc);
    expect(sortedResources[0].resource.last_used).toEqual(15);
    expect(sortedResources[1].resource.last_used).toEqual(10);
    expect(sortedResources[2].resource.last_used).toEqual(5);
  });

  it('should consider resources with the same field value as equal', () => {
    const sortByNameAsc = sortBy('name');
    const sortedResources = resources.sort(sortByNameAsc);
    expect(sortedResources[0].resource.name).toEqual('Resource1');
    expect(sortedResources[1].resource.name).toEqual('Resource2');
    expect(sortedResources[2].resource.name).toEqual('Resource3');
  });
});
describe('getRightSizeLLM', () => {
  beforeEach(() => {
    // Reset the LLM resources array
    while (LlmResources.length) {
      LlmResources.pop();
    }
  });

  it('should return undefined when there are no free resources', () => {
    const result = getRightSizeLLM(false, 25);
    expect(result).toBeUndefined();
  });

  it('should return free resource with priority to GPT-4 Turbo model', () => {
    LlmResources.push({
      id: 1,
      name: 'Resource1',
      provider: 'openai',
      basemodel: 'gpt-3',
      current: {
        requests_this_minute: 10,
        tokens_this_minute: 100,
      },
      limits: {
        requests_per_minute: 20,
        tokens_per_minute: 4000,
        tokens_per_request: 10000,
      },
      is_internal: true,
      url: 'https://www.llmresource1.com',
      api_key: 'key123',
      cost_per_1k_token_prompt: 0,
      cost_per_1k_token_completion: 0,
    });

    LlmResources.push({
      id: 2,
      name: 'Resource2-gpt-4-turbo',
      provider: 'openai',
      basemodel: 'gpt-4',
      current: {
        requests_this_minute: 10,
        tokens_this_minute: 100,
      },
      limits: {
        requests_per_minute: 20,
        tokens_per_minute: 4000,
        tokens_per_request: 10000,
      },
      is_internal: true,
      url: 'https://www.llmresource2.com',
      api_key: 'key456',
      cost_per_1k_token_prompt: 0,
      cost_per_1k_token_completion: 0,
    });

    const result = getRightSizeLLM(false, 20, ResourceBaseModel.GPT_4_TURBO);
    expect(result).toBeDefined();
    expect(result?.resource.basemodel).toEqual('gpt-4');
    expect(result?.resource.name).toEqual('Resource2-gpt-4-turbo');
  });

  it('should return free resource with priority to GPT-3.5 Turbo model', () => {
    LlmResources.push({
      id: 1,
      name: 'Resource1',
      provider: 'openai',
      basemodel: 'gpt-4',
      current: {
        requests_this_minute: 10,
        tokens_this_minute: 100,
      },
      limits: {
        requests_per_minute: 20,
        tokens_per_minute: 4000,
        tokens_per_request: 10000,
      },
      is_internal: true,
      url: 'https://www.llmresource1.com',
      api_key: 'key123',
      cost_per_1k_token_prompt: 0,
      cost_per_1k_token_completion: 0,
    });

    LlmResources.push({
      id: 2,
      name: 'Resource2',
      provider: 'openai',
      basemodel: 'gpt-35-turbo',
      current: {
        requests_this_minute: 10,
        tokens_this_minute: 100,
      },
      limits: {
        requests_per_minute: 20,
        tokens_per_minute: 4000,
        tokens_per_request: 10000,
      },
      is_internal: true,
      url: 'https://www.llmresource2.com',
      api_key: 'key456',
      cost_per_1k_token_prompt: 0,
      cost_per_1k_token_completion: 0,
    });

    const result = getRightSizeLLM(false, 20, ResourceBaseModel.GPT_3_5_TURBO);
    expect(result).toBeDefined();
    expect(result?.resource.basemodel).toEqual('gpt-35-turbo');
  });

  it('should return free resource with priority to GPT-4 model', () => {
    LlmResources.push({
      id: 1,
      name: 'Resource1',
      provider: 'openai',
      basemodel: 'gpt-3',
      current: {
        requests_this_minute: 10,
        tokens_this_minute: 100,
      },
      limits: {
        requests_per_minute: 20,
        tokens_per_minute: 4000,
        tokens_per_request: 10000,
      },
      is_internal: true,
      url: 'https://www.llmresource1.com',
      api_key: 'key123',
      cost_per_1k_token_prompt: 0,
      cost_per_1k_token_completion: 0,
    });

    LlmResources.push({
      id: 2,
      name: 'Resource2',
      provider: 'openai',
      basemodel: 'gpt-4',
      current: {
        requests_this_minute: 10,
        tokens_this_minute: 100,
      },
      limits: {
        requests_per_minute: 20,
        tokens_per_minute: 4000,
        tokens_per_request: 10000,
      },
      is_internal: true,
      url: 'https://www.llmresource2.com',
      api_key: 'key456',
      cost_per_1k_token_prompt: 0,
      cost_per_1k_token_completion: 0,
    });

    const result = getRightSizeLLM(false, 20);
    expect(result).toBeDefined();
    expect(result?.resource.basemodel).toEqual('gpt-4');
  });

  it('should return free resource with default priority', () => {
    LlmResources.push({
      id: 1,
      name: 'Resource1',
      provider: 'openai',
      basemodel: 'gpt-3',
      current: {
        requests_this_minute: 10,
        tokens_this_minute: 100,
      },
      limits: {
        requests_per_minute: 20,
        tokens_per_minute: 4000,
        tokens_per_request: 10000,
      },
      is_internal: true,
      url: 'https://www.llmresource1.com',
      api_key: 'key123',
      cost_per_1k_token_prompt: 0,
      cost_per_1k_token_completion: 0,
    });

    LlmResources.push({
      id: 2,
      name: 'Resource2',
      provider: 'openai',
      basemodel: 'gpt-4',
      current: {
        requests_this_minute: 10,
        tokens_this_minute: 100,
      },
      limits: {
        requests_per_minute: 20,
        tokens_per_minute: 4000,
        tokens_per_request: 10000,
      },
      is_internal: true,
      url: 'https://www.llmresource2.com',
      api_key: 'key456',
      cost_per_1k_token_prompt: 0,
      cost_per_1k_token_completion: 0,
    });

    const result = getRightSizeLLM(false, 20);
    expect(result).toBeDefined();
    expect(result?.resource.basemodel).toEqual('gpt-4');
  });

  it('should return free internal resource when forceInternalOnly is true', () => {
    LlmResources.push({
      id: 1,
      name: 'Resource1',
      provider: 'openai',
      basemodel: 'gpt-3',
      current: {
        requests_this_minute: 10,
        tokens_this_minute: 100,
      },
      limits: {
        requests_per_minute: 20,
        tokens_per_minute: 4000,
        tokens_per_request: 10000,
      },
      is_internal: true,
      url: 'https://www.llmresource1.com',
      api_key: 'key123',
      cost_per_1k_token_prompt: 0,
      cost_per_1k_token_completion: 0,
    });

    LlmResources.push({
      id: 2,
      name: 'Resource2',
      provider: 'openai',
      basemodel: 'gpt-4',
      current: {
        requests_this_minute: 10,
        tokens_this_minute: 100,
      },
      limits: {
        requests_per_minute: 20,
        tokens_per_minute: 4000,
        tokens_per_request: 10000,
      },
      is_internal: false,
      url: 'https://www.llmresource2.com',
      api_key: 'key456',
      cost_per_1k_token_prompt: 0,
      cost_per_1k_token_completion: 0,
    });

    const result = getRightSizeLLM(true, 20);
    expect(result).toBeDefined();
    expect(result?.resource.is_internal).toBe(true);
  });
});
