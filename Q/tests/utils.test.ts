jest.mock('../src/utils/db');
import { mergeObjects } from '../src/utils/logging';

describe('mergeObjects', () => {
  test('should merge two simple objects', () => {
    const obj1 = { a: 1, b: 2 };
    const obj2 = { b: 3, c: 4 };
    const result = mergeObjects(obj1, obj2);
    expect(result).toEqual({ a: 1, b: [2, 3], c: 4 });
  });

  test('should merge nested objects', () => {
    const obj1 = { a: { b: 1 }, c: 3 };
    const obj2 = { a: { d: 2 }, e: 5 };
    const result = mergeObjects(obj1, obj2);
    expect(result).toEqual({ a: { b: 1, d: 2 }, c: 3, e: 5 });
  });

  test('should merge arrays', () => {
    const obj1 = { a: [1, 2] };
    const obj2 = { a: [3, 4] };
    const result = mergeObjects(obj1, obj2);
    expect(result).toEqual({ a: [1, 2, 3, 4] });
  });

  test('should handle null values', () => {
    const obj1 = null;
    const obj2 = { a: 1 };
    const result = mergeObjects(obj1, obj2);
    expect(result).toEqual({ a: 1 });
  });

  test('should handle empty strings', () => {
    const obj1 = '';
    const obj2 = { a: 1 };
    const result = mergeObjects(obj1, obj2);
    expect(result).toEqual({ a: 1 });
  });

  test('should handle JSON strings', () => {
    const obj1 = '{"a": 1}';
    const obj2 = '{"b": 2}';
    const result = mergeObjects(obj1, obj2);
    expect(result).toEqual({ a: 1, b: 2 });
  });
});
