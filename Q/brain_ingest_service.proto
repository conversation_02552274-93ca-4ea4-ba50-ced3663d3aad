syntax = "proto3";

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

package brain;

// Where to find an original document.
message DocumentSourceInfo {
  // The source of the document.
  DocumentSource source = 1;

  // The unique identifier for the document in the source.
  string source_id = 2;

  // Unique locator for the document in the source, e.g. a URL for a web page,
  // Google doc or other web accessible resource.
  string source_location = 3;
}

// Where documents come from.
enum DocumentSource {
  UNKNOWN = 0;       // Default value, should not be used.
  GOOGLE_DRIVE = 1;  // Document sourced from Google Drive.
  LOCAL_FILE = 2;    // Document sourced from a local file.
  ZENDESK = 3;       // Document sourced from Zendesk.
  HIGHSPOT = 4;      // Document sourced from Highspot.
  SHAREPOINT = 5;    // Sharepoint
  SALESFORCE = 6;    // Salesforce
  BOX = 7;           // Document sourced from Box.
}

// Accepted MIME types.
enum MimeType {
  MIME_TYPE_UNKNOWN = 0;  // Default value, should not be used.
  MIME_TYPE_CSV = 1;      // CSV file type.
  MIME_TYPE_PDF = 2;      // PDF file type.
  MIME_TYPE_PPTX = 3;     // PPTX file type.
  MIME_TYPE_DOCX = 4;     // DOCX file type.
  MIME_TYPE_XLSX = 5;     // XLSX file type.
}

// Indicates asset privacy status.
enum PrivacyStatus {
  PRIVACY_STATUS_UNKNOWN = 0;
  PRIVACY_STATUS_PUBLIC = 1;
  PRIVACY_STATUS_PRIVATE = 2;
}

message MetadataFilterValue {
  int64 id = 1;
  int64 type_id = 2;
  string value = 3;
}

// AddDocumentRequest is used to add a document.
message AddDocumentRequest {
  // The schema identifies which customer owns this document.
  string schema = 1;

  // The unique identifier for the client who owns this document.
  int64 client_id = 2;

  // The MIME type of the document.
  MimeType mime_type = 3;

  // Original filename of the document.
  string filename = 4;

  // Title of the document.
  string title = 5;

  // ID of the Brain Document record this ingest document belongs to.
  int64 document_id = 6;

  // ID for the user who provided this document.
  int64 user_id = 7;

  // An optional job ID if the document is part of a batch process.
  int64 job_id = 8;

  // Whether this is a known update to an existing document.
  bool is_update = 9;

  // The timestamp when the document was last modified.
  google.protobuf.Timestamp last_modified_time = 10;

  // The timestamp of when this document should expire from the brain.
  google.protobuf.Timestamp expiry_date = 11;

  // The source information of the document.
  DocumentSourceInfo source_info = 12;

  // One of blob_name or raw_bytes must be provided.
  oneof file {
    // The path to the file within shared container.
    string blob_name = 13;

    // The raw bytes of the document if it's small enough to be sent directly.
    bytes raw_bytes = 14;
  }

  // Indicates whether this doc is public or private.
  PrivacyStatus privacy = 15;

  // Whether to automatically tag metadata based on content.
  bool auto_tag_metadata = 16;
  // The tags to always apply to the metadata.
  repeated MetadataFilterValue always_tag_metadata = 17;
  // Never tag metadata with these tags.
  repeated MetadataFilterValue never_tag_metadata = 18;

  // The timestamp when the document was originally created in the source system.
  google.protobuf.Timestamp source_created_date = 19;

  // The timestamp when the document was originally modified in the source system.
  google.protobuf.Timestamp source_modified_date = 20;
}

// AddQuestionnaireRequest is used to add a CSV containing previous RFP or
// Security Questionnaire questions.
message AddQuestionnaireRequest {
  // The schema identifies which customer owns this document.
  string schema = 1;

  // The unique identifier for the client who owns this document.
  int64 client_id = 2;

  // The MIME type of the document.
  MimeType mime_type = 3;

  // Original filename of the document.
  string filename = 4;

  // Title of the document.
  string title = 5;

  // ID of the Brain Document record this ingest document belongs to.
  int64 document_id = 6;

  // ID for the user who provided this document.
  int64 user_id = 7;

  // An optional job ID if the document is part of a batch process.
  int64 job_id = 8;

  // Whether this is a known update to an existing document.
  bool is_update = 9;

  // The timestamp when the document was last modified.
  google.protobuf.Timestamp last_modified_time = 10;

  // The timestamp of when this document should expire from the brain.
  google.protobuf.Timestamp expiry_date = 11;

  // The source information of the document.
  DocumentSourceInfo source_info = 12;

  // One of blob_name or raw_bytes must be provided.
  oneof file {
    // The path to the file within shared container.
    string blob_name = 13;

    // The raw bytes of the document if it's small enough to be sent directly.
    bytes raw_bytes = 14;
  }

  // Indicates whether this doc is public or private.
  PrivacyStatus privacy = 15;

  // Whether to automatically tag metadata based on content.
  bool auto_tag_metadata = 16;
  // The tags to always apply to the metadata.
  repeated MetadataFilterValue always_tag_metadata = 17;
  // Never tag metadata with these tags.
  repeated MetadataFilterValue never_tag_metadata = 18;

  // The index of the column containing the questions.
  int64 question_column_index = 19;
  // The index of the column containing the answers.
  int64 answer_column_index = 20;
  // Whether the first row is a header.
  bool first_row_is_header = 21;
  // The index of the column containing metadata.
  int64 metadata_column_index = 22;

  // UI v2?
  // The index of the row containing the header.
  int64 header_row_index = 23;
  // The indices of the columns containing the questions.
  repeated int64 question_columns_indices = 24;
  // The indices of the columns containing the answers.
  repeated int64 answer_columns_indices = 25;

  // The timestamp when the document was originally created in the source system.
  google.protobuf.Timestamp source_created_date = 26;

  // The timestamp when the document was originally modified in the source system.
  google.protobuf.Timestamp source_modified_date = 27;

  // The indices of the columns containing the insights.
  repeated int64 insight_columns_indices = 28;  
}

service BrainIngest {
  rpc AddDocument(AddDocumentRequest) returns (google.protobuf.Empty);
  rpc AddQuestionnaire(AddQuestionnaireRequest) returns (google.protobuf.Empty);
}
