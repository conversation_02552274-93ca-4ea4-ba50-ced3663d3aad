# Q

Q is the traffic cop for LLM calls. It provides a GRPC interface for LLM requests and an Azure
service bus/event grid interface for triggering Azure Functions.

Your .env needs the following:

```
ENV_PREFIX=test
PORT=

#For azure credential
AZURE_CLIENT_ID=
AZURE_TENANT_ID=
AZURE_CLIENT_SECRET=
AZURE_KEY_VAULT_NAME=kv-tribble-test

APPLICATION_INSIGHTS_DISABLE=true
# If you want application_insights turned on, have these guys:
APPLICATIONINSIGHTS_CONNECTION_STRING=
APPLICATION_INSIGHTS_URL=

# These topic connection strings can be found in LLMQUEUE_test --> topics --> [topic name] --> shared access policies --> send_listen
# or you can use ds9/scripts/setupServiceBusEventGrid.sh
TOPIC_ANSWERED_CONNECTION_STRING=
TOPIC_NEW_REQUEST_CONNECTION_STRING=
TOPIC_CNC_CONNECTION_STRING=

ENV_PREFIX=test # test | staging | blank for prod
AZURE_KEY_VAULT_NAME=kv-tribble-test

#For API keys.
# Key can be generated using: crypto.randomBytes(32).toString('base64')
# IV can be generated using: crypto.randomBytes(12).toString('base64')
# But if you're working with the test db, you need the same values that are found
# in the tribble-test-q
ENCRYPTION_KEY=
ENCRYPTION_IV=

ENVIRONMENT='development' # Ensures function calls to localhost

# For the grpc server
CHAT_COMPLETION_SERVER_ADDRESS=localhost:50051
```

Note: legacy dequeue logic and some diagrams can be found in [Google Drive](https://docs.google.com/document/d/1pdcAn8DuP3BXG9lN5P8CELAXw-THFfT4RjB568NIof0/edit?usp=sharing)

## LLMs

We have access to a certain number of LLMs. Our LLMs vary in rate-limit, request size, and cost. When Q inits it fetches our LLM resources from the db `tribble.llm_resource` and populates a global array which is used to keep track of running limits and availability.

## How we handle requests:

When a request comes in Q grabs a free LLM. (If the request is small enough Q prioritizes the smaller (cheaper) LLMs.) If an LLM is available, Q processes the request and passes back the result. Easy peasy.
If all the LLMs are busy the request goes into a **queue**. Whenever a request is finished being serviced (and hence we have a free LLM) we look in the **queue** to see if anything's waiting. (we also have a deferred queue - get to that in a moment)
​

## Generating gRPC service files

Q provides a [gRPC](https://grpc.io) service interface. To use it, either as a
client or a server, you need to generate files for your language from this
project's `service.proto`.

To generate files for a Javascript/Typescript project, you need to install the
proto compiler and gRPC tools:

    npm install -g protoc-gen-grpc grpc-tools

The [gRPC tools](https://www.npmjs.com/package/grpc-tools) package contains the
proto dependencies and you may need to add the locations of the included protos
to the command below with an additional `--proto_path ~/my/google/protos/here`.
For us, it's installed in ds9/node_modules so we include this line:
`--proto_path ../node_modules/grpc-tools/bin \`

To generate the required files and place them in their packages you can run 

`./proto_build.sh`

This will run both brain_ingest and chat_completion services.
