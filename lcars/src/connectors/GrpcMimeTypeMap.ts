import { MIME_TYPE } from '@tribble/types-shared';
import { MimeType as GrpcMimeType } from 'brain-ingest-service';

export const GrpcMimeTypeMap: { [key: string]: GrpcMimeType } = {
  [MIME_TYPE.DOCX]: GrpcMimeType.MIME_TYPE_DOCX,
  [MIME_TYPE.CSV]: GrpcMimeType.MIME_TYPE_CSV,
  [MIME_TYPE.PDF]: GrpcMimeType.MIME_TYPE_PDF,
  [MIME_TYPE.XLSX]: GrpcMimeType.MIME_TYPE_XLSX,
  [MIME_TYPE.EXCEL]: GrpcMimeType.MIME_TYPE_XLSX,
  [MIME_TYPE.PPTX]: GrpcMimeType.MIME_TYPE_PPTX,
};
