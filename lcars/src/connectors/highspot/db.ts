import {
  HighspotAsset,
  NewHighspotAsset,
} from '@tribble/tribble-db/clients/HighspotAsset';
import { constants } from '../../constants';

const { query, getClient } = require('../../db/index');

// export type HighspotAsset = Asset & {
//   item_id: number;
//   spot_id: string;
//   content_name: string;
//   mime_type: string;
//   last_modified: Date;
// };

export const getAssets = async (
  schema: string,
  assetType?: string,
): Promise<HighspotAsset[]> => {
  let queryString;
  if (!assetType) {
    queryString = `
      SELECT ha.* 
      FROM ${schema}.${constants.TABLE_HIGHSPOT_ASSET} ha
      JOIN ${schema}.${constants.TABLE_DOC} d ON d.id = ha.document_id
      WHERE d.deleted_date IS NULL AND ha.type != 'spot'

      UNION

      SELECT * 
      FROM ${schema}.${constants.TABLE_HIGHSPOT_ASSET}
      WHERE type = 'spot'
    ;`;
  } else if (assetType === 'spot') {
    queryString = `
      SELECT * 
      FROM ${schema}.${constants.TABLE_HIGHSPOT_ASSET}
      WHERE type = 'spot'
    `;
  }

  const result = await query(queryString);
  if (result && result.rows) {
    return result.rows as HighspotAsset[];
  }
  return [];
};

export const getWatchedAssets = async (
  schema: string,
): Promise<HighspotAsset[]> => {
  const queryString = `SELECT * FROM ${schema}.${constants.TABLE_HIGHSPOT_ASSET}
  WHERE watch_for_changes = true;`;

  const result = await query(queryString);
  if (result && result.rows) {
    return result.rows as HighspotAsset[];
  }
  return [];
};

export const watchAsset = async (schema: string, itemId: string) => {
  const queryString = `UPDATE ${schema}.${constants.TABLE_HIGHSPOT_ASSET} SET watch_for_changes = true WHERE item_id = $1;`;

  await query(queryString, [itemId]);
};

export const unwatchAsset = async (schema: string, itemId: string) => {
  const queryString = `UPDATE ${schema}.${constants.TABLE_HIGHSPOT_ASSET} SET watch_for_changes = false WHERE item_id = $1;`;

  await query(queryString, [itemId]);
};

export const unwatchAssets = async (schema: string, itemIds: string[]) => {
  const queryString = `UPDATE ${schema}.${constants.TABLE_HIGHSPOT_ASSET} SET watch_for_changes = false WHERE item_id = ANY($1::text[]);`;

  await query(queryString, [itemIds]);
};

export const deleteAssetByDocumentIds = async (
  schema: string,
  documentIds: number[],
) => {
  const placeholders = documentIds
    .map((_, index) => `$${index + 1}`)
    .join(', ');
  const queryString = `DELETE FROM ${schema}.${constants.TABLE_HIGHSPOT_ASSET} WHERE document_id IN (${placeholders});`;

  await query(queryString, documentIds);
};

export const upsertHighspotAssets = async (
  schema: string,
  assets: NewHighspotAsset[],
) => {
  if (!assets || assets.length === 0) {
    console.warn(
      '[ upsertHighspotAssets ] No assets. Did something else fail?',
    );
    return [];
  }
  try {
    const valueCount = 19, // Number of columns in the Highspot asset table (now includes source dates)
      values = assets
        .map(
          (_, index) => `(
       $${index * valueCount + 1}, $${index * valueCount + 2},
       $${index * valueCount + 3}, $${index * valueCount + 4}, 
       $${index * valueCount + 5}, $${index * valueCount + 6}, 
       $${index * valueCount + 7}, $${index * valueCount + 8},
       $${index * valueCount + 9}, $${index * valueCount + 10},
       $${index * valueCount + 11}, $${index * valueCount + 12},
       $${index * valueCount + 13}, $${index * valueCount + 14},
       $${index * valueCount + 15}, $${index * valueCount + 16},
       $${index * valueCount + 17}, $${index * valueCount + 18},
       $${index * valueCount + 19}
      )`,
        )
        .join(', ');

    const queryString = `
      INSERT INTO ${schema}.${constants.TABLE_HIGHSPOT_ASSET}
      (item_id, spot_id, name, content_name, mime_type, last_modified, blob_id, document_id, type, job_id, user_id, watch_for_changes, status, last_checked, created_at, updated_at, task_id, source_created_date, source_modified_date)
      VALUES ${values}
      ON CONFLICT (item_id)
      DO UPDATE SET content_name = EXCLUDED.content_name, 
      mime_type = EXCLUDED.mime_type, 
      last_modified = EXCLUDED.last_modified,
      last_checked = NOW(),
      updated_at = NOW(),
      document_id = EXCLUDED.document_id,
      job_id = EXCLUDED.job_id,
      watch_for_changes = EXCLUDED.watch_for_changes,
      source_created_date = EXCLUDED.source_created_date,
      source_modified_date = EXCLUDED.source_modified_date
      RETURNING *;
    `;

    const queryParams = [];
    assets.forEach((asset) => {
      queryParams.push(
        asset.item_id,
        asset.spot_id,
        asset.name,
        asset.content_name,
        asset.mime_type,
        asset.last_modified,
        asset.blob_id,
        asset.document_id,
        asset.type,
        asset.job_id,
        asset.user_id,
        asset.watch_for_changes,
        asset.status,
        asset.last_checked,
        asset.created_at,
        asset.updated_at,
        asset.task_id,
        asset.source_created_date,
        asset.source_modified_date,
      );
    });

    const result = await query(queryString, queryParams);
    if (result && result.rows) {
      return result.rows as HighspotAsset[];
    }
    return [];
  } catch (e) {
    console.error('[upsertHighspotAsset]', e);
    return [];
  }
};

export const getAssetsByItemIds = async (schema: string, itemIds: string[]) => {
  const queryString = `
    SELECT * 
    FROM ${schema}.${constants.TABLE_HIGHSPOT_ASSET} 
    WHERE item_id = ANY($1::text[])
  `;

  const result = await query(queryString, [itemIds]);
  if (result && result.rows && result.rows.length > 0) {
    return result.rows as HighspotAsset[];
  }
  return [];
};

export const unwatchFilesByUserId = async (schema: string, userId: number) => {
  const queryString = `UPDATE ${schema}.${constants.TABLE_HIGHSPOT_ASSET} 
  SET watch_for_changes = false
  WHERE user_id = $1;`;

  try {
    await query(queryString, [userId]);
  } catch (error) {
    console.error(
      '[highspot/unwatchFilesByUserId] Error unwatching files:',
      error,
    );
  }
};

// Since Highspot is now a client-level integration, disconnecting
// the integration should unwatch ALL assets for that client.
export const unwatchAllAssetsByClient = async (schema: string) => {
  const queryString = `
    UPDATE ${schema}.${constants.TABLE_HIGHSPOT_ASSET} 
    SET watch_for_changes = false
  `;

  try {
    await query(queryString, []);
  } catch (error) {
    console.error(
      '[highspot/unwatchFilesByClient] Error unwatching files:',
      error,
    );
  }
};
