import { ClientUtils } from '@tribble/common-utils';
import express, { NextFunction, Request, Response } from 'express';
import { getSpotItems, getSpots, setupIngestItems, watchSpot } from '.';
import { constants } from '../../constants';
import {
  deleteClientIntegration,
  getIntegrationSystems,
} from '../../db/integrations';
import { getClientById } from '../../db/queries';
import { UserRequest } from '../../utils/auth';
import { getHighspotAuth, saveHighspotAuth } from './auth';
import {
  getAssets,
  getWatchedAssets,
  unwatchAllAssetsByClient,
  unwatchFilesByUserId,
} from './db';

const { clientAdminCheck, m2mCheck } = require('../../utils/auth');
const router = express.Router();

interface HighspotRequest extends UserRequest {
  basicAuth: string;
}

const authCheck = async (
  req: Request & HighspotRequest,
  res: Response,
  next: NextFunction,
) => {
  const { schema, userDetail } = req.user;

  const userId = userDetail.id;

  const { auth } = await getHighspotAuth(schema, userId);

  if (!auth) {
    return res
      .status(401)
      .json({ success: false, error: 'Highspot authentication not found' });
  }

  req.basicAuth = auth;

  next();
};

router.get(
  '/connectors/highspot/auth',
  clientAdminCheck,
  async (req: Request & HighspotRequest, res: Response) => {
    try {
      const { schema, userDetail } = req.user;

      const { auth, createdByName } = await getHighspotAuth(
        schema,
        userDetail.id,
      );

      res.status(200).json({ success: true, hasAuth: !!auth, createdByName });
    } catch (e) {
      console.error(`[/connectors/highspot/auth] ${e}`);
      res.status(500).json({ success: false, error: 'Internal Server Error' });
    }
  },
);

router.post(
  '/connectors/highspot/auth',
  clientAdminCheck,
  async (req: Request & HighspotRequest, res: Response) => {
    try {
      const { schema, userDetail } = req.user;
      const { username, password } = req.body;

      const result = await saveHighspotAuth(schema, userDetail.id, {
        username,
        password,
      });

      if (!result) {
        return res
          .status(400)
          .json({ success: false, error: 'Failed to save Highspot auth' });
      }

      return res.status(200).json({ success: true, assets: [] });
    } catch (e) {
      console.error(`[/connectors/highspot/auth] ${e}`);
      res.status(500).json({ success: false, error: 'Internal Server Error' });
    }
  },
);

router.post(
  '/connectors/highspot/disconnect',
  clientAdminCheck,
  authCheck,
  async (req: Request & HighspotRequest, res: Response) => {
    try {
      const { schema, userDetail } = req.user;

      const integrations = await getIntegrationSystems(
        schema,
        userDetail.id,
        false,
      );
      const highspot = integrations.find(
        (integration) => integration.system_name === 'highspot',
      );

      if (!highspot) {
        return res
          .status(404)
          .json({ success: false, error: 'Highspot integration not found' });
      }

      // Highspot is now a client-level integration, so we need to unwatch
      // all assets for the client.
      console.log(
        `[/connectors/highspot/disconnect] Unwatching all assets for client: ${schema}`,
      );
      await unwatchAllAssetsByClient(schema);
      await deleteClientIntegration(schema, highspot.id);

      return res.status(200).json({ success: true });
    } catch (e) {
      console.error(`[/connectors/highspot/disconnect] ${e}`);
      res.status(500).json({ success: false, error: 'Internal Server Error' });
    }
  },
);

router.get(
  '/connectors/highspot/spot/:spotId/items',
  clientAdminCheck,
  authCheck,
  async (req: Request & HighspotRequest, res: Response) => {
    try {
      const { schema } = req.user;
      const { spotId } = req.params;
      const canDownload = req.query.can_download === 'true';

      const auth = req.basicAuth;
      const clientHighspotApiBaseUrl = await ClientUtils.getClientSetting(
        schema,
        constants.SETTING_CLIENT_HIGHSPOT_API_BASE_URL,
      );
      let items = await getSpotItems(spotId, auth, clientHighspotApiBaseUrl);
      if (canDownload) {
        items = items.filter((item) => item.can_download);
      }

      return res.status(200).json({ success: true, items });
    } catch (e) {
      console.error(`[/connectors/highspot/items] ${e}`);
      res.status(500).json({ success: false, error: 'Internal Server Error' });
    }
  },
);

router.get(
  '/connectors/highspot/spots',
  clientAdminCheck,
  authCheck,
  async (req: Request & HighspotRequest, res: Response) => {
    try {
      const { schema } = req.user;
      const auth = req.basicAuth;
      const clientHighspotApiBaseUrl = await ClientUtils.getClientSetting(
        schema,
        constants.SETTING_CLIENT_HIGHSPOT_API_BASE_URL,
      );
      const spots = await getSpots(auth, clientHighspotApiBaseUrl);

      return res.status(200).json({ success: true, spots });
    } catch (e) {
      console.error(`[/connectors/highspot/spots] ${e}`);
      res.status(500).json({ success: false, error: 'Internal Server Error' });
    }
  },
);

router.get(
  '/connectors/highspot/assets',
  clientAdminCheck,
  async (req: Request & HighspotRequest, res: Response) => {
    try {
      const { schema } = req.user;
      const { asset_type } = req.query;

      const assets = await getAssets(schema, asset_type as string);

      return res.status(200).json({ success: true, assets });
    } catch (e) {
      console.error(`[/connectors/highspot/assets] ${e}`);
      res.status(500).json({ success: false, error: 'Internal Server Error' });
    }
  },
);

router.post(
  '/connectors/highspot/assets',
  clientAdminCheck,
  authCheck,
  async (req: Request & HighspotRequest, res: Response) => {
    try {
      const { schema, userDetail } = req.user;
      const { items, watchForChanges, spotId, once } = req.body;
      const auth = req.basicAuth;

      if (!!spotId) {
        const numItems = await watchSpot(
          spotId,
          auth,
          watchForChanges,
          schema,
          userDetail.id,
        );
        return res.status(201).json({ success: true, count: numItems });
      }

      if (items && items.length > 0) {
        await setupIngestItems(
          schema,
          userDetail.id,
          auth,
          items,
          watchForChanges,
          once,
        );
      }

      return res.status(201).json({ success: true });
    } catch (e) {
      console.error(`[/connectors/highspot/assets] ${e}`);
      res.status(500).json({ success: false, error: 'Internal Server Error' });
    }
  },
);

router.post(
  '/clients/:clientId/connectors/highspot/sync',
  m2mCheck,
  async (req: Request & HighspotRequest, res: Response) => {
    try {
      const clientId = parseInt(req.params.clientId, 10);

      if (isNaN(clientId)) {
        return res.status(400).json({ error: 'Invalid client ID' });
      }
      const client = await getClientById(clientId);
      if (!client) {
        return res.status(404).json({ error: 'Client not found' });
      }

      const assets = await getWatchedAssets(client.database_schema_id);

      if (!assets || assets.length == 0) {
        console.log(
          `[ /clients/${clientId}/connectors/highspot/sync ] No file assets found for client: ${client.database_schema_id}`,
        );
        return res.status(204).json({ success: true });
      }

      const userIds = [...new Set(assets.map((asset) => asset.user_id))];
      for (const userId of userIds) {
        const usersAssets = assets.filter((asset) => asset.user_id === userId);

        const { auth } = await getHighspotAuth(
          client.database_schema_id,
          userId,
        );

        if (!auth) {
          console.warn(
            `[ /clients/:clientId/connectors/highspot/sync ] No auth found for user: ${userId}`,
          );

          await unwatchFilesByUserId(client.database_schema_id, userId);

          continue;
        }

        await setupIngestItems(
          client.database_schema_id,
          userId,
          auth,
          usersAssets.map((asset) => {
            return {
              id: asset.item_id,
              mime_type: asset.mime_type,
              can_download: true, // set so we can download the asset
            };
          }),
          true,
          false,
        );
      }

      return res.status(200).json({ success: true, assets });
    } catch (e) {
      console.error(`[/connectors/highspot/assets] ${e}`);
      res.status(500).json({ success: false, error: 'Internal Server Error' });
    }
  },
);

module.exports = router;
