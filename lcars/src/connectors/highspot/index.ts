import * as grpc from '@grpc/grpc-js';
import { ClientUtils } from '@tribble/common-utils';
import { generateEmbeddings } from '@tribble/comms-shared';
import { DocumentId } from '@tribble/tribble-db/clients/Document';
import {
  HighspotAsset,
  NewHighspotAsset,
} from '@tribble/tribble-db/clients/HighspotAsset';
import { JobId } from '@tribble/tribble-db/clients/Job';
import { UserId } from '@tribble/tribble-db/tribble/User';
import { MIME_TYPE } from '@tribble/types-shared';
import {
  AddDocumentRequest,
  DocumentSource,
  DocumentSourceInfo,
} from 'brain-ingest-service';
import * as timestamppb from 'google-protobuf/google/protobuf/timestamp_pb';
import mime from 'mime-types';
import { getDocumentDocType, getGrpcBrainIngestClient } from '..';
import { constants } from '../../constants';
import {
  deleteDocsAndEmbeddings,
  deleteDocumentMetadataEmbedding,
  getDocsByIds,
  upsertDocumentMetadataEmbedding,
} from '../../db/docs';
import {
  getClientIdByUserId,
  insertPartialDocRecord,
  insertPartialJobRecord,
  updateJobRecordStatusWithError,
} from '../../db/queries';
import { callAzureFunction } from '../../utils/azure';
import { GrpcMimeTypeMap } from '../GrpcMimeTypeMap';
import {
  deleteAssetByDocumentIds,
  getAssetsByItemIds,
  unwatchAsset,
  unwatchAssets,
  upsertHighspotAssets,
  watchAsset,
} from './db';

const DEFAULT_HIGHSPOT_API_URL = 'https://api.highspot.com/v1.0';
const VIEW_RIGHT = 'right=view';

type Spot = {
  title: string;
  description: string;
  id: string;
};

type HighspotItem = {
  author: string;
  can_download: boolean;
  content_name: string;
  content_type: string;
  date_added: string;
  date_original_added: string;
  date_updated: string;
  description: string;
  id: string;
  imported_at: string;
  internal: boolean;
  language: string;
  lists: string[];
  references: string[];
  mime_type: string;
  spot: string;
  title: string;
};

const getHighspotApiUrl = async (clientHighspotApiBaseUrl: string) => {
  let highspotApiUrl = clientHighspotApiBaseUrl || DEFAULT_HIGHSPOT_API_URL;
  if (highspotApiUrl.endsWith('/')) {
    highspotApiUrl = highspotApiUrl.slice(0, -1);
  }

  return highspotApiUrl;
};

const fetchWithBackoff = async (
  url: string,
  basicAuth: string,
  attempt = 0,
) => {
  const response = await fetch(url, {
    headers: {
      Authorization: `Basic ${basicAuth}`,
    },
  });

  if (response.status === 429) {
    const sleep = Math.pow(2, attempt) * 1000;

    console.warn(`Too many requests to Highspot, retrying in ${sleep}ms`);
    await new Promise((resolve) => setTimeout(resolve, sleep));

    if (attempt > 5) {
      console.warn('Too many requests to Highspot');
      return undefined;
    }
    return fetchWithBackoff(url, basicAuth, attempt + 1);
  }
  return response.json();
};

export const getSpots = async (
  basicAuth: string,
  clientHighspotApiBaseUrl?: string,
) => {
  const highspotApiUrl = await getHighspotApiUrl(clientHighspotApiBaseUrl);
  const url = `${highspotApiUrl}/spots?${VIEW_RIGHT}`;

  return (await fetchWithBackoff(url, basicAuth)) as Spot[];
};

const getSpot = async (
  spotId: string,
  basicAuth: string,
  clientHighspotApiBaseUrl?: string,
) => {
  const highspotApiUrl = await getHighspotApiUrl(clientHighspotApiBaseUrl);
  const url = `${highspotApiUrl}/spots/${spotId}`;

  return (await fetchWithBackoff(url, basicAuth)) as Spot;
};

export const getSpotItems = async (
  spotId: string,
  basicAuth: string,
  clientHighspotApiBaseUrl?: string,
) => {
  const highspotApiUrl = await getHighspotApiUrl(clientHighspotApiBaseUrl);
  const url = `${highspotApiUrl}/items?spot=${spotId}`;

  const response = await fetchWithBackoff(url, basicAuth);
  return response.collection as HighspotItem[];
};

const getItem = async (
  itemId: string,
  basicAuth: string,
  clientHighspotApiBaseUrl?: string,
) => {
  const highspotApiUrl = await getHighspotApiUrl(clientHighspotApiBaseUrl);
  const url = `${highspotApiUrl}/items/${itemId}`;

  const response = await fetchWithBackoff(url, basicAuth);
  return response as HighspotItem;
};

const SPOT_MIME_TYPE = 'application/vnd.highspot.spot';

export const watchSpot = async (
  spotId: string,
  auth: string,
  watchForChanges: boolean,
  schema: string,
  userId: number,
) => {
  const clientHighspotApiBaseUrl = await ClientUtils.getClientSetting(
    schema,
    constants.SETTING_CLIENT_HIGHSPOT_API_BASE_URL,
  );

  let spotItems = await getSpotItems(spotId, auth, clientHighspotApiBaseUrl);
  const spot = await getSpot(spotId, auth, clientHighspotApiBaseUrl);

  await upsertHighspotAssets(schema, [
    {
      item_id: spotId,
      spot_id: spot.id,
      name: spot.title,
      mime_type: SPOT_MIME_TYPE,
      type: 'spot',
      user_id: userId as UserId,
      watch_for_changes: watchForChanges,
      status: 'new',
      created_at: new Date(),
      updated_at: new Date(),
      last_checked: new Date(),
    },
  ]);

  if (watchForChanges) {
    spotItems = spotItems.filter(
      (s) =>
        !!s.can_download &&
        (s.mime_type === MIME_TYPE.DOCX ||
          s.mime_type === MIME_TYPE.PDF ||
          s.mime_type === MIME_TYPE.PPTX),
    );

    // For a small number of items, can do async/await.
    // However, if > 20 it could take >1 min to get this setup.
    if (spotItems.length < 5) {
      await setupIngestAssets(
        spotItems,
        schema,
        auth,
        userId,
        watchForChanges,
        false,
      );
    } else {
      setupIngestAssets(
        spotItems,
        schema,
        auth,
        userId,
        watchForChanges,
        false,
      );
    }

    return spotItems.length;
  } else {
    // If unwatching, just set the flag in the asset table
    await unwatchAssets(
      schema,
      spotItems.map((s) => s.id),
    );

    return spotItems.length;
  }
};

const canDownloadItem = (item: Partial<HighspotItem>) => {
  const isSpot = item.mime_type === SPOT_MIME_TYPE;
  const isDownloadableAttachment =
    !!item.can_download &&
    (item.mime_type === MIME_TYPE.DOCX ||
      item.mime_type === MIME_TYPE.PDF ||
      item.mime_type === MIME_TYPE.PPTX);

  const isPageDesign = item.content_type === 'PageDesign';

  return !isSpot && (isDownloadableAttachment || isPageDesign);
};

/**
 *
 * @param schema
 * @param userId
 * @param auth
 * @param items Needs id and spot
 * @param watchForChanges
 * @param batchSize
 */
export const setupIngestItems = async (
  schema: string,
  userId: number,
  auth: string,
  items: Partial<HighspotItem>[],
  watchForChanges: boolean,
  once: boolean,
  batchSize: number = 10,
) => {
  try {
    let fileMap: { [key: string]: Partial<HighspotItem> } = {};
    for (const item of items) {
      fileMap[item.id] = item;
    }

    const spots = items.filter((a) => a.mime_type === SPOT_MIME_TYPE);
    const clientHighspotApiBaseUrl = await ClientUtils.getClientSetting(
      schema,
      constants.SETTING_CLIENT_HIGHSPOT_API_BASE_URL,
    );

    console.log(
      `(${schema}): [ Highspot SetupIngestItems ] ${spots.length} spots`,
    );

    for (const spot of spots) {
      const spotItems = await getSpotItems(
        spot.id,
        auth,
        clientHighspotApiBaseUrl,
      );
      for (const item of spotItems) {
        fileMap[item.id] = item;
      }
    }

    const allItems = Object.values(fileMap);
    const allDownloadableItems = allItems.filter((f) => canDownloadItem(f));

    console.log(
      `(${schema}): [ Highspot setupIngestFiles ] Ingesting ${allDownloadableItems.length} files`,
    );

    for (let i = 0; i < allDownloadableItems.length; i += batchSize) {
      const batch = allDownloadableItems.slice(i, i + batchSize);

      for (let i = 0; i < batch.length; i++) {
        let partialItem = batch[i];
        if (!partialItem.date_updated || !partialItem.date_original_added) {
          const fullItem = await getItem(
            partialItem.id,
            auth,
            clientHighspotApiBaseUrl,
          );
          batch[i] = fullItem;
        }
      }

      setupIngestAssets(
        batch as HighspotItem[],
        schema,
        auth,
        userId,
        watchForChanges,
        once,
      );

      await new Promise((resolve) => setTimeout(resolve, 1000));
    }
  } catch (e) {
    console.error(`(${schema}): [ HighSpot setupIngestItems ] Error:`, e);
  }
};

const setupIngestAssets = async (
  items: HighspotItem[],
  schema: string,
  auth: string,
  userId: number,
  watchForChanges: boolean,
  once: boolean,
) => {
  const updated_at = new Date();

  const assets: NewHighspotAsset[] = [];
  const documentDocTypeId = await getDocumentDocType(schema);
  const clientId = await getClientIdByUserId(userId);
  const existingAssets = await getAssetsByItemIds(
    schema,
    items.map((i) => i.id),
  );
  const clientHighspotApiBaseUrl = await ClientUtils.getClientSetting(
    schema,
    constants.SETTING_CLIENT_HIGHSPOT_API_BASE_URL,
  );
  const highspotApiUrl = await getHighspotApiUrl(clientHighspotApiBaseUrl);

  console.log(
    `(${schema}): [ HighSpot setupIngestAssets ] Called for ${items.length} items. Found ${existingAssets.length} existing assets`,
  );

  for (const item of items) {
    if (!canDownloadItem(item)) {
      console.warn(
        `(${schema}): [ HighSpot setupIngestAssets ] Can't download ${item.title}`,
      );
      continue;
    }

    const existingAsset = existingAssets.find((e) => e.item_id === item.id);

    if (existingAsset && !watchForChanges) {
      console.log(
        `(${schema}): [ HighSpot setupIngestAssets ] Unwatching asset ${item.id}`,
      );

      await unwatchAsset(schema, existingAsset.item_id);

      continue;
    }

    // For things with a mime_type, these should be files so look at imported_at.
    // Else, look at date_updated.
    const itemLastUpdated =
      item.mime_type && item.imported_at ? item.imported_at : item.date_updated;

    if (
      !!existingAsset?.last_checked &&
      existingAsset.last_checked > new Date(itemLastUpdated)
    ) {
      console.log(
        `(${schema}): [ HighSpot setupIngestAssets ] Skipping asset ${item.id} because it was last checked at ${existingAsset.last_checked} and the file was last modified at ${itemLastUpdated}`,
      );

      // Make sure assets are watched (in case of watch->unwatch->watch switcheroo)
      if (watchForChanges) {
        await watchAsset(schema, existingAsset.item_id);
      }

      // Check if the item.description has changed.
      // If so, update the document_metadata_embedding (type=label_and_description) record
      const existingDoc = await getDocsByIds(schema, [
        Number(existingAsset.document_id),
      ]);
      if (existingDoc?.[0].description !== item.description) {
        if ((item.description ?? '').trim().length > 0) {
          const labelEmbedding = await generateEmbeddings(
            item.title + '\n\n' + item.description,
          );
          if (labelEmbedding && labelEmbedding.embedding) {
            console.log(
              `(${schema}): [ HighSpot setupIngestAssets ] Updating document_metadata_embedding for asset ${item.id}`,
            );

            await upsertDocumentMetadataEmbedding(
              schema,
              Number(existingAsset.document_id),
              'label_and_description',
              labelEmbedding.embedding,
            );
          }
        } else {
          console.log(
            `(${schema}): [ HighSpot setupIngestAssets ] Deleting label_and_description document_metadata_embedding for asset ${item.id}`,
          );
          await deleteDocumentMetadataEmbedding(
            schema,
            Number(existingAsset.document_id),
            'label_and_description',
          );
        }
      }

      continue;
    }

    if (watchForChanges || once) {
      const highspotContentUrl = `${highspotApiUrl}/items/${item.id}/content`;
      const highspotApiHeaders = {
        Authorization: `Basic ${auth}`,
      };

      // Call the file download upload endpoint.
      // Don't download on LCARS in case large files and it OOM.
      let blobId;
      try {
        // For other file attachments (DOCX, PDF, PPTX), download and upload to blob.
        const downloadUploadFunctionBody = {
          url: highspotContentUrl,
          method: 'GET',
          headers: highspotApiHeaders,
          schema,
        };

        if (item.content_type === 'PageDesign') {
          // For "Pages" / "Plays", the content is unstructured text.
          // Grab the text, format in Markdown, use Gotenberg to convert to PDF.
          // Then upload to blob.
          // Why PDF? So we can show the user something in the UI.
          downloadUploadFunctionBody['postDownloadAction'] =
            'highspot-play-page-convert';
          downloadUploadFunctionBody['postDownloadActionData'] = { item };

          // Overwrite as we're converting to a PDF
          item.mime_type = MIME_TYPE.PDF;

          console.log(
            `(${schema}): [ HighSpot setupIngestAssets ] Ingesting PageDesign ${item.id}`,
          );
        }

        const downloadUploadResponse = await callAzureFunction(
          'helper',
          downloadUploadFunctionBody,
          false,
          process.env.FILE_DOWNLOAD_UPLOAD_BLOB_ENDPOINT ??
            '/api/download-file-upload-to-blob',
        );

        const resp = await downloadUploadResponse.json();
        blobId = resp.blob_id;

        if (!blobId) {
          console.error(
            `(${schema}): [ HighSpot setupIngestAssets ] Error calling download/upload function for item ${item.id}: ${resp.error || 'Unknown error'}`,
          );
          continue;
        }
      } catch (err) {
        console.error(
          `(${schema}): [ HighSpot setupIngestAssets ] Error calling download/upload function for item ${item.id}: ${err.message}`,
        );
        continue;
      }

      const newDocJob = await insertPartialJobRecord(
        'ingest',
        new Date(),
        userId,
        schema,
        'new',
      );
      const job_id: JobId = newDocJob.id;

      let prior_version_document_id;
      if (existingAsset) {
        prior_version_document_id = existingAsset?.document_id;
      }
      const newDoc = await insertPartialDocRecord(
        newDocJob.id,
        item.content_name || item.title,
        item.title,
        blobId,
        'public',
        new Date(),
        userId,
        schema,
        documentDocTypeId.id,
        prior_version_document_id,
        'processing',
        {},
        // We don't have a proper external source url from API.
        // But we use this to render the doc in the Documents tab.
        'https://www.highspot.com',
        mime.extension(item.mime_type),
        item.description ?? '',
      );
      const document_id: DocumentId = newDoc.id;

      const asset = {
        item_id: item.id,
        spot_id: item.spot,
        name: item.title,
        content_name: item.content_name,
        mime_type: item.mime_type,
        last_modified: itemLastUpdated,
        source_created_date: item.date_original_added
          ? new Date(item.date_original_added)
          : null,
        source_modified_date: item.date_updated
          ? new Date(item.date_updated)
          : null,
        blob_id: blobId,
        document_id,
        type: 'document',
        job_id,
        user_id: userId as UserId,
        watch_for_changes: watchForChanges,
        status: 'new',
        created_at: new Date(),
        updated_at,
        last_checked: new Date(),
      };

      assets.push(asset);

      const newAsset = await upsertHighspotAssets(schema, [asset]);

      if (newAsset && newAsset.length > 0) {
        try {
          await sendIngestRequest(
            newAsset[0],
            schema,
            clientId,
            !!existingAssets.find((a) => a.item_id === newAsset[0].item_id),
            highspotApiUrl,
          );

          // Short pause to help w bursty requests
          await new Promise((resolve) => setTimeout(resolve, 250));
        } catch (err) {
          console.error(
            `(${schema}): [ HighSpot setupIngestAssets ] Error sending ingest request for ${newAsset[0].item_id} (job id=${newDocJob.id}): ${err.message}`,
          );

          // Need to undo the above partial job/upsertHighspotAssets
          if (existingAsset) {
            await upsertHighspotAssets(schema, [existingAsset]);
          } else {
            // Delete the highspot asset record -- it'll get picked up next time around (if watched)
            await deleteAssetByDocumentIds(schema, [newDoc.id]);
          }

          await deleteDocsAndEmbeddings(schema, [newDoc.id], userId);
          await updateJobRecordStatusWithError(
            newDocJob.id,
            constants.JOB_STATUS_ERROR,
            `Error sending ingest request: ${err.message}`,
            schema,
          );
        }
      } else {
        await deleteDocsAndEmbeddings(schema, [newDoc.id], userId);
        await updateJobRecordStatusWithError(
          newDocJob.id,
          constants.JOB_STATUS_ERROR,
          `Unknown error during upsert of Highspot asset record`,
          schema,
        );
      }
    }
  }

  console.log(
    `(${schema}): [ HighSpot ingestAssets ] watchForChanges (${watchForChanges}): ingesting ${assets.length} assets`,
  );
};

const sendIngestRequest = async (
  asset: HighspotAsset,
  schema: string,
  clientId: number,
  isUpdate: boolean,
  highspotApiUrl: string,
) => {
  const ingestClient = getGrpcBrainIngestClient();

  const req = new AddDocumentRequest();
  req.setClientId(clientId);
  req.setSchema(schema);
  req.setUserId(asset.user_id);
  req.setDocumentId(Number(asset.document_id));
  req.setJobId(asset.job_id);
  req.setMimeType(GrpcMimeTypeMap[asset.mime_type]);
  req.setFilename(asset.name);
  req.setBlobName(asset.blob_id);
  req.setIsUpdate(isUpdate);

  if (asset.last_modified) {
    const lastModified = new timestamppb.Timestamp();
    lastModified.fromDate(asset.last_modified);
    req.setLastModifiedTime(lastModified);
  }

  // Set source dates for temporal filtering
  if (asset.source_created_date) {
    const sourceCreated = new timestamppb.Timestamp();
    sourceCreated.fromDate(new Date(asset.source_created_date));
    req.setSourceCreatedDate(sourceCreated);
  }

  if (asset.source_modified_date) {
    const sourceModified = new timestamppb.Timestamp();
    sourceModified.fromDate(new Date(asset.source_modified_date));
    req.setSourceModifiedDate(sourceModified);
  }

  const sourceInfo = new DocumentSourceInfo();
  sourceInfo.setSource(DocumentSource.HIGHSPOT);
  sourceInfo.setSourceId(asset.item_id);
  sourceInfo.setSourceLocation(
    `${highspotApiUrl}/items/${asset.item_id}/content`,
  );
  req.setSourceInfo(sourceInfo);

  const deadline = new Date(),
    timeoutSecs = 5;
  deadline.setSeconds(deadline.getSeconds() + timeoutSecs);

  // Send brain ingest request using Promise
  return new Promise<void>((resolve, reject) => {
    ingestClient.addDocument(
      req,
      new grpc.Metadata(),
      { deadline: deadline.getTime() },
      (err: Error, _) => {
        if (err) {
          console.error(
            `(${schema}): [ Highspot sendIngestRequest ] Error calling [addDocument] for (${asset.item_id}): ${err.message}`,
          );
          reject(
            new Error(
              `Failed to ingest document ${asset.item_id}: ${err.message}`,
            ),
          );
        } else {
          console.log(
            `(${schema}): [ Highspot sendIngestRequest ] Successfully called ingest document for (${asset.item_id})`,
          );
          resolve();
        }
      },
    );
  });
};
