import { decryptText, encryptText } from '@tribble/auth-helper';
import {
  getClientIntegration,
  getIntegrationSystems,
  saveClientIntegration,
} from '../../db/integrations';

type HighspotAuth = {
  username: string;
  password: string;
};

export const getHighspotAuth = async (
  schema: string,
  userId: number,
): Promise<{ auth: string; createdByName: string }> => {
  // Moved away from per-user auth for Highspot to client-level.
  const integrationSystems = await getIntegrationSystems(schema, userId, false);
  const highspot = integrationSystems.find(
    (integration) => integration.system_name === 'highspot',
  );
  if (!highspot) {
    return { auth: undefined, createdByName: undefined };
  }
  const clientIntegration = await getClientIntegration(schema, highspot.id);
  if (!clientIntegration) {
    return { auth: undefined, createdByName: undefined };
  }

  const authString = await decryptText(clientIntegration.auth_details);
  const auth = JSON.parse(authString) as HighspotAuth;

  const basicAuth = Buffer.from(`${auth.username}:${auth.password}`).toString(
    'base64',
  );

  return { auth: basicAuth, createdByName: clientIntegration.created_by_name };
};

export const saveHighspotAuth = async (
  schema: string,
  userId: number,
  auth: HighspotAuth,
) => {
  const integrationSystems = await getIntegrationSystems(schema, userId, false);
  const highspot = integrationSystems.find(
    (integration) => integration.system_name === 'highspot',
  );
  if (!highspot) {
    return undefined;
  }

  const authString = JSON.stringify(auth);
  const encryptedAuth = await encryptText(authString);

  await saveClientIntegration(schema, userId, highspot.id, encryptedAuth);

  return true;
};
