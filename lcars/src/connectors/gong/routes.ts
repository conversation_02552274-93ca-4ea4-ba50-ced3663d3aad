import { getGongAppDetails, getGongAuth } from '@tribble/gong-service';
import { GongCalls } from '@tribble/tribble-db/clients/GongCalls';
import { getDB } from '@tribble/tribble-db/db_ops';
import { UserId } from '@tribble/tribble-db/tribble/User';
import express from 'express';
import { GongCallsApiResponse } from '.';
import { getClientById } from '../../db/clients';
import {
  deleteClientIntegration,
  getClientIntegration,
  getIntegrationBySystemName,
  saveClientIntegration,
} from '../../db/integrations';
import { getClientSettingByName } from '../../db/users';
import {
  encryptText,
  generateBasicAuth,
  RequestWithUser,
} from '../../utils/auth';
import { prepAndInitateIngestIntegration } from '../../utils/azure';
import { snakeCaseKeys } from '../../utils/case';
import { telemetryClient } from '../../utils/telemetry';
import { gongAuth, RequestWithGong } from './auth';
import {
  clearGongCallData,
  getGongCallsByIds,
  updateLatestGongIngestJobLogData,
} from './db';

const { clientAdminCheck, m2mCheck } = require('../../utils/auth');

const router = express.Router();

const GONG_APP_URL = 'https://app.gong.io';
const GONG_API_SCOPES = [
  'api:calls:read:transcript',
  'api:calls:read:basic',
  'api:users:read',
  'api:calls:read:extensive',
  'api:crm-calls:manual-association:read',
];

router.get(
  '/connectors/gong/authorize',
  clientAdminCheck,
  async (req: RequestWithUser, res: express.Response) => {
    try {
      const { userDetail } = req.user;
      const { forwardTo, reconnect } = req.query;

      const { CLIENT_ID, REDIRECT_URI } = await getGongAppDetails();

      const state = JSON.stringify({
        user_id: userDetail.id,
        client_id: userDetail.client_id,
        schema: userDetail.client.database_schema_id,
        user_email: userDetail.email,
        forward_to: forwardTo,
        is_reconnect: reconnect === 'true',
      });

      const urlParams = new URLSearchParams({
        client_id: CLIENT_ID,
        redirect_uri: REDIRECT_URI,
        scope: GONG_API_SCOPES.join(' '),
        response_type: 'code',
        state,
      });

      res.status(200).json({
        success: true,
        response: `${GONG_APP_URL}/oauth2/authorize?${urlParams.toString()}`,
      });
    } catch (error) {
      console.error(`[connectors/gong/authorize]`, error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
      });
    }
  },
);

router.get(
  '/connectors/gong/callback',
  async (req: RequestWithUser, res: express.Response) => {
    try {
      const { code, state } = req.query;
      const stateData = JSON.parse(state as string);
      const { CLIENT_ID, CLIENT_SECRET, REDIRECT_URI } =
        await getGongAppDetails();

      const basicAuthHeader = generateBasicAuth(CLIENT_ID, CLIENT_SECRET);

      const urlParams = new URLSearchParams({
        code: code as string,
        grant_type: 'authorization_code',
        redirect_uri: REDIRECT_URI,
        client_id: CLIENT_ID,
        validity_duration: (60 * 60 * 24 * 30).toString(), // 30 days
      });
      const response = await fetch(
        `${GONG_APP_URL}/oauth2/generate-customer-token?${urlParams.toString()}`,
        {
          method: 'POST',
          headers: {
            Authorization: basicAuthHeader,
          },
        },
      );

      const data = await response.json();
      const gongIntegration = await getIntegrationBySystemName('gong');

      const encryptedAuthDetails = encryptText(JSON.stringify(data));
      await saveClientIntegration(
        stateData.schema,
        stateData.user_id,
        gongIntegration.id,
        encryptedAuthDetails,
      );

      // Log a message about what happened
      const actionType = stateData.is_reconnect ? 'reconnected' : 'connected';
      console.log(
        `[connectors/gong/callback] Successfully ${actionType} Gong for client schema ${stateData.schema}`,
      );

      if (stateData.forward_to) {
        res.redirect(stateData.forward_to);
      } else {
        res.status(200).send('OK. You can close this');
      }
    } catch (error) {
      console.error(`[connectors/gong/callback]`, error);
      telemetryClient.trackException({ exception: error });
      res.status(200).send();
    }
  },
);

router.get(
  '/connectors/gong/auth',
  clientAdminCheck,
  async (req: RequestWithUser, res: express.Response) => {
    try {
      const authDetails = await getGongAuth(
        req.user.userDetail.client.database_schema_id,
      );

      if (!authDetails) {
        return res.status(200).json({
          success: false,
          error: 'Gong details not found',
        });
      }

      return res.status(200).json({
        success: true,
      });
    } catch (error) {
      console.log(`[connectors/gong/auth]`, error);
      return res.status(200).json({
        success: false,
        error: error.message,
      });
    }
  },
);

router.delete(
  '/connectors/gong/auth',
  clientAdminCheck,
  async (req: RequestWithUser, res: express.Response) => {
    try {
      const { schema } = req.user;

      const gongIntegration = await getIntegrationBySystemName('gong');

      if (!gongIntegration) {
        res.status(404).json({
          success: false,
          error: 'Gong integration not found',
        });
      }

      await deleteClientIntegration(schema, gongIntegration.id);

      res.status(200).json({ success: true });
    } catch (error) {
      console.error(`[connectors/gong/auth]`, error);
      res.status(500).json({
        success: false,
        error: error.message,
      });
    }
  },
);

router.get(
  '/connectors/gong/calls',
  clientAdminCheck,
  gongAuth,
  async (req: RequestWithGong, res: express.Response) => {
    try {
      const gongClient = req.gongClient;
      const { schema } = req.user;
      const { cursor, search } = req.query;

      const callsParams = new URLSearchParams();
      if (cursor) {
        callsParams.set('cursor', cursor as string);
      }

      try {
        const startDateSetting = await getClientSettingByName(
          schema,
          'gong_ingestion_start_date',
        );

        if (startDateSetting) {
          try {
            // Parse the stored ISO date string with timezone
            const startDate = new Date(startDateSetting);

            if (isNaN(startDate.getTime())) {
              throw new Error(`Invalid date format: ${startDateSetting}`);
            }

            // Format for Gong API
            const fromDateTimeFilter = startDate.toISOString();
            callsParams.set('fromDateTime', fromDateTimeFilter);
          } catch (dateError) {
            console.warn(
              `[GET /connectors/gong/calls] Error parsing date ${startDateSetting}: ${dateError.message}. Ignoring filter.`,
            );
          }
        }
      } catch (settingError) {
        console.error(
          `[GET /connectors/gong/calls] Error fetching gong_ingestion_start_date setting: ${settingError.message}. Proceeding without filter.`,
        );
      }

      const callsResponse = await gongClient.get<GongCallsApiResponse>(
        '/v2/calls',
        callsParams,
      );

      const db = await getDB(schema);

      const syncedCalls = await getGongCallsByIds(
        db,
        callsResponse.calls.map((call) => call.id),
      );
      const syncedCallsMap = syncedCalls.reduce(
        (acc, call) => {
          acc[call.gong_id] = call;
          return acc;
        },
        {} as Record<string, GongCalls>,
      );

      const calls = callsResponse.calls
        .map((call) => {
          const snakeCaseCall = snakeCaseKeys(call);
          const syncedCall = syncedCallsMap[call.id];

          // Filter out private calls
          if (snakeCaseCall.is_private === true) {
            return null;
          }

          // Include search term filtering if provided
          if (
            search &&
            !call.title.toLowerCase().includes((search as string).toLowerCase())
          ) {
            return null;
          }

          return {
            ...snakeCaseCall,
            id: syncedCall?.id ?? null,
            created_at: syncedCall?.created_at ?? null,
            updated_at: syncedCall?.updated_at ?? null,
            gong_id: call.id,
            last_ingested_at: syncedCall?.last_ingested_at ?? null,
            processing_status: syncedCall?.processing_status ?? null,
            last_processing_started:
              syncedCall?.last_processing_started ?? null,
          };
        })
        .filter(Boolean); // Remove null items from search filtering and private calls

      res.status(200).json({
        success: true,
        response: {
          ...callsResponse,
          calls,
        },
      });
    } catch (error) {
      console.error(`[connectors/gong/calls]`, error);
      res.status(500).json({
        success: false,
        error: error.message,
      });
    }
  },
);

router.post(
  '/connectors/gong/sync',
  clientAdminCheck,
  gongAuth,
  async (req: RequestWithGong, res: express.Response) => {
    try {
      const { schema, userDetail } = req.user;
      const { callIds } = req.body;

      // Start the ingest integration
      if (callIds && Array.isArray(callIds) && callIds.length > 0) {
        // Get DB connection
        const db = await getDB(schema);

        // Get the database IDs for the Gong calls if they exist
        const gongCalls = await getGongCallsByIds(db, callIds);
        const gongCallsMap = gongCalls.reduce(
          (acc, call) => {
            acc[call.gong_id] = call;
            return acc;
          },
          {} as Record<string, GongCalls>,
        );

        await prepAndInitateIngestIntegration({
          client_id: userDetail.client_id,
          schema,
          system: 'gong',
          params: {
            callIds: callIds.map((gongId) => ({
              id: gongCallsMap[gongId]?.id || gongId, // Use database ID if available, fallback to Gong ID
              gong_id: gongId,
            })),
            created_by_id: userDetail.id,
          },
        });
      } else {
        await prepAndInitateIngestIntegration({
          client_id: userDetail.client_id,
          schema,
          system: 'gong',
          params: {
            created_by_id: userDetail.id,
            fetchCalls: true,
            processCalls: true,
          },
        });
      }

      return res.status(200).json({
        success: true,
        message: callIds
          ? `Started ingestion for ${callIds.length} call(s)`
          : 'Started full sync',
      });
    } catch (error) {
      console.error(`[connectors/gong/sync]`, error);
      res.status(500).json({
        success: false,
        error: error.message,
      });
    }
  },
);

router.post(
  '/connectors/gong/clear-data',
  clientAdminCheck,
  gongAuth,
  async (req: RequestWithGong, res: express.Response) => {
    try {
      const { schema } = req.user;
      const { callIds } = req.body;

      // Get DB connection
      const db = await getDB(schema);

      // Clear call data
      // Ensure callIds are numbers if they are passed, otherwise pass undefined for full clear
      const callDbIds = Array.isArray(callIds) ? callIds : undefined;

      const { deletedCallsCount } = await clearGongCallData(db, callDbIds);

      let message = '';
      if (callDbIds && callDbIds.length > 0) {
        message = `Successfully cleared data for ${deletedCallsCount} specified Gong call(s).`;
      } else if (
        callIds === undefined ||
        (Array.isArray(callIds) &&
          callIds.length === 0 &&
          callDbIds === undefined)
      ) {
        // This condition handles undefined callIds or an empty array of callIds, meaning clear all.
        message = `Successfully cleared all Gong call data (${deletedCallsCount} calls).`;
      }

      return res.status(200).json({
        success: true,
        message,
        deleted_calls_count: deletedCallsCount,
      });
    } catch (error) {
      console.error(`[connectors/gong/clear-data]`, error);
      res.status(500).json({
        success: false,
        error: error.message,
      });
    }
  },
);

router.get(
  '/clients/:clientId/connectors/gong/sync',
  m2mCheck,
  async (req: RequestWithUser, res: express.Response) => {
    let clientId;
    try {
      clientId = parseInt(req.params.clientId, 10);
      if (isNaN(clientId)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid client ID',
        });
      }

      const client = await getClientById(clientId);
      if (!client) {
        return res.status(204).send({
          success: false,
          error: 'Client not found',
        });
      }

      // Check if scheduled Gong sync is enabled for this client
      const scheduledSyncEnabled = await getClientSettingByName(
        client.database_schema_id,
        'client_gong_scheduled_sync_enabled',
      );

      // If the setting is explicitly set to 'false', don't proceed with the sync
      if (scheduledSyncEnabled === 'false') {
        return res.status(200).json({
          success: true,
          message: 'Scheduled Gong sync is disabled for this client',
        });
      }

      const gongIntegration = await getIntegrationBySystemName('gong');
      if (!gongIntegration) {
        return res.status(204).send({
          success: false,
          error: 'Gong integration not found',
        });
      }

      const clientIntegration = await getClientIntegration(
        client.database_schema_id,
        gongIntegration.id,
      );

      if (!clientIntegration) {
        return res.status(204).send({
          success: false,
          error: 'Gong token not found',
        });
      }

      await prepAndInitateIngestIntegration({
        client_id: client.id,
        schema: client.database_schema_id,
        system: 'gong',
        params: {
          created_by_id: clientIntegration.created_by as UserId,
          fetchCalls: true,
          processCalls: true,
        },
      });

      res.status(200).json({ success: true });
    } catch (error) {
      console.error(`[clients/${clientId}/connectors/gong/sync]`, error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
      });
    }
  },
);

router.post(
  '/connectors/gong/clear-job-log-intervals',
  clientAdminCheck,
  gongAuth,
  async (req: RequestWithGong, res: express.Response) => {
    try {
      const { schema } = req.user;
      const { clearSuccessfulIntervals, clearFailedIntervals } = req.body;

      // Validate parameters
      if (
        typeof clearSuccessfulIntervals !== 'boolean' &&
        clearSuccessfulIntervals !== undefined
      ) {
        return res.status(400).json({
          success: false,
          error: 'clearSuccessfulIntervals must be a boolean',
        });
      }

      if (
        typeof clearFailedIntervals !== 'boolean' &&
        clearFailedIntervals !== undefined
      ) {
        return res.status(400).json({
          success: false,
          error: 'clearFailedIntervals must be a boolean',
        });
      }

      // Get DB connection
      const db = await getDB(schema);

      const updateData: Record<string, any> = {};

      if (clearSuccessfulIntervals === true) {
        updateData.successful_intervals = [];
      }

      if (clearFailedIntervals === true) {
        updateData.failed_intervals = [];
      }

      // No updates to make
      if (Object.keys(updateData).length === 0) {
        return res.status(400).json({
          success: false,
          error: 'No valid parameters provided for update',
        });
      }

      const updated = await updateLatestGongIngestJobLogData(db, updateData);

      if (!updated) {
        return res.status(404).json({
          success: false,
          error: 'No active Gong ingestion job log found',
        });
      }

      return res.status(200).json({
        success: true,
        message: 'Job log updated successfully',
      });
    } catch (error) {
      console.error(`[connectors/gong/job-log]`, error);
      res.status(500).json({
        success: false,
        error: error.message,
      });
    }
  },
);

module.exports = router;
