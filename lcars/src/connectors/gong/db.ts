import { <PERSON><PERSON><PERSON><PERSON>, NewGongCalls } from '@tribble/tribble-db/clients/GongCalls';
import { DB, TRX } from '@tribble/tribble-db/db_ops';
import { sql } from 'kysely';

export async function getGongCallsByIds(
  db: DB | TRX,
  ids: GongCalls['gong_id'][],
): Promise<GongCalls[]> {
  if (ids.length === 0) {
    return [];
  }
  return db
    .selectFrom('gong_calls')
    .where('gong_id', 'in', ids)
    .selectAll()
    .execute();
}

export async function getLastestGongCall(
  db: DB | TRX,
): Promise<GongCalls | null> {
  return db
    .selectFrom('gong_calls')
    .selectAll()
    .orderBy('started', 'desc')
    .executeTakeFirst();
}

/**
 * Clears Gong call data from the 'gong_calls' table.
 * If callDbIds are provided, only those specific calls are deleted.
 * Otherwise, all Gong calls are deleted.
 * Associated data in 'gong_call_extensive_data' is deleted automatically
 * due to ON DELETE CASCADE foreign key constraint.
 *
 * @param db The database connection or transaction.
 * @param callDbIds Optional array of gong_calls.id (database primary keys) to delete.
 * @returns A promise resolving to an object with the count of deleted calls.
 */
export async function clearGongCallData(
  db: DB | TRX,
  callDbIds?: GongCalls['id'][],
): Promise<{
  deletedCallsCount: number;
}> {
  let deleteQuery = db.deleteFrom('gong_calls');

  if (callDbIds && callDbIds.length > 0) {
    deleteQuery = deleteQuery.where('id', 'in', callDbIds);
  }

  const result = await deleteQuery.execute();

  const deletedCallsCount = result.reduce(
    (sum, res) => sum + Number(res.numDeletedRows || 0),
    0,
  );

  return {
    deletedCallsCount,
  };
}

/**
 * Updates the data column of the latest job log with type "ingest_integration_gong".
 * Merges the new data with existing data rather than replacing it completely.
 *
 * @param db The database connection or transaction.
 * @param data The new data to merge with existing job log data.
 * @returns A promise resolving to a boolean indicating whether the update was successful.
 */
export async function updateLatestGongIngestJobLogData(
  db: DB | TRX,
  data: Record<string, any>,
): Promise<boolean> {
  // Find the latest job log with the specified type
  const latestJobLog = await db
    .selectFrom('job_log')
    .where('type', '=', 'ingest_integration_gong')
    .orderBy('created_date', 'desc')
    .selectAll()
    .executeTakeFirst();

  if (!latestJobLog) {
    return false;
  }

  // Merge existing data with new data
  const existingData = (latestJobLog.data as Record<string, any>) || {};
  const mergedData = {
    ...existingData,
    ...data,
  };

  // Update the data column for the found job log
  const result = await db
    .updateTable('job_log')
    .set({ data: mergedData })
    .where('id', '=', latestJobLog.id)
    .execute();

  return result.length > 0;
}
