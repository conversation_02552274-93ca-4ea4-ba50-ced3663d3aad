import { GongCallsId } from '@tribble/tribble-db/clients/GongCalls';
import { IGongAuthDetails } from './auth';

export interface GongCallApi {
  id: GongCallsId;
  url: string;
  title: string;
  scheduled: string;
  started: string;
  duration: number;
  primaryUserId: string;
  direction: 'Inbound' | 'Outbound' | 'Conference' | 'Unknown';
  system: string;
  scope: 'Internal' | 'External' | 'Unknown';
  media: 'Video' | 'Audio';
  language: string;
  workspaceId: string;
  sdrDisposition: string;
  clientUniqueId: string;
  customData: string;
  purpose: string;
  meetingUrl: string;
  isPrivate: boolean;
  calendarEventId: string;
}

export interface GongCallsApiResponse {
  calls: GongCallA<PERSON>[];
  records: {
    totalRecords: number;
    currentPage: number;
    currentPageNumber: number;
    cursor?: string;
  };
  requestId: string;
}

export interface GongCallApiResponse {
  call: GongCallApi;
  requestId: string;
}

export class GongClient {
  private authDetails: IGongAuthDetails;

  constructor(authDetails: IGongAuthDetails) {
    this.authDetails = authDetails;
  }

  async get<T>(path: string, params?: URLSearchParams): Promise<T> {
    const response = await fetch(
      `${this.authDetails.api_base_url_for_customer}${path}?${params?.toString()}`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${this.authDetails.access_token}`,
        },
      },
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch ${path}: ${response.statusText}`);
    }

    return response.json();
  }

  async post<T>(
    path: string,
    body?: any,
    params?: URLSearchParams,
  ): Promise<T> {
    const response = await fetch(
      `${this.authDetails.api_base_url_for_customer}${path}?${params?.toString()}`,
      {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${this.authDetails.access_token}`,
        },
        body: JSON.stringify(body),
      },
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch ${path}: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Fetches multiple Gong calls by their IDs using parallel API requests
   * @param callIds Array of call IDs to fetch
   * @returns Array of fetched call objects
   */
  async fetchCallsByIds(callIds: string[]): Promise<GongCallApi[]> {
    if (!callIds || callIds.length === 0) {
      return [];
    }

    // Create an array of promises for each call
    const fetchPromises = callIds.map(async (callId) => {
      try {
        // Get call details using the /v2/calls/{id} endpoint
        const callResponse = await this.get<GongCallApiResponse>(
          `/v2/calls/${callId}`,
        );
        return callResponse.call;
      } catch (error) {
        console.error(`Error fetching call ${callId}:`, error);
        // Return null for failed calls
        return null;
      }
    });

    // Wait for all promises to resolve and filter out failed calls (nulls)
    const results = await Promise.all(fetchPromises);
    return results.filter((call): call is GongCallApi => call !== null);
  }
}
