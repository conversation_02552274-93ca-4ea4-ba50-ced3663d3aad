import { getGongAuth, refreshGongToken } from '@tribble/gong-service';
import express, { NextFunction } from 'express';
import { GongClient } from '.';
import { getIntegrationBySystemName } from '../../db/integrations';
import { RequestWithUser } from '../../utils/auth';

export interface IGongAuthDetails {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
  scope: string;
  api_base_url_for_customer: string;
}

export interface RequestWithGong extends RequestWithUser {
  gongClient: GongClient;
}

// Update the gongAuth middleware to handle token refresh
export async function gongAuth(
  req: RequestWithGong,
  res: express.Response,
  next: NextFunction,
) {
  const { schema, userDetail } = req.user;

  try {
    const gongIntegration = await getIntegrationBySystemName('gong');

    if (!gongIntegration) {
      return res.status(204).json({
        success: false,
        error: 'Gong integration not found',
      });
    }

    let authDetails = await getGongAuth(schema);

    if (!authDetails) {
      return res.status(401).json({
        success: false,
        error: 'Gong auth details not found',
        error_code: 'GONG_AUTH_REQUIRED',
      });
    }

    // Create the Gong client with the current auth details
    let gongClient = new GongClient(authDetails);

    // Test the token with a simple API call
    try {
      // Make a lightweight API call to verify the token works
      await gongClient.get('/v2/users');
    } catch (error) {
      // If the token is invalid, try refreshing it
      console.log(
        `[gongAuth] Token validation failed, attempting refresh: ${error.message}`,
      );

      try {
        // Refresh the token
        authDetails = await refreshGongToken(
          schema,
          authDetails,
          userDetail.id,
        );

        // Create a new client with the refreshed token
        gongClient = new GongClient(authDetails);
      } catch (refreshError) {
        console.error(
          `[gongAuth] Token refresh failed: ${refreshError.message}`,
        );
        return res.status(401).json({
          success: false,
          error:
            'Gong authentication failed - please reconnect your Gong integration',
          error_code: 'GONG_AUTH_REQUIRED', // Specific error code for frontend to detect
        });
      }
    }

    // Attach the client to the request and continue
    req.gongClient = gongClient;
    next();
  } catch (err) {
    console.error(`[gongAuth] Authentication error: ${err.message}`);
    return res.status(401).json({
      success: false,
      error: 'Gong auth failed',
      error_code: 'GONG_AUTH_REQUIRED', // Add the same error code here too
    });
  }
}
