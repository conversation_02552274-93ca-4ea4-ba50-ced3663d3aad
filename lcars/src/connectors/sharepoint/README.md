# SharePointConnector

SharePoint API ultimately uses the Microsoft 365 Graph API which can grant access to multiple M365 Services. Files from different services such as SharePoint or Office are ultimately stored in OneDrive

## SharePoint API Access

### Sites/Drives

**A. Sites**

A SharePoint instance will likely have multiple sites (the instance is also called a site 😑)

https://graph.microsoft.com/v1.0/sites?search=*

**B. Site**

https://graph.microsoft.com/v1.0/sites/{siteId}

**C. Drives** The instance _probably_ has multiple sites and every site can have multiple drives, so call best to call /drives for every site

Root Drive

https://graph.microsoft.com/v1.0/sites/{siteId}/drive

Site Drives

https://graph.microsoft.com/v1.0/sites/{siteId}/drives

**D. Drive Children** 

~~Root Drive Children~~ For info only: don't use this since it would only be the root drive of the site, better to get the list of drives from [C. Drives](#c-drives)

~~https://graph.microsoft.com/v1.0/sites/{siteId}/drive/root/children~~

Folders (maybe files?) in the root of drive

https://graph.microsoft.com/v1.0/drives/{driveId}/root/children

**E. Folder Children** - will be files or other folders

https://graph.microsoft.com/v1.0/drives/{driveId}/items/{folderId}/children

**F. Drive Item** Get a single file or folder

You can get any file or folder with the driveId and itemId regardless of how deeply nested the folder is
 
https://graph.microsoft.com/v1.0/drives/{driveId}/items/{itemId}

 ### Pages

 **Pages** 

 https://graph.microsoft.com/v1.0/sites/{siteId}/pages

Articles:

 https://graph.microsoft.com/v1.0/sites/{siteId}/pages?$filter=pageLayout eq 'Article'&$orderby=lastModifiedDateTime desc

**Page**
https://graph.microsoft.com/v1.0/sites/{siteId}/pages/{pageId}

**Page WebParts**
https://graph.microsoft.com/v1.0/sites/{siteId}/pages/{pageId}/microsoft.graph.sitePage/webparts