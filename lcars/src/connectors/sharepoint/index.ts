import * as grpc from '@grpc/grpc-js';
import {
  BlobContainer,
  downloadBlob,
  getBlobUrl,
  uploadFilesToBlob,
} from '@tribble/azure-helper';
import { LOG_TOGGLE_NAME, Logger } from '@tribble/common-utils';
import { insertDocs } from '@tribble/comms-shared';
import { DocumentId } from '@tribble/tribble-db/clients/Document';
import {
  M365driveAsset,
  NewM365driveAsset,
} from '@tribble/tribble-db/clients/M365driveAsset';

import { IntegrationAssetSetting } from '@tribble/tribble-db/clients/IntegrationAssetSetting';
import { ClientId } from '@tribble/tribble-db/tribble/Client';
import { UserId } from '@tribble/tribble-db/tribble/User';
import { MIME_TYPE, StructuredFileRequest } from '@tribble/types-shared';
import {
  AddDocumentRequest,
  AddQuestionnaireRequest,
  BrainIngestClient,
  DocumentSource,
  DocumentSourceInfo,
  MimeType,
} from 'brain-ingest-service';
import { MetadataFilterValue as MetadataFilterValueIngest } from 'brain-ingest-service/dist/brain_ingest_service_pb';
import ExcelJS from 'exceljs';
import * as timestamppb from 'google-protobuf/google/protobuf/timestamp_pb';
import mime from 'mime-types';
import { v4 as uuidv4 } from 'uuid';
import {
  ASSET_TYPE_INSIGHT,
  ASSET_TYPE_RFP,
  ASSET_TYPE_SPREADSHEET,
  AssetSheet,
  GrpcMimeTypeMap,
  Rfp,
  getDocumentDocType,
  getGrpcBrainIngestClient,
} from '..';
import { constants } from '../../constants';
import { writePartialJobRecord } from '../../db/job';
import { upsertScheduledNotification } from '../../db/notifications';
import {
  insertPartialDocRecord,
  insertPartialJobRecord,
} from '../../db/queries';
import { upsertMultipleAssetTasks } from '../../db/tasks';
import { getClientSettingsAsUser } from '../../db/users';
import { FileWithMetadata, MetadataFilterValue } from '../../model';
import { send_function_request } from '../../utils/eventGrid';
import { createRememberTagsJson } from '../../utils/mdfUtils';
import { createSheetTaskNotificationDetails } from '../../utils/review';
import { MicrosoftAuth, getMicrosoftToken } from './auth';
import {
  M365DriveAssetWithSettings,
  getAssetsByItemIds,
  getSharePointAssetSheetsByAssetId,
  getSharePointAssetSheetsByItemIds,
  unwatchFileById,
  updateAssets,
  upsertSharePointAssetSheets,
  upsertm365Assets,
  upsertm365AssetsUnconfiguredSheets,
  watchFileById,
} from './db';
import {
  M365DriveItem,
  M365ItemsResponse,
  ParentReference,
  SiteDrive,
} from './types';

const API_MAX_ATTEMPTS = 5;
const GRAPH_API_URL = 'https://graph.microsoft.com/v1.0';
const BATCH_SIZE = 10;

// Initialize logger for SharePoint connector
const logger = new Logger({
  appName: 'SharePoint',
  toggleName: LOG_TOGGLE_NAME.SHAREPOINT,
});

/**
 * Extract temporal data (creation and modification dates) from a SharePoint file or folder
 * @param item The SharePoint drive item containing date information
 * @returns Object with createdDate and modifiedDate
 */
export const getFileTemporalData = (item: Partial<M365DriveItem>) => {
  return {
    createdDate: item?.fileSystemInfo?.createdDateTime
      ? new Date(item.fileSystemInfo.createdDateTime)
      : item?.createdDateTime
        ? new Date(item.createdDateTime)
        : null,
    modifiedDate: item?.fileSystemInfo?.lastModifiedDateTime
      ? new Date(item.fileSystemInfo.lastModifiedDateTime)
      : item?.lastModifiedDateTime
        ? new Date(item.lastModifiedDateTime)
        : null,
  };
};

/**
 * Call the Microsoft Graph API with exponential backoff for rate limiting
 * @param url
 * @param userId
 * @param schema
 * @param auth
 * @param attempt
 * @param nextLink
 * @returns
 */
const callGraphApi = async (
  url: string,
  userId: number,
  schema: string,
  auth: MicrosoftAuth,
  attempt: number = 1,
): Promise<any> => {
  try {
    const token = await getMicrosoftToken(userId, schema, auth);
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      if (response.status === 429) {
        if (attempt > API_MAX_ATTEMPTS) {
          console.error(
            '[ SharePoint callGraphApi ] Rate limited by Microsoft. Max attempts reached.',
          );
          return undefined;
        }

        const backoffTime = attempt * 5000;

        console.log(
          `[ SharePoint callGraphApi ] Rate limited by Microsoft. Backing off for ${backoffTime}ms`,
        );
        await new Promise((resolve) => setTimeout(resolve, backoffTime));

        return await callGraphApi(url, userId, schema, auth, attempt + 1);
      }
      console.error('[ SharePoint callGraphApi ]', response.statusText);
      return undefined;
    }

    const json = await response.json();
    return json;
  } catch (e) {
    console.error('[ SharePoint callGraphApi ]', e);
    return undefined;
  }
};

/**
 * Get all 'sites' a user has access to. Not necessarily limited to the organization.
 * @param schema
 * @param userId
 * @param auth
 * @returns
 */
export const getSites = async (
  schema: string,
  userId: number,
  auth: MicrosoftAuth,
) => {
  // If client has restriction on, then only return the explicitly allowlist'd sites. Else,
  // can use the Graph API search endpoint
  const clientSettings = await getClientSettingsAsUser(schema);
  const settingUseRestricted = clientSettings.find(
    (s) => s.name === constants.SETTING_CLIENT_SHAREPOINT_SITES_RESTRICTED,
  );
  const settingRestrictedList = clientSettings.find(
    (s) => s.name === constants.SETTING_CLIENT_SHAREPOINT_SITES_ALLOWLIST,
  );

  if (!settingUseRestricted || settingUseRestricted.value === 'false') {
    const sitesUrl = `${GRAPH_API_URL}/sites?search=*`;
    const allSites = await callGraphApi(sitesUrl, userId, schema, auth);
    return allSites?.value ?? [];
  } else {
    // Call Graph API for each site (assume it's a small finite list)
    if (!settingRestrictedList) {
      return [];
    } else {
      // User will define a "normal" looking Sharepoint URL, but the Graph API
      // expects a slightly different format (server relative path):
      // https://learn.microsoft.com/en-us/graph/api/site-getbypath?view=graph-rest-1.0#http-request
      const convertUrlToRelativePath = (url: string) => {
        const hostname = new URL(url).hostname;
        const relativePath = url.replace(`https://${hostname}`, '');

        return `${GRAPH_API_URL}/sites/${hostname}:${relativePath}`;
      };

      const sites = settingRestrictedList.value.split(',');
      const siteData = [];
      for (const site of sites) {
        const siteUrl = convertUrlToRelativePath(site.trim());
        console.log('Calling Graph API on site: ', siteUrl, site);
        const siteResponse = await callGraphApi(siteUrl, userId, schema, auth);
        if (siteResponse) {
          siteData.push(siteResponse);
        }
      }
      return siteData;
    }
  }
};

export const getDriveChildren = async (
  schema: string,
  userId: number,
  auth: MicrosoftAuth,
  driveId: string,
) => {
  let url = `${GRAPH_API_URL}/drives/${driveId}/root/children`;

  const items: M365DriveItem[] = [];

  do {
    const response = (await callGraphApi(
      url,
      userId,
      schema,
      auth,
      1,
    )) as M365ItemsResponse;

    if (response.value && Array.isArray(response.value)) {
      items.push(...response.value);
    }

    url = response['@odata.nextLink'];
  } while (url);

  return items;
};

/**
 * Get children of a folder in a drive, including the download URL
 * SharePoint files are ultimately stored in a drive.
 * Drive IDs can be found by calling getSiteDrives.
 * @param schema
 * @param userId
 * @param auth
 * @param driveId
 * @param folderId
 */
export const getFolderChildren = async (
  schema: string,
  userId: number,
  auth: MicrosoftAuth,
  driveId: string,
  folderId: string,
) => {
  let url = `${GRAPH_API_URL}/drives/${driveId}/items/${folderId}/children`;

  const items: M365DriveItem[] = [];

  do {
    const response = (await callGraphApi(
      url,
      userId,
      schema,
      auth,
      1,
    )) as M365ItemsResponse;

    if (response.value && Array.isArray(response.value)) {
      items.push(...response.value);
    }

    url = response['@odata.nextLink'];
  } while (url);

  return items;
};

/**
 * Get the drive associated with a SharePoint site
 * @param schema
 * @param userId
 * @param auth
 * @param siteId
 */
export const getSiteDrives = async (
  schema: string,
  userId: number,
  auth: MicrosoftAuth,
  siteId: string,
) => {
  const url = `${GRAPH_API_URL}/sites/${siteId}/drives`;

  return await callGraphApi(url, userId, schema, auth);
};

/**
 * Get all lists in a SharePoint site
 * @param schema
 * @param userId
 * @param auth
 * @param siteId
 * @returns
 */
export const getLists = async (
  schema: string,
  userId: number,
  auth: MicrosoftAuth,
  siteId: string,
) => {
  const url = `${GRAPH_API_URL}/sites/${siteId}/lists`;

  return await callGraphApi(url, userId, schema, auth);
};

/**
 * Get a specific drive item by ID
 * @param schema
 * @param userId
 * @param auth
 * @param driveId
 * @param itemId
 * @returns
 */
export const getDriveItem = async (
  schema: string,
  userId: number,
  auth: MicrosoftAuth,
  driveId: string,
  itemId: string,
) => {
  const url = `${GRAPH_API_URL}/drives/${driveId}/items/${itemId}`;

  return (await callGraphApi(
    url,
    userId,
    schema,
    auth,
  )) as Promise<M365DriveItem>;
};

export const getFilesInFolder = async (
  schema: string,
  userId: number,
  auth: MicrosoftAuth,
  driveId: string,
  folderId: string,
) => {
  let url = `${GRAPH_API_URL}/drives/${driveId}/items/${folderId}/children`;

  let response: M365ItemsResponse;
  let files: M365DriveItem[] = [];
  do {
    response = (await callGraphApi(
      url,
      userId,
      schema,
      auth,
      1,
    )) as M365ItemsResponse;

    if (!!response.value && Array.isArray(response.value)) {
      files.push(...response.value);
    }
    url = response['@odata.nextLink'];
  } while (url);

  return files;
};

export const getFolderChildrenRecursive = async (
  schema: string,
  userId: number,
  auth: MicrosoftAuth,
  driveId: string,
  folderId: string,
) => {
  let files: { [key: string]: M365DriveItem } = {};

  const newFiles: M365DriveItem[] = await getFilesInFolder(
    schema,
    userId,
    auth,
    driveId,
    folderId,
  );
  for (const file of newFiles) {
    files[file.id] = file;
  }

  const folders = newFiles.filter((f) => !!f.folder);

  while (folders.length > 0) {
    const folder = folders.shift();
    const children = await getFilesInFolder(
      schema,
      userId,
      auth,
      driveId,
      folder.id,
    );
    for (const child of children) {
      files[child.id] = child;
      if (!!child.folder) {
        folders.push(child);
      }
    }
  }

  return Object.values(files);
};

export const watchDirectory = async (
  schema: string,
  userId: number,
  clientId: number,
  auth: MicrosoftAuth,
  watchForChanges: boolean,
  drive: SiteDrive,
  folder: M365DriveItem,
  asset_settings: Partial<IntegrationAssetSetting>,
) => {
  if (!drive && !folder) {
    console.error('[ SharePoint ingestDirectory ] No drive or folder provided');
    return;
  }

  try {
    // Get temporal data for folder/drive
    const { createdDate, modifiedDate } = folder
      ? getFileTemporalData(folder)
      : {
          createdDate: drive?.createdDateTime
            ? new Date(drive.createdDateTime)
            : null,
          modifiedDate: drive?.lastModifiedDateTime
            ? new Date(drive.lastModifiedDateTime)
            : null,
        };

    await upsertm365Assets(schema, [
      {
        item_id: !!folder ? folder?.id : drive?.id,
        drive_id: !!folder ? folder?.parentReference.driveId : drive?.id,
        name: !!folder ? folder?.name : drive.name,
        mime_type: !!folder
          ? MIME_TYPE.SHAREPOINT_FOLDER
          : MIME_TYPE.SHAREPOINT_DRIVE,
        last_modified: !!folder
          ? folder?.lastModifiedDateTime
          : drive.lastModifiedDateTime,
        url: !!folder ? folder?.webUrl : drive.webUrl,
        parent_reference: folder?.parentReference,
        // last_checked: new Date('1997-08-29'),
        folder: folder?.folder,
        user_id: userId as UserId,
        created_at: new Date(),
        updated_at: new Date(),
        watch_for_changes: watchForChanges,
        status: 'new',
        type: !!folder ? 'folder' : 'drive',
        asset_settings,
        source_created_date: createdDate,
        source_modified_date: modifiedDate,
      },
    ]);

    const rootFolders = !!folder
      ? [folder]
      : await getDriveChildren(schema, userId, auth, drive.id);

    for (const rootFolder of rootFolders) {
      // Set the "always tags" and/or privacy for each root folder
      rootFolder.asset_settings = asset_settings;

      setupIngestItems(
        schema,
        userId,
        clientId,
        auth,
        [rootFolder],
        watchForChanges,
      );
    }

    return true;
  } catch (e) {
    console.error('[ SharePoint ingestDirectory ] Error:', e);
    return false;
  }
};

/**
 * @param schema
 * @param userId
 * @param auth
 * @param items Only needs: item id, parentReference, and file or folder
 * @param watchForChanges
 */
export const setupIngestItems = async (
  schema: string,
  userId: number,
  clientId: number,
  auth: MicrosoftAuth,
  items: Partial<
    M365DriveItem & { asset_settings?: Partial<IntegrationAssetSetting> }
  >[],
  watchForChanges: boolean,
  assetOnly: boolean = false,
): Promise<{ [key: string]: Partial<M365DriveItem> }> => {
  try {
    const fileMap: { [key: string]: Partial<M365DriveItem> } = {};
    logger.info(
      'Processing items for ingestion',
      { itemCount: items.length },
      schema,
    );

    for (const item of items) {
      if (!item?.id) {
        logger.warn('Skipping item with no ID', { item }, schema);
        continue;
      }
      fileMap[item.id] = item;
    }

    logger.info(
      'FileMap initialized',
      {
        fileMapSize: Object.keys(fileMap).length,
      },
      schema,
    );

    const folders = items.filter((f) => !!f?.folder);
    if (folders.length > 0) {
      logger.info(
        'Processing folders for ingestion',
        {
          folderCount: folders.length,
        },
        schema,
      );
      for (const folder of folders) {
        if (!folder?.parentReference?.driveId || !folder?.id) {
          logger.warn(
            'Skipping folder with missing parentReference or ID',
            {
              folderId: folder?.id,
              driveId: folder?.parentReference?.driveId,
              folderName: folder?.name,
            },
            schema,
          );
          continue;
        }

        const children = (await getFolderChildrenRecursive(
          schema,
          userId,
          auth,
          folder.parentReference.driveId,
          folder.id,
        )) as (M365DriveItem & {
          asset_settings?: Partial<IntegrationAssetSetting>;
        })[];

        logger.info(
          'Found children for folder',
          {
            childrenCount: children.length,
            folderName: folder.name,
          },
          schema,
        );

        for (const child of children) {
          if (!child?.id) {
            logger.warn('Skipping child with no ID', { child }, schema);
            continue;
          }

          // If it's a new file (i.e. doesn't exist in fileMap),
          // set the auto tags to the folder's auto tags
          if (folder?.asset_settings && !fileMap[child.id]) {
            if (!child.asset_settings) {
              child.asset_settings = {};
            }
            child.asset_settings = folder.asset_settings;
            logger.debug(
              'Applied folder asset_settings to child',
              {
                childId: child.id,
              },
              schema,
            );
          }

          // Overwrite the partial with the full object
          fileMap[child.id] = child;
        }
      }
    }

    const ingestableMimeTypes = [
      MIME_TYPE.PDF,
      MIME_TYPE.DOCX,
      MIME_TYPE.PPTX,
      MIME_TYPE.CSV,
      MIME_TYPE.EXCEL,
      MIME_TYPE.XLSX,
    ];

    logger.info(
      'FileMap finalized',
      {
        finalFileMapSize: Object.keys(fileMap).length,
      },
      schema,
    );

    const allItems = Object.values(fileMap).filter((i) => {
      if (!i) {
        logger.warn(
          'Filtering out null/undefined item from fileMap',
          null,
          schema,
        );
        return false;
      }
      const isFolder = i.folder && !i.file;
      const hasIngestableMimeType =
        i.file?.mimeType && ingestableMimeTypes.includes(i.file.mimeType);
      return isFolder || hasIngestableMimeType;
    });

    const nonSpreadSheets = allItems.filter(
      (i) =>
        !(
          i.file?.mimeType === MIME_TYPE.CSV ||
          i.file?.mimeType === MIME_TYPE.EXCEL ||
          i.file?.mimeType === MIME_TYPE.XLSX
        ),
    );

    const allSpreadSheets = allItems.filter(
      (i) =>
        i.file?.mimeType === MIME_TYPE.CSV ||
        i.file?.mimeType === MIME_TYPE.EXCEL ||
        i.file?.mimeType === MIME_TYPE.XLSX,
    );

    logger.info(
      'Items categorized for ingestion',
      {
        totalItems: allItems.length,
        nonSpreadSheets: nonSpreadSheets.length,
        spreadSheets: allSpreadSheets.length,
      },
      schema,
    );

    if (allSpreadSheets?.length > 0) {
      const validSpreadSheets = allSpreadSheets.filter((a) => a?.id);
      if (validSpreadSheets.length !== allSpreadSheets.length) {
        logger.warn(
          'Filtered out spreadsheets with missing IDs',
          {
            filteredCount: allSpreadSheets.length - validSpreadSheets.length,
            totalSpreadSheets: allSpreadSheets.length,
          },
          schema,
        );
      }

      const assetSheets = await getSharePointAssetSheetsByItemIds(
        schema,
        validSpreadSheets.map((a) => a.id),
      );
      const rfps: Rfp[] = [];
      for (const spreadSheet of validSpreadSheets) {
        if (!spreadSheet?.id) {
          logger.warn(
            'Skipping spreadsheet with no ID',
            { spreadSheet },
            schema,
          );
          continue;
        }

        const sheetConfigs = assetSheets.filter(
          (as) => as.item_id === spreadSheet.id,
        );

        if (!sheetConfigs || sheetConfigs.length === 0) {
          rfps.push({
            externalId: spreadSheet.id,
            docName: spreadSheet.name,
          } as Rfp);
          continue;
        }

        for (const config of sheetConfigs) {
          if (!config?.details) {
            logger.warn('Skipping config with no details', { config }, schema);
            continue;
          }

          rfps.push({
            externalId: spreadSheet.id,
            docName: config.details.doc_name,
            docTypeId: config.details.doc_type_id,
            sheetType: config.type,
            selectedSheetId: config.sheet_id,
            selectedSheetName: config.sheet_name,
            headerRow: config.details.header_row,
            questionColumns: config.details?.question_columns,
            answerColumns: config.details?.answer_columns,
            insightColumns: config.details?.insight_columns,
            // This isn't used anymore
            // firstRowIsHeader: config.details?.header_row === 0,
          });
        }
      }

      // TODO: batch here
      await setupIngestRfps(
        schema,
        userId,
        clientId,
        auth,
        rfps,
        fileMap,
        watchForChanges,
        true,
      );
    }

    for (let i = 0; i < nonSpreadSheets.length; i += BATCH_SIZE) {
      const batch = nonSpreadSheets.slice(i, i + BATCH_SIZE);

      for (let i = 0; i < batch.length; i++) {
        let partialItem = batch[i];
        if (
          !!partialItem.file &&
          !partialItem['@microsoft.graph.downloadUrl']
        ) {
          const fullItem = await getDriveItem(
            schema,
            userId,
            auth,
            partialItem.parentReference.driveId,
            partialItem.id,
          );

          if (partialItem.asset_settings) {
            fullItem.asset_settings = partialItem.asset_settings;
          }

          batch[i] = fullItem;
        }
      }

      setupIngestAssets(
        batch,
        schema,
        clientId,
        userId,
        watchForChanges,
        assetOnly,
      );

      await new Promise((resolve) => setTimeout(resolve, 1000));
    }

    // Return list (well, an object...) of files handled
    return fileMap;
  } catch (e) {
    logger.error('Error in setupIngestItems', { error: e }, schema);
    return {};
  }
};

export const setupIngestRfps = async (
  schema: string,
  userId: number,
  clientId: number,
  auth: MicrosoftAuth,
  rfps: Rfp[],
  fileMap: { [key: string]: Partial<M365DriveItem> },
  watchForChanges: boolean,
  assetOnly: boolean = false,
) => {
  const itemIds = [...new Set(rfps.map((r) => r.externalId))];
  let assets = await getAssetsByItemIds(schema, itemIds);

  const configuredSheets = rfps.filter(
    (r) =>
      !!r.sheetType &&
      (r.sheetType === ASSET_TYPE_SPREADSHEET ||
        (r.sheetType === ASSET_TYPE_RFP &&
          !!r.questionColumns?.length &&
          !!r.answerColumns?.length) ||
        (r.sheetType === ASSET_TYPE_INSIGHT && !!r.insightColumns?.length)),
  );

  if (configuredSheets.length > 0) {
    const newAssets = configuredSheets
      .filter((r) => !assets.find((a) => a.item_id === r.externalId))
      .map((r) => {
        return {
          item_id: r.externalId,
        };
      });

    // Create new assets for configured sheets
    const newM365Assets: { [k in string]: M365DriveAssetWithSettings } = {};
    for (const newAsset of newAssets) {
      const driveFile = fileMap[newAsset.item_id];
      if (driveFile && !newM365Assets[newAsset.item_id]) {
        let fileData;
        try {
          const fileDataResponse = await fetch(
            driveFile['@microsoft.graph.downloadUrl'],
          );

          if (!fileDataResponse.ok) {
            console.error(
              '[SharePoint ingestAssets] Error fetching file data',
              fileDataResponse.statusText,
            );
          }

          fileData = await fileDataResponse.arrayBuffer();
        } catch (err) {
          console.error(
            '[SharePoint ingestAssets] Error fetching file data',
            err,
          );
        }

        if (fileData) {
          const blob_id = uuidv4();
          const blobUrl = getBlobUrl(schema);
          const blobFileMetadata = {
            uuid: blob_id,
            buffer: Buffer.from(fileData),
          } as Partial<FileWithMetadata>;

          try {
            await uploadFilesToBlob(
              [blobFileMetadata],
              blobUrl,
              BlobContainer.DOCS,
            );

            // Get temporal data from SharePoint file
            const { createdDate, modifiedDate } =
              getFileTemporalData(driveFile);

            const newM365Asset = await upsertm365Assets(schema, [
              {
                item_id: newAsset.item_id,
                drive_id: driveFile.parentReference.driveId,
                name: driveFile.name,
                mime_type: driveFile.file?.mimeType || null,
                last_modified: driveFile.lastModifiedDateTime,
                url: driveFile.webUrl || null,
                file: driveFile.file,
                parent_reference: driveFile.parentReference,
                folder: driveFile.folder,
                blob_id,
                user_id: userId as any,
                created_at: new Date(),
                updated_at: new Date(),
                watch_for_changes: watchForChanges,
                // last_checked: new Date(),
                status: 'new',
                type: 'document',
                asset_settings: driveFile.asset_settings,
                source_created_date: createdDate,
                source_modified_date: modifiedDate,
              },
            ]);

            // Above returns an array
            newM365Assets[newM365Asset[0].item_id] = newM365Asset[0];
          } catch (err) {
            console.error(
              '[SharePoint ingestAssets] Error fetching file data',
              err,
            );
          }
        }
      }
    }

    // Refetch assets to get the new ones
    if (newAssets.length > 0) {
      assets = await getAssetsByItemIds(schema, itemIds, true);
    }

    const overwriteDocJobBlobIds = false;
    await upsertSharePointAssetSheets(
      schema,
      configuredSheets
        .filter((r) => assets.find((a) => a.item_id === r.externalId))
        .map((r) => {
          return {
            asset_id: Number(assets.find((a) => a.item_id === r.externalId).id),
            sheet_id: r.selectedSheetId,
            sheet_name: r.selectedSheetName,
            type: r.sheetType,
            details: {
              doc_name: r.docName,
              doc_type_id: r.docTypeId,
              header_row: r.headerRow,
              question_columns: r.questionColumns,
              answer_columns: r.answerColumns,
              insight_columns: r.insightColumns,
            },
          };
        }),
      overwriteDocJobBlobIds,
    );

    const processedM365Assets = {};
    Object.values(newM365Assets).forEach(async (newM365Asset) => {
      if (!processedM365Assets[newM365Asset.id]) {
        await sendRfpIngestRequest(
          newM365Asset,
          schema,
          userId,
          clientId,
          false,
        );

        processedM365Assets[newM365Asset.id] = newM365Asset;
      }
    });
  }

  if (assetOnly) {
    const unconfiguredSheets = rfps.filter(
      (rfp) =>
        !rfp.sheetType ||
        (rfp.sheetType === ASSET_TYPE_RFP &&
          (!rfp.questionColumns?.length || !rfp?.answerColumns?.length)) ||
        (rfp.sheetType === ASSET_TYPE_INSIGHT && !rfp.insightColumns?.length),
    );

    if (unconfiguredSheets.length) {
      const unconfiguredAssets = await upsertm365AssetsUnconfiguredSheets(
        schema,
        unconfiguredSheets.map((rfp) => {
          const driveFile = fileMap[rfp.externalId];
          return {
            item_id: rfp.externalId,
            drive_id: driveFile.parentReference.driveId,
            name: rfp.docName,
            mime_type: driveFile.file?.mimeType || null,
            parent_reference: driveFile.parentReference,
            watch_for_changes: watchForChanges,
            blob_id: null,
            status: 'new',
            type: '',
            user_id: userId as any,
            url: driveFile.webUrl || null,
            last_modified: driveFile.lastModifiedDateTime,
            created_at: driveFile.createdDateTime,
            file: driveFile.file,
            asset_settings: driveFile.asset_settings,
          };
        }),
      );
      await createReviewTaskAndNotification(
        unconfiguredAssets,
        schema,
        userId,
        clientId,
      );
    }
    return;
  }
};

/**
 * Call with the full m365 item object
 * Files must contain download Urls if watchForChanges is true
 * If watchForChanges is false DB records are upserted only
 * @param items
 * @param schema
 * @param userId
 * @param watchForChanges
 */
const setupIngestAssets = async (
  items: Partial<M365DriveItem>[],
  schema: string,
  clientId: number,
  userId: number,
  watchForChanges: boolean,
  assetOnly: boolean = false,
) => {
  const updated_at = new Date();
  const blobUrl = getBlobUrl(schema);

  // Upsert the folders first
  const folders = items.filter((a) => a.folder && !a.file);
  if (folders.length > 0) {
    console.log(
      `[SharePoint setupIngestAssets] upserting ${folders.length} folders`,
    );

    await upsertm365Assets(
      schema,
      folders.map((asset) => {
        return {
          item_id: asset.id,
          drive_id: asset.parentReference.driveId,
          name: asset.name,
          mime_type: MIME_TYPE.SHAREPOINT_FOLDER,
          last_modified: asset.lastModifiedDateTime,
          url: asset.webUrl,
          file: asset.file,
          parent_reference: asset.parentReference,
          // last_checked: new Date('1997-08-29'),
          folder: asset.folder,
          user_id: userId as UserId,
          created_at: updated_at,
          updated_at,
          watch_for_changes: watchForChanges,
          type: '', //todo: more coming
          status: 'new',
          asset_settings: asset.asset_settings,
        };
      }),
    );
  }

  const files = items.filter((a) => !!a.file);
  console.log(
    `[SharePoint ingestAssets] batch ingesting ${files.length} files`,
  );

  const assets: NewM365driveAsset[] = [];
  const documentDocTypeId = await getDocumentDocType(schema);

  const existingAssets = await getAssetsByItemIds(
    schema,
    files.map((f) => f.id),
    true,
  );

  console.log(
    `[SharePoint ingestAssets] Found ${existingAssets.length} existing assets`,
  );

  for (const file of files) {
    if (!file['@microsoft.graph.downloadUrl']) {
      console.warn('No download URL found for file', file.name);
      continue;
    }

    const existingAsset = existingAssets.find((a) => a.item_id === file.id);

    if (existingAsset && !watchForChanges) {
      console.log(`[Sharepoint setupIngestAssets] Unwatching asset ${file.id}`);

      await unwatchFileById(schema, existingAsset.id);

      continue;
    }

    if (existingAsset?.last_checked > new Date(file.lastModifiedDateTime)) {
      console.log(
        `[SharePoint ingestAssets] Skipping asset ${file.id} because it was last checked at ${existingAsset.last_checked} and the file was last modified at ${file.lastModifiedDateTime}`,
      );

      // Make sure assets are watched (in case of watch->unwatch->watch switcheroo)
      if (watchForChanges) {
        await watchFileById(schema, existingAsset.id);
      }

      continue;
    }

    if (watchForChanges || assetOnly) {
      let fileData;
      try {
        const fileDataResponse = await fetch(
          file['@microsoft.graph.downloadUrl'],
        );

        if (!fileDataResponse.ok) {
          console.error(
            '[SharePoint ingestAssets] Error fetching file data',
            fileDataResponse.statusText,
          );
          continue;
        }

        fileData = await fileDataResponse.arrayBuffer();
      } catch (err) {
        console.error(
          '[SharePoint ingestAssets] Error fetching file data',
          err,
        );
        continue;
      }

      let job_id, document_id: DocumentId;
      const blob_id = uuidv4();

      const fileMimeType = file.file?.mimeType;
      const isSpreadsheet = [
        MIME_TYPE.EXCEL,
        MIME_TYPE.XLSX,
        MIME_TYPE.CSV,
      ].includes(fileMimeType);

      // Each configured spreadsheet sheet writes its own job/doc records
      if (!isSpreadsheet) {
        const newDocJob = await insertPartialJobRecord(
          'ingest',
          new Date(),
          userId,
          schema,
          'new',
        );
        job_id = newDocJob.id;

        let prior_version_document_id;
        if (existingAsset) {
          prior_version_document_id = existingAsset?.document_id;
        }

        let docMetadataJson = {
          externalDocSource: 'sharepoint',
        };

        if (file.asset_settings?.metadata_tags) {
          docMetadataJson = {
            ...docMetadataJson,
            ...createRememberTagsJson(
              file.asset_settings
                ?.metadata_tags as unknown as Partial<MetadataFilterValue>[],
            ),
          };
        }

        const newDoc = await insertPartialDocRecord(
          newDocJob.id,
          file.name,
          file.name,
          blob_id,
          file.asset_settings?.privacy ?? 'public',
          new Date(),
          userId,
          schema,
          documentDocTypeId.id,
          prior_version_document_id,
          'processing',
          docMetadataJson,
          file.webUrl,
          mime.extension(file.file.mimeType),
          '', // description
        );
        document_id = newDoc.id;
      }

      const blobFileMetadata = {
        uuid: blob_id,
        buffer: Buffer.from(fileData),
      } as Partial<FileWithMetadata>;

      await uploadFilesToBlob([blobFileMetadata], blobUrl, BlobContainer.DOCS);

      // Get temporal data from SharePoint file
      const { createdDate, modifiedDate } = getFileTemporalData(file);

      const asset = {
        item_id: file.id,
        drive_id: file.parentReference.driveId,
        name: file.name,
        mime_type: file.file?.mimeType,
        last_modified: file.lastModifiedDateTime,
        url: file.webUrl,
        file: file.file,
        parent_reference: file.parentReference,
        folder: file.folder,
        blob_id,
        user_id: userId as UserId,
        created_at: new Date(),
        updated_at: new Date(),
        watch_for_changes: watchForChanges,
        // last_checked: new Date(),
        status: 'new',
        job_id: isSpreadsheet ? null : job_id, // these get written to asset sheet
        document_id: isSpreadsheet ? null : document_id, // these get written to asset sheet
        type: 'document', // other types coming in next PR
        asset_settings: file.asset_settings,
        source_created_date: createdDate,
        source_modified_date: modifiedDate,
      };

      assets.push(asset);

      const newAsset = await upsertm365Assets(schema, [asset], !isSpreadsheet);

      if (newAsset && newAsset.length > 0) {
        try {
          if (
            asset.mime_type === MIME_TYPE.DOCX ||
            asset.mime_type === MIME_TYPE.PPTX ||
            asset.mime_type === MIME_TYPE.PDF
          ) {
            await sendIngestRequest(
              newAsset[0],
              schema,
              clientId,
              !!existingAssets.find((a) => a.item_id === newAsset[0].item_id),
            );
          } else if (
            asset.mime_type === MIME_TYPE.EXCEL ||
            asset.mime_type === MIME_TYPE.XLSX ||
            asset.mime_type === MIME_TYPE.CSV
          ) {
            await sendRfpIngestRequest(
              newAsset[0],
              schema,
              userId,
              clientId,
              !!existingAssets.find((a) => a.item_id === newAsset[0].item_id),
            );
          } else {
            console.log(
              `[SharePoint ingestAssets] Skipping asset ${newAsset[0].item_id} with mime type ${newAsset[0].mime_type}`,
            );
          }
          await updateAssets(schema, {
            [newAsset[0].id]: {
              last_checked: new Date(),
            },
          });

          // Short pause to help w bursty requests
          await new Promise((resolve) => setTimeout(resolve, 250));
        } catch (err) {
          console.error(
            `[SharePoint ingestAssets] Error sending ingest request for ${newAsset[0].item_id} (job id=${job_id}): ${err.message}`,
          );
        }
      }
    }
  }

  console.log(
    `[SharePoint ingestAssets] watchForChanges (${watchForChanges}): ingesting ${assets.length} assets`,
  );
};

const sendRfpIngestRequest = async (
  asset: M365DriveAssetWithSettings,
  schema: string,
  userId: number,
  clientId: number,
  isUpdate: boolean,
) => {
  const assetSheets = await getSharePointAssetSheetsByAssetId(schema, asset.id);
  if (assetSheets.length === 0) {
    console.warn(
      `[ sendRfpIngestRequest ] No asset sheets found for asset ${asset.id}`,
    );
  }

  for (const sheet of assetSheets) {
    const reqs = [];

    const blobUrl = getBlobUrl(schema);
    const blobBuffer = await downloadBlob(
      blobUrl,
      BlobContainer.DOCS,
      asset.blob_id,
    );

    const fileWithMetadata: FileWithMetadata = {
      file_name: asset.name,
      label: sheet.details.doc_name,
      file_mimetype: MIME_TYPE.CSV,
      buffer: blobBuffer,
      uuid: asset.blob_id,
      privacy: asset.asset_settings?.privacy ?? 'public',
      typeId: sheet.details.doc_type_id,
      typeIsRfp: sheet.type === ASSET_TYPE_RFP,
      typeIsStructured: sheet.type === ASSET_TYPE_SPREADSHEET,
      typeIsInsight: sheet.type === ASSET_TYPE_INSIGHT,
      created_by_id: asset.user_id,
      created_date: new Date(),
      job_id: asset.job_id,
      use_for_generation: true,
      status: 'processing',
      source_url: asset.url,
      sheetId: sheet.sheet_id,
      externalDocSource: 'sharepoint',
    };

    if (sheet.type === ASSET_TYPE_RFP) {
      fileWithMetadata.headerRow = sheet.details.header_row;
      fileWithMetadata.questionColumns = sheet.details.question_columns;
      fileWithMetadata.answerColumns = sheet.details.answer_columns;
    }

    if (sheet.type === ASSET_TYPE_INSIGHT) {
      fileWithMetadata.insightColumns = sheet.details.insight_columns;
    }

    const sheetJob = await writePartialJobRecord(
      [fileWithMetadata],
      userId,
      schema,
      sheet.type === ASSET_TYPE_RFP ? 'ingest' : 'digest',
    );

    fileWithMetadata.job_id = sheetJob;
    fileWithMetadata.prior_version_document_id = sheet.document_id;

    if (asset.asset_settings) {
      fileWithMetadata.always_tag_metadata = asset.asset_settings
        .metadata_tags as unknown as MetadataFilterValue[];
    }

    const sheetDoc = await insertDocs([fileWithMetadata], schema);
    sheet.blob_id = asset.blob_id;
    sheet.document_id = sheetDoc[0].id;
    sheet.job_id = sheetJob;

    if (asset.mime_type === MIME_TYPE.CSV) {
      await upsertSharePointAssetSheets(schema, [sheet]);
      if (sheet.type === ASSET_TYPE_RFP || sheet.type === ASSET_TYPE_INSIGHT) {
        reqs.push(
          createAddQuestionnaireSheetRequest(
            clientId,
            schema,
            asset,
            sheet,
            isUpdate,
            MimeType.MIME_TYPE_CSV,
          ),
        );
      } else if (sheet.type === ASSET_TYPE_SPREADSHEET) {
        const payload: StructuredFileRequest & { is_structured: boolean } = {
          blob_id: sheet.blob_id,
          client_id: clientId,
          document_id: String(sheet.document_id),
          label: fileWithMetadata.file_name,
          mime_type: fileWithMetadata.file_mimetype,
          schema,
          is_structured: true,
          job_id: fileWithMetadata.job_id,
          is_update: fileWithMetadata.prior_version_document_id !== undefined,
        };

        if (fileWithMetadata.sheetId) {
          payload.sheet_id = fileWithMetadata.sheetId;
        }

        send_function_request({
          client_id: clientId,
          user_id: userId,
          function_name: 'digest',
          job_id: fileWithMetadata.job_id,
          payload,
          schema,
        });
      }
    } else if (
      asset.mime_type === MIME_TYPE.EXCEL ||
      asset.mime_type === MIME_TYPE.XLSX
    ) {
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load(blobBuffer);

      try {
        const csvBuffer = await workbook.csv.writeBuffer({
          sheetId: sheet.sheet_id,
        });
        const fileData = Buffer.from(csvBuffer as ArrayBuffer);

        const sheetBlobId = uuidv4();
        sheet.blob_id = sheetBlobId;
        fileWithMetadata.buffer = fileData;
        fileWithMetadata.uuid = sheet.blob_id;

        await upsertSharePointAssetSheets(schema, [sheet]);

        await uploadFilesToBlob(
          [fileWithMetadata],
          blobUrl,
          BlobContainer.DOCS,
        );

        if (
          sheet.type === ASSET_TYPE_RFP ||
          sheet.type === ASSET_TYPE_INSIGHT
        ) {
          reqs.push(
            createAddQuestionnaireSheetRequest(
              clientId,
              schema,
              asset,
              sheet,
              isUpdate,
              MimeType.MIME_TYPE_CSV,
            ),
          );
        } else if (sheet.type === ASSET_TYPE_SPREADSHEET) {
          const payload: StructuredFileRequest & {
            is_structured: boolean;
          } = {
            blob_id: fileWithMetadata.uuid,
            client_id: clientId,
            document_id: String(fileWithMetadata.id), // why does doc_id need to be a string...
            label: fileWithMetadata.file_name,
            mime_type: fileWithMetadata.file_mimetype,
            schema,
            is_structured: true,
            job_id: fileWithMetadata.job_id,
            is_update: isUpdate,
          };
          if (fileWithMetadata.sheetId) {
            payload.sheet_id = fileWithMetadata.sheetId;
          }
          send_function_request({
            client_id: clientId,
            user_id: asset.user_id,
            function_name: 'digest',
            job_id: fileWithMetadata.job_id,
            payload,
            schema,
          });
        }
      } catch (error) {
        console.error(
          `Failed to load workbook for SharePoint Asset Sheet Id=[${sheet.id}]: ${error.message}`,
        );
        return;
      }

      const ingestClient = getGrpcBrainIngestClient();

      // Send brain ingest request.
      // If separate tabs, each gets their own request (distinct doc_id/job_id)
      for (const req of reqs) {
        const deadline = new Date(),
          timeoutSecs = 5;
        deadline.setSeconds(deadline.getSeconds() + timeoutSecs);

        ingestClient.addQuestionnaire(
          req,
          new grpc.Metadata(),
          { deadline: deadline.getTime() },
          (err: Error, _) => {
            if (err) {
              // throw new Error(
              //   `Failed to ingest document ${asset.drive_id}: ${err.message}`,
              // );
              console.error(
                `Failed to ingest document ${asset.drive_id}: ${err.message}`,
              );
            }
          },
        );
      }
    }
  }
};

function createAddQuestionnaireSheetRequest(
  clientId: number,
  schema: string,
  asset: M365DriveAssetWithSettings,
  assetSheet: AssetSheet,
  isUpdate: boolean,
  assetMimeType: MimeType,
) {
  const req = new AddQuestionnaireRequest();
  req.setClientId(clientId);
  req.setSchema(schema);
  req.setUserId(asset.user_id);
  req.setDocumentId(assetSheet.document_id);
  req.setJobId(assetSheet.job_id);
  req.setMimeType(assetMimeType);
  req.setFilename(asset.name);
  req.setBlobName(assetSheet.blob_id);
  req.setIsUpdate(isUpdate);

  if (asset.asset_settings?.metadata_tags) {
    req.setAlwaysTagMetadataList(
      (
        asset.asset_settings?.metadata_tags as unknown as MetadataFilterValue[]
      ).map((t) => {
        const grpcMdfv = new MetadataFilterValueIngest();
        grpcMdfv.setId(t.id);
        grpcMdfv.setTypeId(t.type_id);
        grpcMdfv.setValue(t.value);
        return grpcMdfv;
      }),
    );
  }

  if (asset.last_modified) {
    const lastModified = new timestamppb.Timestamp();
    lastModified.fromDate(
      typeof asset.last_modified === 'string'
        ? new Date(asset.last_modified)
        : asset.last_modified,
    );
    req.setLastModifiedTime(lastModified);
  }

  // Set source dates for temporal filtering
  if (asset.source_created_date) {
    const sourceCreated = new timestamppb.Timestamp();
    sourceCreated.fromDate(
      typeof asset.source_created_date === 'string'
        ? new Date(asset.source_created_date)
        : asset.source_created_date,
    );
    req.setSourceCreatedDate(sourceCreated);
  }

  if (asset.source_modified_date) {
    const sourceModified = new timestamppb.Timestamp();
    sourceModified.fromDate(
      typeof asset.source_modified_date === 'string'
        ? new Date(asset.source_modified_date)
        : asset.source_modified_date,
    );
    req.setSourceModifiedDate(sourceModified);
  }

  const sourceInfo = new DocumentSourceInfo();
  sourceInfo.setSource(DocumentSource.SHAREPOINT);
  sourceInfo.setSourceId(asset.drive_id);
  sourceInfo.setSourceLocation(asset.url);
  req.setSourceInfo(sourceInfo);

  // RFP-specific values.
  // req.setFirstRowIsHeader(asset.rfp_details!.first_row_is_header);
  req.setHeaderRowIndex(assetSheet.details.header_row);
  for (const col of assetSheet.details.question_columns) {
    req.addQuestionColumnsIndices(col);
  }
  for (const col of assetSheet.details.answer_columns) {
    req.addAnswerColumnsIndices(col);
  }

  // Insight-specific values.
  for (const col of assetSheet.details.insight_columns) {
    req.addInsightColumnsIndices(col);
  }

  return req;
}

const sendIngestRequest = async (
  asset: M365DriveAssetWithSettings,
  schema: string,
  clientId: number,
  isUpdate: boolean,
) => {
  const ingestClient = getGrpcBrainIngestClient();

  const req = new AddDocumentRequest();
  req.setClientId(clientId);
  req.setSchema(schema);
  req.setUserId(asset.user_id);
  req.setDocumentId(Number(asset.document_id));
  req.setJobId(asset.job_id);
  req.setMimeType(GrpcMimeTypeMap[asset.mime_type]);
  req.setFilename(asset.name);
  req.setBlobName(asset.blob_id);
  req.setIsUpdate(isUpdate);

  if (asset.asset_settings?.metadata_tags) {
    req.setAlwaysTagMetadataList(
      (
        asset.asset_settings?.metadata_tags as unknown as MetadataFilterValue[]
      ).map((t) => {
        const grpcMdfv = new MetadataFilterValueIngest();
        grpcMdfv.setId(t.id);
        grpcMdfv.setTypeId(t.type_id);
        grpcMdfv.setValue(t.value);
        return grpcMdfv;
      }),
    );
  }

  if (asset.last_modified) {
    const lastModified = new timestamppb.Timestamp();
    lastModified.fromDate(
      typeof asset.last_modified === 'string'
        ? new Date(asset.last_modified)
        : asset.last_modified,
    );
    req.setLastModifiedTime(lastModified);
  }

  // Set source dates for temporal filtering
  if (asset.source_created_date) {
    const sourceCreated = new timestamppb.Timestamp();
    sourceCreated.fromDate(
      typeof asset.source_created_date === 'string'
        ? new Date(asset.source_created_date)
        : asset.source_created_date,
    );
    req.setSourceCreatedDate(sourceCreated);
  }

  if (asset.source_modified_date) {
    const sourceModified = new timestamppb.Timestamp();
    sourceModified.fromDate(
      typeof asset.source_modified_date === 'string'
        ? new Date(asset.source_modified_date)
        : asset.source_modified_date,
    );
    req.setSourceModifiedDate(sourceModified);
  }
  const sourceInfo = new DocumentSourceInfo();
  sourceInfo.setSource(DocumentSource.SHAREPOINT);
  sourceInfo.setSourceId(asset.drive_id);
  sourceInfo.setSourceLocation(asset.url);
  req.setSourceInfo(sourceInfo);

  const deadline = new Date(),
    timeoutSecs = 5;
  deadline.setSeconds(deadline.getSeconds() + timeoutSecs);

  // Send brain ingest request.
  ingestClient.addDocument(
    req,
    new grpc.Metadata(),
    { deadline: deadline.getTime() },
    (err: Error, _) => {
      if (err) {
        // throw new Error(
        //   `Failed to ingest document ${asset.drive_id}: ${err.message}`,
        // );
        console.error(
          `[sendIngestRequest] Failed to ingest document ${asset.drive_id}: ${err.message}`,
        );
      }
    },
  );
};

export const searchFilesAndFolders = async (
  schema: string,
  userId: number,
  auth: MicrosoftAuth,
  siteId: string,
  searchTerm: string,
) => {
  const endpoint = `/sites/${siteId}/drive/root/search(q='${searchTerm}')`;

  const response = await callGraphApi(endpoint, userId, schema, auth, 1);

  return response;
};

export const createReviewTaskAndNotification = async (
  assets: Partial<M365driveAsset>[],
  schema: string,
  userId: number,
  clientId: number,
) => {
  console.log(`[ createReviewTaskAndNotification ] ${assets.length} assets`);
  const reviewTasks = await upsertMultipleAssetTasks(
    schema,
    assets.map((asset) => {
      return {
        assetId: Number(asset.id),
        label: `SharePoint Drive Folder Sync - A new spreadsheet was found: ${asset.name}`,
        priority: 1,
        status: 'not_started',
        description: `A new spreadsheet (${asset.name}) was found. Please configure it before the data can be loaded.`,
        created_by_id: userId as UserId,
        created_at: new Date(),
        origin: 'admin',
        type: constants.TASK_TYPE_CONFIGURE_ASSET_SHEET,
      };
    }),
    constants.TABLE_M365_DRIVE_ASSET,
  );

  if (reviewTasks && reviewTasks.length > 0) {
    const details = createSheetTaskNotificationDetails(
      reviewTasks,
      'Sharepoint',
      `${process.env.APP_PATH}/review/sharepoint`,
    );
    await upsertScheduledNotification({
      user_id: userId as UserId,
      client_id: clientId as ClientId,
      type: 'configure_sheet_asset',
      parent_id: reviewTasks[0],
      created_by: userId as UserId,
      details,
    });
    console.log(`[ createReviewTaskAndNotification ] Notification scheduled`);
  }
};
