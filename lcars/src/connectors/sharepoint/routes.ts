import {
  callbackPermissions,
  generateCustomToken,
  validateCustomToken,
  validateTokenClaims,
} from '@tribble/auth-helper';
import { M365driveAssetId } from '@tribble/tribble-db/clients/M365driveAsset';
import { TaskId } from '@tribble/tribble-db/clients/Task';
import { getDB } from '@tribble/tribble-db/db_ops';
import { MIME_TYPE } from '@tribble/types-shared';
import express, { NextFunction, Request, Response } from 'express';
import {
  getDriveChildren,
  getDriveItem,
  getFolderChildren,
  getLists,
  getSiteDrives,
  getSites,
  searchFilesAndFolders,
  setupIngestItems,
  setupIngestRfps,
  watchDirectory,
} from '.';
import { Rfp } from '..';
import { getClientById } from '../../db/clients';
import { deleteUserIntegration } from '../../db/integrations';
import { updateTaskStatus } from '../../db/tasks';
import { MetadataFilterValue } from '../../model';
import { UserRequest } from '../../utils/auth';
import {
  MicrosoftAuth,
  authCallbackUrl,
  getAuthUrl,
  getMicrosoftAccessTokenWithCode,
  getMicrosoftAuth,
  getSpIntegrationSystem,
} from './auth';
import {
  getAssets,
  getSharePointAssetSheetsByAssetId,
  getSharePointAssetSheetsByUserId,
  getSharePointAssetsWithTask,
  getWatchedAssets,
  unwatchFilesByUserId,
} from './db';
import { M365DriveItem, ParentReference, SiteDrive } from './types';

const { clientAdminCheck, m2mCheck } = require('../../utils/auth');
const { Readable } = require('stream');

const router = express.Router();

interface SharePointUserRequest extends UserRequest {
  auth: MicrosoftAuth;
}

const authCheck = async (
  req: Request & SharePointUserRequest,
  res: Response,
  next: NextFunction,
) => {
  const { schema, userDetail } = req.user;

  const userId = userDetail.id;

  try {
    const auth = await getMicrosoftAuth(schema, userId);
    if (!auth) {
      return res
        .status(401)
        .json({ message: 'SharePoint authentication not found' });
    }

    req.auth = auth;
  } catch (e) {
    console.error(`[ /connectors/sharepoint ] Auth Check ${e}`);
    return res.status(401).json({ error: 'Internal Server Error' });
  }

  next();
};

router.get(`/${authCallbackUrl}`, async (req: Request, res: Response) => {
  try {
    const code = req.query.code as string;
    const state = req.query.state as string;
    const error = req.query.error as string;
    const error_description = req.query.error_description as string;

    if (!code || !state || error || error_description) {
      if (error || error_description) {
        return res
          .status(200)
          .redirect(
            `/sources/add/integration/sharepoint?error=${error}&error_description=${error_description}`,
          );
      } else {
        console.warn(
          `[ ${authCallbackUrl} ] Auth Callback missing required query params`,
          { code, state },
        );

        const fallbackError =
          'There was an error during the authentication process: state or code parameters are missing';
        return res
          .status(200)
          .redirect(
            `/sources/add/integration/sharepoint?error=${fallbackError}`,
          );
      }
    }

    const isValid = await validateCustomToken(state);
    const claims = await validateTokenClaims(
      state,
      authCallbackUrl,
      callbackPermissions,
    );

    if (!isValid || !claims) {
      console.warn('Invalid state token');
      return res.status(401).send('Invalid state token');
    }

    const { schema, sub } = claims;

    const token = await getMicrosoftAccessTokenWithCode(
      Number(sub),
      code,
      schema,
    );

    if (!token) {
      console.error(
        `[ ${authCallbackUrl} ] Failed to get Microsoft access token`,
      );
      return res.status(500).send('Failed to get access token');
    }

    return res.status(200).redirect('/sources/add/integration/sharepoint');
  } catch (error) {
    console.error(`[ ${authCallbackUrl} ] auth callback error`, error);
    return res.status(500).redirect('/error');
  }
});

router.get(
  `/connectors/sharepoint/auth`,
  clientAdminCheck,
  async (req: Request & UserRequest, res: Response) => {
    try {
      const { schema, userDetail } = req.user;

      const userId = userDetail.id;

      try {
        const auth = await getMicrosoftAuth(schema, userId);
        if (!!auth) {
          return res.status(200).json({ success: true, hasAuth: true });
        }
      } catch (e) {
        console.error(
          '[ /connectors/sharepoint/auth ]Error getting Microsoft Auth - Check integration',
          e,
        );
        return res.status(400).json({
          success: false,
          hasAuth: false,
          error: 'Error validating SharePoint auth',
        });
      }

      const { token } = await generateCustomToken(
        userDetail.id,
        callbackPermissions,
        userDetail.email,
        [authCallbackUrl],
        schema,
        600000,
      );

      const authUrl = await getAuthUrl(token, schema);

      return res.status(200).json({
        success: true,
        authUrl,
      });
    } catch (error) {
      console.error('[ /connectors/sharepoint/auth ] auth error', error);
      return res.status(500).json({
        success: false,
        error: 'Could not authenticate with SharePoint',
      });
    }
  },
);

router.post(
  '/connectors/sharepoint/disconnect',
  clientAdminCheck,
  authCheck,
  async (req: Request & SharePointUserRequest, res: Response) => {
    try {
      const { schema, userDetail } = req.user;

      const userId = userDetail.id;

      const sharePoint = await getSpIntegrationSystem(schema, userId);

      await deleteUserIntegration(schema, userId, sharePoint.id);

      await unwatchFilesByUserId(schema, userId);

      return res.status(200).json({ success: true });
    } catch (e) {
      console.error(
        '[ /connectors/sharepoint/disconnect ] Error disconnecting',
        e,
      );
      return res
        .status(500)
        .json({ message: 'Error disconnecting SharePoint' });
    }
  },
);

router.get(
  '/connectors/sharepoint/sites',
  clientAdminCheck,
  authCheck,
  async (req: Request & SharePointUserRequest, res: Response) => {
    try {
      const { schema, userDetail } = req.user;
      const userId = userDetail.id;

      const auth = req.auth;
      const sites = await getSites(schema, userId, auth);

      return res.status(200).json({ success: true, sites: sites ?? [] });
    } catch (e) {
      console.error('[ /connectors/sharepoint/sites ] Error fetching sites', e);
      return res
        .status(500)
        .json({ message: 'Error fetching SharePoint sites' });
    }
  },
);

router.get(
  '/connectors/sharepoint/sites/:siteId/drives',
  clientAdminCheck,
  authCheck,
  async (req: Request & SharePointUserRequest, res: Response) => {
    const { schema, userDetail } = req.user;
    const { siteId } = req.params;

    try {
      const userId = userDetail.id;

      const auth = req.auth;

      const drives = await getSiteDrives(schema, userId, auth, siteId);

      return res.status(200).json({ success: true, drives: drives.value });
    } catch (e) {
      console.error(
        `[ /connectors/sharepoint/sites/:site/drives ] Error fetching drives for ${siteId}`,
        e,
      );
      return res
        .status(500)
        .json({ message: 'Error fetching SharePoint sites' });
    }
  },
);

router.get(
  '/connectors/sharepoint/drive/:driveId/children',
  clientAdminCheck,
  authCheck,
  async (req: Request & SharePointUserRequest, res: Response) => {
    try {
      console.log({ req: req.params });
      const { schema, userDetail } = req.user;
      const userId = userDetail.id;
      const { driveId } = req.params;

      const auth = req.auth;

      const items = await getDriveChildren(schema, userId, auth, driveId);

      console.log({ items });

      return res.status(200).json({ success: true, items });
    } catch (e) {
      console.error(
        '[ /connectors/sharepoint/drive/:driveId/children ] Error fetching SharePoint drives',
        e,
      );
      return res
        .status(500)
        .json({ message: 'Error fetching SharePoint drives' });
    }
  },
);

router.get(
  '/connectors/sharepoint/drive/:driveId/items/:itemId',
  clientAdminCheck,
  authCheck,
  async (req: Request & SharePointUserRequest, res: Response) => {
    try {
      const { schema, userDetail } = req.user;
      const userId = userDetail.id;
      const { driveId, itemId } = req.params;
      if (!driveId || !itemId) {
        return res.status(400).json({ message: 'Invalid driveId or itemId' });
      }

      const auth = req.auth;

      const response = await getDriveItem(
        schema,
        userId,
        auth,
        driveId,
        itemId,
      );

      return res.status(200).json({ success: true, item: response });
    } catch (e) {
      console.error(
        '[ /connectors/sharepoint/drive/:driveId/items/:itemId ] Error fetching SharePoint drives',
        e,
      );
      return res
        .status(500)
        .json({ message: 'Error fetching SharePoint drives' });
    }
  },
);

router.get(
  '/connectors/sharepoint/sites/:siteId/items/search',
  clientAdminCheck,
  authCheck,
  async (req: Request & SharePointUserRequest, res: Response) => {
    try {
      const { schema, userDetail } = req.user;
      const userId = userDetail.id;

      const { siteId } = req.params;
      const { searchTerm } = req.query;

      if (!searchTerm) {
        return res.status(400).json({ message: 'Missing search term' });
      }

      const auth = req.auth;

      const items = await searchFilesAndFolders(
        schema,
        userId,
        auth,
        siteId,
        searchTerm as string,
      );

      return res.status(200).json({ success: true, items: items.value });
    } catch (e) {
      console.error(
        '[ /connectors/sharepoint/sites/:siteId/items/search ] Error fetching SharePoint items',
        e,
      );
      return res
        .status(500)
        .json({ message: 'Error fetching SharePoint items' });
    }
  },
);

router.get(
  '/connectors/sharepoint/drive/:driveId/folder/:folderId/children',
  clientAdminCheck,
  authCheck,
  async (req: Request & SharePointUserRequest, res: Response) => {
    try {
      const { schema, userDetail } = req.user;
      const userId = userDetail.id;
      const { driveId, folderId } = req.params;

      const auth = req.auth;

      const items = await getFolderChildren(
        schema,
        userId,
        auth,
        driveId,
        folderId,
      );

      return res.status(200).json({ success: true, items: items });
    } catch (e) {
      console.error(
        '[ /connectors/sharepoint/drive/:driveId/folder/:folderId/children ]Error fetching SharePoint drives',
        e,
      );
      return res
        .status(500)
        .json({ message: 'Error fetching SharePoint drives' });
    }
  },
);

router.get(
  '/connectors/sharepoint/sites/:siteId/lists',
  clientAdminCheck,
  authCheck,
  async (req: Request & SharePointUserRequest, res: Response) => {
    try {
      const { schema, userDetail } = req.user;
      const userId = userDetail.id;
      const { siteId } = req.params;

      const auth = req.auth;

      const assets = await getLists(schema, userId, auth, siteId);

      return res.status(200).json({ assets });
    } catch (e) {
      console.error(
        '[ /connectors/sharepoint/sites/:siteId/lists ] Error fetching SharePoint lists',
        e,
      );
      return res
        .status(500)
        .json({ message: 'Error fetching SharePoint lists' });
    }
  },
);

router.get(
  `/connectors/sharepoint/assetsheets/:assetId`,
  clientAdminCheck,
  async (req: Request & SharePointUserRequest, res: Response) => {
    try {
      const { schema } = req.user;
      const { assetId } = req.params;

      const assetSheets = await getSharePointAssetSheetsByAssetId(
        schema,
        assetId as M365driveAssetId,
      );

      res.json(
        assetSheets.map((sheet) => {
          // Need to map into the front end's SingleSheetConfig interface type
          return {
            ...sheet,
            sheetId: sheet.sheet_id,
            sheetName: sheet.sheet_name,
            sheetType: sheet.type,
            docName: sheet.details?.doc_name ?? '',
            docTypeId: sheet.details?.doc_type_id ?? -1,
            headerRow: sheet.details?.header_row ?? -1,
            questionColumns: sheet.details?.question_columns ?? [],
            answerColumns: sheet.details?.answer_columns ?? [],
          };
        }),
      );
    } catch (e) {
      console.error(`[ /connectors/sharepoint/assetsheets/:assetId ] ${e}`);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  },
);

router.get(
  '/connectors/sharepoint/assets',
  clientAdminCheck,
  authCheck,
  async (req: Request & SharePointUserRequest, res: Response) => {
    try {
      const { schema } = req.user;

      const assets = await getAssets(schema);

      res.status(200).json({ success: true, assets });
    } catch (e) {
      console.error(`[ /connectors/sharepoint/assets ] ${e}`);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  },
);

router.post(
  '/connectors/sharepoint/assets',
  clientAdminCheck,
  authCheck,
  async (req: Request & SharePointUserRequest, res: Response) => {
    try {
      const { schema, userDetail } = req.user;
      const {
        files,
        watchForChanges,
        drive,
        folder,
        rfps,
        assetOnly,
        taskId,
        driveItem,
        metadataTags,
        privacy,
      } = req.body as {
        files: Partial<M365DriveItem>[];
        rfps: Rfp[];
        drive: SiteDrive;
        folder: M365DriveItem;
        watchForChanges: boolean;
        assetOnly: boolean;
        taskId: number;
        driveItem: M365DriveItem;
        metadataTags: MetadataFilterValue[];
        privacy: string;
      };

      const userId = userDetail.id;
      const auth = req.auth;

      if (!!drive || !!folder) {
        const result = await watchDirectory(
          schema,
          userId,
          userDetail.client_id,
          auth,
          watchForChanges,
          drive,
          folder,
          {
            metadata_tags: metadataTags.map((tag) => {
              return {
                id: tag.id,
                type_id: tag.type_id,
                value: tag.value,
              };
            }) as any,
            privacy,
          },
        );
        if (!result) {
          return res.status(500).json({ error: 'Error watching directory' });
        }
        return res.status(201).json({ success: true });
      }

      if (!!files && files.length > 0) {
        await setupIngestItems(
          schema,
          userId,
          userDetail.client_id,
          auth,
          files,
          watchForChanges,
          assetOnly,
        );
      }
      if (!!rfps && rfps.length > 0) {
        await setupIngestRfps(
          schema,
          userId,
          userDetail.client_id,
          auth,
          rfps,
          {
            [driveItem.id]: driveItem,
          }, // No need for fileMap param
          watchForChanges,
          assetOnly,
        );
      }
      if (taskId) {
        // If this was part of a task, mark it as complete
        const db = await getDB(schema);
        await updateTaskStatus(db, taskId as TaskId, 'completed');
      }

      res.status(201).json({ success: true });
    } catch (e) {
      console.error(`[/connectors/sharepoint/assets] ${e}`);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  },
);

router.post(
  '/clients/:clientId/connectors/sharepoint/sync',
  m2mCheck,
  async (req: Request & UserRequest, res: Response) => {
    let clientId;
    try {
      clientId = parseInt(req.params.clientId, 10);

      if (isNaN(clientId)) {
        return res.status(400).json({ error: 'Invalid client ID' });
      }
      const client = await getClientById(clientId);
      if (!client) {
        return res.status(404).json({ error: 'Client not found' });
      }

      const fileAssets = await getWatchedAssets(client.database_schema_id);
      if (!fileAssets) {
        console.log(
          `[/clients/${clientId}/connectors/sharepoint/sync] No file assets found for client: ${client.database_schema_id}`,
        );
        return res.status(204);
      }

      const userIds = [...new Set(fileAssets.map((asset) => asset.user_id))];

      console.log(
        `[/clients/${clientId}/connectors/sharepoint/sync] ${fileAssets.length} files across ${userIds.length} users`,
      );

      for (const userId of userIds) {
        const assets = fileAssets.filter((asset) => asset.user_id === userId);

        const configuredSheets = await getSharePointAssetSheetsByUserId(
          client.database_schema_id,
          clientId,
          userId,
        );

        const auth = await getMicrosoftAuth(client.database_schema_id, userId);
        if (!auth) {
          console.warn(
            `[/clients/${clientId}/connectors/sharepoint/sync] No auth found for user: ${userId}`,
          );

          await unwatchFilesByUserId(client.database_schema_id, userId);

          continue;
        }

        const fileMapProcessed = await setupIngestItems(
          client.database_schema_id,
          userId,
          clientId,
          auth,
          assets
            .filter(
              (a) =>
                ![MIME_TYPE.CSV, MIME_TYPE.EXCEL, MIME_TYPE.XLSX].includes(
                  a.mime_type,
                ),
            )
            .map((asset) => {
              return {
                id: asset.item_id,
                parentReference: asset.parent_reference as ParentReference,
                file: asset.file as any,
                folder: asset.folder as any,
                asset_settings: asset.asset_settings,
              };
            }),
          true,
        );

        await setupIngestRfps(
          client.database_schema_id,
          userId,
          client.id,
          auth,
          configuredSheets
            .map((s) => {
              const asset = assets.find((a) => Number(a.id) === s.asset_id);
              if (!asset) {
                console.warn(
                  `[/clients/${clientId}/connectors/sharepoint/sync] asset record not found for configured sheet: `,
                  s,
                );
                return null;
              }

              // Anything in fileMapProcessed was handled above in setupIngestItems()
              if (fileMapProcessed[asset.item_id]) {
                return null;
              }
              return {
                id: s.id,
                externalId: asset.item_id,
                docName: s.details?.doc_name,
                selectedSheetId: s.sheet_id,
                selectedSheetName: s.sheet_name,
                sheetType: s.type,
                docTypeId: s.details.doc_type_id,
                headerRow: s.details?.header_row,
                questionColumns: s.details?.question_columns,
                answerColumns: s.details?.answer_columns,
                // This isn't used anymore
                // firstRowIsHeader: s.details.header_row === 0,
              };
            })
            .filter((s) => !!s) as Rfp[],
          {}, // No need for fileMap param
          true,
        );
      }

      res.status(202).json({ success: true });
    } catch (e) {
      console.error(`[/clients/${clientId}/connectors/sharepoint/sync] ${e}`);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  },
);

router.get(
  '/connectors/sharepoint/assets/with-open-task',
  clientAdminCheck,
  async (req: Request & UserRequest, res: Response) => {
    const { schema } = req.user;
    try {
      const assets = await getSharePointAssetsWithTask(schema);
      const asset = assets;
      res.json(asset);
    } catch (error) {
      console.error('Error fetching Sharepoint Tasks:', error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  },
);

// Function to convert a web ReadableStream to a Node.js Readable stream
function streamToNodeReadable(stream) {
  const reader = stream.getReader();
  return new Readable({
    async read() {
      const { done, value } = await reader.read();
      if (done) {
        this.push(null); // Signal that the stream has ended
      } else {
        this.push(Buffer.from(value)); // Push data as a Buffer
      }
    },
  });
}

// Fetch a Sharepoint item blob via its itemUrl on behalf of the front-end (to avoid CORS)
router.post(
  '/connectors/sharepoint/assets/fetch',
  clientAdminCheck,
  async (req: Request & UserRequest, res: Response) => {
    const { itemUrl } = req.body as { itemUrl: string };

    try {
      const itemResp = await fetch(itemUrl);

      // Check if the response is successful
      if (!itemResp.ok) {
        throw new Error(`Failed to fetch blob. Status: ${itemResp.status}`);
      }

      // Set the appropriate content type from the response headers
      res.setHeader('Content-Type', itemResp.headers.get('content-type'));

      const nodeStream = streamToNodeReadable(itemResp.body);

      // Pipe the readable stream from the fetch itemResp to the client response
      nodeStream.pipe(res);

      // Handle errors in the stream
      nodeStream.on('error', (err) => {
        console.error('Error in streaming response:', err);
        res.status(500).send('Error fetching blob');
      });
    } catch (error) {
      console.error(
        `[/connectors/sharepoint/assets/fetch] Error fetching Sharepoint item blob [${itemUrl}]`,
        error,
      );
      res.status(500).json({ error: 'Internal Server Error' });
    }
  },
);

module.exports = router;
