import { decryptText, encryptText } from '@tribble/auth-helper';
import { get365Vars } from '@tribble/azure-helper';
import { constants } from '../../constants';
import {
  getIntegrationSystems,
  saveUserIntegration,
} from '../../db/integrations';
import { getClientSettingsAsUser } from '../../db/users';

// Deprecated: use getSpScopeForClient() instead
// const scope = `offline_access Sites.Selected User.Read`;

export const authCallbackUrl = 'connectors/sharepoint/auth/callback';
const baseUrl = process.env.TUNNEL_URL ?? process.env.APP_PATH;
const callbackUri = `${baseUrl}/api/${authCallbackUrl}`;

export type MicrosoftAuth = {
  token_type: string;
  scope: string;
  expires_in: number;
  ext_expires_in: number;
  access_token: string;
  refresh_token: string;
  expires_on: number;
};

type IntegrationSystem = {
  id: number;
  system_name: string;
  label: string;
  auth_details: string;
};

export const getSpScopeForClient = async (schema: string) => {
  // Client can either configure a "Sites.Read.All" or a "Sites.Selected"
  // implementation. The former is simpler, and just shows all sites from Sharepoint
  // for a client to select from. The latter is more restrictive and requires client
  // admin to grant access to specific sites before they can be accessed.

  // There will be 2 client settings:
  // - client_sharepoint_sites_restricted
  // - client_sharepoint_sites_allowlist

  // If the restricted is set, the the scopes will be "Sites.Selected"
  const clientSettings = await getClientSettingsAsUser(schema);
  const settingUseRestricted = clientSettings?.find(
    (s) => s.name === constants.SETTING_CLIENT_SHAREPOINT_SITES_RESTRICTED,
  );

  // Not restricted
  if (!settingUseRestricted || settingUseRestricted.value === 'false') {
    return 'offline_access User.Read Sites.Read.All';
  } else {
    return 'offline_access User.Read Sites.Selected';
  }
};

// Note: we should switch over to client_integration (vs. user_integration).
// But when we do this, we should do for a bunch of other systems (Google Drive, etc.)
export const getSpIntegrationSystem = async (
  schema: string,
  userId: number,
) => {
  const systems = (await getIntegrationSystems(
    schema,
    userId,
    true,
  )) as IntegrationSystem[];

  const sharePoint = systems.find((s) => s.system_name === 'sharepoint');
  if (!sharePoint) {
    console.warn('[ getIntegrationSystem ] No SharePoint integration found', {
      schema,
      userId,
    });
    throw new Error('No SharePoint integration found');
  }
  return sharePoint;
};

export const getMicrosoftAuth = async (schema: string, userId: number) => {
  const sharePoint = await getSpIntegrationSystem(schema, userId);

  if (!sharePoint.auth_details) {
    console.warn('[ getMicrosoftAuth ] No auth details found for user', {
      schema,
      userId,
    });
    return undefined;
  }

  const authString = await decryptText(sharePoint.auth_details);
  const auth = JSON.parse(authString) as MicrosoftAuth;

  if (!auth.access_token && !auth.refresh_token) {
    console.warn('[ getMicrosoftAuth ] No access and refresh token found', {
      schema,
      userId,
    });
    return undefined;
  }

  return auth;
};

export const getMicrosoftToken = async (
  userId: number,
  schema: string,
  auth: MicrosoftAuth,
) => {
  if (auth?.access_token && auth?.expires_on > Date.now() + 60000) {
    return auth.access_token;
  }

  if (
    auth?.access_token &&
    auth?.expires_on < Date.now() + 60000 &&
    auth?.refresh_token
  ) {
    console.log('[ getSharePointToken ] Access token expired, refreshing...');

    try {
      const sharePoint = await getSpIntegrationSystem(schema, userId);

      const newAuth = await refreshAuthToken(
        userId,
        schema,
        auth.refresh_token,
        sharePoint.id,
      );

      return newAuth.access_token;
    } catch (e) {
      console.warn('[ getSharePointToken ]', e);
      return undefined;
    }
  }

  console.warn('[ getSharePointToken ] No valid access token found', {
    userId,
    schema,
  });

  return undefined;
};

export const getAuthUrl = async (token: string, schema: string) => {
  const msftParams = await get365Vars();
  const scopes = await getSpScopeForClient(schema);

  const params = new URLSearchParams();
  params.append('client_id', msftParams.clientId);
  params.append('scope', scopes);
  params.append('response_type', 'code');
  params.append('redirect_uri', callbackUri);
  params.append('response_mode', 'query');
  params.append('state', token);

  // This doesn't work for multitenant:
  // https://learn.microsoft.com/en-us/sharepoint/dev/sp-add-ins-modernize/multi-tenant-applications

  // const authUrl = `https://login.microsoftonline.com/${msftParams.tenantId}/oauth2/v2.0/authorize?${params.toString()}`;
  const authUrl = `https://login.microsoftonline.com/organizations/oauth2/v2.0/authorize?${params.toString()}`;

  return authUrl;
};

const refreshAuthToken = async (
  userId: number,
  schema: string,
  refreshToken: string,
  systemId: number,
) => {
  const msftParams = await get365Vars();
  const scopes = await getSpScopeForClient(schema);

  const params = new URLSearchParams();
  params.append('client_id', msftParams.clientId);
  params.append('scope', scopes);
  params.append('grant_type', 'refresh_token');
  params.append('refresh_token', refreshToken);
  params.append('client_secret', msftParams.client_secret);

  return await fetchAndSaveToken(params, userId, schema, systemId);
};

export const getMicrosoftAccessTokenWithCode = async (
  userId: number,
  code: string,
  schema: string,
) => {
  const msftParams = await get365Vars();
  const scopes = await getSpScopeForClient(schema);

  const params = new URLSearchParams();
  params.append('client_id', msftParams.clientId);
  params.append('scope', scopes);
  params.append('code', code);
  params.append('redirect_uri', callbackUri);
  params.append('grant_type', 'authorization_code');
  params.append('client_secret', msftParams.client_secret);

  const result = await fetchAndSaveToken(params, userId, schema);

  return !!result;
};

const fetchAndSaveToken = async (
  params: URLSearchParams,
  userId: number,
  schema: string,
  systemId?: number,
) => {
  const msftParams = await get365Vars();

  // This doesn't work for multitenant:
  // https://learn.microsoft.com/en-us/sharepoint/dev/sp-add-ins-modernize/multi-tenant-applications

  const response = await fetch(
    // `https://login.microsoftonline.com/${msftParams.tenantId}/oauth2/v2.0/token`,
    `https://login.microsoftonline.com/organizations/oauth2/v2.0/token`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: params.toString(),
    },
  );

  if (!response.ok) {
    console.error('SharePoint auth callback error', response.statusText);
    return undefined;
  }

  const responseBody = (await response.json()) as MicrosoftAuth;
  responseBody.expires_on = Date.now() + responseBody.expires_in * 1000;

  const authDetails = await encryptText(JSON.stringify(responseBody));

  if (!systemId) {
    const integration = await getSpIntegrationSystem(schema, userId);
    systemId = integration.id;
  }

  await saveUserIntegration(schema, userId, systemId, authDetails);

  return responseBody;
};
