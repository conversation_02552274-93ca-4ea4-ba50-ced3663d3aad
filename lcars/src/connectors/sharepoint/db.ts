import { IntegrationAssetSetting } from '@tribble/tribble-db/clients/IntegrationAssetSetting';
import {
  M365driveAsset,
  M365driveAssetId,
  NewM365driveAsset,
} from '@tribble/tribble-db/clients/M365driveAsset';
import { getClient, query } from '@tribble/tribble-db/db_ops';
import { AssetSheet } from '..';
import { getIntegrationBySystemName } from '../../db/integrations';
import { MetadataFilterValue } from '../../model';

import { constants } from '../../constants';

export const getAssets = async (
  schema: string,
): Promise<M365DriveAssetWithSettings[]> => {
  const queryString = `
    SELECT 
      mda.*,
      json_build_object(
        'metadata_tags', COALESCE(ias.metadata_tags, null),
        'privacy', COALESCE(ias.privacy, null)
      ) as asset_settings            
    FROM ${schema}.${constants.TABLE_M365_DRIVE_ASSET} mda
    LEFT JOIN ${schema}.${constants.TABLE_INTEGRATION_ASSET_SETTING} ias
      ON mda.id = ias.asset_id    
    JOIN ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_INTEGRATION_SYSTEM} ins
      ON ias.integration_id = ins.id
      AND ins.system_name = '${constants.INTEGRATION_SYSTEM_SHAREPOINT}'    
    ;
  `;

  const result = await query(queryString);
  if (result && result.rows) {
    return result.rows as M365DriveAssetWithSettings[];
  }
  return [];
};

export const getSharePointAssetSheetsByAssetId = async (
  schema: string,
  assetId: M365driveAssetId,
): Promise<AssetSheet[]> => {
  const queryString = `
    SELECT ias.*
    FROM ${schema}.${constants.TABLE_INTEGRATION_ASSET_SHEET} ias
    INNER JOIN ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_INTEGRATION_SYSTEM} ins 
      ON ias.integration_id = ins.id
    WHERE ins.system_name = '${constants.INTEGRATION_SYSTEM_SHAREPOINT}'
      AND asset_id = $1`;

  const result = await query(queryString, [assetId]);

  return result && result.rows ? (result.rows as AssetSheet[]) : [];
};

interface AssetSheetWitemId extends AssetSheet {
  item_id: string;
}
export const getSharePointAssetSheetsByItemIds = async (
  schema: string,
  itemIds: string[],
): Promise<AssetSheetWitemId[]> => {
  if (itemIds.length === 0) {
    return [];
  }
  const placeholders = itemIds.map((_, i) => `$${i + 1}`).join(', ');
  const queryString = `
    SELECT ias.*, ma.item_id
    FROM ${schema}.${constants.TABLE_INTEGRATION_ASSET_SHEET} ias
    INNER JOIN ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_INTEGRATION_SYSTEM} ins 
      ON ias.integration_id = ins.id
    INNER JOIN ${schema}.${constants.TABLE_M365_DRIVE_ASSET} ma
      ON ias.asset_id = ma.id
    WHERE ins.system_name = '${constants.INTEGRATION_SYSTEM_SHAREPOINT}'
      AND ma.item_id IN (${placeholders})`;

  const result = await query(queryString, itemIds);

  return result && result.rows ? (result.rows as AssetSheetWitemId[]) : [];
};

export const getSharePointAssetSheetsByUserId = async (
  schema: string,
  clientId: number,
  userId: number,
) => {
  const queryString = `
    SELECT ias.*
    FROM ${schema}.${constants.TABLE_INTEGRATION_ASSET_SHEET} ias
    JOIN ${schema}.${constants.TABLE_M365_DRIVE_ASSET} ma
      ON ias.asset_id = ma.id
    JOIN ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_INTEGRATION_SYSTEM} ins 
      ON ias.integration_id = ins.id
    JOIN ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_USER} u 
      ON u.id = $1
      -- in case of user teleportation, need to match client id too
      AND u.client_id = $2
      AND u.id = ma.user_id`;

  const result = await query(queryString, [userId, clientId]);

  return result && result.rows ? (result.rows as AssetSheet[]) : [];
};

export const upsertSharePointAssetSheets = async (
  schema: string,
  assetSheets: Partial<AssetSheet>[],
  overwriteDocJobBlobIds = true,
): Promise<Partial<AssetSheet>[]> => {
  if (!assetSheets || assetSheets.length === 0) {
    return [];
  }
  const client = await getClient();

  try {
    await client.query('BEGIN');

    const sharePointIntegrationSystem = await getIntegrationBySystemName(
      constants.INTEGRATION_SYSTEM_SHAREPOINT,
    );
    const integrationId = sharePointIntegrationSystem.id;

    const valueCount = 9;
    const values = assetSheets
      .map((_, index) => {
        const placeholders = Array.from(
          { length: valueCount },
          (_, i) => `$${index * valueCount + i + 1}`,
        );
        return `(${placeholders.join(', ')})`;
      })
      .join(', ');

    const queryString = `
      INSERT INTO ${schema}.${constants.TABLE_INTEGRATION_ASSET_SHEET} (
        asset_id,
        document_id,
        job_id,
        blob_id,
        sheet_id,
        sheet_name,
        type,
        details,
        integration_id
      ) VALUES ${values}
      ON CONFLICT (asset_id, sheet_id, integration_id) DO UPDATE SET
      details = EXCLUDED.details,
      ${overwriteDocJobBlobIds ? ' blob_id = EXCLUDED.blob_id, document_id = EXCLUDED.document_id, job_id = EXCLUDED.job_id, ' : ''}
      type = EXCLUDED.type,
      sheet_name = EXCLUDED.sheet_name
      RETURNING *;
    `;

    const queryParams = [];
    assetSheets.forEach((asset) => {
      queryParams.push(
        asset.asset_id,
        asset.document_id,
        asset.job_id,
        asset.blob_id,
        asset.sheet_id,
        asset.sheet_name,
        asset.type,
        asset.details,
        integrationId,
      );
    });

    const result = await client.query(queryString, queryParams);
    await client.query('COMMIT');

    if (result && result.rows) {
      return result.rows.map((row: any) => ({
        id: row.id,
        asset_id: row.asset_id,
        document_id: row.document_id,
        job_id: row.job_id,
        blob_id: row.blob_id,
        sheet_id: row.sheet_id,
        sheet_name: row.sheet_name,
        type: row.type,
        details: row.details,
      }));
    }
  } catch (error) {
    console.error('Error upserting SharePoint asset sheets:', error);
    await client.query('ROLLBACK');
  } finally {
    client.release();
  }
  return [];
};

export type M365DriveAssetWithSettings = NewM365driveAsset & {
  asset_settings?: Partial<IntegrationAssetSetting>;
};

export const upsertm365Assets = async (
  schema: string,
  assets: M365DriveAssetWithSettings[],
  overwriteDocJobIds: boolean = false,
): Promise<M365DriveAssetWithSettings[]> => {
  if (assets.length === 0) return [];

  const valueCount = 20; // Number of columns in the M365 asset table
  const values = assets
    .map((_, index) => {
      const placeholders = Array.from(
        { length: valueCount },
        (_, i) => `$${index * valueCount + i + 1}`,
      );
      return `(${placeholders.join(', ')})`;
    })
    .join(', ');

  const queryString = `
      INSERT INTO ${schema}.${constants.TABLE_M365_DRIVE_ASSET} (
        item_id,
        drive_id,
        name,
        mime_type,
        last_modified,
        url,
        file,
        parent_reference,
        folder,
        blob_id,
        document_id,
        job_id,
        user_id,
        watch_for_changes,
        status,
        -- last_checked,
        created_at,
        updated_at,
        task_id,
        source_created_date,
        source_modified_date
      ) VALUES ${values}
      ON CONFLICT (item_id) DO UPDATE
      SET 
        user_id = EXCLUDED.user_id,
        last_checked = NOW(),
        blob_id = EXCLUDED.blob_id,
        ${overwriteDocJobIds ? ' document_id = EXCLUDED.document_id, job_id = EXCLUDED.job_id, ' : ''}
        updated_at = EXCLUDED.updated_at,
        watch_for_changes = EXCLUDED.watch_for_changes,
        parent_reference = EXCLUDED.parent_reference
      RETURNING *;
    `;

  const queryParams = [];
  const assetItemIdMap = {};

  assets.forEach((asset) => {
    assetItemIdMap[asset.item_id] = asset;

    queryParams.push(
      asset.item_id,
      asset.drive_id,
      asset.name,
      asset.mime_type,
      asset.last_modified,
      asset.url,
      asset.file,
      asset.parent_reference,
      asset.folder,
      asset.blob_id,
      asset.document_id,
      asset.job_id,
      asset.user_id,
      asset.watch_for_changes,
      asset.status,
      // asset.last_checked,
      asset.created_at,
      asset.updated_at,
      asset.task_id,
      asset.source_created_date,
      asset.source_modified_date,
    );
  });

  try {
    const result = await query(queryString, queryParams);
    if (result && result.rows) {
      const m365AssetRows = result.rows;
      const m365AssetIdMap = {};

      const sharePointIntegrationSystem = await getIntegrationBySystemName(
        constants.INTEGRATION_SYSTEM_SHAREPOINT,
      );
      const integrationId = sharePointIntegrationSystem.id;

      // If successful, and metadata or privacy settings are provided,
      // upsert them to settings table
      const settingsQueryParams = [];
      const settingsValueCount = 4; // Number of columns in the integration setting table
      const settingsValues = m365AssetRows
        .filter((row: M365driveAsset) => {
          m365AssetIdMap[row.id] = row;
          return assetItemIdMap[row.item_id].asset_settings;
        })
        .map((row: M365driveAsset, index) => {
          settingsQueryParams.push(
            integrationId,
            row.id,
            JSON.stringify(
              assetItemIdMap[row.item_id].asset_settings.metadata_tags,
            ),
            assetItemIdMap[row.item_id].asset_settings.privacy,
          );

          const placeholders = Array.from(
            { length: settingsValueCount },
            (_, i) => `$${index * settingsValueCount + i + 1}`,
          );
          return `(${placeholders.join(', ')})`;
        })
        .join(', ');

      const settingsQueryString = `
        INSERT INTO ${schema}.${constants.TABLE_INTEGRATION_ASSET_SETTING} (
          integration_id,
          asset_id,
          metadata_tags,
          privacy
        ) VALUES ${settingsValues}
        ON CONFLICT (integration_id, asset_id) DO UPDATE
        SET 
          metadata_tags = EXCLUDED.metadata_tags,
          privacy = EXCLUDED.privacy
        RETURNING *;
      `;

      if (settingsValues.trim().length > 0) {
        const settingsResult = await query(
          settingsQueryString,
          settingsQueryParams,
        );

        if (settingsResult && settingsResult.rows) {
          settingsResult.rows.forEach((row: IntegrationAssetSetting) => {
            m365AssetIdMap[row.asset_id].asset_settings = row;
          });
        }
      }

      return Object.values(m365AssetIdMap);
    }
  } catch (error) {
    console.error('[upsertm365Assets] Error upserting M365 assets:', error);
  }
  return [];
};

// Specifically called for M365 spreadsheets that have unconfigured sheets.
// If upserting, DO NOT set the last_checked date (to NOW()), otherwise when
// the sheet is eventually configured, it may not actually be ingested.
export const upsertm365AssetsUnconfiguredSheets = async (
  schema: string,
  assets: (NewM365driveAsset & {
    metadata_tags?: Partial<MetadataFilterValue>[];
  })[],
): Promise<M365driveAsset[]> => {
  if (assets.length === 0) return [];

  const valueCount = 20; // Number of columns in the M365 asset table
  const values = assets
    .map((_, index) => {
      const placeholders = Array.from(
        { length: valueCount },
        (_, i) => `$${index * valueCount + i + 1}`,
      );
      return `(${placeholders.join(', ')})`;
    })
    .join(', ');

  const queryString = `
      INSERT INTO ${schema}.${constants.TABLE_M365_DRIVE_ASSET} (
        item_id,
        drive_id,
        name,
        mime_type,
        last_modified,
        url,
        file,
        parent_reference,
        folder,
        blob_id,
        document_id,
        job_id,
        user_id,
        watch_for_changes,
        status,
        created_at,
        updated_at,
        task_id,
        source_created_date,
        source_modified_date
      ) VALUES ${values}
      ON CONFLICT (item_id) DO UPDATE
      SET 
        user_id = EXCLUDED.user_id,
        blob_id = EXCLUDED.blob_id,
        updated_at = EXCLUDED.updated_at,
        watch_for_changes = EXCLUDED.watch_for_changes,
        parent_reference = EXCLUDED.parent_reference
      RETURNING *;
    `;

  const queryParams = [];
  const assetItemIdMap = {};

  assets.forEach((asset) => {
    assetItemIdMap[asset.item_id] = asset;

    queryParams.push(
      asset.item_id,
      asset.drive_id,
      asset.name,
      asset.mime_type,
      asset.last_modified,
      asset.url,
      asset.file,
      asset.parent_reference,
      asset.folder,
      asset.blob_id,
      asset.document_id,
      asset.job_id,
      asset.user_id,
      asset.watch_for_changes,
      asset.status,
      asset.created_at,
      asset.updated_at,
      asset.task_id,
      asset.source_created_date,
      asset.source_modified_date,
    );
  });

  try {
    const result = await query(queryString, queryParams);
    if (result && result.rows) {
      const m365AssetRows = result.rows;
      const m365AssetIdMap = {};

      const sharePointIntegrationSystem = await getIntegrationBySystemName(
        constants.INTEGRATION_SYSTEM_SHAREPOINT,
      );
      const integrationId = sharePointIntegrationSystem.id;

      // If successful, and metadata or privacy settings are provided,
      // upsert them to settings table
      const settingsQueryParams = [];
      const settingsValueCount = 4; // Number of columns in the integration setting table
      const settingsValues = m365AssetRows
        .filter((row: M365driveAsset) => {
          m365AssetIdMap[row.id] = row;
          return assetItemIdMap[row.item_id].asset_settings;
        })
        .map((row: M365driveAsset, index) => {
          settingsQueryParams.push(
            integrationId,
            row.id,
            JSON.stringify(
              assetItemIdMap[row.item_id].asset_settings.metadata_tags,
            ),
            assetItemIdMap[row.item_id].asset_settings.privacy,
          );

          const placeholders = Array.from(
            { length: settingsValueCount },
            (_, i) => `$${index * settingsValueCount + i + 1}`,
          );
          return `(${placeholders.join(', ')})`;
        })
        .join(', ');

      const settingsQueryString = `
        INSERT INTO ${schema}.${constants.TABLE_INTEGRATION_ASSET_SETTING} (
          integration_id,
          asset_id,
          metadata_tags,
          privacy
        ) VALUES ${settingsValues}
        ON CONFLICT (integration_id, asset_id) DO UPDATE
        SET 
          metadata_tags = EXCLUDED.metadata_tags,
          privacy = EXCLUDED.privacy
        RETURNING *;
      `;

      if (settingsValues.trim().length > 0) {
        const settingsResult = await query(
          settingsQueryString,
          settingsQueryParams,
        );

        if (settingsResult && settingsResult.rows) {
          settingsResult.rows.forEach((row: IntegrationAssetSetting) => {
            m365AssetIdMap[row.asset_id].asset_settings = row;
          });
        }
      }

      return Object.values(m365AssetIdMap);
    }
  } catch (error) {
    console.error(
      '[upsertm365AssetsUnconfiguredSheets] Error inserting M365 assets:',
      error,
    );
  }
  return [];
};

export const getAssetById = async (
  schema: string,
  id: number,
): Promise<M365driveAsset> => {
  const queryString = `SELECT * FROM ${schema}.${constants.TABLE_M365_DRIVE_ASSET} WHERE id = $1;`;

  const result = await query(queryString, [id]);
  if (result && result.rows && result.rows.length > 0) {
    return result.rows[0] as M365driveAsset;
  }
  return undefined;
};

export const getAssetByDriveId = async (schema: string, driveId: string) => {
  const queryString = `SELECT * FROM ${schema}.${constants.TABLE_M365_DRIVE_ASSET} WHERE drive_id = $1;`;

  const result = await query(queryString, [driveId]);
  if (result && result.rows && result.rows.length > 0) {
    return result.rows[0] as M365driveAsset;
  }
  return undefined;
};

export const getAssetsByItemIds = async (
  schema: string,
  driveIds: string[],
  // This function is called by non-rfp ingest too, so set this to true (e.g. for PDFs, which don't have sheets)
  ignoreMissingSheetRecords: boolean = false,
): Promise<M365DriveAssetWithSettings[]> => {
  const queryString = `
    SELECT 
      mda.*,
      json_build_object(
        'metadata_tags', COALESCE(ias.metadata_tags, null),
        'privacy', COALESCE(ias.privacy, null)
      ) as asset_settings
    FROM ${schema}.${constants.TABLE_M365_DRIVE_ASSET} mda
    LEFT JOIN ${schema}.${constants.TABLE_INTEGRATION_ASSET_SETTING} ias
      ON mda.id = ias.asset_id    
    ${ignoreMissingSheetRecords ? 'LEFT' : ''} JOIN ${schema}.${constants.TABLE_INTEGRATION_ASSET_SHEET} sheet
      ON mda.id = sheet.asset_id
      AND sheet.integration_id = (
        SELECT id 
        FROM ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_INTEGRATION_SYSTEM} 
        WHERE system_name = '${constants.INTEGRATION_SYSTEM_SHAREPOINT}'
      ) 
    WHERE mda.item_id = ANY($1::text[])
  `;

  const result = await query(queryString, [driveIds]);
  if (result && result.rows && result.rows.length > 0) {
    return result.rows as M365DriveAssetWithSettings[];
  }
  return [];
};

export const getWatchedAssets = async (
  schema: string,
): Promise<M365DriveAssetWithSettings[]> => {
  const queryString = `
    SELECT 
      mda.*,
      json_build_object(
        'metadata_tags', COALESCE(ias.metadata_tags, null),
        'privacy', COALESCE(ias.privacy, null)
      ) as asset_settings
    FROM ${schema}.${constants.TABLE_M365_DRIVE_ASSET} mda
    LEFT JOIN ${schema}.${constants.TABLE_INTEGRATION_ASSET_SETTING} ias
      ON mda.id = ias.asset_id    
    JOIN ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_INTEGRATION_SYSTEM} ins
      ON ias.integration_id = ins.id
      AND ins.system_name = '${constants.INTEGRATION_SYSTEM_SHAREPOINT}'
    WHERE (file IS NOT NULL or folder IS NOT NULL)
    AND watch_for_changes = true;
  `;

  try {
    const result = await query(queryString);
    if (result && result.rows) {
      return result.rows as M365DriveAssetWithSettings[];
    }
    return [];
  } catch (error) {
    console.error(
      '[ sharepoint/getWatchedFileAssets ] Error querying file assets:',
      error,
    );
    // Empty case and error get handled differently by the caller
    throw error;
  }
};

export const unwatchFilesByUserId = async (schema: string, userId: number) => {
  const queryString = `UPDATE ${schema}.${constants.TABLE_M365_DRIVE_ASSET} 
      SET watch_for_changes = false
      WHERE user_id = $1;`;

  try {
    await query(queryString, [userId]);
  } catch (error) {
    console.error(
      '[ sharepoint/unwatchFilesByUserId ]Error unwatching files:',
      error,
    );
  }
};

export const watchFileById = async (schema: string, id: M365driveAssetId) => {
  const queryString = `UPDATE ${schema}.${constants.TABLE_M365_DRIVE_ASSET} 
      SET watch_for_changes = true
      WHERE id = $1;`;

  try {
    await query(queryString, [id]);
  } catch (error) {
    console.error(
      `[sharepoint/watchFilesById] Error watching file [${id}]: `,
      error,
    );
  }
};

export const unwatchFileById = async (schema: string, id: M365driveAssetId) => {
  const queryString = `UPDATE ${schema}.${constants.TABLE_M365_DRIVE_ASSET} 
      SET watch_for_changes = false
      WHERE id = $1;`;

  try {
    await query(queryString, [id]);
  } catch (error) {
    console.error(
      `[sharepoint/unwatchFilesById] Error unwatching file [${id}]: `,
      error,
    );
  }
};

export async function updateAssets(
  schema: string,
  assets: { [id: number]: Partial<M365driveAsset> },
): Promise<boolean> {
  const client = await getClient();
  await client.query('BEGIN');

  const updatedAt = new Date();

  try {
    for (const [assetId, asset] of Object.entries(assets)) {
      const setClauses: string[] = [];
      const queryParams: any[] = [];
      let paramIndex = 1; // Ensure unique param indices

      for (const key of Object.keys(asset)) {
        if (asset[key] !== undefined) {
          setClauses.push(`${key} = $${paramIndex}`);
          queryParams.push(asset[key]);
          paramIndex++;
        }
      }

      asset.updated_at = updatedAt; // Set updated_at on the asset object

      setClauses.push(`updated_at = $${paramIndex}`);
      queryParams.push(updatedAt);

      queryParams.push(assetId); // Add the assetId to the parameters for the WHERE clause

      if (setClauses.length > 0) {
        const queryString = `
            UPDATE ${schema}.${constants.TABLE_M365_DRIVE_ASSET}
            SET ${setClauses.join(', ')}
            WHERE id = $${paramIndex + 1};
          `;
        await client.query(queryString, queryParams);
      }
    }

    await client.query('COMMIT');
    return true;
  } catch (error) {
    console.error('Error updating multiple Microsoft 365 assets:', error);
    await client.query('ROLLBACK');
    return false;
  } finally {
    client.release();
  }
}

export async function getSharePointAssetsWithTask(
  schema: string,
): Promise<M365driveAsset[]> {
  const queryString = `SELECT tao.*, t.*, a.* FROM ${schema}.${constants.TABLE_M365_DRIVE_ASSET} a
   JOIN ${schema}.${constants.TABLE_TASK} t ON a.task_id = t.id
   JOIN ${schema}.${constants.TABLE_TASK_OWNER} tao ON t.id = tao.task_id
   WHERE t.STATUS != '${constants.TASK_STATUS_COMPLETED}' AND t.STATUS != '${constants.TASK_STATUS_DELETED}'`;

  const result = await query(queryString);
  return result && result.rows ? result.rows : [];
}
