import { IntegrationAssetSetting } from '@tribble/tribble-db/clients/IntegrationAssetSetting';

/**
 * Could be a File or A Folder
 * A File will have a download URL and file property
 * A Folder will have a folder property
 */
export interface M365DriveItem extends ODataResponse {
  '@microsoft.graph.downloadUrl'?: string;
  createdBy: EdBy;
  createdDateTime: Date;
  eTag: string;
  id: string;
  lastModifiedBy: EdBy;
  lastModifiedDateTime: Date;
  name: string;
  parentReference: ParentReference;
  webUrl: string;
  cTag: string;
  file?: File;
  fileSystemInfo: FileSystemInfo;
  folder?: Folder;
  shared: Shared;
  size: number;
  // This is an artificial property that we add to the item
  // in order to facilitate "always tagging" metadata tags
  asset_settings?: Partial<IntegrationAssetSetting>;
}

export interface M365ItemsResponse extends ODataResponse {
  value: M365DriveItem[];
}

export interface EdBy {
  user: User;
}

export interface User {
  email: string;
  id: string;
  displayName: string;
}

export interface File {
  hashes: Hashes;
  mimeType: string;
}

export interface Hashes {
  quickXorHash: string;
}

export interface FileSystemInfo {
  createdDateTime: Date;
  lastModifiedDateTime: Date;
}

export interface ParentReference {
  driveType: string;
  driveId: string;
  id: string;
  name: string;
  path: string;
  siteId: string;
}

export interface Shared {
  scope: string;
}

export interface SiteDrive {
  '@odata.context': string;
  '@odata.nextLink'?: string;
  createdDateTime: Date;
  description: string;
  id: string;
  lastModifiedDateTime: Date;
  name: string;
  webUrl: string;
  driveType: string;
  createdBy: CreatedBy;
  lastModifiedBy: LastModifiedBy;
  owner: Owner;
  quota: Quota;
}

export interface CreatedBy {
  user: CreatedByUser;
}

export interface CreatedByUser {
  displayName: string;
}

export interface LastModifiedBy {
  user: GroupClass;
}

export interface GroupClass {
  email: string;
  id: string;
  displayName: string;
}
export interface Owner {
  group: GroupClass;
}

export interface Quota {}

export interface Folder {
  childCount: number;
  decorator: Decorator;
}

export interface Decorator {
  iconColor: string;
}

export interface ODataResponse {
  '@odata.context': string;
  '@odata.nextLink'?: string;
}
