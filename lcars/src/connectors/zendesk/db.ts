import { constants } from '../../constants';

const { query, getClient } = require('../../db/index');

type ZendeskAsset = Asset & {
  zendesk_id: number;
};
type Asset = {
  id?: number;
  blob_id: string;
  document_id: number;
  job_id: number | null;
  user_id: number;
  name: string | null;
  status: string;
  last_modified: Date | null;
  created_at: Date;
  updated_at: Date;
  url: string | null;
  watch_for_changes: boolean;
  type: string;
  source_created_date?: Date;
  source_modified_date?: Date;
};

export const getAssets = async (schema: string): Promise<ZendeskAsset[]> => {
  const queryString = `SELECT * FROM ${schema}.${constants.TABLE_ZENDESK_ASSET};`;

  const result = await query(queryString);
  if (result && result.rows) {
    return result.rows as ZendeskAsset[];
  }
  return [];
};

export const unwatchAsset = async (schema: string, zendeskId: number) => {
  const queryString = `UPDATE ${schema}.${constants.TABLE_ZENDESK_ASSET} SET watch_for_changes = false WHERE zendesk_id = $1;`;

  await query(queryString, [zendeskId]);
};

export const deleteZendeskAssetByDocumentIds = async (
  schema: string,
  documentIds: number[],
) => {
  try {
    const placeholders = documentIds
      .map((_, index) => `$${index + 1}`)
      .join(', ');

    const queryString = `DELETE FROM ${schema}.${constants.TABLE_ZENDESK_ASSET} WHERE document_id IN (${placeholders});`;

    await query(queryString, documentIds);
  } catch (err) {
    console.error('[deleteZendeskAssetByDocumentIds]', err);
  }
};

export const watchAsset = async (schema: string, zendeskId: number) => {
  const queryString = `UPDATE ${schema}.${constants.TABLE_ZENDESK_ASSET} SET watch_for_changes = true WHERE zendesk_id = $1;`;

  await query(queryString, [zendeskId]);
};

export const upsertZendeskAsset = async (
  asset: ZendeskAsset,
  schema: string,
) => {
  const queryString = `
        INSERT INTO ${schema}.${constants.TABLE_ZENDESK_ASSET}
        (zendesk_id, blob_id, document_id, job_id, user_id, name, status, last_modified, created_at, updated_at, url, watch_for_changes, source_created_date, source_modified_date)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
        ON CONFLICT (zendesk_id)
        DO UPDATE SET
        blob_id = $2,
        document_id = $3,
        job_id = $4,
        name = $6,
        last_modified = $8,
        updated_at = $10,
        url = $11,
        watch_for_changes = $12,
        source_created_date = $13,
        source_modified_date = $14
        RETURNING *;
    `;

  const result = await query(queryString, [
    asset.zendesk_id,
    asset.blob_id,
    asset.document_id,
    asset.job_id,
    asset.user_id,
    asset.name,
    asset.status,
    asset.last_modified,
    asset.created_at,
    asset.updated_at,
    asset.url,
    asset.watch_for_changes,
    asset.source_created_date || null,
    asset.source_modified_date || null,
  ]);
  return result.rows[0] as ZendeskAsset;
};

export const getZendeskAssetByZendeskId = async (
  schema: string,
  zendeskId: number,
): Promise<ZendeskAsset | undefined> => {
  const queryString = `SELECT * FROM ${schema}.${constants.TABLE_ZENDESK_ASSET} WHERE zendesk_id = $1;`;

  const result = await query(queryString, [zendeskId]);
  if (result && result.rows && result.rows.length) {
    return result.rows[0] as ZendeskAsset;
  }
  return undefined;
};
