const { webhookCheck, clientAdminCheck } = require('../../utils/auth');
import { decryptText, encryptText, obfuscateToken } from '@tribble/auth-helper';
import express, { Request, Response } from 'express';
import {
  ArticlePayload,
  createWebhook,
  getAllArticles,
  getArticle,
  getPdf,
  getWebhooks,
  ingestAllArticles,
  initiateIngest,
  removeWebhooks,
} from '.';
import {
  getClientIntegration,
  getIntegrationBySystemName,
  removeClientIntegration,
  saveClientIntegration,
} from '../../db/integrations';
import { UserRequest } from '../../utils/auth';
import {
  getAssets,
  getZendeskAssetByZendeskId,
  unwatchAsset,
  watchAsset,
} from './db';

const router = express.Router();

router.get(
  '/connectors/zendesk/articles',
  clientAdminCheck,
  async (req: Request & UserRequest, res: Response) => {
    try {
      const { schema } = req.user;

      if (!schema) {
        return res.status(400).json({ error: 'No schema found for user' });
      }

      const zendesk = await getIntegrationBySystemName('zendesk');
      if (!zendesk) {
        return res.status(404).json({ error: 'Zendesk integration not found' });
      }

      const integration = await getClientIntegration(schema, zendesk.id);

      if (!integration) {
        return res.status(401).json({ error: 'No integration found for user' });
      }

      const articles = await getAllArticles(integration);

      res.status(200).json({ success: true, articles });
    } catch (e) {
      console.error(`[/connectors/zendesk/articles] ${e}`);
      res.status(500).json({ success: false, error: 'Internal Server Error' });
    }
  },
);

router.post(
  '/connectors/zendesk/article',
  clientAdminCheck,
  async (req: Request & UserRequest, res: Response) => {
    try {
      const { schema, userDetail } = req.user;
      const { article } = req.body as { article: ArticlePayload };

      if (!schema) {
        return res.status(400).json({ error: 'No schema found for user' });
      }

      const zendesk = await getIntegrationBySystemName('zendesk');
      if (!zendesk) {
        return res.status(404).json({ error: 'Zendesk integration not found' });
      }

      const integration = await getClientIntegration(schema, zendesk.id);

      if (!integration) {
        return res.status(401).json({ error: 'No integration found for user' });
      }

      const html = article.body;

      const pdf = await getPdf(html, article.title);

      await initiateIngest(pdf, schema, userDetail.id, article);

      res.status(200).json({ success: true });
    } catch (e) {
      console.error(`[/connectors/zendesk/articles] ${e}`);
      res.status(500).json({ success: false, error: 'Internal Server Error' });
    }
  },
);

router.get(
  '/connectors/zendesk/assets',
  clientAdminCheck,
  async (req: Request & UserRequest, res: Response) => {
    try {
      const { schema } = req.user;

      const articles = await getAssets(schema);

      res.status(200).json({ success: true, articles });
    } catch (e) {
      console.error(`[/connectors/zendesk/articles/watched] ${e}`);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  },
);

router.post(
  '/connectors/zendesk/asset/:asset_id/:action',
  clientAdminCheck,
  async (req: Request & UserRequest, res: Response) => {
    try {
      const { schema } = req.user;
      const { asset_id } = req.params;
      const { action } = req.params;

      if (action === 'watch') {
        await watchAsset(schema, parseInt(asset_id));
      } else if (action === 'unwatch') {
        await unwatchAsset(schema, parseInt(asset_id));
      } else {
        return res.status(400).json({ error: 'Invalid action' });
      }
      return res.status(200).json({ success: true });
    } catch (e) {
      console.error(`[/connectors/zendesk/asset/unwatch] ${e}`);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  },
);

router.post(
  '/connectors/zendesk/article/hook',
  webhookCheck,
  async (req: Request & { user: { id: number; schema: string } }, res) => {
    try {
      const { id, schema } = req.user;
      if (!id || !schema) {
        return res.status(400).json({ error: 'No user found' });
      }

      console.log(
        `[/connectors/zendesk/article/hook] Processing webhook for client ${schema}`,
      );

      const zendesk = await getIntegrationBySystemName('zendesk');
      if (!zendesk) {
        return res.status(400).json({ error: 'Zendesk integration not found' });
      }

      const subject = req.body.subject;
      const articleId = parseInt(subject.split('zen:article:')[1]);
      const eventType = req.body.type;
      const isPublishedEvent = eventType.endsWith('article.published');

      const asset = await getZendeskAssetByZendeskId(schema, articleId);
      // If asset exists, but not watching for changes, just ignore.
      // This is different from "article does not exist" yet (i.e. a new doc)
      if (asset && !asset.watch_for_changes) {
        return res.status(202).json({ success: true });
      }

      // If asset not in Tribble, and it's an unpublished event, ignore too
      if (!isPublishedEvent) {
        return res.status(202).json({ success: true });
      }

      const article = await getArticle(id, articleId, schema, zendesk.id);
      if (!article) {
        return res
          .status(400)
          .json({ error: 'Something went wrong getting article' });
      }

      const html = article.body;

      const pdf = await getPdf(html, article.title);

      await initiateIngest(pdf, schema, id, article);

      return res.status(200).json({ success: true, articleId });
    } catch (e) {
      console.error(`[/connectors/zendesk/article/hook] ${e}`);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  },
);

router.post(
  '/connectors/zendesk/config',
  clientAdminCheck,
  async (req: Request & UserRequest, res: Response) => {
    try {
      const { schema, userDetail } = req.user;
      const { api_token, domain, email } = req.body;

      if (!api_token || !domain || !email) {
        console.error('Missing required fields', { api_token, domain, email });
        return res.status(400).json({ error: 'Missing required fields' });
      }
      const integration = await getIntegrationBySystemName('zendesk');

      if (!integration || integration.length === 0) {
        return res.status(404).json({ error: 'Zendesk integration not found' });
      }

      await saveClientIntegration(
        schema,
        userDetail.id,
        integration.id,
        await encryptText(JSON.stringify({ api_token, domain, email })),
      );
      res.status(201).json({ success: true });
    } catch (e) {
      console.error(`[/connectors/zendesk/config] ${e}`);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  },
);

router.get(
  '/connectors/zendesk/config',
  clientAdminCheck,
  async (req: Request & UserRequest, res: Response) => {
    try {
      const { schema } = req.user;

      const integration = await getIntegrationBySystemName('zendesk');

      if (!integration || integration.length === 0) {
        return res.status(404).json({ error: 'Zendesk integration not found' });
      }

      const integrationDetails = await getClientIntegration(
        schema,
        integration.id,
      );

      if (!integrationDetails) {
        return res.status(404).json({ error: 'No integration found for user' });
      }

      const zendeskConfig = JSON.parse(
        await decryptText(integrationDetails.auth_details),
      );

      zendeskConfig.api_token = obfuscateToken(zendeskConfig.api_token);

      res.status(200).json({ success: true, zendeskConfig });
    } catch (e) {
      console.error(`[/connectors/zendesk/config] ${e}`);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  },
);

/**
 * Get all webhooks from zendesk
 */
router.get(
  '/connectors/zendesk/webhooks/:action?',
  clientAdminCheck,
  async (req: Request & UserRequest, res: Response) => {
    try {
      const { schema } = req.user;
      const { action } = req.params;
      const status = action === 'status';

      if (!schema) {
        return res.status(400).json({ error: 'No schema found for user' });
      }

      const zendesk = await getIntegrationBySystemName('zendesk');
      if (!zendesk) {
        return res.status(404).json({ error: 'Zendesk integration not found' });
      }

      const integration = await getClientIntegration(schema, zendesk.id);

      if (!integration) {
        return res.status(401).json({ error: 'No integration found for user' });
      }

      const webhooks = await getWebhooks(integration);

      if (status) {
        const active = webhooks.some((w) => w.status === 'active');
        return res.status(200).json({ success: true, active });
      }

      res.status(200).json({ success: true, webhooks });
    } catch (e) {
      console.error(`[/connectors/zendesk/webhooks] ${e}`);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  },
);

/**
 * Create a new webhook in zendesk
 */
router.post(
  '/connectors/zendesk/webhook',
  clientAdminCheck,
  async (req: Request & UserRequest, res: Response) => {
    try {
      const { schema, userDetail } = req.user;

      if (!schema) {
        return res.status(400).json({ error: 'No schema found for user' });
      }

      const zendesk = await getIntegrationBySystemName('zendesk');
      if (!zendesk) {
        return res.status(404).json({ error: 'Zendesk integration not found' });
      }

      const integration = await getClientIntegration(schema, zendesk.id);

      if (!integration) {
        return res.status(401).json({ error: 'No integration found for user' });
      }

      await createWebhook(
        integration,
        userDetail.id,
        userDetail.email,
        schema,
        zendesk.id,
      );

      ingestAllArticles(integration, schema, userDetail.client_id);

      return res.status(201).json({ success: true });
    } catch (e) {
      console.error(`[/connectors/zendesk/webhook] ${e}`);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  },
);

router.delete(
  '/connectors/zendesk/webhook',
  clientAdminCheck,
  async (req: Request & UserRequest, res: Response) => {
    try {
      const { schema } = req.user;

      if (!schema) {
        return res.status(400).json({ error: 'No schema found for user' });
      }

      const zendesk = await getIntegrationBySystemName('zendesk');
      if (!zendesk) {
        return res.status(404).json({ error: 'Zendesk integration not found' });
      }

      const integration = await getClientIntegration(schema, zendesk.id);

      if (!integration) {
        return res.status(401).json({ error: 'No integration found for user' });
      }

      await removeWebhooks(integration);

      res.status(200).json({ success: true });
    } catch (e) {
      console.error(
        `[/connectors/zendesk/webhook] error deleting webhook ${e}`,
      );
      res.status(500).json({ error: 'Internal Server Error' });
    }
  },
);

router.post(
  '/connectors/zendesk/disconnect',
  clientAdminCheck,
  async (req: Request & UserRequest, res: Response) => {
    try {
      const { schema } = req.user;

      if (!schema) {
        return res.status(400).json({ error: 'No schema found for user' });
      }

      const zendesk = await getIntegrationBySystemName('zendesk');
      if (!zendesk) {
        return res.status(404).json({ error: 'Zendesk integration not found' });
      }

      const integration = await getClientIntegration(schema, zendesk.id);

      if (!integration) {
        return res.status(401).json({ error: 'No integration found for user' });
      }

      try {
        await removeWebhooks(integration);
      } catch (e) {
        console.warn(
          `[/connectors/zendesk/disconnect] unable to remove webhook ${e}`,
        );
      }

      await removeClientIntegration(schema, zendesk.id);

      return res.status(200).json({ success: true });
    } catch (e) {
      console.error(`[/connectors/zendesk/disconnect] ${e}`);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  },
);

module.exports = router;
