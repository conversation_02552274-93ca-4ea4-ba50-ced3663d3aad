import * as grpc from '@grpc/grpc-js';
import {
  decryptText,
  encryptText,
  generateCustomToken,
  getApiUrl,
  webhookIngestPermissions,
} from '@tribble/auth-helper';
import {
  BlobContainer,
  getBlobUrl,
  uploadFilesToBlob,
} from '@tribble/azure-helper';
import { FileWithMetadata } from '@tribble/types-shared';
import {
  AddDocumentRequest,
  DocumentSource,
  DocumentSourceInfo,
  MimeType,
} from 'brain-ingest-service';
import * as timestamppb from 'google-protobuf/google/protobuf/timestamp_pb';
import { v4 as uuidv4 } from 'uuid';
import { getDocumentDocType, getGrpcBrainIngestClient } from '..';
import { insertDocs } from '../../db/docs';
import { getUserIntegration, saveUserIntegration } from '../../db/integrations';
import { writePartialJobRecord } from '../../db/job';
import { getUser } from '../../db/queries';
import { callAzureFunction } from '../../utils/azure';
import { getZendeskAssetByZendeskId, upsertZendeskAsset } from './db';

const getHookUrl = async () => {
  const api = await getApiUrl();
  return `${api}/connectors/zendesk/article/hook`;
};

export const getArticle = async (
  userId: number,
  articleId: number,
  schema: string,
  zendeskId: number,
): Promise<ArticlePayload> => {
  const integration = await getUserIntegration(schema, userId, zendeskId);
  if (!integration) {
    console.warn('Integration not found', { userId, schema });
    return undefined;
  }

  const zendeskConfig = JSON.parse(await decryptText(integration.auth_details));

  const token = zendeskConfig.api_token; //zendeskConfig.api_token;
  const zendeskDomain = zendeskConfig.domain;
  const email = zendeskConfig.email;

  if (!token || !zendeskDomain || !email) {
    console.warn('Missing zendesk config', { zendeskConfig });
    return undefined;
  }

  let url = `https://${zendeskDomain}.zendesk.com/api/v2/help_center/articles/${articleId}`;
  const credentials = Buffer.from(`${email}/token:${token}`).toString('base64');

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      Authorization: `Basic ${credentials}`,
      'Content-Type': 'application/json',
    },
  });
  if (response.ok) {
    const data = await response.json();
    return data.article;
  } else {
    console.warn(`Failed to get article: ${response.status}`);
    return undefined;
  }
};

export const getWebhooks = async (integration: { auth_details: string }) => {
  const zendeskConfig = JSON.parse(await decryptText(integration.auth_details));

  const token = zendeskConfig.api_token; //zendeskConfig.api_token;
  const zendeskDomain = zendeskConfig.domain;
  const email = zendeskConfig.email;

  let url = `https://${zendeskDomain}.zendesk.com/api/v2/webhooks`;
  const credentials = Buffer.from(`${email}/token:${token}`).toString('base64');

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      Authorization: `Basic ${credentials}`,
      'Content-Type': 'application/json',
    },
  });

  const data = await response.json();

  const webhooks = data.webhooks as Webhook[];

  const hookUrl = await getHookUrl();

  return webhooks.filter((w) => w.endpoint === hookUrl);
};

export const removeWebhooks = async (integration: { auth_details: string }) => {
  const webhooks = await getWebhooks(integration);
  const zendeskConfig = JSON.parse(await decryptText(integration.auth_details));

  const token = zendeskConfig.api_token; //zendeskConfig.api_token;
  const zendeskDomain = zendeskConfig.domain;
  const email = zendeskConfig.email;
  const credentials = Buffer.from(`${email}/token:${token}`).toString('base64');
  const hookUrl = await getHookUrl();

  for (const webhook of webhooks) {
    if (webhook.endpoint === hookUrl) {
      const deleteUrl = `https://${zendeskDomain}.zendesk.com/api/v2/webhooks/${webhook.id}`;
      const deleteResponse = await fetch(deleteUrl, {
        method: 'DELETE',
        headers: {
          Authorization: `Basic ${credentials}`,
          'Content-Type': 'application/json',
        },
      });
      if (!deleteResponse.ok) {
        console.error(`Failed to delete webhook: ${deleteResponse.status}`);
        throw new Error('Failed to delete webhook');
      }
    }
  }
};

export const createWebhook = async (
  integration: { auth_details: string },
  userId: number,
  userEmail: string,
  schema: string,
  systemId: number,
) => {
  const zendeskConfig = JSON.parse(await decryptText(integration.auth_details));

  const token = zendeskConfig.api_token;
  const zendeskDomain = zendeskConfig.domain;
  const email = zendeskConfig.email;

  let url = `https://${zendeskDomain}.zendesk.com/api/v2/webhooks`;
  const credentials = Buffer.from(`${email}/token:${token}`).toString('base64');

  const hookUrl = await getHookUrl();
  const webhookToken = await generateCustomToken(
    userId,
    webhookIngestPermissions,
    userEmail,
    [`connectors/zendesk/article/hook`],
    schema,
  );

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      Authorization: `Basic ${credentials}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      webhook: {
        authentication: {
          type: 'bearer_token',
          data: {
            token: webhookToken.token,
          },
          add_position: 'header',
        },
        endpoint: hookUrl,
        http_method: 'POST',
        name: 'Article Webhook',
        request_format: 'json',
        status: 'active',
        subscriptions: [
          EventTypes.articlePublished,
          EventTypes.articleUnpublished,
        ],
      },
    }),
  });

  if (!response.ok) {
    console.error(`Failed to create webhook: ${response.status}`, { response });
    throw new Error('Failed to create webhook');
  }

  await saveUserIntegration(
    schema,
    userId,
    systemId,
    await encryptText(
      JSON.stringify({
        api_token: token,
        domain: zendeskDomain,
        email,
        webhook_jti: webhookToken.jti,
      }),
    ),
  );
};

export const getPdf = async (html: string, articleTitle: string) => {
  const pdfResponse = await callAzureFunction(
    'helper',
    {
      html,
      name: articleTitle,
    },
    false,
    '/api/get_pdf_from_html',
  );

  if (!pdfResponse.ok) {
    console.error(`Failed to get pdf from html: ${pdfResponse.status}`);
    throw new Error('Failed to get pdf from html');
  }

  const blob = await pdfResponse.blob();
  const arrayBuffer = await blob.arrayBuffer();
  const buffer = Buffer.from(arrayBuffer);

  return buffer;
};

/**
 * Ingest an article. Checks for existing asset and if the article has been updated since last ingest.
 * Skips if already ingested
 * @param buffer
 * @param schema
 * @param userId
 * @param article
 * @param clientId
 * @returns
 */
export const initiateIngest = async (
  buffer: Buffer,
  schema: string,
  userId: number,
  article: ArticlePayload,
  clientId?: number,
) => {
  console.log(
    `[zendesk/initiateIngest] Initiating ingest for article ${article.id}`,
  );
  if (!clientId) {
    const u = await getUser(userId);
    clientId = u.client_id;
  }
  const blobUrl = getBlobUrl(schema);

  const previousAsset = await getZendeskAssetByZendeskId(schema, article.id);

  if (previousAsset && previousAsset.last_modified) {
    const lastModified = new Date(article.updated_at);
    if (lastModified <= previousAsset.last_modified) {
      console.log(
        `[zendesk/initiateIngest] Article ${article.id} has not been updated since last ingest. Skipping.`,
      );
      return;
    }
  }

  const isUpdate = previousAsset?.zendesk_id !== undefined;
  const file_name = `${article.title}.pdf`;
  const uuid = uuidv4();

  const docType = await getDocumentDocType(schema);

  const file: FileWithMetadata = {
    buffer,
    uuid,
    file_name,
    label: article.title,
    typeId: docType.id, //defaults to document
    typeIsRfp: false,
    created_by_id: userId,
    created_date: new Date(),
    use_for_generation: true,
    prior_version_document_id: previousAsset?.document_id ?? null,
  };

  await uploadFilesToBlob([file], blobUrl, BlobContainer.DOCS);

  const jobId = await writePartialJobRecord([file], userId, schema);

  file.job_id = jobId;

  const docs = await insertDocs([file], schema);
  const docId = docs[0].id;

  await upsertZendeskAsset(
    {
      zendesk_id: article.id,
      blob_id: uuid,
      document_id: docId,
      job_id: jobId,
      user_id: userId,
      name: article.title,
      status: 'pending',
      last_modified: new Date(article.updated_at),
      created_at: new Date(),
      updated_at: new Date(),
      url: article.html_url,
      watch_for_changes: true,
      type: 'document',
      source_created_date: new Date(article.created_at),
      source_modified_date: new Date(article.updated_at),
    },
    schema,
  );

  const ingestClient = getGrpcBrainIngestClient();

  const req = new AddDocumentRequest();
  req.setClientId(clientId);
  req.setSchema(schema);
  req.setUserId(userId);
  req.setDocumentId(docId);
  req.setJobId(jobId);
  req.setMimeType(MimeType.MIME_TYPE_PDF);
  req.setFilename(file_name);
  req.setBlobName(uuid);
  req.setIsUpdate(isUpdate);

  // Set source dates for temporal filtering
  const sourceCreated = new timestamppb.Timestamp();
  sourceCreated.fromDate(new Date(article.created_at));
  req.setSourceCreatedDate(sourceCreated);

  const sourceModified = new timestamppb.Timestamp();
  sourceModified.fromDate(new Date(article.updated_at));
  req.setSourceModifiedDate(sourceModified);

  const sourceInfo = new DocumentSourceInfo();
  sourceInfo.setSource(DocumentSource.ZENDESK);
  sourceInfo.setSourceId(article.id.toString());
  sourceInfo.setSourceLocation(article.html_url);
  req.setSourceInfo(sourceInfo);

  const deadline = new Date(),
    timeoutSecs = 5;
  deadline.setSeconds(deadline.getSeconds() + timeoutSecs);

  console.log(`[zendesk/initiateIngest] sending brain request ${article.id}`);

  // Send brain ingest request.
  ingestClient.addDocument(
    req,
    new grpc.Metadata(),
    { deadline: deadline.getTime() },
    (err: Error, _) => {
      if (err) {
        console.error(
          `Failed to ingest document ${article.id}: ${err.message}`,
        );
      }
    },
  );
};

export interface ArticlePayload {
  id: number;
  url: string;
  html_url: string;
  author_id: number;
  comments_disabled: boolean;
  draft: boolean;
  promoted: boolean;
  position: number;
  vote_sum: number;
  vote_count: number;
  section_id: number;
  created_at: Date;
  updated_at: Date;
  name: string;
  title: string;
  source_locale: string;
  locale: string;
  outdated: boolean;
  outdated_locales: any[];
  edited_at: Date;
  user_segment_id: number;
  permission_group_id: number;
  content_tag_ids: any[];
  label_names: any[];
  body: string;
}

export const ingestAllArticles = async (
  integration: {
    auth_details: string;
    system_name: string;
    created_by: number;
  },
  schema: string,
  clientId: number,
) => {
  const articles = await getAllArticles(integration);
  for (const article of articles) {
    try {
      const pdf = await getPdf(article.body, article.title);
      await initiateIngest(
        pdf,
        schema,
        integration.created_by,
        article,
        clientId,
      );
    } catch (e) {
      console.error(`Failed to ingest article ${article.id}: ${e}`);
    }
  }
};

export const getAllArticles = async (integration: {
  auth_details: string;
  system_name: string;
}): Promise<ArticlePayload[]> => {
  const zendeskConfig = JSON.parse(await decryptText(integration.auth_details));

  const token = zendeskConfig.api_token; //zendeskConfig.api_token;
  const zendeskDomain = zendeskConfig.domain;
  const email = zendeskConfig.email;

  let articles = [];
  let data: { next_page: string; articles: any[] };
  const credentials = Buffer.from(`${email}/token:${token}`).toString('base64');
  let url = `https://${zendeskDomain}.zendesk.com/api/v2/help_center/articles`;

  do {
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          Authorization: `Basic ${credentials}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        data = await response.json();
        articles = articles.concat(data.articles);
      }
    } catch (e) {
      console.error(`[/connectors/zendesk/articles] ${e}`);
      throw e;
    }
    if (data?.next_page) {
      url = data.next_page;
      console.log(`[/connectors/zendesk/articles] Fetching next page: ${url}`);
    }
    await new Promise((resolve) => setTimeout(resolve, 1400));
  } while (data?.next_page != null);

  return articles;
};

const EventTypes = {
  articlePublished: 'zen:event-type:article.published',
  articleUnpublished: 'zen:event-type:article.unpublished',
};

interface Webhook {
  id: string;
  name: string;
  status: string;
  subscriptions: string[];
  created_at: string;
  created_by: string;
  endpoint: string;
  http_method: string;
  request_format: string;
  authentication: Authentication;
}
interface Authentication {
  type: string;
  add_position: string;
}
