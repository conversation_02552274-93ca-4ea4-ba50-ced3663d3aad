import * as grpc from '@grpc/grpc-js';
import { IntegrationAssetSetting } from '@tribble/tribble-db/clients/IntegrationAssetSetting';
import { query } from '@tribble/tribble-db/db_ops';
import { MIME_TYPE } from '@tribble/types-shared';

import {
  BrainIngestClient,
  MimeType as GrpcMimeType,
} from 'brain-ingest-service';
import { getDocTypes } from '../db/docs';
import { DocumentType } from '../model';
import { GDriveAsset } from './gdrive';
import {
  deleteGDriveAssetSheetsByColumnIds,
  deleteGDriveAssetsByIds,
  getGDriveAssetsByDocumentIds,
} from './gdrive/db';
import { deleteZendeskAssetByDocumentIds } from './zendesk/db';

import { constants } from '../constants';

export interface AssetSheet {
  id: number;
  asset_id: number;
  document_id: number;
  job_id: number;
  blob_id: string;
  sheet_id: number;
  sheet_name: string;
  type: string;
  details: SheetDetails;
}

export interface Rfp {
  id?: number; // this will exist for existng configs
  externalId: string;
  docName: string;
  docTypeId: number;
  sheetType: string;
  selectedSheetId: number;
  selectedSheetName: string;
  headerRow: number;
  firstRowIsHeader?: boolean; // This isn't used anymore
  questionColumns: number[];
  answerColumns: number[];
  insightColumns?: number[];
}

export interface SheetDetails {
  doc_name: string;
  doc_type_id: number;
  header_row: number;
  question_columns: number[];
  answer_columns: number[];
  insight_columns?: number[];
}

export const GrpcMimeTypeMap: { [key: string]: GrpcMimeType } = {
  [MIME_TYPE.DOCX]: GrpcMimeType.MIME_TYPE_DOCX,
  [MIME_TYPE.CSV]: GrpcMimeType.MIME_TYPE_CSV,
  [MIME_TYPE.PDF]: GrpcMimeType.MIME_TYPE_PDF,
  [MIME_TYPE.XLSX]: GrpcMimeType.MIME_TYPE_XLSX,
  [MIME_TYPE.EXCEL]: GrpcMimeType.MIME_TYPE_XLSX,
  [MIME_TYPE.PPTX]: GrpcMimeType.MIME_TYPE_PPTX,
};

export const ASSET_TYPE_DOCUMENT = 'document';
export const ASSET_TYPE_RFP = 'rfp';
export const ASSET_TYPE_SPREADSHEET = 'spreadsheet';
export const ASSET_TYPE_INSIGHT = 'insight';
export const ASSET_TYPE_SLIDE = 'slide';

export async function deleteConnectorAssets(
  schema: string,
  docIds: number[],
): Promise<number[]> {
  const gDriveAssets = await getGDriveAssetsByDocumentIds(schema, docIds);

  // Find GDrive Asset Sheets
  // Note: possible that a user only deletes one out of many sheets ingested
  await deleteGDriveAssetSheetsByColumnIds(schema, 'document_id', docIds);
  await deleteZendeskAssetByDocumentIds(schema, docIds);

  return await deleteGDriveAssetsByIds(
    schema,
    gDriveAssets.map((a: GDriveAsset) => a.id),
  );
}

let grpcBrainIngestClient: BrainIngestClient;

export function getGrpcBrainIngestClient() {
  if (!grpcBrainIngestClient) {
    grpcBrainIngestClient = new BrainIngestClient(
      process.env.CHAT_COMPLETION_SERVER_ADDRESS,
      getGrpcCreds(process.env.ENVIRONMENT),
    );
  }
  return grpcBrainIngestClient;
}

export function getGrpcCreds(env: string) {
  if (env === 'development') {
    return grpc.credentials.createInsecure();
  }
  return grpc.credentials.createSsl();
}

export const getDocumentDocType = async (
  schema: string,
): Promise<DocumentType> => {
  const docTypes = await getDocTypes(schema);
  const docType = docTypes.find((dt) => dt.name === 'Document');
  return docType;
};

export const upsertIntegrationAssetSetting = async (
  schema: string,
  integrationId: number,
  assets: Partial<IntegrationAssetSetting>[],
): Promise<IntegrationAssetSetting[]> => {
  if (assets.length === 0) return [];

  const valueCount = 4; // Number of columns in the integration asset setting table
  const values = assets
    .map((_, index) => {
      const placeholders = Array.from(
        { length: valueCount },
        (_, i) => `$${index * valueCount + i + 1}`,
      );
      return `(${placeholders.join(', ')})`;
    })
    .join(', ');

  const queryString = `
      INSERT INTO ${schema}.${constants.TABLE_INTEGRATION_ASSET_SETTING} (
        integration_id,
        asset_id,
        metadata_tags,
        privacy
      ) VALUES ${values}
      ON CONFLICT (integration_id, asset_id) DO UPDATE
      SET 
        metadata_tags = EXCLUDED.metadata_tags,
        privacy = EXCLUDED.privacy
      RETURNING *;
    `;

  const queryParams = [];
  assets.forEach((asset) => {
    queryParams.push(
      integrationId,
      asset.asset_id,
      asset.metadata_tags ? JSON.stringify(asset.metadata_tags) : null,
      asset.privacy ?? 'public',
    );
  });

  try {
    const result = await query(queryString, queryParams);
    if (result && result.rows) {
      return result.rows;
    }
  } catch (error) {
    console.error(
      '[upsertIntegrationAssetSetting] Error upserting integration asset setting:',
      error,
    );
  }
  return [];
};
