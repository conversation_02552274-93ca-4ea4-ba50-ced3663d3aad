# GDrive Connector API

The connector itself is a small module inside the lcars API. In the future, it
could live in its own deployment but, for now, lcars is easiest ¯\_(ツ)\_/¯.

These API endpoints manage the list of watched documents on Drive and translate
them into calls to the BrainIngest service.

BrainIngest is a gRPC service hosted in Q that handles the actual parsing,
chunking, embedding and, of course construction of the brain.

There is one functions: sync.

Sync checks the metadata for all assets in a given schema and re-ingests any
if they're out of date. It can be triggered by timer or from the UI.

```
    POST   /connectors/gdrive/assets
    GET    /connectors/gdrive/assets
    GET    /connectors/gdrive/assets/:assetId
    DELETE /connectors/gdrive/assets/:assetId

    POST   /clients/:client_id/connectors/gdrive/sync
    PATCH  /clients/:client_id/connectors/gdrive/assets/:assetId

    POST   /clients/:client_id/connectors/gdrive/assets/:assetId/webhook
```

Ingest results for each asset published to the BrainIngest service are sent
back using the client-scoped update endpoint as a callback.

```
    service BrainIngest {
      rpc AddDocument(AddDocumentRequest) returns (AddDocumentResponse);
      rpc AddQAPair(AddQAPairRequest) returns (AddQAPairResponse);
      rpc AddStructuredData(AddStructuredDataRequest) returns (AddStructuredDataResponse);
      ...
    }
```
