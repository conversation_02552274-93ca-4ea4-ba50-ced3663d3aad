import { DefaultAzureCredential } from '@azure/identity';
import { BlobServiceClient, ContainerClient } from '@azure/storage-blob';
import * as grpc from '@grpc/grpc-js';
import {
  acquireDatabaseLock,
  releaseDatabaseLock,
} from '@tribble/common-utils';
import { ClientId } from '@tribble/tribble-db/tribble/Client';
import { UserId } from '@tribble/tribble-db/tribble/User';
import { StructuredFileRequest } from '@tribble/types-shared';
import {
  AddDocumentRequest,
  AddQuestionnaireRequest,
  BrainIngestClient,
  DocumentSource,
  DocumentSourceInfo,
  MimeType,
} from 'brain-ingest-service';
import ExcelJS from 'exceljs';
import * as timestamppb from 'google-protobuf/google/protobuf/timestamp_pb';
import { drive_v3, google } from 'googleapis';
import { v4 as uuidv4 } from 'uuid';
import {
  ASSET_TYPE_DOCUMENT,
  ASSET_TYPE_INSIGHT,
  ASSET_TYPE_RFP,
  ASSET_TYPE_SLIDE,
  ASSET_TYPE_SPREADSHEET,
  AssetSheet,
  Rfp,
  getDocumentDocType,
  getGrpcBrainIngestClient,
} from '..';
import { constants } from '../../constants';
import { insertDocs, updateDocumentUuid } from '../../db/docs';
import { getUserAndSystemIntegration } from '../../db/integrations';
import { setJobAsError, writePartialJobRecord } from '../../db/job';
import { upsertScheduledNotification } from '../../db/notifications';
import { upsertMultipleAssetTasks } from '../../db/tasks';
import { Client, FileWithMetadata } from '../../model/model';
import { callAzureFunction, getBlobUrl } from '../../utils/azure';
import { send_function_request } from '../../utils/eventGrid';
import { createSheetTaskNotificationDetails } from '../../utils/review';
import {
  deleteGDriveAssetTasks,
  getGDriveAssetSheetsByAssetId,
  getGDriveAssetsByDriveIds,
  insertGDriveAssetSheets,
  updateAssetSheets,
  updateGDriveAsset,
  updateGDriveAssets,
  upsertGDriveAssets,
} from './db';

const { decryptText } = require('../../utils/auth');
const { getClient } = require('../../db/index');

export const BLOB_STORAGE_CONTAINER_NAME = 'documents';

interface SheetDetails {
  doc_name: string;
  doc_type_id: number;
  header_row: number;
  question_columns: number[];
  answer_columns: number[];
}
export const SYSTEM_NAME = 'googledrive';

// GDriveAsset is a Google Drive file accessible using credentials belonging
// to the user indicated by user_id.
export interface GDriveAsset {
  id: number;
  drive_id: string;
  blob_id: string;
  document_id: number;
  job_id: number | null;
  user_id: number;
  name: string | null;
  mime_type: string | null;
  parent_id: string | null;
  status: string;
  last_checked: Date | null;
  last_modified: Date | null;
  source_created_date: Date | null; // Original creation time from Google Drive
  created_at: Date;
  updated_at: Date;
  url: string | null;
  type: string;
  watch_for_changes: boolean;
  task_id: number;
}

// GDriveAsset is a Google Drive file accessible using credentials belonging
// to the user indicated by user_id.
export interface GDriveAssetSheet {
  id: number;
  asset_id: number;
  document_id: number;
  job_id: number;
  blob_id: string;
  sheet_id: number;
  sheet_name: string;
  type: string;
  details: SheetDetails;
}

// GDriveWatch describes a channel subscription to a Google Drive file, we need
// to manage these on our side to ensure we only receive the ones we want.
export interface GDriveWatch {
  id: number;
  asset_id: number;
  user_id: number;
  drive_id: string;
  channel_id: string;
  resource_id: string;
  expiration: Date | null;
  created_at: Date;
}

// export interface Rfp {
//   id?: number; // this will exist for existng configs
//   driveId: string;
//   docName: string;
//   docTypeId: number;
//   sheetType: string;
//   selectedSheetId: number;
//   selectedSheetName: string;
//   headerRow: number;
//   firstRowIsHeader: boolean;
//   questionColumns: number[];
//   answerColumns: number[];
// }

const exportMimeTypesMap = {
  [constants.MIME_TYPE_GOOGLE_DOC]: constants.MIME_TYPE_PDF,
  [constants.MIME_TYPE_GOOGLE_DOC]: constants.MIME_TYPE_GOOGLE_DOC,
  // Need to export Google Sheet as XLSX, then find out which Sheet user wants
  [constants.MIME_TYPE_GOOGLE_SHEET]: constants.MIME_TYPE_XLSX,
  [constants.MIME_TYPE_CSV]: constants.MIME_TYPE_CSV,
  [constants.MIME_TYPE_GOOGLE_SLIDE]: constants.MIME_TYPE_PPTX,
};

export const watchDirectory = async (
  folder: drive_v3.Schema$File,
  drive: drive_v3.Schema$TeamDrive,
  userId: number,
  client: Client,
  schema: string,
  watchForChanges: boolean,
) => {
  try {
    const folderId = folder?.id ?? drive?.id;
    if (!folderId) {
      console.error(
        `[ connectors/gdrive/watchDirectory ] No folderId or driveId not found. (Name? = ${folder?.name ?? drive?.name})`,
      );
      return 0;
    }

    const children = await getFoldersChildrenRecursive(
      folderId,
      userId,
      schema,
    );

    const documentIds = children
      .filter(
        (c) =>
          c.mimeType !== constants.MIME_TYPE_GOOGLE_SHEET &&
          c.mimeType !== constants.MIME_TYPE_XLSX &&
          c.mimeType !== constants.MIME_TYPE_CSV,
      )
      .map((child) => child.id);
    const rfps = children
      .filter(
        (c) =>
          c.mimeType === constants.MIME_TYPE_GOOGLE_SHEET ||
          c.mimeType === constants.MIME_TYPE_XLSX ||
          c.mimeType === constants.MIME_TYPE_CSV,
      )
      .map((r) => {
        return {
          externalId: r.id,
          docName: r.name,
        } as Rfp;
      });

    const folders = children.filter(
      (c) => c.mimeType === constants.MIME_TYPE_GOOGLE_FOLDER,
    );

    await upsertGDriveAssets(schema, [
      {
        drive_id: folderId,
        name: folder?.name ?? drive.name,
        mime_type: folder
          ? constants.MIME_TYPE_GOOGLE_FOLDER
          : constants.MIME_TYPE_GOOGLE_DRIVE,
        type: folder ? 'folder' : 'drive',
        last_modified: new Date(),
        created_at: new Date(),
        watch_for_changes: watchForChanges,
        url:
          folder?.webViewLink ?? `https://drive.google.com/drive/${drive.id}`,
        last_checked: new Date(),
        blob_id: uuidv4(),
        user_id: userId,
        parent_id: folder?.parents?.[0] ?? null,
        status: 'new',
      },
      ...folders.map((f) => ({
        drive_id: f.id,
        name: f.name,
        mime_type: constants.MIME_TYPE_GOOGLE_FOLDER,
        type: 'folder',
        last_modified: new Date(),
        created_at: new Date(),
        watch_for_changes: watchForChanges,
        url: f.webViewLink,
        last_checked: new Date(),
        blob_id: uuidv4(),
        user_id: userId,
        parent_id: f.parents?.[0] ?? null,
        status: 'new',
      })),
    ]);

    console.log(
      `[ connectors/gdrive/watchDirectory ] watching folder with ${children.length} children. ${documentIds.length} documents and ${rfps.length} RFPs.`,
    );

    const driveClient = await driveClientForUser(schema, userId);

    ingestGDriveFiles(
      client,
      driveClient,
      userId,
      documentIds,
      rfps,
      watchForChanges,
    );

    return children.length;
  } catch (e) {
    console.error(e);
    return 0;
  }
};

export async function ingestGDriveFiles(
  client: Client,
  drive: drive_v3.Drive,
  userId: number,
  documentIds: string[],
  rfps: Rfp[] = [],
  watchForChanges: boolean,
  batchSize: number = 5, // Adjust based on your testing and use case
): Promise<GDriveAsset[]> {
  let allAssets: GDriveAsset[] = [];

  if (documentIds) {
    // Batch our metadata requests to the Drive API.
    for (let i = 0; i < documentIds.length; i += batchSize) {
      const batchIds = documentIds.slice(i, i + batchSize);

      const fileReqs = batchIds.map((fileId) =>
          drive.files.get({
            fileId,
            fields:
              'id, name, mimeType, parents, webViewLink, modifiedTime, createdTime',
            supportsAllDrives: true,
          }),
        ),
        resps = await Promise.all(fileReqs),
        files = resps.map((resp) => resp.data);

      // Map Google Drive file objects to Partial<GDriveAsset>
      const assets: Partial<GDriveAsset>[] = files.map((file) => ({
        drive_id: file.id,
        blob_id: uuidv4(),
        user_id: userId,
        name: file.name || null,
        mime_type: file.mimeType || null,
        parent_id: file.parents ? file.parents[0] : null,
        status: 'new',
        last_checked: new Date(), // Current timestamp as last checked
        last_modified: file.modifiedTime ? new Date(file.modifiedTime) : null, // Map 'modifiedTime' to 'last_modified'. null if not available.
        source_created_date: file.createdTime
          ? new Date(file.createdTime)
          : null, // Map 'createdTime' to 'source_created_date'. null if not available.
        url: file.webViewLink || null,
        type: ASSET_TYPE_DOCUMENT,
        watch_for_changes: watchForChanges,
      }));

      const docs = ingestGDriveGoogleDocs(
        client,
        drive,
        userId,
        assets.filter(
          (asset) =>
            asset.mime_type === constants.MIME_TYPE_GOOGLE_DOC ||
            asset.mime_type === constants.MIME_TYPE_GOOGLE_SLIDE ||
            asset.mime_type === constants.MIME_TYPE_DOCX ||
            asset.mime_type === constants.MIME_TYPE_PPTX ||
            asset.mime_type === constants.MIME_TYPE_PDF ||
            asset.mime_type === constants.MIME_TYPE_TEXT_PLAIN,
        ),
      );

      // Await each batch to throttle API throughput.
      const ingested = await Promise.all([docs]);

      allAssets = allAssets.concat(ingested[0]);
    }
  }

  if (rfps?.length) {
    // For sheets, since multi-sheet is possible (i.e. same driveId),
    // cache drive data
    const driveIdFileMap: { [driveId: string]: drive_v3.Schema$File } =
      rfps.reduce((acc, rfp) => {
        acc[rfp.externalId] = null;
        return acc;
      }, {});

    // Map Google Drive file objects to Partial<GDriveAsset>
    const gDriveAssets: Partial<GDriveAsset>[] = [];

    // Need to also create gDriveAssetSheet records
    const gDriveAssetSheetsMap: {
      [driveId: string]: Partial<AssetSheet>[];
    } = {};

    for (let i = 0; i < rfps.length; i += batchSize) {
      const batch = rfps.slice(i, i + batchSize);

      // Get the unique driveIds to fetch
      const batchDrivesIds = Object.keys(
        batch.reduce((acc, rfp) => {
          acc[rfp.externalId] = null;
          return acc;
        }, {}),
      );

      const fileReqs = batchDrivesIds.map((driveId) => {
          if (driveIdFileMap[driveId]) {
            return Promise.resolve({ data: driveIdFileMap[driveId] });
          } else {
            return drive.files.get({
              fileId: driveId,
              fields:
                'id, name, mimeType, parents, webViewLink, modifiedTime, createdTime',
              supportsAllDrives: true,
            });
          }
        }),
        resps = await Promise.all(fileReqs);

      resps.forEach((resp) => {
        const fileData = resp.data as drive_v3.Schema$File;
        driveIdFileMap[fileData.id] = fileData;
      });

      batch
        // New -> skip if no config -> send to Review
        .filter(
          (rfp) =>
            (rfp.sheetType === ASSET_TYPE_RFP &&
              rfp.questionColumns?.length > 0 &&
              rfp?.answerColumns?.length > 0) ||
            rfp.sheetType === ASSET_TYPE_SPREADSHEET ||
            (rfp.sheetType === ASSET_TYPE_INSIGHT &&
              rfp?.insightColumns?.length > 0),
        )
        .forEach((rfp) => {
          const driveId = rfp.externalId;
          const driveFile = driveIdFileMap[driveId];

          if (driveFile) {
            // Multiple sheets can point to the same GDrive asset.
            // So only insert the asset once.
            if (!gDriveAssetSheetsMap[driveId]) {
              gDriveAssets.push({
                drive_id: driveId,
                blob_id: uuidv4(),
                user_id: userId,
                name: driveFile.name,
                mime_type: driveFile.mimeType || null,
                parent_id: driveFile.parents ? driveFile.parents[0] : null,
                status: 'new',

                // Current timestamp as last checked
                last_checked: new Date(),

                // Map 'modifiedTime' to 'last_modified'
                last_modified: driveFile.modifiedTime
                  ? new Date(driveFile.modifiedTime)
                  : null,
                // Map 'createdTime' to 'source_created_date'
                source_created_date: driveFile.createdTime
                  ? new Date(driveFile.createdTime)
                  : null,

                url: driveFile.webViewLink || null,
                type: rfp.sheetType,
                watch_for_changes: watchForChanges,
              });
              gDriveAssetSheetsMap[driveId] = [];
            }

            gDriveAssetSheetsMap[driveId].push({
              sheet_id: rfp.selectedSheetId,
              sheet_name: rfp.selectedSheetName,
              type: rfp.sheetType,
              blob_id: uuidv4(),
              details: {
                doc_name: rfp.docName,
                doc_type_id: rfp.docTypeId,
                header_row: rfp.headerRow,
                question_columns: rfp.questionColumns,
                answer_columns: rfp.answerColumns,
                insight_columns: rfp.insightColumns,
              },
            });
          }
        });

      const unconfiguredRfp = batch.filter(
        (rfp) =>
          !rfp.sheetType ||
          (rfp.sheetType === ASSET_TYPE_RFP &&
            (!rfp.questionColumns?.length || !rfp?.answerColumns?.length)) ||
          (rfp.sheetType === ASSET_TYPE_INSIGHT &&
            !rfp?.insightColumns?.length),
      );

      console.log(
        '[ ingestGDriveFiles ] RFPs with no configuration: ',
        unconfiguredRfp.length,
      );

      if (unconfiguredRfp.length > 0) {
        const unconfiguredAssets = await upsertGDriveAssets(
          client.database_schema_id,
          unconfiguredRfp.map((rfp) => {
            const driveFile = driveIdFileMap[rfp.externalId];
            return {
              drive_id: rfp.externalId,
              name: rfp.docName,
              watch_for_changes: watchForChanges,
              blob_id: uuidv4(),
              mime_type: driveFile.mimeType || null,
              parent_id: driveFile.parents ? driveFile.parents[0] : null,
              status: 'new',
              type: '',
              user_id: userId,
              url: driveFile.webViewLink || null,
              last_modified: driveFile.modifiedTime
                ? new Date(driveFile.modifiedTime)
                : null,
              source_created_date: driveFile.createdTime
                ? new Date(driveFile.createdTime)
                : null,
            };
          }),
        );
        await createReviewTaskAndNotification(
          unconfiguredAssets,
          client.database_schema_id,
          userId,
          client.id,
        );
      }

      const ingestPromises = ingestGDriveRfps(
        client,
        drive,
        userId,
        gDriveAssets.filter(
          (asset) =>
            asset.mime_type === constants.MIME_TYPE_GOOGLE_SHEET ||
            asset.mime_type === constants.MIME_TYPE_CSV ||
            asset.mime_type === constants.MIME_TYPE_XLSX,
        ),
        gDriveAssetSheetsMap,
      );

      // Await each batch to throttle API throughput.
      const ingested = await Promise.all([ingestPromises]); //([folderDocs, docs]);

      allAssets = allAssets.concat(ingested[0]); //, ingested[1]);
    }
  }

  return allAssets;
}

// Takes a list of GDriveAssets, returns the ones that were actually inserted.
// We only want one copy of each Drive asset, so if two users try to ingest the
// same file, only the first one will be inserted.
async function ingestGDriveGoogleDocs(
  client: Client,
  drive: drive_v3.Drive,
  userId: number,
  assets: Partial<GDriveAsset>[],
): Promise<GDriveAsset[]> {
  const blobUrl = getBlobUrl(client.database_schema_id),
    creds = new DefaultAzureCredential(),
    blobServiceClient = new BlobServiceClient(blobUrl, creds),
    containerClient = blobServiceClient.getContainerClient(
      BLOB_STORAGE_CONTAINER_NAME,
    ),
    docType = await getDocumentDocType(client.database_schema_id);

  // This will return only the newly inserted documents.
  const upserted = await upsertGDriveAssets(client.database_schema_id, assets);
  if (upserted.length === 0) {
    return [];
  }

  const newFileAssetMap = {};
  const newFiles: [GDriveAsset, FileWithMetadata][] = upserted
    .filter((asset: GDriveAsset) => !asset.document_id)
    .map((asset) => {
      newFileAssetMap[asset.id] = asset;
      return [
        asset,
        {
          file_name: asset.name,
          label: asset.name,
          file_mimetype: asset.mime_type,
          buffer: null,
          uuid: asset.blob_id,
          privacy: 'public',
          typeId: docType.id,
          typeIsRfp: false,
          created_by_id: userId,
          created_date: asset.created_at,
          use_for_generation: true,
          status: 'processing',
          source_url: asset.url,
          // Note: for GDocs this is fine; but for GSheets/XLSX, would need to look in integration_asset_sheet table to get document_id
          prior_version_document_id: asset.document_id,
        },
      ];
    });

  console.log(
    `[ingestGDriveGoogleDocs] (client: ${client.id}, user: ${userId}) ${assets.length} assets found, ${newFiles.length} new assets to ingest`,
  );

  // Similar to ingest RFP, insert each doc in its own job.
  // More so since folder sync now possible, and a folder
  // may have many docs.
  for (let i = 0; i < newFiles.length; i++) {
    const [asset, file] = newFiles[i];
    const jobId = await writePartialJobRecord(
      [file],
      userId,
      client.database_schema_id,
    );
    asset.job_id = jobId;
    file.job_id = jobId;
  }

  await insertDocs(
    newFiles.map(([_, file]) => file),
    client.database_schema_id,
  );

  // Save document and job IDs.
  newFiles.forEach(([asset, file]) => {
    asset.document_id = file.id;
  });
  await updateGDriveAssets(
    client.database_schema_id,
    newFiles.reduce((acc, [asset, _]) => {
      acc[asset.id] = {
        document_id: asset.document_id,
        job_id: asset.job_id,
      };
      return acc;
    }, {}),
  );

  // Docs must be exported one-at-a-time so we loop through one by one.
  for (const asset of upserted) {
    try {
      const isUpdate = !newFileAssetMap[asset.id];
      await acquireDocument(
        client,
        drive,
        containerClient,
        asset,
        isUpdate ? AcquisitionType.UPDATE : AcquisitionType.ACQUIRE,
      );
    } catch (error) {
      const errorId = uuidv4();

      console.error(
        `[ingestGDriveGoogleDocs] (client: ${client.id}, user: ${userId}) Failed to process document ${asset.id}: ${error.message} (Error Id: ${errorId})`,
      );

      // Update doc status and error message
      await setJobAsError(
        client.database_schema_id,
        asset.job_id,
        (error.message ?? 'Unknown error') + ` (Error Id: ${errorId})`,
      );
    }
  }

  return upserted;
}

/**
 * Takes a list of GDriveAssets containing Rfps, returns the ones that were
 * actually inserted. We only want one copy of each Drive asset, so if two
 * users try to ingest the same file, only the first one will be inserted.
 * If an RFP has no sheet configuration -> notification is upserted to Review.
 * @param client
 * @param drive
 * @param userId
 * @param newGDriveAssets
 * @param gDriveAssetSheetsMap
 * @param existingGDriveAsset
 * @returns
 */
export async function ingestGDriveRfps(
  client: Client,
  drive: drive_v3.Drive,
  userId: number,
  newGDriveAssets: Partial<GDriveAsset>[],
  gDriveAssetSheetsMap: { [driveId: string]: Partial<AssetSheet>[] },
  existingGDriveAssetSheets: AssetSheet[] = [],
): Promise<GDriveAsset[]> {
  const schema = client.database_schema_id,
    blobUrl = getBlobUrl(schema),
    creds = new DefaultAzureCredential(),
    blobServiceClient = new BlobServiceClient(blobUrl, creds),
    containerClient = blobServiceClient.getContainerClient(
      BLOB_STORAGE_CONTAINER_NAME,
    );

  let gDriveAssets = await upsertGDriveAssets(schema, newGDriveAssets);

  // Insert the asset sheet records
  let insertedAssetSheets = [];
  const updatedAssetSheetIds = [];

  for (const asset of gDriveAssets) {
    try {
      const assetSheets = gDriveAssetSheetsMap[asset.drive_id];
      if (!assetSheets) {
        // New -> send notification to Review
        continue;
      }

      const currAssetSheetsInserted = await insertGDriveAssetSheets(
        schema,
        assetSheets.map((sheet) => {
          sheet.asset_id = asset.id;
          sheet.blob_id = uuidv4();
          return sheet;
        }),
      );

      insertedAssetSheets.push(...currAssetSheetsInserted);
    } catch (error) {
      console.error(
        `Failed to insert GDrive Asset Sheets for asset id [${asset.id}]: ${error.message}`,
      );
    }
  }

  // Map for gDriveAsset.id --> assetSheet FileMetadata
  // (used in the acquireRFP function)
  const assetSheetFileAssetIdMap = {};

  const assetSheetFiles: FileWithMetadata[] = insertedAssetSheets.map(
    (sheet) => {
      const gDriveAsset = gDriveAssets.find(
        (asset) => Number(asset.id) === Number(sheet.asset_id),
      );

      const assetSheetFileMeta = {
        file_name: gDriveAsset.name,
        label: sheet.details.doc_name,
        file_mimetype: constants.MIME_TYPE_CSV,
        buffer: null,
        uuid: sheet.blob_id,

        privacy: 'public',
        typeId: sheet.details.doc_type_id,

        typeIsRfp: sheet.type === ASSET_TYPE_RFP,
        typeIsStructured: sheet.type === ASSET_TYPE_SPREADSHEET,
        typeIsInsight: sheet.type === ASSET_TYPE_INSIGHT,

        created_by_id: userId,
        created_date: new Date(),
        use_for_generation: true,
        status: 'processing',
        source_url: gDriveAsset.url,
        sheetId: sheet.sheet_id,
      } as FileWithMetadata;

      if (gDriveAsset.type === ASSET_TYPE_RFP) {
        assetSheetFileMeta.headerRow = sheet.details.header_row;
        assetSheetFileMeta.questionColumns = sheet.details.question_columns;
        assetSheetFileMeta.answerColumns = sheet.details.answer_columns;
      }

      if (gDriveAsset.type === ASSET_TYPE_INSIGHT) {
        assetSheetFileMeta.insightColumns = sheet.details.insight_columns;
      }

      const previouslyExistingAssetSheet = existingGDriveAssetSheets.find(
        (prevSheet) =>
          prevSheet.asset_id == sheet.asset_id &&
          prevSheet.sheet_id === sheet.sheet_id,
      );
      if (
        previouslyExistingAssetSheet &&
        previouslyExistingAssetSheet.document_id
      ) {
        assetSheetFileMeta.prior_version_document_id =
          previouslyExistingAssetSheet.document_id;

        updatedAssetSheetIds.push(Number(sheet.id));
      }

      assetSheetFileAssetIdMap[String(sheet.id)] = assetSheetFileMeta;

      return assetSheetFileMeta;
    },
  );

  // Each asset sheet needs its own job id
  // TODO: bulk insert (similar pattern to insertDocs)
  for (let i = 0; i < assetSheetFiles.length; i++) {
    const jobId = await writePartialJobRecord(
      [assetSheetFiles[i]],
      userId,
      schema,
      assetSheetFiles[i].typeIsStructured ? 'digest' : 'ingest',
    );
    insertedAssetSheets[i].job_id = jobId;
    assetSheetFiles[i].job_id = jobId;
  }

  await insertDocs(assetSheetFiles, schema);

  // Save document and job IDs.
  insertedAssetSheets.forEach((asset, idx) => {
    asset.document_id = assetSheetFiles[idx].id;
  });

  await updateAssetSheets(
    schema,
    insertedAssetSheets.reduce((acc, asset) => {
      acc[asset.id] = {
        document_id: asset.document_id,
        job_id: asset.job_id,
      };
      return acc;
    }, {}),
  );

  // Docs must be exported one-at-a-time so we loop through one by one.
  for (const asset of gDriveAssets) {
    try {
      await acquireSheet(
        client,
        drive,
        containerClient,
        asset,
        AcquisitionType.ACQUIRE,
        {
          updatedAssetSheetIds,
          assetSheetFileAssetIdMap,
        },
      );
    } catch (error) {
      console.error(`Failed to process RFP ${asset.id}: ${error.message}`);
    }
  }

  return gDriveAssets;
}

export enum AcquisitionType {
  ACQUIRE,
  UPDATE,
}

export async function acquireDocument(
  client: Client,
  drive: drive_v3.Drive,
  containerClient: ContainerClient,
  asset: GDriveAsset,
  acquisitionType: AcquisitionType = AcquisitionType.ACQUIRE,
): Promise<void> {
  const dbClient = await getClient();

  try {
    // Acquire Postgres lock on the asset for ingestion.
    const lock = await acquireDatabaseLock(
      client.id,
      asset.id,
      'google_drive',
      dbClient,
    );
    if (!lock) {
      console.log(
        `[acquireDocument] Failed to acquire lock for asset ${asset.id}, skipping.`,
      );
      return;
    }

    try {
      console.log(
        `[acquireDocument] Calling helper function to upload file [${asset.drive_id}]`,
      );

      // Call function to download and upload to blob
      const downloadUploadResponse = await callAzureFunction(
        'helper',
        {
          assetId: asset.id,
          fileId: asset.drive_id,
          mimeType: asset.mime_type,
          schema: client.database_schema_id,
          userId: asset.user_id,
        },
        false,
        process.env.FILE_DOWNLOAD_UPLOAD_BLOB_GDRIVE_ENDPOINT ??
          '/api/download-file-from-gdrive-upload-to-blob',
      );

      if (!downloadUploadResponse) {
        throw new Error('Failed to download and upload to blob');
      }

      const resp = await downloadUploadResponse.json();
      const blob_id = resp.blob_id;

      if (!blob_id) {
        throw new Error(resp.error ?? 'Failed to download and upload to blob');
      }

      console.log(
        `[acquireDocument] Successfully uploaded file [${asset.drive_id}] to blob id [${blob_id}].`,
      );

      let blobMimeType: string;
      let gRpcMimeType: MimeType;

      if (
        asset.mime_type === constants.MIME_TYPE_PDF ||
        asset.mime_type === constants.MIME_TYPE_DOCX
      ) {
        if (asset.mime_type === constants.MIME_TYPE_DOCX) {
          blobMimeType = constants.MIME_TYPE_DOCX;
          gRpcMimeType = MimeType.MIME_TYPE_DOCX;
        } else {
          blobMimeType = constants.MIME_TYPE_PDF;
          gRpcMimeType = MimeType.MIME_TYPE_PDF;
        }
      } else if (
        asset.mime_type === constants.MIME_TYPE_GOOGLE_SLIDE ||
        asset.mime_type === constants.MIME_TYPE_PPTX
      ) {
        blobMimeType = constants.MIME_TYPE_PPTX;
        gRpcMimeType = MimeType.MIME_TYPE_PPTX;
      } else {
        blobMimeType = constants.MIME_TYPE_PDF;
        gRpcMimeType = MimeType.MIME_TYPE_PDF;
      }

      asset.blob_id = blob_id;

      // If acquisition type is update, then we need to create a new version of the document.
      if (acquisitionType === AcquisitionType.UPDATE) {
        // Create a new version of the document.
        const docType = await getDocumentDocType(client.database_schema_id),
          file = {
            file_name: asset.name,
            label: asset.name,
            file_mimetype: asset.mime_type,
            buffer: null,
            uuid: blob_id,
            privacy: 'public',
            typeId: docType.id,
            typeIsRfp: false,
            created_by_id: asset.user_id,
            created_date: asset.created_at,
            use_for_generation: !asset.document_id, // Only set to false if it's an update
            prior_version_document_id: asset.document_id,
            source_url: asset.url,
          } as FileWithMetadata,
          jobId = await writePartialJobRecord(
            [file],
            asset.user_id,
            client.database_schema_id,
          );

        asset.job_id = jobId;
        file.job_id = jobId;

        const doc = await insertDocs([file], client.database_schema_id);
        asset.document_id = doc[0].id;
      } else {
        await updateDocumentUuid(
          client.database_schema_id,
          asset.document_id,
          blob_id,
        );
      }

      await updateGDriveAsset(client.database_schema_id, asset.id, {
        blob_id: asset.blob_id,
        document_id: asset.document_id,
        job_id: asset.job_id,
        status: 'acquiring',
      });
      asset.status = 'acquiring';

      const blobClient = containerClient.getBlockBlobClient(asset.blob_id);

      // Not sure if this is still needed
      asset.url = blobClient.url;

      await updateGDriveAsset(client.database_schema_id, asset.id, {
        status: 'processing',
      });
      asset.status = 'processing';

      const ingestClient = getGrpcBrainIngestClient();

      const req = new AddDocumentRequest();
      req.setClientId(client.id);
      req.setSchema(client.database_schema_id);
      req.setUserId(asset.user_id);
      req.setDocumentId(asset.document_id);
      req.setJobId(asset.job_id);
      req.setMimeType(gRpcMimeType);
      req.setFilename(asset.name);
      req.setBlobName(asset.blob_id);
      req.setIsUpdate(acquisitionType === AcquisitionType.UPDATE);

      if (asset.last_modified) {
        const lastModified = new timestamppb.Timestamp();
        lastModified.fromDate(asset.last_modified);
        req.setLastModifiedTime(lastModified);
        // Also set source modified date. For Google Drive, this is the same as last_modified
        req.setSourceModifiedDate(lastModified);
      }

      // Add source creation date if available
      if (asset.source_created_date) {
        const sourceCreatedDate = new timestamppb.Timestamp();
        sourceCreatedDate.fromDate(asset.source_created_date);
        req.setSourceCreatedDate(sourceCreatedDate);
      }
      const sourceInfo = new DocumentSourceInfo();
      sourceInfo.setSource(DocumentSource.GOOGLE_DRIVE);
      sourceInfo.setSourceId(asset.drive_id);
      sourceInfo.setSourceLocation(asset.url);
      req.setSourceInfo(sourceInfo);

      const deadline = new Date(),
        timeoutSecs = 5;
      deadline.setSeconds(deadline.getSeconds() + timeoutSecs);

      // Send brain ingest request.
      ingestClient.addDocument(
        req,
        new grpc.Metadata(),
        { deadline: deadline.getTime() },
        (err: Error, _) => {
          if (err) {
            // throw new Error(
            //   `Failed to ingest document ${asset.drive_id}: ${err.message}`,
            // );
            console.error(
              `Failed to ingest document ${asset.drive_id}: ${err.message}`,
            );
          }
        },
      );
    } finally {
      // Finally ensures lock is released when complete.
      await releaseDatabaseLock(client.id, asset.id, 'google_drive', dbClient);
    }
  } finally {
    dbClient.release();
  }
}

export async function acquireSheet(
  client: Client,
  drive: drive_v3.Drive,
  containerClient: ContainerClient,
  gDriveAsset: GDriveAsset,
  acquisitionType: AcquisitionType = AcquisitionType.ACQUIRE,
  assetSheetData?: {
    updatedAssetSheetIds: any;
    assetSheetFileAssetIdMap: any;
  },
): Promise<ArrayBuffer | null> {
  const dbClient = await getClient();

  try {
    // Acquire Postgres lock on the asset for ingestion.
    const lock = await acquireDatabaseLock(
      client.id,
      gDriveAsset.id,
      'google_drive',
      dbClient,
    );
    if (!lock) {
      console.log(
        `Failed to acquire lock for asset ${gDriveAsset.id}, skipping.`,
      );
      return;
    }

    let fileData: Buffer;

    const schema = client.database_schema_id;

    try {
      // Get related asset sheets
      const assetSheets = await getGDriveAssetSheetsByAssetId(
        schema,
        gDriveAsset.id,
      );
      if (!assetSheets.length) {
        console.log(`No asset sheets found for asset id [${gDriveAsset.id}]`);
        return;
      }

      const assetSheetFileAssetIdMap =
        assetSheetData?.assetSheetFileAssetIdMap ?? {};

      // If acquisition type is update, then we need to create a new version of the document.
      if (acquisitionType === AcquisitionType.UPDATE) {
        const assetSheetFiles: FileWithMetadata[] = assetSheets.map(
          (assetSheet) => {
            const fileMetadata = {
              file_name: gDriveAsset.name,
              label: assetSheet.details.doc_name,
              file_mimetype: constants.MIME_TYPE_CSV,
              buffer: null,
              uuid: uuidv4(),

              privacy: 'public',

              typeId: assetSheet.details.doc_type_id,
              source_url: gDriveAsset.url,

              // Important to uploading a new version
              prior_version_document_id: assetSheet.document_id,

              typeIsRfp: assetSheet.type === ASSET_TYPE_RFP,
              typeIsStructured: assetSheet.type === ASSET_TYPE_SPREADSHEET,
              typeIsInsight: assetSheet.type === ASSET_TYPE_INSIGHT,

              created_by_id: gDriveAsset.user_id,
              created_date: new Date(),

              // Only false if there's a prior version to be set
              use_for_generation: !assetSheet.document_id,

              status: 'processing',

              headerRow: assetSheet.details.header_row,
              questionColumns: assetSheet.details.question_columns,
              answerColumns: assetSheet.details.answer_columns,
            } as FileWithMetadata;

            assetSheetFileAssetIdMap[assetSheet.id] = fileMetadata;

            return fileMetadata;
          },
        );

        // Each asset sheet needs its own job id
        // TODO: bulk insert (similar pattern to insertDocs)
        for (let i = 0; i < assetSheetFiles.length; i++) {
          const jobId = await writePartialJobRecord(
            [assetSheetFiles[i]],
            gDriveAsset.user_id,
            schema,
            assetSheetFiles[i].typeIsRfp ? 'ingest' : 'digest',
          );
          assetSheetFiles[i].job_id = jobId;
          assetSheets[i].job_id = jobId;
          assetSheets[i].blob_id = assetSheetFiles[i].uuid;
        }

        await insertDocs(assetSheetFiles, schema);
        assetSheets.forEach((asset, idx) => {
          asset.document_id = assetSheetFiles[idx].id;
        });

        await updateAssetSheets(
          client.database_schema_id,
          assetSheets.reduce((acc, asset) => {
            acc[asset.id] = {
              document_id: asset.document_id,
              job_id: asset.job_id,
              blob_id: asset.blob_id,
            };
            return acc;
          }, {}),
        );
      }

      await updateGDriveAsset(schema, gDriveAsset.id, {
        status: 'acquiring',
      });
      gDriveAsset.status = 'acquiring';

      // RFP-specific workflow below.
      const exportType =
        exportMimeTypesMap[gDriveAsset.mime_type] || gDriveAsset.mime_type;
      const reqs = [];

      if (gDriveAsset.mime_type === constants.MIME_TYPE_CSV) {
        const resp = await drive.files.get(
          {
            fileId: gDriveAsset.drive_id,
            supportsAllDrives: true,
            alt: 'media',
          },
          {
            responseType: 'arraybuffer',
          },
        );

        fileData = Buffer.from(resp.data as ArrayBuffer);

        const blobClient = containerClient.getBlockBlobClient(
          assetSheets[0].blob_id,
        );

        // Upload to Azure Blob Storage
        await blobClient.uploadData(fileData, {
          blobHTTPHeaders: { blobContentType: exportType },
        });

        if (
          assetSheets[0].type === ASSET_TYPE_RFP ||
          assetSheets[0].type === ASSET_TYPE_INSIGHT
        ) {
          reqs.push(
            createAddQuestionnaireSheetRequest(
              client,
              gDriveAsset,
              assetSheets[0],
              acquisitionType === AcquisitionType.UPDATE ||
                (assetSheetData?.updatedAssetSheetIds ?? []).includes(
                  Number(assetSheets[0].id),
                ),
              MimeType.MIME_TYPE_CSV,
            ),
          );
        } else if (assetSheets[0].type === ASSET_TYPE_SPREADSHEET) {
          const assetSheetFileMetadata =
            assetSheetFileAssetIdMap[assetSheets[0].id];

          // This should exist, but just in case...
          if (assetSheetFileMetadata) {
            // Call Digest directly (same as flat file import)
            const payload: StructuredFileRequest & { is_structured: boolean } =
              {
                blob_id: assetSheetFileMetadata.uuid,
                client_id: client.id,
                document_id: String(assetSheetFileMetadata.id), // why does doc_id need to be a string...
                label: assetSheetFileMetadata.file_name,
                mime_type: assetSheetFileMetadata.file_mimetype,
                schema,
                is_structured: true,
                job_id: assetSheetFileMetadata.job_id,
                is_update:
                  assetSheetFileMetadata.prior_version_document_id !==
                  undefined,
              };
            if (assetSheetFileMetadata.sheetId) {
              payload.sheet_id = assetSheetFileMetadata.sheetId;
            }
            send_function_request({
              client_id: client.id,
              user_id: gDriveAsset.user_id,
              function_name: 'digest',
              job_id: assetSheetFileMetadata.job_id,
              payload,
              schema,
            });
          }
        }
      } else {
        let result;

        if (gDriveAsset.mime_type === constants.MIME_TYPE_XLSX) {
          result = await drive.files.get(
            {
              fileId: gDriveAsset.drive_id,
              supportsAllDrives: true,
              alt: 'media',
            },
            {
              responseType: 'arraybuffer',
            },
          );
        } else {
          result = await drive.files.export(
            {
              fileId: gDriveAsset.drive_id,
              mimeType: exportType,
            },
            { responseType: 'arraybuffer' },
          );
        }

        const fileDataArrayBuffer = result.data as ArrayBuffer;

        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.load(fileDataArrayBuffer);

        for (const assetSheet of assetSheets) {
          try {
            const csvBuffer = await workbook.csv.writeBuffer({
              sheetId: assetSheet.sheet_id,
            });
            fileData = Buffer.from(csvBuffer as ArrayBuffer);

            const blobClient = containerClient.getBlockBlobClient(
              assetSheet.blob_id,
            );

            // Upload Csv to Azure Blob Storage
            await blobClient.uploadData(fileData, {
              blobHTTPHeaders: { blobContentType: exportType },
            });

            if (
              assetSheet.type === ASSET_TYPE_RFP ||
              assetSheet.type === ASSET_TYPE_INSIGHT
            ) {
              reqs.push(
                createAddQuestionnaireSheetRequest(
                  client,
                  gDriveAsset,
                  assetSheet,
                  acquisitionType === AcquisitionType.UPDATE ||
                    (assetSheetData?.updatedAssetSheetIds ?? []).includes(
                      Number(assetSheet.id),
                    ),
                  MimeType.MIME_TYPE_CSV,
                ),
              );
            } else if (assetSheet.type === ASSET_TYPE_SPREADSHEET) {
              const assetSheetFileMetadata =
                assetSheetFileAssetIdMap[assetSheet.id];

              // This should exist, but just in case...
              if (assetSheetFileMetadata) {
                // Call Digest directly (same as flat file import)
                const payload: StructuredFileRequest & {
                  is_structured: boolean;
                } = {
                  blob_id: assetSheetFileMetadata.uuid,
                  client_id: client.id,
                  document_id: String(assetSheetFileMetadata.id), // why does doc_id need to be a string...
                  label: assetSheetFileMetadata.file_name,
                  mime_type: assetSheetFileMetadata.file_mimetype,
                  schema,
                  is_structured: true,
                  job_id: assetSheetFileMetadata.job_id,
                  is_update:
                    assetSheetFileMetadata.prior_version_document_id !==
                    undefined,
                };
                if (assetSheetFileMetadata.sheetId) {
                  payload.sheet_id = assetSheetFileMetadata.sheetId;
                }
                send_function_request({
                  client_id: client.id,
                  user_id: gDriveAsset.user_id,
                  function_name: 'digest',
                  job_id: assetSheetFileMetadata.job_id,
                  payload,
                  schema,
                });
              }
            }
          } catch (error) {
            console.error(
              `Failed to load workbook for GDrive Asset Sheet Id=[${assetSheet.id}]: ${error.message}`,
            );
            return;
          }
        }
      }

      await updateGDriveAsset(client.database_schema_id, gDriveAsset.id, {
        status: 'processing',
      });
      gDriveAsset.status = 'processing';

      const ingestClient = getGrpcBrainIngestClient();

      // Send brain ingest request.
      // If separate tabs, each gets their own request (distinct doc_id/job_id)
      for (const req of reqs) {
        const deadline = new Date(),
          timeoutSecs = 5;
        deadline.setSeconds(deadline.getSeconds() + timeoutSecs);

        ingestClient.addQuestionnaire(
          req,
          new grpc.Metadata(),
          { deadline: deadline.getTime() },
          (err: Error, _) => {
            if (err) {
              // throw new Error(
              //   `Failed to ingest document ${asset.drive_id}: ${err.message}`,
              // );
              console.error(
                `Failed to ingest document ${gDriveAsset.drive_id}: ${err.message}`,
              );
            }
          },
        );
      }
    } finally {
      // Finally ensures lock is released when complete.
      await releaseDatabaseLock(
        client.id,
        gDriveAsset.id,
        'google_drive',
        dbClient,
      );
    }
  } finally {
    dbClient.release();
  }
}

async function listAllFilesInFolders(
  drive: drive_v3.Drive,
  folderIds: string[],
): Promise<string[]> {
  let allFileIds: string[] = [];

  for (const folderId of folderIds) {
    let pageToken: string | undefined;

    do {
      const response = await drive.files.list({
        q: `'${folderId}' in parents and trashed = false`,
        fields: 'nextPageToken, files(id)',
        pageSize: 1000, // Adjust the page size as appropriate
        pageToken: pageToken,
      });

      if (response.data.files) {
        allFileIds = allFileIds.concat(
          response.data.files.map((file) => file.id),
        );
      }

      pageToken = response.data.nextPageToken;
    } while (pageToken);
  }

  return allFileIds;
}

export async function driveClientForUser(
  schema: string,
  userId: number,
): Promise<drive_v3.Drive> {
  // Load Google Drive system integration.
  const res = await getUserAndSystemIntegration(schema, SYSTEM_NAME, userId);
  if (!res && res.length === 0) {
    throw new Error('Google Drive integration not found');
  }
  if (!res[0].auth_details) {
    throw new Error('Google Drive integration auth not found');
  }
  const authDetails = JSON.parse(decryptText(res[0].auth_details));
  const oauth2Client = new google.auth.OAuth2(
    process.env.GOOGLE_DRIVE_CLIENT_ID,
    process.env.GOOGLE_DRIVE_CLIENT_SECRET,
    process.env.GOOGLE_DRIVE_REDIRECT_URL,
  );
  oauth2Client.setCredentials({
    refresh_token: authDetails.refreshToken,
  });

  return google.drive({ version: 'v3', auth: oauth2Client });
}

function createAddQuestionnaireSheetRequest(
  client: Client,
  asset: GDriveAsset,
  assetSheet: AssetSheet,
  isUpdate: boolean,
  assetMimeType: MimeType,
) {
  const req = new AddQuestionnaireRequest();
  req.setClientId(client.id);
  req.setSchema(client.database_schema_id);
  req.setUserId(asset.user_id);
  req.setDocumentId(assetSheet.document_id);
  req.setJobId(assetSheet.job_id);
  req.setMimeType(assetMimeType);
  req.setFilename(asset.name);
  req.setBlobName(assetSheet.blob_id);
  req.setIsUpdate(isUpdate);
  if (asset.last_modified) {
    const lastModified = new timestamppb.Timestamp();
    lastModified.fromDate(asset.last_modified);
    req.setLastModifiedTime(lastModified);
    // Also set source modified date. For Google Drive, this is the same as last_modified
    req.setSourceModifiedDate(lastModified);
  }
  if (asset.source_created_date) {
    const sourceCreatedDate = new timestamppb.Timestamp();
    sourceCreatedDate.fromDate(asset.source_created_date);
    req.setSourceCreatedDate(sourceCreatedDate);
  }
  const sourceInfo = new DocumentSourceInfo();
  sourceInfo.setSource(DocumentSource.GOOGLE_DRIVE);
  sourceInfo.setSourceId(asset.drive_id);
  sourceInfo.setSourceLocation(asset.url);
  req.setSourceInfo(sourceInfo);

  // RFP-specific values.
  // req.setFirstRowIsHeader(asset.rfp_details!.first_row_is_header);
  req.setHeaderRowIndex(assetSheet.details.header_row);
  for (const col of assetSheet.details.question_columns) {
    req.addQuestionColumnsIndices(col);
  }
  for (const col of assetSheet.details.answer_columns) {
    req.addAnswerColumnsIndices(col);
  }

  // Insight-specific values.
  for (const col of assetSheet.details.insight_columns) {
    req.addInsightColumnsIndices(col);
  }

  return req;
}

export const unwatchRecursive = async (
  userId: number,
  schema: string,
  assetId: number,
  parentId: string,
) => {
  const childrens = await getFoldersChildrenRecursive(parentId, userId, schema);
  const assets = await getGDriveAssetsByDriveIds(
    schema,
    childrens.map((c) => c.id),
  );
  console.log(`[ unwatchRecursive ] ${assets.length + 1} assets`);

  await deleteGDriveAssetTasks(
    schema,
    assets.map((asset) => asset.id),
  );
  return await updateGDriveAssets(
    schema,
    [{ id: assetId }, ...assets].reduce(
      (acc, asset) => {
        acc[asset.id] = {
          watch_for_changes: false,
          task_id: null,
        };
        return acc;
      },
      {} as { [id: number]: Partial<GDriveAsset> },
    ),
  );
};

const getFoldersChildrenRecursive = async (
  parentId: string,
  userId: number,
  schema: string,
) => {
  const drive = await driveClientForUser(schema, userId);
  console.log(`[ getFoldersChildrenRecursive ] parentId: ${parentId}`);

  let files: { [key: string]: drive_v3.Schema$File } = {};
  const folders: drive_v3.Schema$File[] = [];
  const newFiles = await getAllFilesWithPaginate(parentId, drive);
  for (const file of newFiles) {
    if (!files[file.id]) {
      files[file.id] = file;
    }
    if (file.mimeType === constants.MIME_TYPE_GOOGLE_FOLDER) {
      folders.push(file);
    }
  }

  while (folders.length > 0) {
    const folder = folders.shift();
    if (!folder?.id) {
      console.warn(
        `[ getFoldersChildrenRecursive ] Folder id not found (Name? ${folder?.name})`,
      );
      continue;
    }

    const children = await getFoldersChildrenRecursive(
      folder.id,
      userId,
      schema,
    );
    for (const child of children) {
      if (!files[child.id]) {
        files[child.id] = child;
      }
    }
  }

  return Object.values(files);
};

const getAllFilesWithPaginate = async (
  parentId: string,
  drive: drive_v3.Drive,
) => {
  let files: drive_v3.Schema$File[] = [];
  let FILES_UPPER_LIMIT = 5000;
  let nextPageToken = null;

  const queryParent = `and '${parentId}' in parents`;
  const mimeTypes =
    `mimeType = '${constants.MIME_TYPE_GOOGLE_DOC}' ` +
    `or mimeType = '${constants.MIME_TYPE_GOOGLE_SHEET}' ` +
    `or mimeType = '${constants.MIME_TYPE_GOOGLE_SLIDE}' ` +
    `or mimeType = '${constants.MIME_TYPE_DOCX}' ` +
    `or mimeType = '${constants.MIME_TYPE_PPTX}' ` +
    `or mimeType = '${constants.MIME_TYPE_XLSX}' ` +
    `or mimeType = '${constants.MIME_TYPE_CSV}'` +
    `or mimeType = '${constants.MIME_TYPE_PDF}'` +
    `or mimeType = '${constants.MIME_TYPE_GOOGLE_FOLDER}'` +
    `or mimeType = '${constants.MIME_TYPE_TEXT_PLAIN}'`;

  do {
    const resp = await drive.files.list({
      includeItemsFromAllDrives: true,
      supportsAllDrives: true,
      fields:
        'nextPageToken, files(id, driveId, name, owners, mimeType, iconLink, thumbnailLink, webViewLink, modifiedTime, parents)',
      q: `trashed=false ${queryParent} and (${mimeTypes})`,
      pageSize: 1000,
      pageToken: nextPageToken,
    });
    files = files.concat((resp.data.files as drive_v3.Schema$File[]) ?? []);
    nextPageToken = resp.data.nextPageToken;

    if (nextPageToken) {
      console.log(
        `[/api/googledrive/list] nextPageToken found (# of files: ${files.length})`,
      );
    }
  } while (nextPageToken && files.length < FILES_UPPER_LIMIT);

  return files;
};

const createReviewTaskAndNotification = async (
  assets: GDriveAsset[],
  schema: string,
  userId: number,
  clientId: number,
) => {
  console.log(`[ createReviewTaskAndNotification ] ${assets.length} assets`);
  const reviewTasks = await upsertMultipleAssetTasks(
    schema,
    assets.map((asset) => {
      return {
        assetId: Number(asset.id),
        label: `Google Drive Folder Sync - A new spreadsheet was found: ${asset.name}`,
        priority: 1,
        status: 'not_started',
        description: `A new spreadsheet (${asset.name}) was found. Please configure it before the data can be loaded.`,
        created_by_id: userId as UserId,
        created_at: new Date(),
        origin: 'admin',
        type: constants.TASK_TYPE_CONFIGURE_ASSET_SHEET,
      };
    }),
    'gdrive_asset',
  );

  if (reviewTasks && reviewTasks.length > 0) {
    const details = createSheetTaskNotificationDetails(
      reviewTasks,
      'Google Drive',
      `${process.env.APP_PATH}/review/gdrive`,
    );
    await upsertScheduledNotification({
      user_id: userId as UserId,
      client_id: clientId as ClientId,
      type: 'configure_sheet_asset',
      parent_id: reviewTasks[0],
      created_by: userId as UserId,
      details,
    });
    console.log(`[ createReviewTaskAndNotification ] Notification scheduled`);
  }
};

export const getNewChildren = async (
  assets: GDriveAsset[],
  userId: number,
  schema: string,
  clientId: number,
) => {
  const directories = assets.filter(
    (g) =>
      g.mime_type === constants.MIME_TYPE_GOOGLE_FOLDER ||
      g.mime_type === constants.MIME_TYPE_GOOGLE_DRIVE,
  );

  let directoryChildren: drive_v3.Schema$File[] = [];
  for (const directory of directories) {
    const children = await getFoldersChildrenRecursive(
      directory.drive_id,
      userId,
      schema,
    );

    directoryChildren = directoryChildren.concat(children);
  }

  const newChildren = directoryChildren
    .filter((c) => !assets.find((g) => g.drive_id === c.id))
    .filter(
      (value, index, self) =>
        // Apparently there can be duplicates...remove them
        index === self.findIndex((c) => c.id === value.id),
    );

  console.log(`[ getNewChildren ] Found [${newChildren.length}] new children`);

  if (newChildren.length === 0) {
    return [];
  }

  const newAssets = await upsertGDriveAssets(
    schema,
    newChildren.map((c) => ({
      drive_id: c.id,
      user_id: userId,
      name: c.name,
      mime_type: c.mimeType,
      status: 'new',
      watch_for_changes: true,
      url: c.webViewLink,
      type:
        c.mimeType === constants.MIME_TYPE_GOOGLE_FOLDER
          ? 'folder'
          : c.mimeType === constants.MIME_TYPE_GOOGLE_SHEET // Sheet can be an RFP Q&A or sprreadsheet
            ? null
            : c.mimeType === constants.MIME_TYPE_GOOGLE_SLIDE
              ? ASSET_TYPE_SLIDE
              : ASSET_TYPE_DOCUMENT,
      parent_id: c.parents?.[0],
      created_at: new Date(),
    })),
  );

  // For now, only send to Review if it's a Google Sheet
  const unconfiguredAssets = newAssets.filter(
    (n) =>
      n.mime_type === constants.MIME_TYPE_GOOGLE_SHEET ||
      n.mime_type === constants.MIME_TYPE_XLSX,
  );

  if (unconfiguredAssets.length > 0) {
    await createReviewTaskAndNotification(
      unconfiguredAssets,
      schema,
      userId,
      clientId,
    );
  }

  return newAssets.filter(
    (n) =>
      n.mime_type !== constants.MIME_TYPE_GOOGLE_SHEET &&
      n.mime_type !== constants.MIME_TYPE_XLSX,
  );
};
