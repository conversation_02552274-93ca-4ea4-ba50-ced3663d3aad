import { AssetSheet } from '..';
import { constants } from '../../constants';
import { GDriveAsset } from './index';

const { query, getClient } = require('../../db/index');

// insertGDriveAssets inserts a list of GDrive assets into the database, if
// the asset is already there it updates watch_for_changes and user_id
// Returns the inserted assets, excluding those already in the database.
export async function upsertGDriveAssets(
  schema: string,
  assets: Partial<GDriveAsset>[],
): Promise<GDriveAsset[]> {
  if (assets.length === 0) return [];

  const valueCount = 15, // Number of columns in the GDrive asset table
    values = assets
      .map(
        (_, index) => `(
       $${index * valueCount + 1}, $${index * valueCount + 2},
       $${index * valueCount + 3}, $${index * valueCount + 4}, 
       $${index * valueCount + 5}, $${index * valueCount + 6}, 
       $${index * valueCount + 7}, $${index * valueCount + 8},
       $${index * valueCount + 9}, $${index * valueCount + 10},
       $${index * valueCount + 11}, $${index * valueCount + 12},
       $${index * valueCount + 13}, $${index * valueCount + 14},
       $${index * valueCount + 15}
      )`,
      )
      .join(', ');

  const queryString = `
      INSERT INTO ${schema}.${constants.TABLE_GDRIVE_ASSET} (
        drive_id,
        blob_id,
        document_id,
        job_id,
        user_id,
        name,
        mime_type,
        parent_id,
        status,
        last_checked,
        last_modified,
        source_created_date,
        url,
        type,
        watch_for_changes
      ) VALUES ${values}
      ON CONFLICT (drive_id) DO UPDATE
       SET 
        watch_for_changes = EXCLUDED.watch_for_changes,
        user_id = EXCLUDED.user_id
      RETURNING *;
    `;

  const queryParams = [];
  assets.forEach((asset) => {
    queryParams.push(
      asset.drive_id,
      asset.blob_id,
      asset.document_id,
      asset.job_id,
      asset.user_id,
      asset.name,
      asset.mime_type,
      asset.parent_id,
      asset.status,
      asset.last_checked,
      asset.last_modified,
      asset.source_created_date,
      asset.url,
      asset.type,
      asset.watch_for_changes,
    );
  });

  try {
    const result = await query(queryString, queryParams);
    if (result && result.rows) {
      return result.rows.map((row: any) => ({
        id: row.id,
        drive_id: row.drive_id,
        blob_id: row.blob_id,
        document_id: row.document_id,
        user_id: row.user_id,
        name: row.name,
        mime_type: row.mime_type,
        parent_id: row.parent_id,
        status: row.status,
        last_checked: row.last_checked,
        last_modified: row.last_modified,
        source_created_date: row.source_created_date,
        created_at: row.created_at,
        updated_at: row.updated_at,
        url: row.url,
        type: row.type,
        watch_for_changes: row.watch_for_changes,
      }));
    }
  } catch (error) {
    console.error('Error inserting GDrive assets:', error);
  }
  return [];
}

// For Sheets asset, write a record in the gdrive_asset_sheets table
// for each tab/sheet that was ingested from the Spreadsheet
export async function insertGDriveAssetSheets(
  schema: string,
  assetSheets: Partial<AssetSheet>[],
): Promise<AssetSheet[]> {
  if (assetSheets.length === 0) return [];
  const client = await getClient();

  try {
    await client.query('BEGIN');

    const integrationResult = await client.query(
      `SELECT id from ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_INTEGRATION_SYSTEM} where system_name = '${constants.INTEGRATION_SYSTEM_GDRIVE}'`,
    );
    const integrationId = integrationResult.rows[0].id;

    const valueCount = 7, // Number of columns in the GDrive asset table
      values = assetSheets
        .map(
          (_, index) => `(
       $${index * valueCount + 1}, $${index * valueCount + 2},
       $${index * valueCount + 3}, $${index * valueCount + 4}, 
       $${index * valueCount + 5}, $${index * valueCount + 6},
       $${index * valueCount + 7}, ${integrationId}
      )`,
        )
        .join(', ');

    const queryString = `
      INSERT INTO ${schema}.${constants.TABLE_INTEGRATION_ASSET_SHEET} (
        asset_id,
        document_id,
        blob_id,
        sheet_id,
        sheet_name,
        type,
        details,
        integration_id
      ) VALUES ${values}
        ON CONFLICT (asset_id, sheet_id, integration_id) DO UPDATE SET
        details = EXCLUDED.details,
        blob_id = EXCLUDED.blob_id,
        document_id = EXCLUDED.document_id,
        type = EXCLUDED.type
      RETURNING *;
    `;

    const queryParams = [];
    assetSheets.forEach((asset) => {
      queryParams.push(
        asset.asset_id,
        asset.document_id,
        asset.blob_id,
        asset.sheet_id,
        asset.sheet_name,
        asset.type,
        asset.details,
      );
    });

    const result = await client.query(queryString, queryParams);
    await client.query('COMMIT');

    if (result && result.rows) {
      return result.rows.map((row: any) => ({
        id: row.id,
        asset_id: row.asset_id,
        document_id: row.document_id,
        blob_id: row.blob_id,
        sheet_id: row.sheet_id,
        sheet_name: row.sheet_name,
        type: row.type,
        details: row.details,
      }));
    }
  } catch (error) {
    console.error('Error upserting GDrive asset sheets:', error);
    await client.query('ROLLBACK');
  } finally {
    client.release();
  }
  return [];
}

export async function getGDriveAssets(
  schema: string,
  limit?: number,
  offset?: number,
  userId?: number,
  watchForChanges?: boolean,
): Promise<GDriveAsset[]> {
  let queryString = `
    SELECT
      g.id,
      g.drive_id,
      COALESCE(ias.blob_id, g.blob_id) as blob_id,
      COALESCE(ias.document_id, g.document_id) as document_id,
      COALESCE(ias.job_id, g.job_id) as job_id,
      g.user_id,
      g.name,
      g.mime_type,
      g.parent_id,
      g.status,
      g.last_checked,
      g.last_modified,
      g.created_at,
      g.updated_at,
      g.url,
      g.type,
      g.watch_for_changes
    FROM ${schema}.${constants.TABLE_GDRIVE_ASSET} g
    LEFT JOIN ${schema}.${constants.TABLE_INTEGRATION_ASSET_SHEET} ias
      ON ias.asset_id = g.id
    LEFT JOIN ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_INTEGRATION_SYSTEM} ins
      ON ins.id = ias.integration_id
    WHERE ins.system_name = '${constants.INTEGRATION_SYSTEM_GDRIVE}' OR ins.system_name IS NULL
  `;

  const queryParams: any[] = [];

  // Add condition for user_id if provided
  if (userId !== undefined) {
    queryString += ` AND user_id = $${queryParams.length + 1}`;
    queryParams.push(userId);
  }

  if (watchForChanges !== undefined) {
    queryString += ` AND watch_for_changes = $${queryParams.length + 1}`;
    queryParams.push(watchForChanges);
  }

  // Append LIMIT clause if limit is provided
  if (limit !== undefined) {
    queryString += ` LIMIT $${queryParams.length + 1}`;
    queryParams.push(limit);
  }

  // Append OFFSET clause if offset is provided
  if (offset !== undefined) {
    queryString += ` OFFSET $${queryParams.length + 1}`;
    queryParams.push(offset);
  }

  try {
    const result = await query(queryString, queryParams);
    if (result && result.rows) {
      return result.rows.map((row: any) => ({
        id: row.id,
        drive_id: row.drive_id,
        blob_id: row.blob_id,
        document_id: row.document_id,
        job_id: row.job_id,
        user_id: row.user_id,
        name: row.name,
        mime_type: row.mime_type,
        parent_id: row.parent_id,
        status: row.status,
        last_checked: row.last_checked,
        last_modified: row.last_modified,
        created_at: row.created_at,
        updated_at: row.updated_at,
        url: row.url,
        type: row.type,
        watch_for_changes: row.watch_for_changes,
      }));
    }
  } catch (error) {
    console.error('Error reading GDrive assets:', error);
  }
  return [];
}

async function fetchGDriveAssets(
  schema: string,
  ids: string[] | number[],
  column: keyof GDriveAsset,
): Promise<GDriveAsset[]> {
  const placeholders = ids.map((_, index) => `$${index + 1}`).join(', ');
  const queryString = `
    SELECT
      g.id,
      g.drive_id,
      g.blob_id,
      g.document_id,
      g.job_id,
      g.user_id,
      g.name,
      g.mime_type,
      g.parent_id,
      g.status,
      g.last_checked,
      g.last_modified,
      g.created_at,
      g.updated_at,
      g.url,
      g.type,
      g.watch_for_changes
    FROM ${schema}.${constants.TABLE_GDRIVE_ASSET} g
    WHERE g.${column} IN (${placeholders})`;

  const result = await query(queryString, ids);

  return result && result.rows
    ? result.rows.map((row: any) => ({
        id: row.id,
        drive_id: row.drive_id,
        blob_id: row.blob_id,
        document_id: row.document_id,
        job_id: row.job_id,
        user_id: row.user_id,
        name: row.name,
        mime_type: row.mime_type,
        parent_id: row.parent_id,
        status: row.status,
        last_checked: row.last_checked,
        last_modified: row.last_modified,
        created_at: row.created_at,
        updated_at: row.updated_at,
        url: row.url,
        type: row.type,
        watch_for_changes: row.watch_for_changes,
      }))
    : [];
}

async function fetchGDriveAssetSheets(
  schema: string,
  ids: number[],
  column: keyof AssetSheet,
): Promise<AssetSheet[]> {
  const placeholders = ids.map((_, index) => `$${index + 1}`).join(', ');
  const queryString = `
    SELECT
      ias.id,
      ias.asset_id,
      ias.document_id,
      ias.job_id,
      ias.blob_id,
      ias.sheet_id,
      ias.sheet_name,
      ias.type,
      ias.details
    FROM ${schema}.${constants.TABLE_INTEGRATION_ASSET_SHEET} ias
    INNER JOIN ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_INTEGRATION_SYSTEM} ins
      ON ias.integration_id = ins.id
    WHERE ins.system_name = '${constants.INTEGRATION_SYSTEM_GDRIVE}'
    AND ias.${column} IN (${placeholders})`;

  const result = await query(queryString, ids);

  return result && result.rows
    ? result.rows.map((row: any) => ({
        id: row.id,
        asset_id: row.asset_id,
        document_id: row.document_id,
        job_id: row.job_id,
        blob_id: row.blob_id,
        sheet_id: row.sheet_id,
        sheet_name: row.sheet_name,
        type: row.type,
        details: row.details,
      }))
    : [];
}

async function getGDriveAssetsByIds(
  schema: string,
  ids: number[],
): Promise<GDriveAsset[]> {
  return await fetchGDriveAssets(schema, ids, 'id');
}

export async function getGDriveAssetById(
  schema: string,
  id: number,
): Promise<GDriveAsset | null> {
  return await getGDriveAssetsByIds(schema, [id]).then((assets) =>
    assets.length > 0 ? assets[0] : null,
  );
}

export async function getGDriveAssetsByDriveIds(
  schema: string,
  driveIds: string[],
): Promise<GDriveAsset[]> {
  return await fetchGDriveAssets(schema, driveIds, 'drive_id');
}

export async function getGDriveAssetsByDocumentIds(
  schema: string,
  documentIds: number[],
): Promise<GDriveAsset[]> {
  return await fetchGDriveAssets(schema, documentIds, 'document_id');
}

export async function getGDriveAssetByDocumentId(
  schema: string,
  documentId: number,
): Promise<GDriveAsset | null> {
  return await getGDriveAssetsByDocumentIds(schema, [documentId]).then(
    (assets) => (assets.length > 0 ? assets[0] : null),
  );
}

export async function getGDriveAssetsWithTask(
  schema: string,
): Promise<GDriveAsset[]> {
  const queryString = `SELECT tao.*, t.*, a.* FROM ${schema}.${constants.TABLE_GDRIVE_ASSET} a
   JOIN ${schema}.${constants.TABLE_TASK} t ON a.task_id = t.id
   JOIN ${schema}.${constants.TABLE_TASK_OWNER} tao ON t.id = tao.task_id
   WHERE t.STATUS != '${constants.TASK_STATUS_COMPLETED}' AND t.STATUS != '${constants.TASK_STATUS_DELETED}'`;

  const result = await query(queryString);
  return result && result.rows ? result.rows : [];
}

export async function getGDriveAssetSheetsByAssetId(
  schema: string,
  assetId: number,
): Promise<AssetSheet[]> {
  const queryString = `
    SELECT ias.*
    FROM ${schema}.${constants.TABLE_INTEGRATION_ASSET_SHEET} ias
    INNER JOIN ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_INTEGRATION_SYSTEM} ins 
      ON ias.integration_id = ins.id
    WHERE ins.system_name = '${constants.INTEGRATION_SYSTEM_GDRIVE}'
      AND asset_id = $1`;

  const result = await query(queryString, [assetId]);

  return result && result.rows ? (result.rows as AssetSheet[]) : [];
}

export async function getGDriveAssetSheetsByDocumentIds(
  schema: string,
  documentIds: number[],
): Promise<AssetSheet[]> {
  return await fetchGDriveAssetSheets(schema, documentIds, 'document_id');
}

export async function updateGDriveAsset(
  schema: string,
  assetId: number,
  asset: Partial<GDriveAsset>,
): Promise<void> {
  const setClauses: string[] = [];
  const queryParams: any[] = [];

  let paramIndex = 1;

  for (const key of Object.keys(asset)) {
    if (asset[key] !== undefined) {
      setClauses.push(`${key} = $${paramIndex}`);
      queryParams.push(asset[key]);
      paramIndex++;
    }
  }

  const updatedAt = new Date();
  asset.updated_at = updatedAt; // Set updated_at on the asset object

  setClauses.push(`updated_at = $${paramIndex}`);
  queryParams.push(updatedAt);

  queryParams.push(assetId); // Add the assetId to the parameters for the WHERE clause

  const queryString = `
    UPDATE ${schema}.${constants.TABLE_GDRIVE_ASSET}
    SET ${setClauses.join(', ')}
    WHERE id = $${paramIndex + 1}
  `;

  try {
    await query(queryString, queryParams);
  } catch (error) {
    console.error('Error updating GDrive asset:', error);
  }
}

export async function updateGDriveAssets(
  schema: string,
  assets: { [id: number]: Partial<GDriveAsset> },
): Promise<boolean> {
  const client = await getClient();
  await client.query('BEGIN');

  const updatedAt = new Date();

  try {
    for (const [assetId, asset] of Object.entries(assets)) {
      const setClauses: string[] = [];
      const queryParams: any[] = [];
      let paramIndex = 1; // Ensure unique param indices

      for (const key of Object.keys(asset)) {
        if (asset[key] !== undefined) {
          setClauses.push(`${key} = $${paramIndex}`);
          queryParams.push(asset[key]);
          paramIndex++;
        }
      }

      asset.updated_at = updatedAt; // Set updated_at on the asset object

      setClauses.push(`updated_at = $${paramIndex}`);
      queryParams.push(updatedAt);

      queryParams.push(assetId); // Add the assetId to the parameters for the WHERE clause

      if (setClauses.length > 0) {
        const queryString = `
          UPDATE ${schema}.${constants.TABLE_GDRIVE_ASSET}
          SET ${setClauses.join(', ')}
          WHERE id = $${paramIndex + 1};
        `;
        await client.query(queryString, queryParams);
      }
    }

    await client.query('COMMIT');
    return true;
  } catch (error) {
    console.error('Error updating multiple GDrive assets:', error);
    await client.query('ROLLBACK');
    return false;
  } finally {
    client.release();
  }
}

export async function deleteGDriveAssetTasks(
  schema: string,
  assetIds: number[],
) {
  const placeholders = assetIds.map((_, index) => `$${index + 1}`).join(', ');
  const queryString = `
    DELETE FROM ${schema}.${constants.TABLE_TASK} t
    USING ${schema}.${constants.TABLE_TASK_OWNER} tao
    WHERE t.id = tao.task_id
    AND t.id IN (
      SELECT task_id
      FROM ${schema}.${constants.TABLE_GDRIVE_ASSET}
      WHERE id IN (${placeholders})
    )
  `;
  try {
    await query(queryString, assetIds);
  } catch (error) {
    console.error('Error deleting tasks and task owners:', error);
  }
}

export async function updateAssetSheets(
  schema: string,
  assetSheets: { [id: number]: Partial<AssetSheet> },
): Promise<void> {
  const client = await getClient();

  try {
    await client.query('BEGIN');

    for (const [assetSheetId, asset] of Object.entries(assetSheets)) {
      const queryParams: any[] = [];
      const setClauses: string[] = [];

      let paramIndex = 1;

      for (const key of Object.keys(asset)) {
        if (asset[key] !== undefined) {
          setClauses.push(`${key} = $${paramIndex}`);
          queryParams.push(asset[key]);
          paramIndex++;
        }
      }

      // Add the assetId to the parameters for the WHERE clause
      queryParams.push(assetSheetId);

      if (setClauses.length > 0) {
        const queryString = `
          UPDATE ${schema}.${constants.TABLE_INTEGRATION_ASSET_SHEET}
          SET ${setClauses.join(', ')}
          WHERE id = $${paramIndex};
        `;

        try {
          await client.query(queryString, queryParams);
        } catch (error) {
          console.error(`Error updating Asset sheet [${assetSheetId}]`, error);
        }
      }
    }

    await client.query('COMMIT');
  } catch (err) {
    await client.query('ROLLBACK');
    console.error(`[updateAssetSheets] Error encountered: `, err);
  } finally {
    client.release();
  }
}

export async function deleteGDriveAssetsByIds(
  schema: string,
  ids: number[],
): Promise<number[]> {
  if (ids.length === 0) return [];
  const placeholders = ids.map((_, index) => `$${index + 1}`).join(', ');
  const queryString = `
    DELETE FROM ${schema}.${constants.TABLE_GDRIVE_ASSET}
    WHERE id IN (${placeholders})
    RETURNING id;
  `;

  try {
    const result = await query(queryString, ids);
    if (result && result.rows) {
      return result.rows.map((row: { id: number }) => row.id);
    }
  } catch (error) {
    console.error('Error deleting GDrive assets:', error);
  }

  return [];
}

export async function deleteGDriveAssetSheetsByColumnIds(
  schema: string,
  column: keyof AssetSheet,
  ids: number[],
): Promise<Partial<GDriveAsset>[]> {
  if (ids.length === 0) return [];
  const placeholders = ids.map((_, index) => `$${index + 1}`).join(', ');
  const queryString = `
    DELETE FROM ${schema}.${constants.TABLE_INTEGRATION_ASSET_SHEET} ias
    USING ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_INTEGRATION_SYSTEM} ins
    WHERE ias.integration_id = ins.id
    AND ins.system_name = '${constants.INTEGRATION_SYSTEM_GDRIVE}'
    AND ias.${column} IN (${placeholders})
    RETURNING *;
  `;

  try {
    const result = await query(queryString, ids);
    if (result && result.rows) {
      const assetIdsMap = result.rows.reduce((acc, row) => {
          acc[row.asset_id] = true;
          return acc;
        }, {}),
        assetIds = Object.keys(assetIdsMap).map((k) => Number(k));

      // Query if there exists any asset sheets remaining for the asset
      const assetQueryString = `
        SELECT a.id, count(ash.*) as cnt
        FROM ${schema}.${constants.TABLE_GDRIVE_ASSET} a
        LEFT JOIN ${schema}.${constants.TABLE_INTEGRATION_ASSET_SHEET} ash
          ON a.id = ash.asset_id
        JOIN ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_INTEGRATION_SYSTEM} ins
          ON ash.integration_id = ins.id
        WHERE ins.system_name = '${constants.INTEGRATION_SYSTEM_GDRIVE}'
          AND ash.integration_id = ins.id 
          AND a.id = ANY($1::bigint[])
        GROUP BY a.id
        HAVING count(ash.*) = 0
      `;

      // For any where there are no more asset sheets, delete the asset record
      const assetResult = await query(assetQueryString, [assetIds]);
      if (assetResult && assetResult.rows) {
        const assetsToDelete = assetResult.rows.map((row: any) => row.id);
        if (assetsToDelete.length > 0) {
          await deleteGDriveAssetsByIds(schema, assetsToDelete);
        }
      }
    }
  } catch (error) {
    console.error('Error deleting GDrive asset sheets:', error);
  }

  return [];
}

export async function deleteGDriveAssetSheetsByIds(
  schema: string,
  ids: number[],
  checkForOrphanGDriveAsset = false,
): Promise<Partial<GDriveAsset>[]> {
  if (ids.length === 0) return [];
  const queryString = `
    DELETE FROM ${schema}.${constants.TABLE_INTEGRATION_ASSET_SHEET}
    WHERE id = ANY($1::bigint[])
    RETURNING *;
  `;

  try {
    const result = await query(queryString, [ids]);
    if (result && result.rows) {
      // (Leaving this here in case ever useful)
      // Query if there exists any asset sheets remaining for the asset
      if (checkForOrphanGDriveAsset) {
        const assetIdsMap = result.rows.reduce((acc, row) => {
            acc[row.asset_id] = true;
            return acc;
          }, {}),
          assetIds = Object.keys(assetIdsMap).map((k) => Number(k));

        const assetQueryString = `
        SELECT a.id, count(ash.*) as cnt
        FROM ${schema}.${constants.TABLE_GDRIVE_ASSET} a
        LEFT JOIN ${schema}.${constants.TABLE_INTEGRATION_ASSET_SHEET} ash
          ON a.id = ash.asset_id
        JOIN ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_INTEGRATION_SYSTEM} ins
          ON ash.integration_id = ins.id
        WHERE a.id = ANY($1::bigint[])
          AND ins.system_name = '${constants.INTEGRATION_SYSTEM_GDRIVE}'
        GROUP BY a.id
        HAVING count(ash.*) = 0
      `;

        // For any where there are no more asset sheets, delete the asset record
        const assetResult = await query(assetQueryString, [assetIds]);
        if (assetResult && assetResult.rows) {
          const assetsToDelete = assetResult.rows.map((row: any) => row.id);
          if (assetsToDelete.length > 0) {
            await deleteGDriveAssetsByIds(schema, assetsToDelete);
          }
        }
      }

      return result.rows;
    }
  } catch (error) {
    console.error('Error deleting GDrive asset sheets:', error);
  }

  return [];
}

export async function getGDriveAssetUserIds(schema: string): Promise<number[]> {
  let queryString = `
    SELECT DISTINCT user_id
    FROM ${schema}.${constants.TABLE_GDRIVE_ASSET}
    WHERE user_id IS NOT NULL
  `;

  try {
    const result = await query(queryString);
    if (result && result.rows) {
      // Extract user_id from each row and return them
      return result.rows.map((row: any) => row.user_id);
    }
  } catch (error) {
    console.error('Error fetching unique user IDs:', error);
  }
  return [];
}
