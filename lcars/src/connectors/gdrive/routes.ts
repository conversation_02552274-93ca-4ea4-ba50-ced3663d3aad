import { DefaultAzureCredential } from '@azure/identity';
import { BlobServiceClient } from '@azure/storage-blob';
import express, { Request, Response } from 'express';
import { drive_v3 } from 'googleapis';
import { v4 as uuidv4 } from 'uuid';
import { Rfp } from '..';
import { constants } from '../../constants';
import { getClientById } from '../../db/clients';
import { UserRequest } from '../../utils/auth';
import { getBlobUrl } from '../../utils/azure';
import {
  deleteGDriveAssetSheetsByIds,
  deleteGDriveAssetTasks,
  getGDriveAssetById,
  getGDriveAssetSheetsByAssetId,
  getGDriveAssetUserIds,
  getGDriveAssets,
  getGDriveAssetsWithTask,
  updateGDriveAsset,
  updateGDriveAssets,
} from './db';
import {
  AcquisitionType,
  BLOB_STORAGE_CONTAINER_NAME,
  GDriveAsset,
  acquireDocument,
  acquireSheet,
  driveClientForUser,
  getNewChildren,
  ingestGDriveFiles,
  ingestGDriveRfps,
  unwatchRecursive,
  watchDirectory,
} from './index';

const { m2mCheck, userCheck, userOrM2mCheck } = require('../../utils/auth');

const router = express.Router();

router.post(
  '/connectors/gdrive/assets',
  userCheck,
  async (req: Request & UserRequest, res: Response) => {
    const { schema, userDetail } = req.user;
    let { documentIds, rfps, watchForChanges, folder, drive } = req.body as {
      documentIds: string[];
      rfps: Rfp[];
      watchForChanges: boolean;
      folder?: drive_v3.Schema$File;
      drive?: { id: string; name: string };
    };

    if (!documentIds && !rfps && !folder && !drive) {
      return res
        .status(400)
        .json({ error: 'Missing required field: one of asset ids' });
    }

    if (!userDetail.id) {
      return res
        .status(400)
        .json({ error: 'Missing required fields: userDetail.id' });
    }

    if (documentIds) {
      console.log('documentIds:', documentIds);
    }
    if (rfps?.length > 0) {
      console.log('rfps:', rfps);
    }
    if (folder || drive) {
      console.log(
        `[ connectors/gdrive/assets ] watching ${folder ? 'folder' : 'drive'} with id ${folder?.id ?? drive?.id}`,
      );

      const result = await watchDirectory(
        folder,
        drive,
        userDetail.id,
        userDetail.client,
        schema,
        watchForChanges,
      );

      if (!result) {
        return res.status(500).json({ error: 'Internal Server Error' });
      }
      return res.status(201).json({ success: true, createdCount: result });
    }

    try {
      const drive = await driveClientForUser(schema, userDetail.id);

      // Recursively ingest all files and folders.
      const assets = await ingestGDriveFiles(
        userDetail.client,
        drive,
        userDetail.id,
        documentIds,
        rfps,
        watchForChanges,
      );

      res.status(201).json({ success: true, created: assets });
    } catch (error) {
      console.error('Error acquiring GDrive assets:', error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  },
);

router.get(
  '/connectors/gdrive/assets',
  userCheck,
  async (req: Request & UserRequest, res: Response) => {
    const { schema } = req.user;

    const limit = req.query.limit
      ? parseInt(req.query.limit as string, 10)
      : undefined;
    const offset = req.query.offset
      ? parseInt(req.query.offset as string, 10)
      : undefined;

    try {
      const assets: GDriveAsset[] = await getGDriveAssets(
        schema,
        limit,
        offset,
      );
      res.json(assets);
    } catch (error) {
      console.error('Error fetching GDrive assets:', error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  },
);

router.get(
  '/connectors/gdrive/assets/with-open-task',
  userCheck,
  async (req: Request & UserRequest, res: Response) => {
    const { schema } = req.user;
    try {
      const assets = await getGDriveAssetsWithTask(schema);
      const asset = assets;
      res.json(asset);
    } catch (error) {
      console.error('Error fetching GDrive asset:', error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  },
);

router.get(
  '/connectors/gdrive/assets/:asset_id',
  userCheck,
  async (req: Request & UserRequest, res: Response) => {
    const { schema } = req.user;

    const assetId = parseInt(req.params.asset_id, 10);

    if (isNaN(assetId)) {
      return res.status(400).json({ error: 'Invalid asset ID' });
    }

    try {
      const asset = await getGDriveAssetById(schema, assetId);
      res.json(asset);
    } catch (error) {
      console.error('Error fetching GDrive asset:', error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  },
);

router.get(
  '/connectors/gdrive/files/:file_id',
  userCheck,
  async (req: Request & UserRequest, res: Response) => {
    const { schema, userDetail } = req.user;
    const { file_id } = req.params;

    try {
      const drive = await driveClientForUser(schema, userDetail.id);

      const file = await drive.files.get({
        fileId: file_id,
      });

      const data = file.data;

      res.status(200).json({ success: true, file: data });
    } catch (e) {
      console.error(
        e,
        `[ connectors/gdrive/files/:file_id ] Error getting gdrive file ${file_id}`,
      );
      res.status(500).json({ error: 'Error getting file' });
    }
  },
);

// Get any GDrive Asset Sheets by the parent asset id
router.get(
  '/connectors/gdrive/assetsheets/:asset_id',
  userCheck,
  async (req: Request & UserRequest, res: Response) => {
    const { schema } = req.user;

    const assetId = parseInt(req.params.asset_id, 10);

    if (isNaN(assetId)) {
      return res.status(400).json({ error: 'Invalid asset ID' });
    }

    try {
      const assetSheets = await getGDriveAssetSheetsByAssetId(schema, assetId);
      res.json(
        assetSheets.map((sheet) => {
          // Need to map into the front end's SingleSheetConfig interface type
          return {
            ...sheet,
            sheetId: sheet.sheet_id,
            sheetName: sheet.sheet_name,
            sheetType: sheet.type,
            docName: sheet.details?.doc_name ?? '',
            docTypeId: sheet.details?.doc_type_id ?? -1,
            headerRow: sheet.details?.header_row ?? -1,
            questionColumns: sheet.details?.question_columns ?? [],
            answerColumns: sheet.details?.answer_columns ?? [],
          };
        }),
      );
    } catch (error) {
      console.error('Error fetching GDrive asset sheets:', error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  },
);

router.post(
  '/clients/:client_id/connectors/gdrive/sync',
  userOrM2mCheck,
  async (req: Request, res: Response) => {
    const clientId = parseInt(req.params.client_id, 10);
    if (isNaN(clientId)) {
      return res.status(400).json({ error: 'Invalid client ID' });
    }
    const client = await getClientById(clientId);
    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }
    const schema = client.database_schema_id,
      blobUrl = getBlobUrl(schema),
      creds = new DefaultAzureCredential(),
      blobServiceClient = new BlobServiceClient(blobUrl, creds),
      containerClient = blobServiceClient.getContainerClient(
        BLOB_STORAGE_CONTAINER_NAME,
      );

    // Each user will use a different oauth token and thus a different drive
    // client, therefore we need to proceed on a user-by-user basis.
    const userIds = await getGDriveAssetUserIds(schema);
    for (const userId of userIds) {
      // Load auth for user's drive client.
      let drive;
      try {
        drive = await driveClientForUser(client.database_schema_id, userId);
      } catch (err) {
        console.error(
          `[/clients/client_id/connectors/gdrive/sync] Failed to get drive client for user ${userId}...skipping`,
          err,
        );
        continue;
      }

      // Iterate over this user's documents in batches.
      const batchSize = 25;
      let offset = 0;

      let gDriveAssetsToUpdate: GDriveAsset[] = [];

      try {
        while (true) {
          let assets = await getGDriveAssets(
            schema,
            batchSize,
            offset,
            userId,
            true,
          );

          const trashed = await drive.files.list({
            q: 'trashed=true',
            fields: 'files(id, modifiedTime)',
            supportsAllDrives: true,
            pageSize: 1000,
            orderBy: 'modifiedTime desc',
          });

          const assetsToUnwatch = assets.filter((asset) =>
            trashed.data.files.some((file) => file.id === asset.drive_id),
          );

          if (assetsToUnwatch.length > 0) {
            console.log(
              `[GDrive Sync] Unwatching [${assetsToUnwatch.length}] trashed GDrive Assets for User [${userId}]`,
            );
            await updateGDriveAssets(
              schema,
              assetsToUnwatch.reduce((acc, asset) => {
                acc[asset.id] = {
                  watch_for_changes: false,
                };
                return acc;
              }, {}),
            );

            assets = assets.filter(
              (a) => !assetsToUnwatch.some((b) => a.id === b.id),
            );
          }

          if (!assets || assets.length === 0) {
            console.log('[GDrive Sync] No files to sync');
            break;
          }

          gDriveAssetsToUpdate.push(...assets);

          // Increment the offset by the batch size to fetch the next batch
          offset += assets.length;
        }
      } catch (err) {
        console.error(
          `[/clients/client_id/connectors/gdrive/sync] Failure fetching or updating GDrive assets for user ${userId}...skipping `,
          err,
        );
        continue;
      }

      try {
        const updated = [];

        console.log(
          `[GDrive Sync] Checking [${gDriveAssetsToUpdate.length}] GDrive Assets for User [${userId}]`,
        );

        const newAssets = await getNewChildren(
          gDriveAssetsToUpdate,
          userId,
          schema,
          clientId,
        );

        if (newAssets?.length > 0) {
          gDriveAssetsToUpdate = gDriveAssetsToUpdate.concat(newAssets);
        }

        for (const asset of gDriveAssetsToUpdate) {
          try {
            let file;

            const driveResponse = await drive.files.get({
              supportsAllDrives: true,
              fileId: asset.drive_id,
              fields: 'id,name,modifiedTime',
            });
            file = driveResponse.data;

            const driveModifiedTime = new Date(file.modifiedTime);

            if (
              !asset.last_modified ||
              driveModifiedTime > asset.last_modified
            ) {
              asset.name = file.name;
              asset.last_modified = driveModifiedTime;
              updated.push(asset);

              await updateGDriveAsset(client.database_schema_id, asset.id, {
                name: asset.name,
                status: 'processing',
                last_modified: asset.last_modified,
              });

              const isFolder =
                asset.mime_type === constants.MIME_TYPE_GOOGLE_FOLDER ||
                asset.type === 'folder';

              if (!isFolder) {
                if (isSheet(asset)) {
                  await acquireSheet(
                    client,
                    drive,
                    containerClient,
                    asset,
                    AcquisitionType.UPDATE,
                  );
                } else {
                  await acquireDocument(
                    client,
                    drive,
                    containerClient,
                    asset,
                    AcquisitionType.UPDATE,
                  );
                }
              }
            }
          } catch (error) {
            if (error.response && error.response.status === 404) {
              console.warn(
                '[/clients/:client_id/connectors/gdrive/sync] File not found:',
                { error },
                asset.drive_id,
              );
              // The file has been deleted
              await updateGDriveAsset(schema, asset.id, {
                watch_for_changes: false,
              });
            } else {
              console.error(
                `Failed to fetch or process file ${asset.drive_id}:`,
                error,
              );
            }
          }
        }

        console.log(
          `[/clients/:client_id/connectors/gdrive/sync] Updated [${updated.length}] GDrive Assets for User [${userId}]`,
        );
      } catch (error) {
        console.error(
          '[/clients/:client_id/connectors/gdrive/sync] Failed to fetch or process files batch:',
          {
            clientId,
            userId,
            batchSize,
            offset,
            error,
          },
        );
      }
    }

    res.status(200).json({ message: 'Sync completed.' });
  },
);

router.patch(
  '/clients/:client_id/connectors/gdrive/assets/:asset_id',
  m2mCheck,
  async (req: Request & UserRequest, res: Response) => {
    const clientId = parseInt(req.params.client_id, 10);
    if (isNaN(clientId)) {
      return res.status(400).json({ error: 'Invalid client ID' });
    }
    const client = await getClientById(clientId);
    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    const assetId = parseInt(req.params.asset_id, 10);
    if (isNaN(assetId)) {
      return res.status(400).json({ error: 'Invalid asset ID' });
    }

    const updatedAssetData: Partial<GDriveAsset> = req.body;

    try {
      // Perform the update with only the provided fields
      await updateGDriveAsset(
        client.database_schema_id,
        assetId,
        updatedAssetData,
      );
      res.status(200).json({ message: 'Asset updated successfully' });
    } catch (error) {
      console.error('Error updating GDrive asset:', error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  },
);

// Called by LCARS UI
router.patch(
  '/connectors/gdrive/assets/:asset_id',
  userCheck,
  async (req: Request & UserRequest, res: Response) => {
    const { schema, userDetail } = req.user;
    const { watchForChanges, driveId, drive, folderId, rfps, deletedRfps } =
      req.body;

    const assetId = parseInt(req.params.asset_id, 10);
    if (isNaN(assetId)) {
      return res.status(400).json({ error: 'Invalid asset ID' });
    }

    if ((drive || folderId) && !watchForChanges) {
      const unwatched = await unwatchRecursive(
        userDetail.id,
        schema,
        assetId,
        drive ?? folderId,
      );
      if (unwatched) {
        return res.status(200).json({ message: 'Asset updated successfully' });
      } else {
        return res.status(500).json({ error: 'Internal Server Error' });
      }
    }

    if (!driveId) {
      return res.status(400).json({ error: 'Must provide a Google Drive ID' });
    }

    try {
      // The only thing a doc can modify is its watch/no watch status.

      if (!watchForChanges) {
        await deleteGDriveAssetTasks(schema, [assetId]);
      }
      await updateGDriveAsset(schema, assetId, {
        watch_for_changes: watchForChanges,
        task_id: !watchForChanges ? null : undefined,
      });

      // If a sheet is modified, then delete it first, and then
      // create a new integration_asset_sheet record.
      let deletedGDriveAssetSheets = [];
      if (deletedRfps?.length > 0) {
        // Delete any existing RFP records that are no longer valid.
        // Note: this just deletes the integration_asset_sheet records, not the
        // Document record.

        // If the same sheet id is also in the list of assets to ingest,
        // then use the existing document_id as the prior_version_document_id
        // for the to-be-created document.

        // Note: we can also assume there will be at least one asset_sheet
        // remaining for this GDrive Asset. If someone wants to remove
        // a GDrive Asset entirely, we would've called the /delete endpoint.
        // TODO: Verify this after notifications for incomplete configs are updated
        deletedGDriveAssetSheets = await deleteGDriveAssetSheetsByIds(
          schema,
          deletedRfps.map((r) => r.id),
        );
      }

      // For Docs and CSV: easy, since don't need to worry about
      // multi-sheet and sheets changing.
      if (rfps?.length > 0) {
        const drive = await driveClientForUser(schema, userDetail.id);

        const newDriveAssetSheets = rfps
          // New - skip unconfigured RFPs
          .filter((rfp) => !!rfp.questionColumns && !!rfp.answerColumns)
          .map((rfp) => {
            return {
              asset_id: assetId,
              sheet_id: rfp.selectedSheetId,
              sheet_name: rfp.selectedSheetName,
              type: rfp.sheetType,
              blob_id: uuidv4(),
              details: {
                doc_name: rfp.docName,
                doc_type_id: rfp.docTypeId,
                header_row: rfp.headerRow,
                question_columns: rfp.questionColumns,
                answer_columns: rfp.answerColumns,
                insight_columns: rfp.insightColumns,
              },
            };
          });

        const existingGDriveAsset = await getGDriveAssetById(schema, assetId);

        const gDriveAssetSheetsMap = {
          [driveId]: newDriveAssetSheets,
        };

        await ingestGDriveRfps(
          userDetail.client,
          drive,
          userDetail.id,
          [
            {
              ...existingGDriveAsset,
              ...{
                watch_for_changes: watchForChanges,
                user_id: userDetail.id,
                status: 'processing',
              },
            },
          ],
          gDriveAssetSheetsMap,
          deletedGDriveAssetSheets,
        );
      }

      res.status(200).json({ message: 'Asset updated successfully' });
    } catch (error) {
      console.error('Error updating GDrive asset:', error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  },
);

function isSheet(asset: GDriveAsset): boolean {
  return (
    asset.mime_type == constants.MIME_TYPE_CSV ||
    asset.mime_type == constants.MIME_TYPE_GOOGLE_SHEET ||
    asset.mime_type == constants.MIME_TYPE_XLSX
  );
}

module.exports = router;
