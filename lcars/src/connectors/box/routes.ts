import { getClientById } from '@tribble/comms-shared';
import { ClientSchema } from '@tribble/tribble-db';
import { BoxAssetId } from '@tribble/tribble-db/clients/BoxAsset';
import { TaskId } from '@tribble/tribble-db/clients/Task';
import { getDB } from '@tribble/tribble-db/db_ops';
import { ClientId } from '@tribble/tribble-db/tribble/Client';
import { UserId } from '@tribble/tribble-db/tribble/User';
import { BoxClient, BoxOAuth, OAuthConfig } from 'box-typescript-sdk-gen';
import express, { NextFunction, Request, Response } from 'express';
import mime from 'mime-types';
import {
  ALLOWED_FILE_EXTENSIONS,
  BoxItem,
  getFileAssetsToProcess,
  processFileAssets,
} from '.';
import {
  deleteUserIntegration,
  getIntegrationBySystemName,
} from '../../db/integrations';
import { updateTaskStatus } from '../../db/tasks';
import { UserRequest } from '../../utils/auth';
import { telemetryClient } from '../../utils/telemetry';
import {
  getBoxAppDetails,
  getBoxAuth,
  UserIntegrationTokenStorage,
} from './auth';
import {
  deleteBoxAssetTask,
  getBoxAssetsByFolderId,
  getBoxAssetsByUserId,
  getBoxAssetSheetsByAssetId,
  getBoxAssetsWithTask,
  getBoxSystemIntegrationId,
  getBoxUserIds,
  unwatchBoxAssetsByUserId,
  updateBoxAssetLastChecked,
  upsertBoxAssetSheets,
} from './db';

const { clientAdminCheck, m2mCheck } = require('../../utils/auth');

const router = express.Router();

interface BoxUserRequest extends UserRequest {
  boxClient: BoxClient;
}

// Update the boxAuth middleware
const boxAuth = async (
  req: Request & BoxUserRequest,
  res: Response,
  next: NextFunction,
) => {
  const { userDetail, schema } = req.user;
  try {
    req.boxClient = await getBoxAuth(schema, userDetail.id as UserId, res);
    next();
  } catch (err) {
    return res.status(401).json({ success: false, error: 'Box auth failed' });
  }
};

router.get(
  '/connectors/box/login',
  clientAdminCheck,
  async (req: Request & UserRequest, res: Response) => {
    try {
      const { userDetail } = req.user;
      const { forwardTo } = req.query;

      const { CLIENT_ID, CLIENT_SECRET, REDIRECT_URI } =
        await getBoxAppDetails();

      const state = JSON.stringify({
        user_id: userDetail.id,
        client_id: userDetail.client_id,
        schema: userDetail.client.database_schema_id,
        user_email: userDetail.email,
        forward_to: forwardTo,
      });

      const config = new OAuthConfig({
        clientId: CLIENT_ID,
        clientSecret: CLIENT_SECRET,
      });

      const boxOAuth = new BoxOAuth({
        config,
      });

      const authUrl = boxOAuth.getAuthorizeUrl({
        redirectUri: REDIRECT_URI,
        state,
      });

      res.status(200).json({ success: true, response: authUrl });
    } catch (err) {
      console.error(`[connectors/box/login] ${err}`);
      res
        .status(500)
        .json({ success: false, error: 'Error logging in to Box' });
    }
  },
);

router.get(
  '/connectors/box/callback',
  async (req: Request & UserRequest, res: Response) => {
    try {
      const { code, state } = req.query;

      if (!code || typeof code !== 'string') {
        return res
          .status(400)
          .json({ error: 'Authorization code is required' });
      }

      if (!state || typeof state !== 'string') {
        return res.status(400).json({ error: 'State is required' });
      }

      const { user_id, schema, forward_to } = JSON.parse(state);
      const { CLIENT_ID, CLIENT_SECRET } = await getBoxAppDetails();

      const db = await getDB(schema);
      const boxSystemId = await getBoxSystemIntegrationId(db);

      const tokenStorage = new UserIntegrationTokenStorage(
        schema,
        user_id,
        boxSystemId,
      );
      const config = new OAuthConfig({
        clientId: CLIENT_ID,
        clientSecret: CLIENT_SECRET,
        tokenStorage,
      });

      const boxOAuth = new BoxOAuth({
        config,
      });

      await boxOAuth.getTokensAuthorizationCodeGrant(code);

      if (forward_to) {
        res.redirect(forward_to);
      } else {
        res.status(200).send('OK. You can close this');
      }
    } catch (err) {
      console.error(`[connectors/box/callback] ${err}`);
      telemetryClient.trackException({ exception: err });
      //Fail silently
      res.status(200).send();
    }
  },
);

router.delete(
  '/connectors/box/disconnect',
  clientAdminCheck,
  boxAuth,
  async (req: Request & UserRequest, res: Response) => {
    try {
      const { userDetail, schema } = req.user;

      const boxIntegration = await getIntegrationBySystemName('box');

      await deleteUserIntegration(schema, userDetail.id, boxIntegration.id);

      // TODO: Unwatch all files

      res.status(200).json({ success: true });
    } catch (err) {
      console.error(`[connectors/box/disconnect] ${err}`);
      res
        .status(500)
        .json({ success: false, error: 'Error disconnecting Box' });
    }
  },
);

router.get(
  '/connectors/box/auth',
  clientAdminCheck,
  boxAuth,
  async (req: Request & BoxUserRequest, res: Response) => {
    res.status(200).json({ success: true });
  },
);

const DEFAULT_BOX_FOLDER_ID = '0';

router.get(
  '/connectors/box/folder/:folderId',
  clientAdminCheck,
  boxAuth,
  async (req: Request & BoxUserRequest, res: Response) => {
    try {
      const { boxClient } = req;
      const { folderId = DEFAULT_BOX_FOLDER_ID } = req.params;

      const folderInfo = await boxClient.folders.getFolderById(folderId, {
        queryParams: {
          fields: [
            'id',
            'name',
            'created_at',
            'created_by',
            'modified_at',
            'modified_by',
            'path_collection',
          ],
        },
      });

      res.status(200).json({ success: true, response: folderInfo });
    } catch (err) {
      console.error(`[connectors/box/folder/:folderId] ${err}`);
      res
        .status(500)
        .json({ success: false, error: 'Error getting folder info' });
    }
  },
);

router.get(
  '/connectors/box/folder/:folderId/items',
  clientAdminCheck,
  boxAuth,
  async (req: Request & BoxUserRequest, res: Response) => {
    try {
      const { boxClient } = req;
      const { folderId = DEFAULT_BOX_FOLDER_ID } = req.params;

      const { offset, limit, search } = req.query;

      if (search) {
        const searchResults = await boxClient.search.searchForContent({
          query: search as string,
          fields: [
            'id',
            'name',
            'type',
            'modified_at',
            'modified_by',
            'size',
            'extension',
          ],
          fileExtensions: ALLOWED_FILE_EXTENSIONS,
          ancestorFolderIds: [folderId],
          contentTypes: ['name'],
          limit: limit ? parseInt(limit as string) : 200,
          offset: offset ? parseInt(offset as string) : 0,
        });
        res.status(200).json({ success: true, response: searchResults });
      } else {
        const folderItems = await boxClient.folders.getFolderItems(folderId, {
          queryParams: {
            fields: [
              'id',
              'name',
              'type',
              'modified_at',
              'modified_by',
              'size',
              'extension',
            ],
            limit: limit ? parseInt(limit as string) : 200,
            offset: offset ? parseInt(offset as string) : 0,
          },
        });
        const filteredEntries = folderItems.entries.filter((item) => {
          if (item.type === 'file') {
            return ALLOWED_FILE_EXTENSIONS.includes(item.extension);
          }
          if (item.type === 'web_link') {
            return false;
          }
          return true;
        });
        res.status(200).json({
          success: true,
          response: { ...folderItems, entries: filteredEntries },
        });
      }
    } catch (err) {
      console.error(`[connectors/box/folder/:folderId/items] ${err}`);
      res
        .status(500)
        .json({ success: false, error: 'Error getting folder items' });
    }
  },
);

router.get(
  '/connectors/box/assets',
  clientAdminCheck,
  boxAuth,
  async (req: Request & BoxUserRequest, res: Response) => {
    try {
      const { schema, userDetail } = req.user;
      const { folder_id } = req.query;
      const db = await getDB(schema);

      if (!folder_id) {
        const assets = await getBoxAssetsByUserId(db, userDetail.id as UserId);
        return res.status(200).json({ success: true, response: assets });
      }

      const userId = userDetail.id as UserId;
      const assets = await getBoxAssetsByFolderId(
        db,
        folder_id as string,
        userId,
      );
      return res.status(200).json({ success: true, response: assets });
    } catch (err) {
      console.error(`[connectors/box/assets] ${err}`);
      return res
        .status(500)
        .json({ success: false, error: 'Error getting box assets' });
    }
  },
);

router.post(
  '/connectors/box/assets',
  clientAdminCheck,
  boxAuth,
  async (req: Request & BoxUserRequest, res: Response) => {
    try {
      const { schema, userDetail } = req.user;
      const { boxClient } = req;
      const { items, watchForChanges, processAssets } = req.body as {
        items: BoxItem[];
        watchForChanges: boolean;
        processAssets: boolean;
      };

      const db = await getDB(schema);

      const userId = userDetail.id as UserId;
      const { fileAssetsToProcess } = await getFileAssetsToProcess(
        db,
        boxClient,
        items,
        {
          watchForChanges,
          processAssets,
          processUnconfiguredSpreadsheetAssets: true,
          userId,
        },
      );

      if (!fileAssetsToProcess.length) {
        return res.status(200).json({
          success: true,
        });
      }

      const { assetsProcessed, assetsCouldNotBeUploaded } =
        await processFileAssets(db, {
          schema: schema as ClientSchema,
          clientId: userDetail.client_id as ClientId,
          boxClient,
          fileAssets: fileAssetsToProcess,
          processUnconfiguredSpreadsheetAssets: true,
        });

      await updateBoxAssetLastChecked(
        db,
        assetsProcessed.map((a) => a.id),
        new Date(),
      );

      res.status(201).json({
        success: true,
        response: {
          assetsProcessed,
          assetsCouldNotBeUploaded,
        },
      });
    } catch (err) {
      console.error(`[connectors/box/assets] ${err}`);
      res
        .status(500)
        .json({ success: false, error: 'Error getting folder items' });
    }
  },
);

router.post(
  '/clients/:clientId/connectors/box/sync',
  m2mCheck,
  async (req: Request & UserRequest, res: Response) => {
    let clientId;
    try {
      clientId = parseInt(req.params.clientId, 10);
      if (isNaN(clientId)) {
        return res.status(400).json({ error: 'Invalid client ID' });
      }
      const client = await getClientById(clientId);
      if (!client) {
        return res.status(404).json({ error: 'Client not found' });
      }

      const db = await getDB(client.database_schema_id);

      const userIds = await getBoxUserIds(db, true);
      if (!userIds.length) {
        console.log(
          `[/clients/${clientId}/connectors/box/sync] No box assets found for client: ${client.database_schema_id}`,
        );
        return res.status(204).json({});
      }

      for (const userId of userIds) {
        const boxClient = await getBoxAuth(client.database_schema_id, userId);
        if (!boxClient) {
          console.warn(
            `[/clients/${clientId}/connectors/box/sync] No auth found for user: ${userId}`,
          );
          await unwatchBoxAssetsByUserId(db, userId);
          continue;
        }

        const assets = await getBoxAssetsByUserId(db, userId);
        const { fileAssetsToProcess } = await getFileAssetsToProcess(
          db,
          boxClient,
          assets,
          {
            watchForChanges: true,
            processAssets: true,
            processUnconfiguredSpreadsheetAssets: true,
            userId,
          },
        );

        if (fileAssetsToProcess.length === 0) {
          continue;
        }

        const { assetsProcessed } = await processFileAssets(db, {
          schema: client.database_schema_id as ClientSchema,
          clientId: client.id as ClientId,
          boxClient,
          fileAssets: fileAssetsToProcess,
          processUnconfiguredSpreadsheetAssets: true,
        });

        await updateBoxAssetLastChecked(
          db,
          assetsProcessed.map((a) => a.id),
          new Date(),
        );
      }

      return res.status(200).json({ success: true });
    } catch (err) {
      console.error(`[clients/${clientId}/connectors/box/sync] ${err}`);
      return res.status(500).json({ error: 'Internal server error' });
    }
  },
);

interface BoxSheetConfig {
  sheetId: number;
  sheetName: string;
  sheetType?: string;
  docName: string;
  docTypeId: number;
  headerRow: number;
  questionColumns: number[];
  answerColumns: number[];
  insightColumns: number[];
}

interface SpreadsheetRequest {
  fileId: string;
  folderId: string;
  assetSheetConfigs: BoxSheetConfig[];
  watchForChanges: boolean;
  taskId: TaskId;
}

router.post(
  '/connectors/box/spreadsheets',
  clientAdminCheck,
  boxAuth,
  async (req: Request & BoxUserRequest, res: Response) => {
    try {
      const { schema, userDetail } = req.user;
      const userId = userDetail.id as UserId;
      const { boxClient } = req;
      const { fileId, folderId, assetSheetConfigs, watchForChanges, taskId } =
        req.body as SpreadsheetRequest;
      if (!fileId || !folderId) {
        return res.status(400).json({ error: 'Invalid request' });
      }
      if (!assetSheetConfigs?.length) {
        return res.status(400).json({ error: 'Invalid request' });
      }
      const db = await getDB(schema);
      const { fileAssetsToProcess } = await getFileAssetsToProcess(
        db,
        boxClient,
        [{ file_id: fileId, folder_id: folderId }],
        {
          watchForChanges,
          processAssets: true,
          processUnconfiguredSpreadsheetAssets: false,
          userId,
        },
      );

      const fileAsset = fileAssetsToProcess[0];
      if (!fileAsset) {
        // nothing to process
        return res.status(201).json({
          success: true,
          response: {
            assetsProcessed: [],
            assetsCouldNotBeUploaded: [],
          },
        });
      }

      await upsertBoxAssetSheets(
        db,
        assetSheetConfigs.map((s) => ({
          asset_id: fileAsset.id,
          sheet_id: s.sheetId,
          sheet_name: s.sheetName,
          type: s.sheetType,
          details: {
            doc_name: s.docName,
            doc_type_id: s.docTypeId,
            header_row: s.headerRow,
            question_columns: s.questionColumns,
            answer_columns: s.answerColumns,
            insight_columns: s.insightColumns,
          },
        })),
      );

      const { assetsProcessed, assetsCouldNotBeUploaded } =
        await processFileAssets(db, {
          schema: schema as ClientSchema,
          clientId: userDetail.client_id as ClientId,
          boxClient,
          fileAssets: fileAssetsToProcess,
          processUnconfiguredSpreadsheetAssets: false,
        });

      await updateBoxAssetLastChecked(
        db,
        assetsProcessed.map((a) => a.id),
        new Date(),
      );

      if (taskId) {
        await updateTaskStatus(db, taskId, 'completed');
      }

      return res.status(201).json({
        success: true,
        response: {
          assetsProcessed,
          assetsCouldNotBeUploaded,
        },
      });
    } catch (err) {
      console.error(`[connectors/box/spreadsheets] ${err}`);
      res
        .status(500)
        .json({ success: false, error: 'Error getting folder items' });
    }
  },
);

router.get(
  '/connectors/box/assetsheets/:assetId',
  clientAdminCheck,
  boxAuth,
  async (req: Request & BoxUserRequest, res: Response) => {
    const { assetId } = req.params as { assetId: BoxAssetId };
    const { schema } = req.user;
    try {
      const db = await getDB(schema);

      const assetSheets = await getBoxAssetSheetsByAssetId(db, assetId);
      const sheetConfigs = assetSheets.map((sheet) => {
        // Need to map into the front end's SingleSheetConfig interface type
        return {
          ...sheet,
          sheetId: sheet.sheet_id,
          sheetName: sheet.sheet_name,
          sheetType: sheet.type,
          docName: sheet.details?.doc_name ?? '',
          docTypeId: sheet.details?.doc_type_id ?? -1,
          headerRow: sheet.details?.header_row ?? -1,
          questionColumns: sheet.details?.question_columns ?? [],
          answerColumns: sheet.details?.answer_columns ?? [],
        };
      });
      return res.status(200).json({ success: true, response: sheetConfigs });
    } catch (err) {
      console.error(`[connectors/box/assetsheets/:assetId] ${err}`);
      res
        .status(500)
        .json({ success: false, error: 'Error getting asset sheets' });
    }
  },
);

router.get(
  '/connectors/box/file/fetch',
  clientAdminCheck,
  boxAuth,
  async (req: Request & BoxUserRequest, res: Response) => {
    const { fileId } = req.query as { fileId: string };
    const { boxClient } = req;
    try {
      const fileInfo = await boxClient.files.getFileById(fileId, {
        queryParams: {
          fields: [
            'id',
            'name',
            'type',
            'modified_at',
            'modified_by',
            'size',
            'extension',
            'path_collection',
            'parent',
          ],
        },
      });
      const readable = await boxClient.downloads.downloadFile(fileId);

      res.setHeader('Content-Type', mime.lookup(fileInfo.extension));

      readable.pipe(res);

      readable.on('error', (err) => {
        console.error('Error in streaming response:', err);
        res.status(500).send('Error fetching blob');
      });
    } catch (error) {
      console.error(
        `[/connectors/box/assets/fetch] Error fetching Box item blob [${fileId}]`,
        error,
      );
      res.status(500).json({ error: 'Internal Server Error' });
    }
  },
);

router.get(
  '/connectors/box/assets/with-open-task',
  clientAdminCheck,
  boxAuth,
  async (req: Request & BoxUserRequest, res: Response) => {
    const { schema } = req.user;
    const { boxClient } = req;
    try {
      const assetsWithTask = await getBoxAssetsWithTask(schema);
      const assetsWithItem = await Promise.all(
        assetsWithTask.map(async (a) => {
          if (a.asset.file_id) {
            return {
              ...a,
              item: await boxClient.files.getFileById(a.asset.file_id, {
                queryParams: {
                  fields: [
                    'id',
                    'name',
                    'type',
                    'modified_at',
                    'modified_by',
                    'size',
                    'extension',
                    'path_collection',
                    'parent',
                  ],
                },
              }),
            };
          }
          return a;
        }),
      );

      res.json({ success: true, response: assetsWithItem });
    } catch (error) {
      console.error('Error fetching Box Tasks:', error);
      res.status(500).json({ success: false, error: 'Internal Server Error' });
    }
  },
);

router.patch(
  '/connectors/box/assets/task/:taskId',
  clientAdminCheck,
  boxAuth,
  async (req: Request & BoxUserRequest, res: Response) => {
    const taskId = parseInt(req.params.taskId, 10) as TaskId;
    const { schema } = req.user;
    const db = await getDB(schema);

    await deleteBoxAssetTask(db, taskId);

    return res.status(200).json({ success: true });
  },
);

module.exports = router;
