import { getAzureKeyValues } from '@tribble/azure-helper';
import { UserId } from '@tribble/tribble-db/tribble/User';
import { BoxClient, BoxOAuth, OAuthConfig } from 'box-typescript-sdk-gen';
import { BoxApiError } from 'box-typescript-sdk-gen/lib/box/errors';
import { TokenStorage } from 'box-typescript-sdk-gen/lib/box/tokenStorage.generated';
import { AccessToken } from 'box-typescript-sdk-gen/lib/schemas/accessToken.generated';
import { Response } from 'express';
import {
  deleteUserIntegration,
  getIntegrationBySystemName,
  getUserIntegration,
  saveUserIntegration,
} from '../../db/integrations';
import { decryptText, encryptText } from '../../utils/auth';

let CLIENT_ID = process.env.BOX_CLIENT_ID;
let CLIENT_SECRET = process.env.BOX_CLIENT_SECRET;
let REDIRECT_URI = process.env.BOX_REDIRECT_URI;

export async function getBoxAppDetails(): Promise<{
  CLIENT_ID: string;
  CLIENT_SECRET: string;
  REDIRECT_URI: string;
}> {
  try {
    if (!CLIENT_ID || !CLIENT_SECRET || !REDIRECT_URI) {
      const secretNames = [
        'BOX-CLIENT-ID',
        'BOX-CLIENT-SECRET',
        'BOX-REDIRECT-URI',
      ];
      const secrets = await getAzureKeyValues(secretNames);
      CLIENT_ID = CLIENT_ID ?? secrets['BOX-CLIENT-ID'];
      CLIENT_SECRET = CLIENT_SECRET ?? secrets['BOX-CLIENT-SECRET'];
      REDIRECT_URI = REDIRECT_URI ?? secrets['BOX-REDIRECT-URI'];

      if (!CLIENT_ID || !CLIENT_SECRET || !REDIRECT_URI) {
        throw new Error('Box app details not found');
      }
    }
    return { CLIENT_ID, CLIENT_SECRET, REDIRECT_URI };
  } catch (err) {
    console.error(`[getBoxAppDetails] ${err}`);
    return null;
  }
}

export class UserIntegrationTokenStorage implements TokenStorage {
  constructor(
    private schema: string,
    private userId: number,
    private integrationId: number,
  ) {}

  async store(token: AccessToken): Promise<undefined> {
    const encryptedAuthDetails = encryptText(JSON.stringify(token));
    await saveUserIntegration(
      this.schema,
      this.userId,
      this.integrationId,
      encryptedAuthDetails,
    );
    return undefined;
  }

  async get(): Promise<undefined | AccessToken> {
    const userIntegration = await getUserIntegration(
      this.schema,
      this.userId,
      this.integrationId,
    );
    if (!userIntegration) {
      return undefined;
    }
    const decryptedAuthDetails = JSON.parse(
      decryptText(userIntegration.auth_details),
    );
    return decryptedAuthDetails;
  }

  async clear(): Promise<undefined> {
    await deleteUserIntegration(this.schema, this.userId, this.integrationId);
    return undefined;
  }
}

export class BoxInvalidGrantError extends Error {
  constructor(message?: string) {
    super(message || 'Box invalid grant error');
    this.name = 'BoxInvalidGrantError';
  }
}

function createBoxClientProxy(
  boxClient: BoxClient,
  tokenStorage: UserIntegrationTokenStorage,
) {
  return new Proxy(boxClient, {
    get(target, prop) {
      const value = target[prop];
      if (typeof value === 'function') {
        return async (...args) => {
          try {
            return await value.apply(target, args);
          } catch (error) {
            console.error(`[Box Client Error] Method: ${String(prop)}`, error);

            if (
              error instanceof BoxApiError &&
              error.responseInfo?.statusCode === 400 &&
              (error.responseInfo?.body as any)?.error === 'invalid_grant'
            ) {
              await tokenStorage.clear();
            }
            throw error;
          }
        };
      }
      if (typeof value === 'object' && value !== null) {
        return new Proxy(value, {
          get(target, prop) {
            const method = target[prop];
            if (typeof method === 'function') {
              return async (...args) => {
                try {
                  return await method.apply(target, args);
                } catch (error) {
                  console.error(
                    `[Box Client Error] Method: ${String(prop)}`,
                    error,
                  );

                  if (
                    error instanceof BoxApiError &&
                    error.responseInfo?.statusCode === 400 &&
                    (error.responseInfo?.body as any)?.error === 'invalid_grant'
                  ) {
                    await tokenStorage.clear();
                  }
                  throw error;
                }
              };
            }
            return method;
          },
        });
      }
      return value;
    },
  });
}

export async function getBoxAuth(
  schema: string,
  userId: UserId,
  res?: Response,
) {
  const boxIntegration = await getIntegrationBySystemName('box');
  const { CLIENT_ID, CLIENT_SECRET } = await getBoxAppDetails();

  if (!boxIntegration) {
    throw new Error('Box integration not found');
  }

  const tokenStorage = new UserIntegrationTokenStorage(
    schema,
    userId,
    boxIntegration.id,
  );

  const token = await tokenStorage.get();
  if (!token) {
    throw new Error('Box token not found');
  }

  const config = new OAuthConfig({
    clientId: CLIENT_ID,
    clientSecret: CLIENT_SECRET,
    tokenStorage,
  });
  const boxOAuth = new BoxOAuth({ config });

  const boxClient = new BoxClient({ auth: boxOAuth });
  const proxyBoxClient = createBoxClientProxy(boxClient, tokenStorage);

  try {
    // Test the connection to ensure the client is working
    await proxyBoxClient.users.getUserMe();
  } catch (error) {
    return null;
  }

  return proxyBoxClient;
}
