import * as grpc from '@grpc/grpc-js';
import { BoxAsset } from '@tribble/tribble-db/clients/BoxAsset';
import {
  AddDocumentRequest,
  AddQuestionnaireRequest,
  DocumentSource,
  DocumentSourceInfo,
  MimeType,
} from 'brain-ingest-service';
import * as timestamppb from 'google-protobuf/google/protobuf/timestamp_pb';
import mime from 'mime-types';
import { getGrpcBrainIngestClient, GrpcMimeTypeMap } from '..';
import { BoxAssetSheet } from './db';

export function sendIngestRequest(
  asset: BoxAsset,
  schema: string,
  clientId: number,
  isUpdate: boolean,
) {
  const ingestClient = getGrpcBrainIngestClient();

  const req = new AddDocumentRequest();
  req.setClientId(clientId);
  req.setSchema(schema);
  req.setUserId(asset.user_id);
  req.setDocumentId(Number(asset.document_id));
  req.setJobId(asset.job_id);
  req.setMimeType(GrpcMimeTypeMap[mime.lookup(asset.extension)]);
  req.setFilename(asset.name);
  req.setBlobName(asset.blob_id);
  req.setIsUpdate(isUpdate);

  if (asset.last_modified) {
    const lastModified = new timestamppb.Timestamp();
    lastModified.fromDate(asset.last_modified);
    req.setLastModifiedTime(lastModified);
  }
  if (asset.source_created_date) {
    const sourceCreated = new timestamppb.Timestamp();
    sourceCreated.fromDate(new Date(asset.source_created_date));
    req.setSourceCreatedDate(sourceCreated);
  }
  if (asset.source_modified_date) {
    const sourceModified = new timestamppb.Timestamp();
    sourceModified.fromDate(new Date(asset.source_modified_date));
    req.setSourceModifiedDate(sourceModified);
  }
  const sourceInfo = new DocumentSourceInfo();
  sourceInfo.setSource(DocumentSource.BOX);
  sourceInfo.setSourceId(asset.file_id);
  sourceInfo.setSourceLocation(`https://app.box.com/file/${asset.file_id}`);
  req.setSourceInfo(sourceInfo);

  const deadline = new Date(),
    timeoutSecs = 5;
  deadline.setSeconds(deadline.getSeconds() + timeoutSecs);

  // Send brain ingest request.
  ingestClient.addDocument(
    req,
    new grpc.Metadata(),
    { deadline: deadline.getTime() },
    (err: Error, _) => {
      if (err) {
        console.error(
          `[Box sendIngestRequest] Error calling [addDocument] for (${asset.file_id}): ${err.message}`,
        );
        throw new Error(
          `Failed to ingest document ${asset.file_id}: ${err.message}`,
        );
      }
    },
  );
}

export function createAddQuestionnaireSheetRequest(
  clientId: number,
  schema: string,
  asset: BoxAsset,
  assetSheet: BoxAssetSheet,
  isUpdate: boolean,
  assetMimeType: MimeType,
) {
  const req = new AddQuestionnaireRequest();
  req.setClientId(clientId);
  req.setSchema(schema);
  req.setUserId(asset.user_id);
  req.setDocumentId(Number(assetSheet.document_id));
  req.setJobId(assetSheet.job_id);
  req.setMimeType(assetMimeType);
  req.setFilename(asset.name);
  req.setBlobName(assetSheet.blob_id);
  req.setIsUpdate(isUpdate);
  if (asset.last_modified) {
    const lastModified = new timestamppb.Timestamp();
    lastModified.fromDate(asset.last_modified);
    req.setLastModifiedTime(lastModified);
  }
  if (asset.source_created_date) {
    const sourceCreated = new timestamppb.Timestamp();
    sourceCreated.fromDate(new Date(asset.source_created_date));
    req.setSourceCreatedDate(sourceCreated);
  }
  if (asset.source_modified_date) {
    const sourceModified = new timestamppb.Timestamp();
    sourceModified.fromDate(new Date(asset.source_modified_date));
    req.setSourceModifiedDate(sourceModified);
  }
  const sourceInfo = new DocumentSourceInfo();
  sourceInfo.setSource(DocumentSource.BOX);
  sourceInfo.setSourceId(asset.file_id);
  sourceInfo.setSourceLocation(asset.url);
  req.setSourceInfo(sourceInfo);

  // RFP-specific values.
  // req.setFirstRowIsHeader(asset.rfp_details!.first_row_is_header);
  req.setHeaderRowIndex(assetSheet.details.header_row);
  for (const col of assetSheet.details.question_columns) {
    req.addQuestionColumnsIndices(col);
  }
  for (const col of assetSheet.details.answer_columns) {
    req.addAnswerColumnsIndices(col);
  }

  // Insights specific values.
  for (const col of assetSheet.details.insight_columns) {
    req.addInsightColumnsIndices(col);
  }

  return req;
}

export function sendQuestionnaireRequest(
  reqs: AddQuestionnaireRequest[],
  boxFileId: string,
) {
  const ingestClient = getGrpcBrainIngestClient();

  // Send brain ingest request.
  // If separate tabs, each gets their own request (distinct doc_id/job_id)
  for (const req of reqs) {
    const deadline = new Date(),
      timeoutSecs = 5;
    deadline.setSeconds(deadline.getSeconds() + timeoutSecs);

    ingestClient.addQuestionnaire(
      req,
      new grpc.Metadata(),
      { deadline: deadline.getTime() },
      (err: Error, _) => {
        if (err) {
          // throw new Error(
          //   `Failed to ingest document ${asset.drive_id}: ${err.message}`,
          // );
          console.error(
            `Failed to ingest document ${boxFileId}: ${err.message}`,
          );
        }
      },
    );
  }
}
