import {
  BlobContainer,
  getBlobUrl,
  uploadFilesToBlob,
} from '@tribble/azure-helper';
import { ClientSchema } from '@tribble/tribble-db';
import { BoxAsset } from '@tribble/tribble-db/clients/BoxAsset';
import { DocumentId } from '@tribble/tribble-db/clients/Document';
import { DocumentTypeId } from '@tribble/tribble-db/clients/DocumentType';
import { JobId } from '@tribble/tribble-db/clients/Job';
import { TaskId } from '@tribble/tribble-db/clients/Task';
import { DB, TRX } from '@tribble/tribble-db/db_ops';
import { ClientId } from '@tribble/tribble-db/tribble/Client';
import { UserId } from '@tribble/tribble-db/tribble/User';
import {
  FileWithMetadata,
  MIME_TYPE,
  StructuredFileRequest,
} from '@tribble/types-shared';
import { BoxClient } from 'box-typescript-sdk-gen';
import { FileFull } from 'box-typescript-sdk-gen/lib/schemas/fileFull.generated';
import { MimeType } from 'brain-ingest-service';
import ExcelJS from 'exceljs';
import mime from 'mime-types';
import { Readable } from 'node:stream';
import { v4 as uuidv4 } from 'uuid';
import {
  ASSET_TYPE_INSIGHT,
  ASSET_TYPE_RFP,
  ASSET_TYPE_SPREADSHEET,
  getDocumentDocType,
} from '..';
import { constants } from '../../constants';
import { deleteDocsAndEmbeddings } from '../../db/docs';
import { upsertScheduledNotification } from '../../db/notifications';
import { updateJobRecordStatusWithError } from '../../db/queries';
import { send_function_request } from '../../utils/eventGrid';
import {
  BoxAssetSheet,
  createBoxAsset,
  deleteBoxAssetById,
  getBoxAssetByFileId,
  getBoxAssetSheetsByAssetId,
  getBoxFolderAssetByFolderId,
  insertDocuments,
  insertPartialJobRecord,
  updateBoxAsset,
  updateBoxAssetSheet,
  upsertMultipleAssetTasks,
} from './db';
import {
  createAddQuestionnaireSheetRequest,
  sendIngestRequest,
  sendQuestionnaireRequest,
} from './grpc';

async function getAllFolderItems(boxClient: BoxClient, folderId: string) {
  let allItems = [];
  let marker = '';

  do {
    const folderItems = await boxClient.folders.getFolderItems(folderId, {
      queryParams: {
        usemarker: true,
        marker: marker,
        limit: 1000,
        fields: [
          'id',
          'name',
          'type',
          'modified_at',
          'modified_by',
          'size',
          'extension',
        ],
      },
    });

    allItems = allItems.concat(folderItems.entries);
    marker = folderItems.nextMarker;
  } while (marker);

  return allItems;
}

interface ProcessBoxItemsOptions {
  watchForChanges: boolean;
  processAssets: boolean;
  processUnconfiguredSpreadsheetAssets: boolean;
  userId: UserId;
}

export type BoxItem =
  | {
      file_id: string;
      folder_id: string;
    }
  | {
      file_id: null;
      folder_id: string;
    };

export const ALLOWED_FILE_EXTENSIONS = [
  'pdf',
  'docx',
  'xlsx',
  'xls',
  'pptx',
  'csv',
];
export const INGESTABLE_MIME_TYPES = [
  MIME_TYPE.PDF,
  MIME_TYPE.DOCX,
  MIME_TYPE.PPTX,
  MIME_TYPE.CSV,
  MIME_TYPE.EXCEL,
  MIME_TYPE.XLSX,
];
export const SPREADSHEET_MIME_TYPES = [
  MIME_TYPE.CSV,
  MIME_TYPE.EXCEL,
  MIME_TYPE.XLSX,
];
export const MAX_FILE_SIZE = 500 * 1024 * 1024; // 500MB

export async function getFileAssetsToProcess(
  db: DB | TRX,
  boxClient: BoxClient,
  items: BoxItem[],
  { watchForChanges, processAssets, userId }: ProcessBoxItemsOptions,
) {
  const fileAssetsToProcess = [];
  const itemsToProcess = items;
  const itemsCouldNotBeProcessed = [];

  while (itemsToProcess.length > 0) {
    const item = itemsToProcess.shift();
    if (item.file_id) {
      let fileAsset = await getBoxAssetByFileId(db, item.file_id, userId);
      const fileInfo = await boxClient.files.getFileById(item.file_id, {
        queryParams: {
          fields: [
            'id',
            'name',
            'type',
            'modified_at',
            'modified_by',
            'size',
            'extension',
            'path_collection',
            'parent',
          ],
        },
      });

      if (!fileInfo) {
        itemsCouldNotBeProcessed.push({
          file_id: item.file_id,
          reason: 'File not found',
        });
        continue;
      }

      if (fileInfo.size > MAX_FILE_SIZE) {
        itemsCouldNotBeProcessed.push({
          file_id: item.file_id,
          reason: 'File exceeds max size',
        });
        continue;
      }

      const isFileModifiedSinceLastChecked = fileAsset
        ? fileAsset.last_checked === null ||
          fileAsset.last_checked.getTime() <
            (fileInfo.modifiedAt?.value.getTime() ??
              Number.NEGATIVE_INFINITY) ||
          fileAsset.blob_id === null
        : true;

      if (fileAsset) {
        fileAsset = await updateBoxAsset(db, {
          ...fileAsset,
          name: fileInfo.name,
          watch_for_changes: watchForChanges,
          user_id: userId,
          last_modified: fileInfo.modifiedAt?.value ?? null,
          extension: fileInfo.extension,
        });
      } else {
        fileAsset = await createBoxAsset(db, {
          name: fileInfo.name,
          file_id: item.file_id,
          folder_id: item.folder_id,
          user_id: userId,
          watch_for_changes: watchForChanges,
          status: 'new',
          type: 'file',
          url: `https://app.box.com/file/${item.file_id}`,
          last_modified: fileInfo.modifiedAt?.value ?? null,
          extension: fileInfo.extension,
        });
      }
      if (
        (watchForChanges || processAssets) &&
        isFileModifiedSinceLastChecked
      ) {
        fileAssetsToProcess.push(fileAsset);
      }
    } else {
      let folderAsset = await getBoxFolderAssetByFolderId(
        db,
        item.folder_id,
        userId,
      );
      const folderInfo = await boxClient.folders.getFolderById(item.folder_id);

      if (!folderInfo) {
        itemsCouldNotBeProcessed.push({
          folder_id: item.folder_id,
          reason: 'Folder not found',
        });
        continue;
      }

      if (folderAsset) {
        folderAsset = await updateBoxAsset(db, {
          ...folderAsset,
          name: folderInfo.name,
          watch_for_changes: watchForChanges,
          user_id: userId,
          last_modified: folderInfo.modifiedAt?.value ?? null,
        });
      } else {
        folderAsset = await createBoxAsset(db, {
          name: folderInfo.name,
          file_id: null,
          folder_id: item.folder_id,
          user_id: userId,
          watch_for_changes: watchForChanges,
          status: 'new',
          type: 'folder',
          url: `https://app.box.com/folder/${item.folder_id}`,
          last_modified: folderInfo.modifiedAt?.value ?? null,
        });
      }

      const folderItems = await getAllFolderItems(boxClient, item.folder_id);
      const newItemsToProcess = folderItems
        .filter((a) => {
          if (a.type === 'file') {
            return INGESTABLE_MIME_TYPES.includes(mime.lookup(a.extension));
          }
          if (a.type === 'web_link') {
            return false;
          }
          return true;
        })
        .map((a) => {
          if (a.type === 'file') {
            return { file_id: a.id, folder_id: folderInfo.id };
          } else {
            return { folder_id: a.id, file_id: null };
          }
        });
      itemsToProcess.push(...newItemsToProcess);
    }
  }

  return { fileAssetsToProcess, itemsCouldNotBeProcessed };
}

export async function processFileAssets(
  db: DB | TRX,
  {
    schema,
    clientId,
    boxClient,
    fileAssets,
    processUnconfiguredSpreadsheetAssets,
  }: {
    schema: ClientSchema;
    clientId: ClientId;
    boxClient: BoxClient;
    fileAssets: BoxAsset[];
    processUnconfiguredSpreadsheetAssets: boolean;
  },
) {
  const spreadsheetFileAssets = fileAssets.filter((asset) =>
    SPREADSHEET_MIME_TYPES.includes(mime.lookup(asset.extension)),
  );
  const documentFileAssets = fileAssets.filter(
    (asset) => !SPREADSHEET_MIME_TYPES.includes(mime.lookup(asset.extension)),
  );

  const [spreadsheetResults, documentResults] = await Promise.all([
    processSpreadsheetFileAssets(
      db,
      schema,
      clientId,
      boxClient,
      spreadsheetFileAssets,
      processUnconfiguredSpreadsheetAssets,
    ),
    processDocumentFileAssets(
      db,
      schema,
      clientId,
      boxClient,
      documentFileAssets,
    ),
  ]);

  return {
    assetsProcessed: [
      ...spreadsheetResults.filesProcessed,
      ...documentResults.filesProcessed,
    ],
    assetsCouldNotBeUploaded: [
      ...spreadsheetResults.filesCouldNotBeUploaded,
      ...documentResults.filesCouldNotBeUploaded,
    ],
  };
}

function isRfpAssetSheetConfigured(assetSheet: BoxAssetSheet) {
  return (
    assetSheet.details.question_columns?.length &&
    assetSheet.details.answer_columns?.length
  );
}

function isInsightAssetSheetConfigured(assetSheet: BoxAssetSheet) {
  return (
    assetSheet.details.insight_columns?.length &&
    assetSheet.details.insight_columns?.length > 0
  );
}

const BATCH_SIZE = 100;

async function processBatch<T>(
  items: T[],
  batchSize: number,
  processor: (item: T) => Promise<any>,
) {
  const results = [];
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    const batchResults = await Promise.all(batch.map(processor));
    results.push(...batchResults);
    // Optional: Add a small delay between batches
    if (i + batchSize < items.length) {
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }
  }
  return results;
}

const getFileTemporalData = async (boxClient: BoxClient, fileId: string) => {
  const getFileDetails = () =>
    boxClient.files.getFileById(fileId, {
      queryParams: {
        fields: ['content_created_at', 'content_modified_at'],
      },
    });

  const extractDates = (fileDetails: FileFull) => ({
    createdDate: fileDetails?.contentCreatedAt
      ? new Date(fileDetails.contentCreatedAt.value)
      : null,
    modifiedDate: fileDetails?.contentModifiedAt
      ? new Date(fileDetails.contentModifiedAt.value)
      : null,
  });

  return getFileDetails()
    .then(extractDates)
    .catch((error) => {
      console.warn(
        `Could not retrieve file details for temporal data: ${error.message}`,
      );
      return { createdDate: null, modifiedDate: null };
    });
};

export async function processSpreadsheetFileAssets(
  db: DB | TRX,
  schema: ClientSchema,
  clientId: ClientId,
  boxClient: BoxClient,
  fileAssets: BoxAsset[],
  processUnconfiguredSpreadsheetAssets: boolean,
) {
  const blobUrl = getBlobUrl(schema);

  const assetsWithAssetSheets = await Promise.all(
    fileAssets.map(async (asset) => {
      return {
        asset,
        assetSheets: await getBoxAssetSheetsByAssetId(db, asset.id),
      };
    }),
  );

  const configuredAssets = assetsWithAssetSheets
    .filter(({ assetSheets }) => assetSheets.length > 0)
    .filter(({ assetSheets }) =>
      assetSheets.some(
        (sheet) =>
          sheet.type === ASSET_TYPE_SPREADSHEET ||
          (sheet.type === ASSET_TYPE_RFP && isRfpAssetSheetConfigured(sheet)) ||
          (sheet.type === ASSET_TYPE_INSIGHT &&
            isInsightAssetSheetConfigured(sheet)),
      ),
    );

  if (processUnconfiguredSpreadsheetAssets) {
    const unconfiguredSpreadsheetAssets = assetsWithAssetSheets
      .filter(
        ({ assetSheets }) =>
          assetSheets.length === 0 ||
          assetSheets.some(
            (sheet) =>
              !sheet.type ||
              (sheet.type === ASSET_TYPE_RFP &&
                !isRfpAssetSheetConfigured(sheet)) ||
              (sheet.type === ASSET_TYPE_INSIGHT &&
                !isInsightAssetSheetConfigured(sheet)),
          ),
      )
      .map(({ asset }) => asset);
    if (unconfiguredSpreadsheetAssets.length > 0) {
      await createReviewTaskAndNotification(
        db,
        unconfiguredSpreadsheetAssets,
        clientId,
      );
    }
  }

  // Process configured assets in batches of 3
  const results = await processBatch(
    configuredAssets,
    BATCH_SIZE,
    async ({ asset: fileAsset, assetSheets }) => {
      try {
        const file = await boxClient.downloads.downloadFile(fileAsset.file_id);

        if (!file) {
          return {
            success: false,
            error: {
              file_id: fileAsset.file_id,
              reason: 'Error downloading file',
            },
          };
        }
        const blob_id = uuidv4();

        const fileBuffer = await streamToBuffer(file);
        const storageRecord: Partial<FileWithMetadata> = {
          uuid: blob_id,
          buffer: fileBuffer,
        };

        await uploadFilesToBlob([storageRecord], blobUrl, BlobContainer.DOCS);

        // Get file creation date from Box API if available
        const { createdDate, modifiedDate } = await getFileTemporalData(
          boxClient,
          fileAsset.file_id,
        );

        const updatedAsset = await updateBoxAsset(db, {
          ...fileAsset,
          blob_id,
          ...(createdDate && { source_created_date: createdDate }),
          ...(modifiedDate && { source_modified_date: modifiedDate }),
        });

        if (!updatedAsset) {
          throw new Error('Error processing file', { cause: { fileAsset } });
        }
        await sendRfpIngestRequest(
          db,
          clientId,
          schema,
          updatedAsset,
          assetSheets,
          fileBuffer,
        );

        return { success: true, fileAsset: updatedAsset };
      } catch (error) {
        console.error(
          `[Box processFileAssets] Error processing file ${fileAsset.file_id}: ${error.message}`,
        );

        return {
          success: false,
          error: {
            file_id: fileAsset.file_id,
            reason: error.message,
          },
        };
      }
    },
  );

  return {
    filesProcessed: results.filter((r) => r.success).map((r) => r.fileAsset),
    filesCouldNotBeUploaded: results
      .filter((r) => !r.success)
      .map((r) => r.error),
  };
}

export async function processDocumentFileAssets(
  db: DB | TRX,
  schema: string,
  clientId: number,
  boxClient: BoxClient,
  fileAssets: BoxAsset[],
) {
  const documentDocTypeId = await getDocumentDocType(schema);
  const blobUrl = getBlobUrl(schema);

  const results = await processBatch(
    fileAssets,
    BATCH_SIZE,
    async (fileAsset) => {
      const isExistingAsset = fileAsset.document_id !== null;
      let newDocJobId: JobId;
      let newDocId: DocumentId;
      try {
        const file = await boxClient.downloads.downloadFile(fileAsset.file_id);

        if (!file) {
          return {
            success: false,
            error: {
              file_id: fileAsset.file_id,
              reason: 'Error downloading file',
            },
          };
        }

        const blob_id = uuidv4();
        const newDocJob = await insertPartialJobRecord(db, {
          type: 'ingest',
          created_date: new Date(),
          created_by_id: fileAsset.user_id,
          status: 'new',
        });

        newDocJobId = newDocJob.id;

        const [newDoc] = await insertDocuments(db, [
          {
            job_id: newDocJobId,
            file_name: fileAsset.name,
            label: fileAsset.name,
            uuid: blob_id,
            privacy: 'public',
            use_for_generation: true,
            created_date: new Date(),
            created_by_id: fileAsset.user_id,
            document_type_id: documentDocTypeId.id as DocumentTypeId,
            prior_version_document_id: fileAsset.document_id,
            status: 'processing',
            metadata_json: {
              externalDocSource: 'box',
              externalDocSourceId: fileAsset.file_id,
            },
            source_url: `https://app.box.com/file/${fileAsset.file_id}`,
            file_type: fileAsset.extension,
          },
        ]);
        newDocId = newDoc.id;

        const fileBuffer = await streamToBuffer(file);
        const storageRecord: Partial<FileWithMetadata> = {
          uuid: blob_id,
          buffer: fileBuffer,
        };

        await uploadFilesToBlob([storageRecord], blobUrl, BlobContainer.DOCS);

        // Get file creation date from Box API if available
        const { createdDate, modifiedDate } = await getFileTemporalData(
          boxClient,
          fileAsset.file_id,
        );

        const updatedAsset = await updateBoxAsset(db, {
          ...fileAsset,
          document_id: newDoc.id,
          job_id: newDocJobId,
          blob_id,
          ...(createdDate && { source_created_date: createdDate }),
          ...(modifiedDate && { source_modified_date: modifiedDate }),
        });

        if (!updatedAsset) {
          throw new Error('Error processing file', { cause: { fileAsset } });
        }

        await sendIngestRequest(
          updatedAsset,
          schema,
          clientId,
          isExistingAsset,
        );

        await new Promise((resolve) => setTimeout(resolve, 250));

        return { success: true, fileAsset: updatedAsset };
      } catch (error) {
        console.error(
          `[Box processFileAssets] Error processing file ${fileAsset.file_id}: ${error.message}`,
        );

        // Cleanup on error
        if (isExistingAsset) {
          await updateBoxAsset(db, fileAsset);
        } else {
          await deleteBoxAssetById(db, fileAsset.id);
        }

        if (newDocId) {
          await deleteDocsAndEmbeddings(
            schema,
            [Number(newDocId)],
            fileAsset.user_id,
          );
        }

        if (newDocJobId) {
          await updateJobRecordStatusWithError(
            newDocJobId,
            constants.JOB_STATUS_ERROR,
            `Error processing file: ${error.message}`,
            schema,
          );
        }

        return {
          success: false,
          error: {
            file_id: fileAsset.file_id,
            reason: error.message,
          },
        };
      }
    },
  );

  return {
    filesProcessed: results.filter((r) => r.success).map((r) => r.fileAsset),
    filesCouldNotBeUploaded: results
      .filter((r) => !r.success)
      .map((r) => r.error),
  };
}

async function streamToBuffer(stream: Readable): Promise<Buffer> {
  const chunks = [];
  return new Promise((resolve, reject) => {
    stream.on('data', (chunk) => chunks.push(chunk));
    stream.on('end', () => resolve(Buffer.concat(chunks)));
    stream.on('error', reject);
  });
}

async function createReviewTaskAndNotification(
  db: DB | TRX,
  assets: BoxAsset[],
  clientId: ClientId,
) {
  console.log(
    `[ createReviewTaskAndNotification (box) ] ${assets.length} assets`,
  );
  const reviewTasks = await upsertMultipleAssetTasks(
    db,
    assets.map((asset) => {
      return {
        assetId: asset.id,
        label: `Box Sync - A new spreadsheet was found: ${asset.name}`,
        priority: 1,
        status: 'not_started',
        description: `A new spreadsheet (${asset.name}) was found. Please configure it before the data can be loaded.`,
        created_by_id: asset.user_id,
        created_at: new Date(),
        origin: 'admin',
        type: constants.TASK_TYPE_CONFIGURE_ASSET_SHEET,
      };
    }),
  );

  if (reviewTasks && reviewTasks.length > 0) {
    const details = createSheetTaskNotificationDetails(reviewTasks);
    const userId = assets[0].user_id;
    await upsertScheduledNotification({
      user_id: userId,
      client_id: clientId,
      type: 'configure_sheet_asset',
      parent_id: reviewTasks[0],
      created_by: userId,
      details,
    });
    console.log(`[ createReviewTaskAndNotification ] Notification scheduled`);
  }
}

function createSheetTaskNotificationDetails(tasks: TaskId[]) {
  const multiple = tasks.length > 1;
  let taskUrl = `${process.env.APP_PATH}/review/box`;

  let baseMessage = `${multiple ? tasks.length : 'A'} Box spreadsheet${multiple ? 's' : ''} require${multiple ? '' : 's'} configuration before ${multiple ? 'they' : 'it'} can be ingested.\n\n`;

  let message = baseMessage;
  message += `<a href="${taskUrl}">Click here</a> to view the task in the Tribble Admin Console. Or copy / paste this URL in your browser: ${taskUrl}.`;

  let slack_message = baseMessage;
  slack_message += `<${taskUrl}|Click here> to view the task in the Tribble Admin Console.`;

  return {
    message,
    slack_message,
    subject:
      '[Tribble Notification] Box Sync detected new files requiring configuration',
  };
}

const sendRfpIngestRequest = async (
  db: DB | TRX,
  clientId: ClientId,
  schema: ClientSchema,
  asset: BoxAsset,
  assetSheets: BoxAssetSheet[],
  blobBuffer: Buffer,
) => {
  const assetMimeType = mime.lookup(asset.extension);
  const grpcRequests = [];

  if (assetMimeType === MIME_TYPE.CSV) {
    for (let sheet of assetSheets) {
      const sheetCreatedDate = new Date();

      const sheetJob = await insertPartialJobRecord(db, {
        type: sheet.type === ASSET_TYPE_SPREADSHEET ? 'digest' : 'ingest',
        created_date: sheetCreatedDate,
        created_by_id: asset.user_id,
        status: constants.JOB_STATUS_INITIAL,
      });
      const sheetJobId = sheetJob.id;

      const [sheetDoc] = await insertDocuments(db, [
        {
          file_name: asset.name,
          label: sheet.details.doc_name,
          uuid: asset.blob_id,
          privacy: 'public',
          document_type_id: sheet.details.doc_type_id,
          typeIsStructured: sheet.type === ASSET_TYPE_SPREADSHEET,
          created_by_id: asset.user_id,
          created_date: sheetCreatedDate,
          use_for_generation: true,
          status: 'processing',
          source_url: asset.url,
          metadata_json: {
            externalDocSource: 'box',
            externalDocSourceId: asset.file_id,
          },
          job_id: sheetJobId,
          prior_version_document_id: sheet.document_id,
        },
      ]);

      const sheetDocId = sheetDoc.id;

      const updatedSheet = await updateBoxAssetSheet(db, {
        ...sheet,
        blob_id: asset.blob_id,
        document_id: sheetDocId,
        job_id: sheetJobId,
      });
      if (sheet.type === ASSET_TYPE_RFP || sheet.type === ASSET_TYPE_INSIGHT) {
        grpcRequests.push(
          createAddQuestionnaireSheetRequest(
            clientId,
            schema,
            asset,
            updatedSheet,
            !!sheet.document_id,
            MimeType.MIME_TYPE_CSV,
          ),
        );
      } else if (sheet.type === ASSET_TYPE_SPREADSHEET) {
        const payload: StructuredFileRequest & { is_structured: boolean } = {
          blob_id: updatedSheet.blob_id,
          client_id: clientId,
          document_id: String(updatedSheet.document_id),
          label: asset.name,
          mime_type: assetMimeType,
          schema,
          is_structured: true,
          job_id: updatedSheet.job_id,
          is_update: !!sheet.document_id,
        };

        if (updatedSheet.sheet_id) {
          payload.sheet_id = updatedSheet.sheet_id;
        }

        send_function_request({
          client_id: clientId,
          user_id: asset.user_id,
          function_name: 'digest',
          job_id: updatedSheet.job_id,
          payload,
          schema,
        });
      }
    }
  } else if (
    assetMimeType === MIME_TYPE.EXCEL ||
    assetMimeType === MIME_TYPE.XLSX
  ) {
    const blobUrl = getBlobUrl(schema);
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(blobBuffer);

    for (let sheet of assetSheets) {
      try {
        const sheetCreatedDate = new Date();

        const csvBuffer = await workbook.csv.writeBuffer({
          sheetId: sheet.sheet_id,
        });
        const fileData = Buffer.from(csvBuffer as ArrayBuffer);

        const sheetBlobId = uuidv4();
        const storageRecord: Partial<FileWithMetadata> = {
          uuid: sheetBlobId,
          buffer: fileData,
        };
        await uploadFilesToBlob([storageRecord], blobUrl, BlobContainer.DOCS);

        const sheetJob = await insertPartialJobRecord(db, {
          type: sheet.type === ASSET_TYPE_RFP ? 'ingest' : 'digest',
          created_date: sheetCreatedDate,
          created_by_id: asset.user_id,
          status: constants.JOB_STATUS_INITIAL,
        });
        const sheetJobId = sheetJob.id;

        const [sheetDoc] = await insertDocuments(db, [
          {
            file_name: asset.name,
            label: sheet.details.doc_name,
            uuid: sheetBlobId,
            privacy: 'public',
            document_type_id: sheet.details.doc_type_id,
            typeIsStructured: sheet.type === ASSET_TYPE_SPREADSHEET,
            created_by_id: asset.user_id,
            created_date: sheetCreatedDate,
            use_for_generation: true,
            status: 'processing',
            source_url: asset.url,
            metadata_json: {
              externalDocSource: 'box',
              externalDocSourceId: asset.file_id,
            },
            job_id: sheetJobId,
            prior_version_document_id: sheet.document_id,
          },
        ]);

        const sheetDocId = sheetDoc.id;

        const updatedSheet = await updateBoxAssetSheet(db, {
          ...sheet,
          blob_id: sheetBlobId,
          document_id: sheetDocId,
          job_id: sheetJobId,
        });

        if (
          sheet.type === ASSET_TYPE_RFP ||
          sheet.type === ASSET_TYPE_INSIGHT
        ) {
          grpcRequests.push(
            createAddQuestionnaireSheetRequest(
              clientId,
              schema,
              asset,
              updatedSheet,
              !!sheet.document_id,
              MimeType.MIME_TYPE_CSV,
            ),
          );
        } else if (sheet.type === ASSET_TYPE_SPREADSHEET) {
          const payload: StructuredFileRequest & {
            is_structured: boolean;
          } = {
            blob_id: updatedSheet.blob_id,
            client_id: clientId,
            document_id: String(updatedSheet.document_id), // why does doc_id need to be a string...
            label: asset.name,
            mime_type: assetMimeType,
            schema,
            is_structured: true,
            job_id: updatedSheet.job_id,
            is_update: !!sheet.document_id,
          };
          if (updatedSheet.sheet_id) {
            payload.sheet_id = updatedSheet.sheet_id;
          }
          send_function_request({
            client_id: clientId,
            user_id: asset.user_id,
            function_name: 'digest',
            job_id: updatedSheet.job_id,
            payload,
            schema,
          });
        }
      } catch (error) {
        console.error(
          `Failed to load workbook for Box Asset Sheet Id=[${sheet.id}]: ${error.message}`,
        );
        return;
      }
    }
  }

  sendQuestionnaireRequest(grpcRequests, asset.file_id);
};
