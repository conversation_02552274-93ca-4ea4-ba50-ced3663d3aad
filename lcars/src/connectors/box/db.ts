import { Redis, constants as redis_const } from '@tribble/redis';
import { BoxAsset, BoxAssetId } from '@tribble/tribble-db/clients/BoxAsset';
import { DocumentId, NewDocument } from '@tribble/tribble-db/clients/Document';
import { DocumentTypeId } from '@tribble/tribble-db/clients/DocumentType';
import {
  IntegrationAssetSheet,
  IntegrationAssetSheetUpdate,
  NewIntegrationAssetSheet,
} from '@tribble/tribble-db/clients/IntegrationAssetSheet';
import { JobId } from '@tribble/tribble-db/clients/Job';
import { NewTask, Task, TaskId } from '@tribble/tribble-db/clients/Task';
import { TaskOwner } from '@tribble/tribble-db/clients/TaskOwner';
import { DB, query, TRX } from '@tribble/tribble-db/db_ops';
import { IntegrationSystemId } from '@tribble/tribble-db/tribble/IntegrationSystem';
import { UserId } from '@tribble/tribble-db/tribble/User';
import { constants } from '../../constants';

export async function getBoxSystemIntegrationId(
  db: DB | TRX,
): Promise<IntegrationSystemId | null> {
  const isLocal = process.env.ENVIRONMENT === 'development';
  const cacheKey = `box_system_id`;
  const redis = new Redis();
  const cached = await redis.get(cacheKey);
  if (cached && !isLocal) {
    return Number(cached) as IntegrationSystemId;
  }

  //Not cached. Query it and cache it.
  try {
    const result = await db
      .selectFrom('tribble.integration_system')
      .where('system_name', '=', 'box')
      .select('id')
      .executeTakeFirstOrThrow();
    const id = result.id;
    if (!isLocal) {
      await redis.set(cacheKey, String(id), redis_const.EX_7_DAYS);
    }
    return id;
  } catch (err) {
    console.error(`[getSalesforceSystemIntegrationId] ${err}`);
    return null;
  }
}

export async function getAllBoxAssets(db: DB | TRX) {
  return db.selectFrom('box_asset').selectAll().execute();
}

export async function getBoxAssetById(db: DB | TRX, id: BoxAssetId) {
  return db
    .selectFrom('box_asset')
    .where('id', '=', id)
    .selectAll()
    .executeTakeFirstOrThrow();
}

export async function getBoxAssetsByFolderId(
  db: DB | TRX,
  folderId: string,
  userId: UserId,
) {
  return db
    .selectFrom('box_asset')
    .where('folder_id', '=', folderId)
    .where('user_id', '=', userId)
    .selectAll()
    .execute();
}

export async function getBoxFolderAssetByFolderId(
  db: DB | TRX,
  folderId: string,
  userId: UserId,
) {
  return db
    .selectFrom('box_asset')
    .where('folder_id', '=', folderId)
    .where('file_id', 'is', null)
    .where('user_id', '=', userId)
    .selectAll()
    .executeTakeFirst();
}

export async function getBoxAssetByFileId(
  db: DB | TRX,
  fileId: string,
  userId: UserId,
) {
  return db
    .selectFrom('box_asset')
    .where('file_id', '=', fileId)
    .where('user_id', '=', userId)
    .selectAll()
    .executeTakeFirst();
}

export async function getBoxAssetsByUserId(db: DB | TRX, userId: UserId) {
  return db
    .selectFrom('box_asset')
    .where('user_id', '=', userId)
    .selectAll()
    .execute();
}

export async function getBoxAssetsByItemIds(
  db: DB | TRX,
  itemIds: string[],
  userId: UserId,
) {
  return db
    .selectFrom('box_asset')
    .where('file_id', 'in', itemIds)
    .where((eb) =>
      eb.and([eb('file_id', 'is', null), eb('folder_id', 'in', itemIds)]),
    )
    .where('user_id', '=', userId)
    .selectAll()
    .execute();
}

export async function getBoxWatchedAssets(db: DB | TRX) {
  return db
    .selectFrom('box_asset')
    .where('watch_for_changes', '=', true)
    .selectAll()
    .execute();
}

export interface BoxAssetSheet extends IntegrationAssetSheet {
  asset_id: BoxAssetId;
  details: {
    doc_name: string;
    doc_type_id: DocumentTypeId;
    header_row: number;
    question_columns: number[];
    answer_columns: number[];
    insight_columns?: number[];
  };
}

export async function getBoxAssetSheetsByAssetId(
  db: DB | TRX,
  assetId: BoxAssetId,
): Promise<BoxAssetSheet[]> {
  const dbQuery = db
    .selectFrom('integration_asset_sheet as ias')
    .innerJoin(
      'tribble.integration_system as ins',
      'ias.integration_id',
      'ins.id',
    )
    .where('ins.system_name', '=', 'box')
    .where('ias.asset_id', '=', assetId)
    .selectAll('ias');
  return dbQuery.execute() as Promise<BoxAssetSheet[]>;
}

export async function upsertBoxAssetSheets(
  db: DB | TRX,
  sheets: NewIntegrationAssetSheet[],
) {
  const integrationId = await getBoxSystemIntegrationId(db);
  return db
    .insertInto('integration_asset_sheet')
    .values(
      sheets.map((sheet) => ({ ...sheet, integration_id: integrationId })),
    )
    .onConflict((oc) =>
      oc
        .columns(['asset_id', 'sheet_id', 'integration_id'])
        .doUpdateSet((eb) => ({
          details: eb.ref('excluded.details'),
          blob_id: eb.ref('excluded.blob_id'),
          document_id: eb.ref('excluded.document_id'),
          job_id: eb.ref('excluded.job_id'),
          type: eb.ref('excluded.type'),
          sheet_name: eb.ref('excluded.sheet_name'),
        })),
    )
    .returningAll()
    .execute();
}

export async function updateBoxAssetSheet(
  db: DB | TRX,
  sheet: IntegrationAssetSheetUpdate,
): Promise<BoxAssetSheet | undefined> {
  return db
    .updateTable('integration_asset_sheet')
    .set(sheet)
    .where('id', '=', sheet.id)
    .returningAll()
    .executeTakeFirst() as Promise<BoxAssetSheet | undefined>;
}

export async function getBoxAssetSheetsByFileIds(
  db: DB | TRX,
  fileIds: string[],
) {
  const dbQuery = db
    .selectFrom('integration_asset_sheet as ias')
    .innerJoin(
      'tribble.integration_system as ins',
      'ias.integration_id',
      'ins.id',
    )
    .innerJoin('box_asset as ba', 'ias.asset_id', 'ba.id')
    .where('ins.system_name', '=', 'box')
    .where('ba.file_id', 'in', fileIds)
    .selectAll('ias')
    .select('ba.file_id');

  return dbQuery.execute();
}
interface NewBoxAsset {
  file_id: string | null;
  folder_id: string;
  name?: string;
  extension?: string;
  last_modified?: Date | string;
  url: string;
  blob_id?: string;
  document_id?: DocumentId;
  type: string;
  job_id?: JobId;
  user_id: UserId;
  watch_for_changes: boolean;
  status: string;
  last_checked?: Date;
  created_at?: Date;
  updated_at?: Date;
  task_id?: TaskId;
  source_created_date?: Date;
  source_modified_date?: Date;
}

export async function createBoxAsset(db: DB | TRX, newAsset: NewBoxAsset) {
  return db
    .insertInto('box_asset')
    .values(newAsset)
    .returningAll()
    .executeTakeFirst();
}

interface UpdateBoxAsset extends Partial<NewBoxAsset> {
  id: BoxAssetId;
}
export async function updateBoxAsset(db: DB | TRX, update: UpdateBoxAsset) {
  return db
    .updateTable('box_asset')
    .set({ ...update, updated_at: new Date() })
    .where('id', '=', update.id)
    .returningAll()
    .executeTakeFirst();
}

export async function updateBoxAssetLastChecked(
  db: DB | TRX,
  assetIds: BoxAssetId[],
  lastChecked: Date,
) {
  if (assetIds.length === 0) {
    return;
  }
  return db
    .updateTable('box_asset')
    .set({ last_checked: lastChecked })
    .where('id', 'in', assetIds)
    .execute();
}

export async function deleteBoxAssetById(db: DB | TRX, id: BoxAssetId) {
  return db.deleteFrom('box_asset').where('id', '=', id).executeTakeFirst();
}

export async function unwatchBoxAssetsByUserId(db: DB | TRX, userId: UserId) {
  return db
    .updateTable('box_asset')
    .set({ watch_for_changes: false })
    .where('user_id', '=', userId)
    .execute();
}

export async function getBoxUserIds(db: DB | TRX, watchForChanges?: boolean) {
  const result = await db
    .selectFrom('box_asset')
    .$if(watchForChanges !== undefined, (qb) =>
      qb.where('watch_for_changes', '=', watchForChanges),
    )
    .select('user_id')
    .distinct()
    .execute();
  return result.map((row) => row.user_id);
}

type TaskObject = NewTask & {
  status: 'not_started' | 'in_progress' | 'completed';
  origin: 'admin' | 'string';
  assetId: BoxAssetId;
};

export async function upsertMultipleAssetTasks(
  dbOrTrx: DB | TRX,
  tasks: TaskObject[],
): Promise<TaskId[]> {
  return await dbOrTrx.transaction().execute(async (trx) => {
    // First find which assets need tasks
    const assetsNeedingTasks = await trx
      .selectFrom('box_asset')
      .select('id')
      .where(
        'id',
        'in',
        tasks.map((t) => t.assetId),
      )
      .where('task_id', 'is', null)
      .execute();

    if (assetsNeedingTasks.length === 0) {
      return [];
    }

    // Filter tasks to only those that need to be created
    const assetsToUpdate = new Set(assetsNeedingTasks.map((a) => a.id));
    const tasksToCreate = tasks.filter((t) => assetsToUpdate.has(t.assetId));

    // Insert only the needed tasks
    const newTasks = await trx
      .insertInto('task')
      .values(
        tasksToCreate.map((task) => ({
          label: task.label,
          priority: task.priority,
          status: task.status,
          due_date: task.due_date,
          description: task.description,
          created_by_id: task.created_by_id,
          created_at: task.created_at,
          origin: task.origin,
          type: task.type,
        })),
      )
      .returning(['id'])
      .execute();

    // Create task owners for each new task
    await trx
      .insertInto('task_owner')
      .values(
        newTasks.map((task, index) => ({
          task_id: task.id,
          owner_id: tasksToCreate[index].created_by_id,
        })),
      )
      .execute();

    // Update assets with new task IDs
    await Promise.all(
      tasksToCreate.map((task, index) =>
        trx
          .updateTable('box_asset')
          .set({ task_id: newTasks[index].id })
          .where('id', '=', task.assetId)
          .execute(),
      ),
    );

    return newTasks.map((t) => t.id);
  });
}

export async function insertPartialJobRecord(
  db: DB | TRX,
  {
    type,
    created_date,
    created_by_id,
    status = constants.JOB_STATUS_SUBMITTED,
  }: {
    type: string;
    created_date: Date;
    created_by_id: UserId;
    status?: string;
  },
) {
  return db
    .insertInto('job')
    .values({
      type,
      created_date,
      created_by_id,
      status,
    })
    .returning('id')
    .executeTakeFirst();
}

interface MetadataJson {
  externalDocSource?: string;
  externalDocSourceId?: string;
}

export interface InsertDocument extends NewDocument {
  metadata_json?: MetadataJson;
  typeIsStructured?: NewDocument['is_custom_table'];
}

export async function insertDocuments(
  db: DB | TRX,
  newDocuments: InsertDocument[],
) {
  return db
    .insertInto('document')
    .values(
      newDocuments.map(({ typeIsStructured, ...doc }) => ({
        ...doc,
        is_custom_table: typeIsStructured ?? doc.is_custom_table,
      })),
    )
    .returning('id')
    .execute();
}

export async function getBoxAssetsWithTask(
  schema: string,
): Promise<{ asset: BoxAsset; task: Task; task_owner: TaskOwner }[]> {
  const queryString = `
    SELECT 
      row_to_json(a) as asset,
      row_to_json(t) as task,
      row_to_json(tao) as task_owner
    FROM ${schema}.${constants.TABLE_BOX_ASSET} a
    JOIN ${schema}.${constants.TABLE_TASK} t ON a.task_id = t.id
    JOIN ${schema}.${constants.TABLE_TASK_OWNER} tao ON t.id = tao.task_id
    WHERE t.STATUS != '${constants.TASK_STATUS_COMPLETED}' 
    AND t.STATUS != '${constants.TASK_STATUS_DELETED}'`;

  const result = await query(queryString);

  return result?.rows ?? [];
}

export async function deleteBoxAssetTask(db: DB | TRX, taskId: TaskId) {
  return db
    .updateTable('box_asset')
    .set({ task_id: null })
    .where('task_id', '=', taskId)
    .execute();
}
