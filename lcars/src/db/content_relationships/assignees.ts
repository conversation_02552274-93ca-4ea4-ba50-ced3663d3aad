import { constants } from '../../constants';
import { Assignee } from '../../model/model';
const { query, getClient } = require('../index');

/**
 * Upsert a new list of content assignees
 * AND changes the is_owner field if it's already set.
 */
export async function upsertContentAssignees(
  contentId: number,
  assignees: Assignee[],
  schema: string,
) {
  const client = await getClient();
  await client.query('BEGIN');
  try {
    const contentOwnerId = await getContentOwnerId(contentId, schema);
    let newOwner;

    for (const assignee of assignees) {
      // Final isNaN check...
      if (!isNaN(assignee.assignee_id)) {
        const queryString = `
          INSERT INTO ${schema}.${constants.TABLE_CONTENT_USER}(content_id, assignee_id, is_owner)
          VALUES ($1, $2, $3)
          ON CONFLICT (content_id, assignee_id)
          DO UPDATE SET is_owner = EXCLUDED.is_owner;
          `;
        await client.query(queryString, [
          contentId,
          assignee.assignee_id,
          assignee.is_owner || false,
        ]);
        newOwner = assignee.is_owner ? assignee.assignee_id : newOwner;
      }
    }

    // For now, content_user_join will only be used to store the owner.
    // Originally there was a UI in the New Questionnaire page where you could
    // select a list of (potential) assignees on a questionnaire. But now that
    // has just moved to the individual question level. We can still use this
    // content_user_join table to store the owner_id (or I suppose multiple owner_ids),
    // or, just create a owner_ids field on content and make it a list of ids.

    // Delete any previously set owners.
    if (contentOwnerId && newOwner && newOwner != contentOwnerId) {
      const clearPreviousOwnerQuery = `
      DELETE 
      FROM  ${schema}.${constants.TABLE_CONTENT_USER}
      WHERE content_id = $1 
      AND   (
        assignee_id != $2
        OR assignee_id IS NULL
      );`;

      await client.query(clearPreviousOwnerQuery, [contentId, newOwner]);

      // await cleanUpContentOwner(contentId, newOwner, schema);
    }

    await client.query('COMMIT');
  } catch (err) {
    await client.query('ROLLBACK');
    console.error(`[db/upsertContentAssignees] ${err}`);
  } finally {
    client.release();
  }
}

async function getContentOwnerId(contentId: number, schema: string) {
  const queryString = `
      SELECT  assignee_id
      FROM    ${schema}.${constants.TABLE_CONTENT_USER}
      WHERE   content_id = $1 AND is_owner = true`;

  const res = await query(queryString, [contentId]);
  if (res.rows && res.rows.length) {
    return res.rows[0].assignee_id;
  }
  return;
}

export async function deleteOneContentAssignee(
  contentId: number,
  userId: number,
  schema,
) {
  try {
    const queryString = `
    DELETE FROM ${schema}.${constants.TABLE_CONTENT_USER}
    WHERE content_id = $1 AND assignee_id = $2
    `;
    await query(queryString, [contentId, userId]);
  } catch (err) {
    console.error(`[db/deleteContentUserJoin] ${err}`);
  }
}

export async function deleteAllContentAssigneesByContentId(
  contentId: number,
  schema: string,
) {
  try {
    const queryString = `
    DELETE FROM ${schema}.${constants.TABLE_CONTENT_USER}
    WHERE content_id = $1
    `;
    await query(queryString, [contentId]);
  } catch (err) {
    console.error(`[db/deleteAllContentAssigneesByContentId] ${err}`);
  }
}
