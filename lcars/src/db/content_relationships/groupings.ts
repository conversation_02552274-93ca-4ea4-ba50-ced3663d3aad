import { constants } from '../../constants';
import { Grouping } from '../../model/model';
const { query, getClient } = require('../index');

export async function insertGroupings(
  groupings: Grouping[],
  contentId: number,
  metadataFilter: Record<string, string[]>,
  schema: string,
) {
  let success = true;
  try {
    for (const grouping of groupings) {
      const queryString = `
          INSERT INTO ${schema}.${constants.TABLE_GROUPING}
          (name, metadata_filter)
          VALUES ($1, $2)
          RETURNING id;`;
      const results = await query(queryString, [grouping.name, metadataFilter]);

      // Add to content_grouping_join table too
      if (results.rows && results.rows.length) {
        const groupingId = results.rows[0].id;

        // It's possible that we deploy the backend prior to some Chrome Ext users
        // updating to the latest. For these users, contentId will be -1
        if (contentId > 0) {
          await insertContentGroupingJoins(
            contentId,
            [{ id: groupingId }],
            schema,
          );
        }
      }
    }
  } catch (err) {
    console.error(`[db/insertGroupings] ${err}`);
    success = false;
  } finally {
    return success;
  }
}

export async function getGroupings(schema: string): Promise<Grouping[]> {
  try {
    const queryString = `
        SELECT 
          id,
          name,
          metadata_filter
        FROM ${schema}.${constants.TABLE_GROUPING}
        ORDER BY name ASC`;
    const result = await query(queryString);
    if (result.rows && result.rows.length) {
      return result.rows;
    }
    return [];
  } catch (err) {
    console.error(`[db/getGroupings] ${err}`);
  }
}

export async function getGroupingById(
  schema: string,
  id: number,
): Promise<Grouping> {
  try {
    const queryString = `
        SELECT 
          id,
          name,
          metadata_filter
        FROM ${schema}.${constants.TABLE_GROUPING}
        WHERE id = $1
        ORDER BY name ASC`;
    const result = await query(queryString, [id]);
    if (result.rows && result.rows.length) {
      return result.rows[0];
    }
    return null;
  } catch (err) {
    console.error(`[db/getGroupingById] ${err}`);
    return null;
  }
}

export async function updateGrouping(
  groupingId: number,
  name: string,
  schema: string,
): Promise<boolean> {
  let success = true;
  try {
    const queryString = `
    UPDATE ${schema}.${constants.TABLE_GROUPING} 
    SET name = $1
    WHERE id = $2
    `;
    const result = await query(queryString, [name, groupingId]);
  } catch (err) {
    console.error(`[db/updateGroupings] ${err}`);
    success = false;
  } finally {
    return success;
  }
}

export async function insertContentGroupingJoins(
  contentId: number,
  groupings: Partial<Grouping>[],
  schema: string,
) {
  const client = await getClient();
  await client.query('BEGIN');
  try {
    for (const grouping of groupings) {
      const queryString = `
            INSERT INTO ${schema}.${constants.TABLE_CONTENT_GROUPING}(content_id, grouping_id)
            VALUES ($1, $2)
            ON CONFLICT (content_id, grouping_id) DO NOTHING;
            `;
      await client.query(queryString, [contentId, grouping.id]);
    }
    await client.query('COMMIT');
  } catch (err) {
    await client.query('ROLLBACK');
    console.error(`[db/insertContentGroupingJoins] ${err}`);
  } finally {
    client.release();
  }
}

export async function getContentGroupingJoins(
  schema: string,
  contentId: number = -1,
): Promise<Grouping[]> {
  try {
    // Prior to v0.0.14 (Dec 6'ish) the content_grouping_join table
    // wasn't used, and all grouping records were "global" (available to all questionnaires).
    // v0.0.14 onwards, groupings will be per-questionnaire: they will still exist
    // as grouping records, but, use the _join table to find out what to show the user.
    // However, to make things compatible with older versions, first check if anything in the _join
    // table, and if not, find all grouping_ids belonging to content_detail (question) records
    // mapped to the current content_id
    const queryString = `
      SELECT  grouping_id
      ,       g.name as grouping_name
      ,       g.metadata_filter as grouping_metadata_filter
      FROM    ${schema}.${constants.TABLE_CONTENT_GROUPING} cg
      JOIN    ${schema}.${constants.TABLE_GROUPING} g
        ON      cg.grouping_id = g.id
      WHERE   content_id = $1`;
    const result = await query(queryString, [contentId]);

    if (result.rows && result.rows.length) {
      return result.rows;
    } else {
      // Try to find all groupings belonging to content_detail records
      const queryStringCd = `
        SELECT  DISTINCT
                grouping_id
        ,       g.name as grouping_name
        ,       g.metadata_filter as grouping_metadata_filter
        FROM    ${schema}.${constants.TABLE_CONTENT_DETAIL} cd
        JOIN    ${schema}.${constants.TABLE_GROUPING} g
          ON      cd.grouping_id = g.id        
        WHERE   content_id = $1
        AND     grouping_id IS NOT NULL`;

      const resultCd = await query(queryStringCd, [contentId]);
      if (resultCd.rows && resultCd.rows.length) {
        // If rows exist, insert them into the content_grouping_join table
        console.log(
          `[db/getContentGroupingJoins] Found results via ${constants.TABLE_CONTENT_DETAIL} table; inserting into ${constants.TABLE_CONTENT_GROUPING}`,
        );
        await insertContentGroupingJoins(
          contentId,
          resultCd.rows.map((grouping) => {
            return {
              id: grouping.grouping_id,
            };
          }),
          schema,
        );

        return resultCd.rows;
      }
    }
    return [];
  } catch (err) {
    console.error(`[db/getContentGroupingJoins] ${err}`);
  }
}

/**
 * Delete a content_grouping_join record
 */
export async function deleteOneContentGroupingJoin(
  contentGroupingId: number,
  schema: string,
): Promise<Boolean> {
  let success = true;
  try {
    const queryString = `
      DELETE FROM ${schema}.${constants.TABLE_CONTENT_GROUPING}
      WHERE id = $1
      `;
    await query(queryString, [contentGroupingId]);
  } catch (err) {
    console.error(`[db/deleteOneContentGroupingJoin] ${err}`);
    success = false;
  } finally {
    return success;
  }
}

export async function deleteAllContentGroupingsByContentId(
  contentId: number,
  schema: string,
) {
  try {
    const queryString = `
      DELETE FROM ${schema}.${constants.TABLE_CONTENT_GROUPING}
      WHERE content_id = $1
      `;
    await query(queryString, [contentId]);
  } catch (err) {
    console.error(`[db/deleteAllContentGroupingsByContentId] ${err}`);
  }
}
