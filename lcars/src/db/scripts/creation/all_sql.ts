import { DB_READONLY_ROLE } from '@tribble/types-shared';
import { constants } from '../../../constants';

export const client_create_schema = (clientId: string) => {
  const createSchema = `
  CREATE SCHEMA ${clientId} AUTHORIZATION ${process.env.POSTGRES_DB_OWNER}`;
  return [createSchema];
};

export const client_client_setting = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.client_setting
  (
    id SMALLSERIAL PRIMARY KEY,
    setting_id smallint REFERENCES ${constants.SCHEMA_TRIBBLE}.setting(id),
    value_string text,
    value_number numeric,
    value_boolean boolean,
    CONSTRAINT client_setting_unique_setting_id UNIQUE (setting_id)
  )`;

  return [createTable];
};

export const client_user_setting = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.user_setting
  (
    id SERIAL PRIMARY KEY,
    user_id integer REFERENCES ${constants.SCHEMA_TRIBBLE}.user(id),
    setting_id smallint REFERENCES ${constants.SCHEMA_TRIBBLE}.setting(id),
    value_string text,
    value_number integer,
    value_boolean boolean,
    CONSTRAINT user_setting_unique_user_id_setting_id UNIQUE (user_id, setting_id)
  )`;

  return [createTable];
};

export const client_user_integration = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.user_integration
  (
    id SERIAL PRIMARY KEY,
    user_id integer REFERENCES ${constants.SCHEMA_TRIBBLE}.user(id),
    integration_system_id smallint REFERENCES ${constants.SCHEMA_TRIBBLE}.integration_system(id),
    auth_details text,
    created_at timestamp,
    CONSTRAINT user_integration_unique_user_id_system_id UNIQUE (user_id, integration_system_id)
  )`;

  return [createTable];
};

export const client_metadata_filter_type = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.metadata_filter_type
  (
    id SMALLSERIAL PRIMARY KEY,
    name text,
    distinct_answer boolean,
    is_content_access boolean,
    is_verbatim boolean
  )`;

  return [createTable];
};

export const client_metadata_filter = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.metadata_filter
  (
    id SMALLSERIAL PRIMARY KEY,
    type_id smallint REFERENCES ${clientId}.metadata_filter_type(id),
    value text,
    description text,
    synonyms text,
    is_active boolean DEFAULT true,
    rfp_default boolean DEFAULT false
  )`;

  const createUnique = `
  ALTER TABLE ${clientId}.metadata_filter ADD CONSTRAINT metadata_filter_type_value_unique UNIQUE (type_id, value)`;

  return [createTable, createUnique];
};

export const client_document_type_priority = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.document_type_priority
  (
    id SMALLSERIAL PRIMARY KEY,
    name text,
    multiplier numeric
  )`;

  const insertDefault = `INSERT INTO ${clientId}.document_type_priority(name, multiplier) VALUES('DEFAULT', 1.0)`;
  const insertHigh = `INSERT INTO ${clientId}.document_type_priority(name, multiplier) VALUES('HIGH', 1.1)`;
  const insertHighest = `INSERT INTO ${clientId}.document_type_priority(name, multiplier) VALUES('HIGHEST', 1.2)`;

  return [createTable, insertDefault, insertHigh, insertHighest];
};

export const client_document_type = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.document_type
  (
    id SMALLSERIAL PRIMARY KEY,
    name text,
    priority_id smallint REFERENCES ${clientId}.document_type_priority(id),
    is_rfp boolean,
    is_spreadsheet boolean,
    is_website boolean,
    is_system boolean
  )`;

  const insertQuestionnaire = `
  INSERT INTO ${clientId}.document_type(name, priority_id, is_rfp, is_system)
  SELECT 'Questionnaire', dtp.id, true, true
  FROM   ${clientId}.document_type_priority dtp
  WHERE  dtp.name = 'DEFAULT'`;

  const insertDocument = `
  INSERT INTO ${clientId}.document_type(name, priority_id, is_rfp, is_system)
  SELECT 'Document', dtp.id, false, true
  FROM   ${clientId}.document_type_priority dtp
  WHERE  dtp.name = 'DEFAULT'`;

  const insertPresentation = `
  INSERT INTO ${clientId}.document_type(name, priority_id, is_rfp, is_system)
  SELECT 'Presentation', dtp.id, false, true
  FROM   ${clientId}.document_type_priority dtp
  WHERE  dtp.name = 'DEFAULT'`;

  const insertWebsite = `
  INSERT INTO ${clientId}.document_type(name, priority_id, is_rfp, is_website, is_system)
  SELECT 'Website', dtp.id, false, true, true
  FROM   ${clientId}.document_type_priority dtp
  WHERE  dtp.name = 'DEFAULT'`;

  const insertSpreadsheet = `
  INSERT INTO ${clientId}.document_type(name, priority_id, is_spreadsheet, is_system)
  SELECT 'Spreadsheet', dtp.id, true, true
  FROM   ${clientId}.document_type_priority dtp
  WHERE  dtp.name = 'DEFAULT'`;

  const insertSalesforce = `
  INSERT INTO ${clientId}.document_type(name, priority_id, is_spreadsheet, is_system)
  SELECT 'Salesforce', dtp.id, false, true
  FROM   ${clientId}.document_type_priority dtp
  WHERE  dtp.name = 'DEFAULT'`;

  const insertCallTranscript = `
  INSERT INTO ${clientId}.document_type(name, priority_id, is_rfp, is_system)
  SELECT 'Call Transcript', dtp.id, false, true
  FROM   ${clientId}.document_type_priority dtp
  WHERE  dtp.name = 'DEFAULT'`;

  const insertInsight = `
  INSERT INTO ${clientId}.document_type(name, priority_id, is_rfp, is_system)
  SELECT 'Insight', dtp.id, false, true
  FROM   ${clientId}.document_type_priority dtp
  WHERE  dtp.name = 'DEFAULT'`;

  const insertCorporateTemplate = `
  INSERT INTO ${clientId}.document_type(name, priority_id, is_rfp, is_system)
  SELECT 'Corporate Template', dtp.id, false, true
  FROM   ${clientId}.document_type_priority dtp
  WHERE  dtp.name = 'DEFAULT'`;

  return [
    createTable,
    insertQuestionnaire,
    insertDocument,
    insertPresentation,
    insertWebsite,
    insertSpreadsheet,
    insertSalesforce,
    insertCallTranscript,
    insertInsight,
    insertCorporateTemplate,
  ];
};

export const client_job = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.job
  (
    id SERIAL PRIMARY KEY,
    type text,
    status text,
    request_body jsonb NOT NULL DEFAULT '{}'::jsonb,
    response_body jsonb NOT NULL DEFAULT '{}'::jsonb,
    error text,
    created_date date,
    updated_at timestamp,
    created_by_id integer REFERENCES ${constants.SCHEMA_TRIBBLE}.user(id)
  )`;

  const createIndex = `
  CREATE INDEX idx_job_type
  ON ${clientId}.job USING btree
  (type COLLATE pg_catalog."default" ASC NULLS LAST)
  TABLESPACE pg_default`;

  const createUpdatedAtIndex = `
  CREATE INDEX IF NOT EXISTS idx_updated_at_timestamp ON ${clientId}.job
  USING btree (updated_at ASC NULLS LAST) WITH (deduplicate_items=True)
  TABLESPACE pg_default`;

  const updatedAtTrigger = `
  CREATE OR REPLACE TRIGGER update_updated_at_before_update
  BEFORE INSERT OR UPDATE ON ${clientId}.job
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column()
  `;

  const createCreatedDateIndex = `CREATE INDEX idx_job_created_date ON ${clientId}.job (created_date) TABLESPACE pg_default`;

  return [
    createTable,
    createIndex,
    createUpdatedAtIndex,
    updatedAtTrigger,
    createCreatedDateIndex,
  ];
};

export const client_content = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.content
  (
    id BIGSERIAL PRIMARY KEY,
    job_id integer REFERENCES ${clientId}.job(id),
    type text,
    source text,
    statistics jsonb NOT NULL DEFAULT '{}'::jsonb,
    metadata_filter jsonb DEFAULT '{}'::jsonb,
    source_excluded_platforms text[] DEFAULT '{}',
    is_deleted boolean,
    due_date date,
    details jsonb DEFAULT '{}'::jsonb
  )`;

  const createIndex1 = `
  CREATE INDEX idx_content_job_id
  ON ${clientId}.content USING btree
  (job_id ASC NULLS LAST)
  TABLESPACE pg_default`;

  const createIndex2 = `
  CREATE INDEX idx_content_source
  ON ${clientId}.content USING btree
  (source ASC NULLS LAST)
  TABLESPACE pg_default`;

  const createIndex3 = `CREATE INDEX idx_content_type ON ${clientId}.content (type) TABLESPACE pg_default`;

  return [createTable, createIndex1, createIndex2, createIndex3];
};

export const client_content_grouping = (clientId: string) => {
  const grouping = `
  CREATE TABLE IF NOT EXISTS ${clientId}.grouping (
    id SERIAL PRIMARY KEY,
    name text NOT NULL,
    metadata_filter jsonb DEFAULT '{}'::jsonb
  )`;

  return [grouping];
};

export const client_content_grouping_join = (clientId: string) => {
  const clientGrouping = `CREATE TABLE IF NOT EXISTS ${clientId}.content_grouping_join (
    id SERIAL PRIMARY KEY,
    content_id integer REFERENCES ${clientId}.content(id),
    grouping_id integer REFERENCES ${clientId}.grouping(id),
    CONSTRAINT content_grouping_join_unique_content_id_grouping_id UNIQUE (content_id, grouping_id)
  )`;

  return [clientGrouping];
};

export const client_content_user_join = (clientId: string) => {
  const contentUser = `
  CREATE TABLE IF NOT EXISTS ${clientId}.content_user_join (
    id SERIAL PRIMARY KEY,
    content_id integer REFERENCES ${clientId}.content(id) ON DELETE CASCADE,
    assignee_id integer REFERENCES tribble.user(id),
    is_owner boolean,
    CONSTRAINT content_user_join_unique_content_id_assignee_id UNIQUE (content_id, assignee_id)
  )`;

  return [contentUser];
};

export const client_content_detail = (clientId: string) => {
  const createTable = `
  CREATE OR REPLACE FUNCTION get_max_answer_version_confidence(answer_versions jsonb)
  RETURNS integer AS $$
    SELECT
      CASE
        WHEN answer_versions IS NULL OR jsonb_array_length(answer_versions) = 0
          THEN NULL
        ELSE (
          SELECT MAX((elem->>'confidence_score')::integer)
          FROM jsonb_array_elements(answer_versions) AS elem
        )
      END
  $$ LANGUAGE sql IMMUTABLE PARALLEL SAFE;

  CREATE TABLE ${clientId}.content_detail
  (
    id BIGSERIAL PRIMARY KEY,
    content_id bigint REFERENCES ${clientId}.content(id) ON DELETE CASCADE,
    index smallint,
    input jsonb NOT NULL DEFAULT '{}'::jsonb,
    output_original jsonb NOT NULL DEFAULT '{}'::jsonb,
    state text,
    state_updated_at TIMESTAMP WITH TIME ZONE,
    output_modified jsonb NOT NULL DEFAULT '{}'::jsonb,
    modified_by_id integer REFERENCES ${constants.SCHEMA_TRIBBLE}.user(id),
    modified_at TIMESTAMP WITH TIME ZONE,
    statistics jsonb NOT NULL DEFAULT '{}'::jsonb,
    user_id integer REFERENCES ${constants.SCHEMA_TRIBBLE}.user(id),
    grouping_id integer REFERENCES ${clientId}.grouping(id),
    assignee_id integer REFERENCES ${constants.SCHEMA_TRIBBLE}.user(id),
    assigned_at TIMESTAMP WITH TIME ZONE,
    conversation_id integer REFERENCES ${clientId}.conversation(id) ON DELETE SET NULL,
    answer_versions_count INT GENERATED ALWAYS AS (jsonb_array_length(output_original -> 'answer_versions')) STORED,
    answer_text TEXT GENERATED ALWAYS AS (output_original ->> 'answer') STORED,
    confidence_score INT GENERATED ALWAYS AS ((output_original -> 'confidence_score')::integer) STORED,
    max_answer_version_confidence INT GENERATED ALWAYS AS
      (get_max_answer_version_confidence(output_original -> 'answer_versions')) STORED,
    loop_in_expert_sent boolean NOT NULL DEFAULT false
  )`;

  const createIndex = `
  CREATE INDEX idx_content_detail_content_id
  ON ${clientId}.content_detail USING btree
  (content_id ASC NULLS LAST)
  INCLUDE (assignee_id)
  TABLESPACE pg_default`;

  const createIndex2 = `
  CREATE INDEX content_detail_query_trgm_idx ON ${clientId}.content_detail USING GIN (jsonb_to_text(input, ARRAY['query_string']) gin_trgm_ops) TABLESPACE pg_default
  `;

  const createIndex3 = `
  CREATE INDEX content_detail_answer_trgm_idx ON ${clientId}.content_detail USING GIN (jsonb_to_text(output_original, ARRAY['answer']) gin_trgm_ops) TABLESPACE pg_default
  `;

  const createIndex4 = `
  CREATE INDEX idx_content_detail_answer_version_length ON ${clientId}.content_detail (jsonb_array_length(output_original -> 'answer_versions')) TABLESPACE pg_default
  `;

  const createIndex5 = `
  CREATE INDEX idx_content_detail_answer_version_count ON ${clientId}.content_detail(answer_versions_count) TABLESPACE pg_default
  `;

  const createIndex6 = `
  CREATE INDEX idx_content_detail_confidence ON ${clientId}.content_detail(confidence_score) TABLESPACE pg_default
  `;

  const createIndex7 = `
  CREATE INDEX idx_content_detail_max_conf ON ${clientId}.content_detail(max_answer_version_confidence) TABLESPACE pg_default
  `;

  return [
    createTable,
    createIndex,
    createIndex2,
    createIndex3,
    createIndex4,
    createIndex5,
    createIndex6,
    createIndex7,
  ];
};

export const client_content_detail_embedding = (clientId: string) => {
  const createTable = `
  CREATE TABLE IF NOT EXISTS ${clientId}.content_detail_embedding (
    content_detail_id bigint REFERENCES ${clientId}.content_detail(id) ON DELETE CASCADE NOT NULL,
    embedding_id bigint REFERENCES ${clientId}.embedding(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (content_detail_id, embedding_id)
  )`;

  const createIndex1 = `
  CREATE INDEX IF NOT EXISTS idx_content_detail_embedding_content_detail_id
  ON ${clientId}.content_detail_embedding USING btree
  (content_detail_id ASC NULLS LAST)
  TABLESPACE pg_default`;

  const createIndex2 = `
  CREATE INDEX IF NOT EXISTS idx_content_detail_embedding_embedding_id
  ON ${clientId}.content_detail_embedding USING btree
  (embedding_id ASC NULLS LAST)
  TABLESPACE pg_default`;

  const createIndex3 = `
  CREATE INDEX idx_content_detail_embedding_usage_stats
  ON ${clientId}.content_detail_embedding (embedding_id, created_at)
  `;

  return [createTable, createIndex1, createIndex2, createIndex3];
};

export const client_content_detail_insight = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.content_detail_insight (
    content_detail_id BIGINT NOT NULL REFERENCES ${clientId}.content_detail(id) ON DELETE CASCADE,
    insight_id BIGINT NOT NULL REFERENCES ${clientId}.embedding(id) ON DELETE CASCADE,
    PRIMARY KEY (content_detail_id, insight_id)
  )`;

  const createIndex1 = `
  CREATE INDEX IF NOT EXISTS idx_content_detail_insight_content_detail_id
  ON ${clientId}.content_detail_insight USING btree
  (content_detail_id ASC NULLS LAST)
  TABLESPACE pg_default`;

  const createIndex2 = `
  CREATE INDEX IF NOT EXISTS idx_content_detail_insight_insight_id
  ON ${clientId}.content_detail_insight USING btree
  (insight_id ASC NULLS LAST)
  TABLESPACE pg_default`;

  return [createTable, createIndex1, createIndex2];
};

export const client_document = (clientId: string) => {
  const createTable = `
    CREATE TABLE ${clientId}.document
    (
      id BIGSERIAL PRIMARY KEY,
      job_id integer REFERENCES ${clientId}.job(id),
      file_name text,
      label text,
      uuid uuid,
      document_revision integer,
      cloud_version_id integer,
      prior_version_document_id integer REFERENCES ${clientId}.document(id),
      created_date date,
      created_by_id integer REFERENCES ${constants.SCHEMA_TRIBBLE}.user(id),
      expiry_date date,
      deleted_date date,
      deleted_by_id integer REFERENCES ${constants.SCHEMA_TRIBBLE}.user(id),
      status text,
      type text,
      document_type_id smallint REFERENCES ${clientId}.document_type(id),
      privacy text,
      use_for_generation boolean,
      metadata_json jsonb,
      is_custom_table boolean DEFAULT false,
      source_url text,
      preview_uuid uuid,
      file_type text,
      description text
    )`;

  const createIndex = `
    CREATE INDEX idx_document_job_id
    ON ${clientId}.document USING btree
    (job_id ASC NULLS LAST)
    TABLESPACE pg_default
  `;

  const createIndex2 = `
    CREATE INDEX document_label_trgm_idx
    ON ${clientId}.document
    USING GIN (label gin_trgm_ops)
    TABLESPACE pg_default
  `;

  const createIndex3 = `
    CREATE INDEX idx_document_id_use_for_generation
    ON ${clientId}.document(id, use_for_generation)
    WHERE deleted_date IS NULL
  `;

  const createIndex4 = `
    CREATE INDEX idx_document_active_label
    ON ${clientId}.document (id, label, document_type_id)
    WHERE deleted_date IS NULL
  `;

  return [createTable, createIndex, createIndex2, createIndex3, createIndex4];
};

export const client_embedding = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.embedding
  (
    id BIGSERIAL PRIMARY KEY,
    document_id integer REFERENCES ${clientId}.document(id) ON DELETE CASCADE,
    index integer,
    content text,
    content_type text,
    content_meta jsonb NOT NULL DEFAULT '{}'::jsonb,
    embedding vector(1536),
    use_for_generation boolean,
    is_deleted boolean NOT NULL DEFAULT FALSE,
    origin text,
    metadata_filter jsonb DEFAULT '{}'::jsonb,
    modified_by_id integer REFERENCES ${constants.SCHEMA_TRIBBLE}.user(id),
    modified_date date,
    orig_content text,
    orig_embedding vector(1536),
    orig_metadata_filter jsonb DEFAULT '{}'::jsonb,
    source_created_date timestamptz,
    source_modified_date timestamptz
  )`;

  const createIndex1 = `
  CREATE INDEX idx_embedding_content_type
  ON ${clientId}.embedding USING btree
  (content_type COLLATE pg_catalog."default" ASC NULLS LAST)
  TABLESPACE pg_default`;

  const createIndex2 = `
  CREATE INDEX idx_embedding_document_id
  ON ${clientId}.embedding USING btree
  (document_id ASC NULLS LAST)
  TABLESPACE pg_default`;

  const createIndex3 = `
  CREATE INDEX idx_embedding_origin
  ON ${clientId}.embedding USING btree
  (origin COLLATE pg_catalog."default" ASC NULLS LAST)
  TABLESPACE pg_default`;

  const createIndex4 = `
  CREATE INDEX embedding_content_trgm_idx ON ${clientId}.embedding USING GIN (content gin_trgm_ops) TABLESPACE pg_default
  `;

  const createIndex5 = `
  CREATE INDEX idx_embedding_content_trim ON ${clientId}.embedding
    (LENGTH(TRIM(content))) WHERE length(trim(content)) > 0
  `;

  const createIndex6 = `
  CREATE INDEX idx_embedding_combined_filters
  ON ${clientId}.embedding(origin, content_type, use_for_generation)`;

  const createIndex7 = `
  CREATE INDEX idx_embedding_metadata_filter
  ON ${clientId}.embedding USING gin (metadata_filter)`;

  const createIndex8 = `
  CREATE INDEX idx_embedding_origin_document_join
  ON ${clientId}.embedding(origin, document_id)
  `;

  const createIndex9 = `
  CREATE INDEX idx_embedding_metadata_filter_type_id
  ON ${clientId}.embedding ((metadata_filter -> 'type_id'))
  WHERE metadata_filter -> 'values' IS NOT NULL
  `;

  return [
    createTable,
    createIndex1,
    createIndex2,
    createIndex3,
    createIndex4,
    createIndex5,
    createIndex6,
    createIndex7,
    createIndex8,
    createIndex9,
  ];
};

export const client_embedding_owner = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.embedding_owner
  (
    id SERIAL PRIMARY KEY,
    embedding_id bigint REFERENCES ${clientId}.embedding(id) ON DELETE CASCADE NOT NULL,
    user_id integer REFERENCES ${constants.SCHEMA_TRIBBLE}.user(id) NOT NULL,
    UNIQUE (embedding_id, user_id)
  )`;

  const createIndex1 = `
  CREATE INDEX idx_embedding_owner_embedding_id
  ON ${clientId}.embedding_owner USING btree
  (embedding_id ASC NULLS LAST)
  TABLESPACE pg_default`;

  const createIndex2 = `
  CREATE INDEX idx_embedding_owner_user_id
  ON ${clientId}.embedding_owner USING btree
  (user_id ASC NULLS LAST)
  TABLESPACE pg_default`;

  return [createTable, createIndex1, createIndex2];
};

export const client_embedding_history = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.embedding_history
  (
    id SERIAL PRIMARY KEY,
    embedding_id bigint REFERENCES ${clientId}.embedding(id) ON DELETE CASCADE NOT NULL,
    user_id integer REFERENCES ${constants.SCHEMA_TRIBBLE}.user(id),
    history_date timestamp without time zone,
    history_content text,
    history_metadata_filter jsonb DEFAULT '{}'::jsonb
  )`;

  const createIndex1 = `
  CREATE INDEX idx_embedding_history_embedding_id
  ON ${clientId}.embedding_history USING btree
  (embedding_id ASC NULLS LAST)
  TABLESPACE pg_default`;

  const createIndex2 = `
  CREATE INDEX idx_embedding_history_user_id
  ON ${clientId}.embedding_history USING btree
  (user_id ASC NULLS LAST)
  TABLESPACE pg_default`;

  return [createTable, createIndex1, createIndex2];
};

export const client_pending_query = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.pending_query
  (
    id BIGSERIAL PRIMARY KEY,
    job_id integer REFERENCES ${clientId}.job(id),
    content_id integer REFERENCES ${clientId}.content(id),
    content_detail_id bigint REFERENCES ${clientId}.content_detail(id) ON DELETE CASCADE,
    ts integer,
    index smallint,
    embedding vector(1536),
    contexts jsonb DEFAULT '{}'::jsonb
  )`;

  const createIndex = `
  CREATE INDEX idx_pending_query_ts
  ON ${clientId}.pending_query USING btree
  (ts ASC NULLS LAST)
  TABLESPACE pg_default`;

  return [createTable, createIndex];
};

export const client_content_review = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.content_review
  (
    id SERIAL PRIMARY KEY,
    embedding_id bigint REFERENCES ${clientId}.embedding(id) ON DELETE CASCADE,
    content_detail_id bigint REFERENCES ${clientId}.content_detail(id) ON DELETE CASCADE,
    created_date date,
    created_by_id integer REFERENCES ${constants.SCHEMA_TRIBBLE}.user(id),
    description text,
    status text,
    processed_date date,
    processed_by_id integer REFERENCES ${constants.SCHEMA_TRIBBLE}.user(id)
  )`;

  const createIndex1 = `
  CREATE INDEX idx_content_review_content_detail_id
  ON ${clientId}.content_review USING btree
  (content_detail_id ASC NULLS LAST)
  TABLESPACE pg_default`;

  const createIndex2 = `
  CREATE INDEX idx_content_review_embedding_id
  ON ${clientId}.content_review USING btree
  (embedding_id ASC NULLS LAST)
  TABLESPACE pg_default`;

  return [createTable, createIndex1, createIndex2];
};

export const client_conversation = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.conversation
  (
    id BIGSERIAL PRIMARY KEY,
    system text,
    channel text,
    message_id text,
    user_id integer REFERENCES ${constants.SCHEMA_TRIBBLE}.user(id),
    embedding_ids integer[],
    created_date date,
    last_activity_date date,
    sources jsonb,
    lock_expires_at timestamp without time zone
  )`;

  const createIndex1 = `
  CREATE INDEX idx_conversation_system
  ON ${clientId}.conversation USING btree
  (system COLLATE pg_catalog."default" ASC NULLS LAST)
  TABLESPACE pg_default`;

  const createIndex2 = `
  CREATE INDEX idx_conversation_channel_message
  ON ${clientId}.conversation USING btree
  (channel COLLATE pg_catalog."default" ASC NULLS LAST, message_id COLLATE pg_catalog."default" ASC NULLS LAST)
  TABLESPACE pg_default`;

  const createIndex3 = `
  CREATE INDEX idx_conversation_user_id
  ON ${clientId}.conversation USING btree
  (user_id ASC NULLS LAST)
  TABLESPACE pg_default`;

  return [createTable, createIndex1, createIndex2, createIndex3];
};

export const client_conversation_state = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.conversation_state
  (
    id SERIAL PRIMARY KEY,
    conversation_id integer REFERENCES ${clientId}.conversation(id),
    metadata_filter jsonb DEFAULT '[]'::jsonb,
    created_date timestamp with time zone DEFAULT now(),
    files JSONB default '[]'::jsonb,
    research JSONB default '[]'::jsonb,
    time_filter JSONB default NULL,
    cartridge_enum_value integer
  )`;
  const createIndex1 = `
  CREATE INDEX IF NOT EXISTS conversation_state_conversation_id_idx
    ON ${clientId}.conversation_state USING btree
    (conversation_id ASC NULLS LAST)
    TABLESPACE pg_default;
  `;

  return [createTable, createIndex1];
};

export const client_conversation_detail = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.conversation_detail
  (
    id SERIAL PRIMARY KEY,
    conversation_id integer REFERENCES ${clientId}.conversation(id),
    message_id text,
    type text,
    seq integer,
    message jsonb NOT NULL DEFAULT '{}'::jsonb,
    output jsonb NOT NULL DEFAULT '{}'::jsonb,
    input jsonb NOT NULL DEFAULT '{}'::jsonb,
    statistics jsonb NOT NULL DEFAULT '{}'::jsonb,
    created_date date,
    sources jsonb,
    user_id integer REFERENCES tribble.user(id) ON DELETE CASCADE,
    is_background_agent_message BOOLEAN DEFAULT FALSE,
    is_injected_message BOOLEAN DEFAULT FALSE,
    cartridge_enum_value integer
  )`;

  const createIndex = `
  CREATE INDEX idx_conversation_detail_conversation_id
  ON ${clientId}.conversation_detail USING btree
  (conversation_id ASC NULLS LAST)
  TABLESPACE pg_default`;

  const createIndex2 = `
  CREATE INDEX IF NOT EXISTS idx_conversation_detail_message_id
  ON ${clientId}.conversation_detail USING btree
  (message_id ASC NULLS LAST)
  TABLESPACE pg_default;
  `;

  const createIndex3 = `
  CREATE INDEX IF NOT EXISTS idx_conversation_detail_is_background_agent_msg
  ON ${clientId}.conversation_detail USING btree
  (is_background_agent_message ASC NULLS LAST)
  TABLESPACE pg_default;
  `;

  const createIndex4 = `
  CREATE INDEX IF NOT EXISTS idx_conversation_detail_is_injected_msg
  ON ${clientId}.conversation_detail USING btree
  (is_injected_message ASC NULLS LAST)
  TABLESPACE pg_default;
  `;

  const createIndex5 = `
  CREATE INDEX IF NOT EXISTS idx_conversation_detail_seq
  ON ${clientId}.conversation_detail USING btree
  (seq ASC NULLS LAST)
  TABLESPACE pg_default;
  `;

  return [
    createTable,
    createIndex,
    createIndex2,
    createIndex3,
    createIndex4,
    createIndex5,
  ];
};

export const client_conversation_detail_embedding = (clientId: string) => {
  const createTable = `
  CREATE TABLE IF NOT EXISTS ${clientId}.conversation_detail_embedding (
    conversation_detail_id BIGINT
      REFERENCES ${clientId}.conversation_detail(id)
        ON DELETE CASCADE
      NOT NULL,
    embedding_id BIGINT
      REFERENCES ${clientId}.embedding(id)
        ON DELETE CASCADE
      NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (conversation_detail_id, embedding_id)
  )`;

  const createIndex = `
  CREATE INDEX idx_conversation_detail_embedding_embedding_id ON ${clientId}.conversation_detail_embedding(embedding_id);
  `;

  const createIndex2 = `
  CREATE INDEX idx_conversation_detail_embedding_conversation_detail_id ON ${clientId}.conversation_detail_embedding(conversation_detail_id);
  `;

  const createIndex3 = `
  CREATE INDEX conversation_detail_output_text_trgm_idx ON ${clientId}.conversation_detail USING GIN (jsonb_to_text(output, ARRAY['text']) gin_trgm_ops) TABLESPACE pg_default;
  `;
  const createIndex4 = `
  CREATE INDEX conversation_detail_message_content_trgm_idx ON ${clientId}.conversation_detail USING GIN (jsonb_to_text(message, ARRAY['content']) gin_trgm_ops) TABLESPACE pg_default;
  `;

  return [createTable, createIndex, createIndex2, createIndex3, createIndex4];
};

export const client_conversation_detail_file = (clientId: string) => {
  const createTable = `
  CREATE TABLE IF NOT EXISTS ${clientId}.conversation_detail_file (
      id BIGSERIAL PRIMARY KEY,
      mime_type TEXT,
      storage_id TEXT, -- uuidv4 identifying this blob in storage.
      conversation_detail_id BIGINT REFERENCES ${clientId}.conversation_detail(id) ON DELETE CASCADE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
  )`;
  const createIndex = `
  CREATE INDEX IF NOT EXISTS idx_conversation_detail_file_conversation_detail_id
    ON ${clientId}.conversation_detail_file (conversation_detail_id);`;
  return [createTable, createIndex];
};

export const client_conversation_summary = (clientId: string) => {
  const createTable = `
  CREATE TABLE IF NOT EXISTS ${clientId}.conversation_summary (
      id SERIAL NOT NULL PRIMARY KEY,
      content TEXT NOT NULL,
      seq INTEGER NOT NULL,
      conversation_id INTEGER REFERENCES ${clientId}.conversation(id)
  )`;

  const createIndex = `
  CREATE INDEX idx_conversation_summary_conversation_id
      ON ${clientId}.conversation_summary USING btree
          (conversation_id ASC NULLS LAST)
      TABLESPACE pg_default`;

  return [createTable, createIndex];
};

export const client_generated_content = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.generated_content
  (
    id SERIAL PRIMARY KEY,
    job_id integer REFERENCES ${clientId}.job(id),
    label text,
    version_id integer,
    prior_version_id integer REFERENCES ${clientId}.generated_content(id),
    created_date date NOT NULL DEFAULT CURRENT_DATE,
    created_by_id integer REFERENCES ${constants.SCHEMA_TRIBBLE}.user(id),
    deleted_date date,
    deleted_by_id integer REFERENCES ${constants.SCHEMA_TRIBBLE}.user(id),
    status text,
    type text,
    input jsonb DEFAULT '{}'::jsonb,
    statistics jsonb DEFAULT '{}'::jsonb,
    output_original jsonb DEFAULT '{}'::jsonb,
    output_modified jsonb DEFAULT '{}'::jsonb,
    modified_by_id integer REFERENCES ${constants.SCHEMA_TRIBBLE}.user(id)
  )`;

  const createIndex = `
  CREATE INDEX idx_generated_content_job_id
  ON ${clientId}.generated_content USING btree
  (job_id ASC NULLS LAST)
  TABLESPACE pg_default`;

  return [createTable, createIndex];
};

export const client_brand_config = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.${constants.TABLE_BRAND_CONFIG}
  (
    id SERIAL PRIMARY KEY,
    name text,
    type text,
    data jsonb DEFAULT '{}'::jsonb
  )`;

  return [createTable];
};

export const client_job_log = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.${constants.TABLE_JOB_LOG}
  (
    id SERIAL PRIMARY KEY,
    job_id integer REFERENCES ${clientId}.job(id),
    type text,
    data jsonb DEFAULT '{}'::jsonb,
    created_date timestamp DEFAULT CURRENT_TIMESTAMP
  )`;

  const createIndex = `
  CREATE INDEX idx_${constants.TABLE_JOB_LOG}_job_id
  ON ${clientId}.${constants.TABLE_JOB_LOG} USING btree
  (job_id ASC NULLS LAST)
  TABLESPACE pg_default`;

  return [createTable, createIndex];
};

export const client_extraction_task_config = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.${constants.TABLE_EXTRACTION_TASK_CONFIG}
  (
    id SERIAL PRIMARY KEY,
    name text NOT NULL,
    is_enabled boolean NOT NULL DEFAULT true,
    configuration jsonb NOT NULL DEFAULT '{}'::jsonb,
    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
  )`;

  return [createTable];
};

export const client_task = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.${constants.TABLE_TASK}
  (
    id SERIAL PRIMARY KEY,
    label TEXT NOT NULL,
    description TEXT,
    priority INTEGER,
    created_by_id INTEGER REFERENCES ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_USER}(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    due_date DATE,
    status TEXT,
    origin TEXT,
    type TEXT,
    resolution_reason TEXT
  )
  `;

  return [createTable];
};

export const client_task_feedback_assignee = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.${constants.TABLE_TASK_FEEDBACK_ASSIGNEE} (
    id SERIAL PRIMARY KEY,
    task_feedback_id INTEGER NOT NULL REFERENCES ${clientId}.${constants.TABLE_TASK_FEEDBACK}(id),
    assignee_id INTEGER REFERENCES ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_USER}(id),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (task_feedback_id, assignee_id)
  )`;
  const idx1 = `CREATE INDEX IF NOT EXISTS idx_task_feedback_assignee_task_feedback_id ON ${clientId}.${constants.TABLE_TASK_FEEDBACK_ASSIGNEE} (task_feedback_id)`;
  const idx2 = `CREATE INDEX IF NOT EXISTS idx_task_feedback_assignee_assignee_id ON ${clientId}.${constants.TABLE_TASK_FEEDBACK_ASSIGNEE} (assignee_id)`;
  return [createTable, idx1, idx2];
};

export const client_task_owner = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.${constants.TABLE_TASK_OWNER} (
    id SERIAL PRIMARY KEY,
    task_embedding_id INTEGER REFERENCES ${clientId}.${constants.TABLE_TASK_EMBEDDING}(id) ON DELETE CASCADE,
    owner_id INTEGER REFERENCES ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_USER}(id),
    task_id INTEGER REFERENCES ${clientId}.${constants.TABLE_TASK}(id) ON DELETE CASCADE
  )`;
  const idx1 = `CREATE INDEX IF NOT EXISTS idx_task_owner_task_embedding_id ON ${clientId}.${constants.TABLE_TASK_OWNER} (task_embedding_id)`;
  const idx2 = `CREATE INDEX IF NOT EXISTS idx_task_owner_owner_id ON ${clientId}.${constants.TABLE_TASK_OWNER} (owner_id)`;

  return [createTable, idx1, idx2];
};

export const client_task_embedding = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.${constants.TABLE_TASK_EMBEDDING} (
    id SERIAL PRIMARY KEY,
    task_id INTEGER REFERENCES ${clientId}.${constants.TABLE_TASK}(id) ON DELETE CASCADE,
    embedding_id bigint REFERENCES ${clientId}.${constants.TABLE_EMBEDDING}(id) ON DELETE CASCADE,
    status TEXT,
    UNIQUE (task_id, embedding_id)
  )`;
  const idx1 = `CREATE INDEX IF NOT EXISTS idx_task_embedding_task_id ON ${clientId}.${constants.TABLE_TASK_EMBEDDING} (task_id)`;
  return [createTable, idx1];
};

export const client_teams_thread = (clientId: string) => {
  const createTable = `
  CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_TEAMS_THREAD} (
    id SERIAL PRIMARY KEY,
    reply_to_Id TEXT NOT NULL,
    original_message_id TEXT NOT NULL,
    thread_data JSONB NOT NULL
  )`;
  const idx1 = `CREATE INDEX IF NOT EXISTS idx_teams_thread_reply_to_id ON ${clientId}.${constants.TABLE_TEAMS_THREAD} (reply_to_id)`;
  const idx2 = `CREATE INDEX IF NOT EXISTS idx_teams_thread_original_message_id ON ${clientId}.${constants.TABLE_TEAMS_THREAD} (original_message_id)`;
  return [createTable, idx1, idx2];
};

export const client_teams_conversation_reference = (clientId: string) => {
  const createTable = `
  CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_TEAMS_CONVERSATION_REFERENCE} (
    id SERIAL PRIMARY KEY,
    teams_conversation_id text NOT NULL,
    conversation_reference JSONB NOT NULL UNIQUE,
    user_id integer REFERENCES tribble.user(id)
  )`;
  const idx1 = `CREATE UNIQUE INDEX IF NOT EXISTS idx_teams_conversation_reference_teams_conversation_id ON ${clientId}.${constants.TABLE_TEAMS_CONVERSATION_REFERENCE} (teams_conversation_id)`;
  const idx2 = `CREATE INDEX IF NOT EXISTS idx_teams_conversation_reference_user_id ON ${clientId}.${constants.TABLE_TEAMS_CONVERSATION_REFERENCE} (user_id)`;
  return [createTable, idx1, idx2];
};

export const client_custom_table_catalog = (clientId: string) => {
  const createTable = `CREATE TABLE IF NOT EXISTS ${clientId}.table_catalog (
      id SERIAL PRIMARY KEY,
      table_name TEXT NOT NULL, -- this will be generated to avoid collisions. something like [hash]_[file_name]
      file_name TEXT NOT NULL,
      document_id integer REFERENCES ${clientId}.document(id) ON DELETE CASCADE,
      label TEXT,
      description TEXT,
      description_embedding vector,
      structure JSONB DEFAULT '{}'::jsonb NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );`;
  return [createTable];
};

export const client_gdrive_asset = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.${constants.TABLE_GDRIVE_ASSET} (
    id BIGSERIAL PRIMARY KEY,
    drive_id TEXT NOT NULL,         -- The ID of the file/folder in Google Drive.
    blob_id TEXT,                   -- The ID of the blob in Blob Storage.
    -- Foreign key to the brain "document" for this asset.
    document_id BIGINT REFERENCES ${clientId}.document(id) ON DELETE CASCADE,
    job_id BIGINT REFERENCES ${clientId}.job(id), -- The job that this asset belongs to.
    user_id BIGINT NOT NULL REFERENCES tribble.user(id), -- The ID of the user who owns the asset.
    name TEXT,                      -- The name of the file/folder.
    mime_type TEXT,                 -- The MIME type to differentiate between files and folders.
    parent_id TEXT,                 -- The ID of the parent folder (nullable).
    status TEXT NOT NULL,           -- The status of the asset (e.g., 'active', 'inactive', 'deleted').
    last_modified TIMESTAMPTZ,      -- The last time the asset was modified.
    last_checked TIMESTAMPTZ,       -- The last time the asset was checked for changes
    created_at TIMESTAMPTZ DEFAULT NOW(), -- The time this record was first created.
    updated_at TIMESTAMPTZ DEFAULT NOW(), -- The time this record was last updated.
    source_created_date TIMESTAMPTZ, -- The original creation time from Google Drive.
    url TEXT,                       -- The web view URL of the file/folder.
    type TEXT,
    --rfp_details JSONB,            -- JSON object containing details about the RFP.
    watch_for_changes BOOLEAN DEFAULT FALSE,      -- Whether to watch for changes in the asset.
    task_id INTEGER REFERENCES ${clientId}.${constants.TABLE_TASK}(id) ON DELETE SET NULL,
    UNIQUE(drive_id)                -- Ensure uniqueness of Google Drive IDs.
  );`,
    userIdIdx = `CREATE INDEX idx_gdriveasset_user_id ON ${clientId}.${constants.TABLE_GDRIVE_ASSET} (user_id)`,
    driveIdIdx = `CREATE INDEX idx_gdriveasset_drive_id ON ${clientId}.${constants.TABLE_GDRIVE_ASSET} (drive_id)`,
    documentIdIdx = `CREATE INDEX idx_gdriveasset_document_id ON ${clientId}.${constants.TABLE_GDRIVE_ASSET} (document_id)`;
  return [createTable, userIdIdx, driveIdIdx, documentIdIdx];
};

export const client_integration_asset_sheet = (clientId: string) => {
  const createTable = `
  CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_INTEGRATION_ASSET_SHEET} (
    id BIGSERIAL PRIMARY KEY,
    asset_id BIGINT,
    document_id BIGINT REFERENCES ${clientId}.${constants.TABLE_DOC}(id),
    job_id BIGINT REFERENCES ${clientId}.${constants.TABLE_JOB}(id),
    blob_id TEXT,
    type TEXT, -- The type of sheet (e.g., 'rfp', 'spreadsheet')
    sheet_id INTEGER,
    sheet_name TEXT,
    details JSONB,
    integration_id BIGINT REFERENCES ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_INTEGRATION_SYSTEM}(id),
    UNIQUE(asset_id, sheet_id, integration_id)
  );`,
    assetIdIdx = `CREATE INDEX IF NOT EXISTS idx_integration_asset_sheet_asset_id ON ${clientId}.${constants.TABLE_INTEGRATION_ASSET_SHEET}(asset_id)`,
    documentIdIdx = `CREATE INDEX IF NOT EXISTS idx_integration_asset_sheet_document_id ON ${clientId}.${constants.TABLE_INTEGRATION_ASSET_SHEET}(document_id)`;
  return [createTable, assetIdIdx, documentIdIdx];
};

// Store Tribble-specific integration asset settings here.
// E.g. metadata_tags, default document privacy (private vs. public)
export const client_integration_asset_settings = (clientId: string) => {
  const createTable = `
  CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_INTEGRATION_ASSET_SETTING} (
    id BIGSERIAL PRIMARY KEY,
    integration_id BIGINT REFERENCES ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_INTEGRATION_SYSTEM}(id),
    asset_id BIGINT,
    metadata_tags JSONB DEFAULT '{}'::jsonb, -- The metadata tags associated with the asset.
    privacy TEXT, -- The default privacy setting for the asset (e.g., 'private', 'public').
    UNIQUE(integration_id, asset_id)
  );`,
    assetIdIdx = `
    CREATE INDEX IF NOT EXISTS
      idx_integration_asset_sheet_asset_id ON ${clientId}.${constants.TABLE_INTEGRATION_ASSET_SETTING}(integration_id, asset_id)`;

  return [createTable, assetIdIdx];
};

export const client_zendesk_asset = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.${constants.TABLE_ZENDESK_ASSET} (
    id BIGSERIAL PRIMARY KEY,
    zendesk_id BIGINT NOT NULL,     -- The ID of the asset in Zendesk.
    blob_id TEXT,                   -- The ID of the blob in Blob Storage.
    -- Foreign key to the brain "document" for this asset.
    document_id BIGINT REFERENCES ${clientId}.document(id) ON DELETE CASCADE,
    job_id BIGINT REFERENCES ${clientId}.job(id), -- The job that this asset belongs to.
    user_id BIGINT NOT NULL REFERENCES tribble.user(id), -- The ID of the user who owns the asset.
    name TEXT,                      -- The name of the asset.
    status TEXT NOT NULL,           -- The status of the asset (e.g., 'active', 'inactive', 'deleted').
    last_modified TIMESTAMPTZ,      -- The last time the asset was modified.
    created_at TIMESTAMPTZ DEFAULT NOW(), -- The time this record was first created.
    updated_at TIMESTAMPTZ DEFAULT NOW(), -- The time this record was last updated.
    url TEXT,                       -- The web view URL of the asset.
    watch_for_changes BOOLEAN DEFAULT FALSE,      -- Whether to watch for changes in the asset.
    UNIQUE(zendesk_id)              -- Ensure uniqueness of Zendesk IDs.
  );`,
    userIdIdx = `CREATE INDEX idx_zendeskasset_user_id ON ${clientId}.${constants.TABLE_ZENDESK_ASSET} (user_id)`,
    zendeskIdIdx = `CREATE INDEX idx_zendeskasset_zendesk_id ON ${clientId}.${constants.TABLE_ZENDESK_ASSET} (zendesk_id)`,
    documentIdIdx = `CREATE INDEX idx_zendeskasset_document_id ON ${clientId}.${constants.TABLE_ZENDESK_ASSET} (document_id)`;

  return [createTable, userIdIdx, zendeskIdIdx, documentIdIdx];
};

export const client_integration = (clientId: string) => {
  const createTable = `
  CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_CLIENT_INTEGRATION} (
    id SERIAL PRIMARY KEY,
    integration_system_id smallint REFERENCES tribble.integration_system(id),
    auth_details text,
    created_by integer REFERENCES tribble.user(id),
    created_at timestamp,
    CONSTRAINT client_integration_unique_system_id UNIQUE (integration_system_id)
  )`;
  return [createTable];
};

export const client_task_feedback = (clientId: string) => {
  const createTable = `
     CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_TASK_FEEDBACK} (
      id SERIAL PRIMARY KEY,
      score_enum integer, -- e.g. 1 | -1  | 0
      question text,
      answer text,
      suggested_correction text,
      category TEXT,
      source_conversation_detail_id integer REFERENCES ${clientId}.conversation_detail(id) ON DELETE SET NULL,
      source_content_detail_id integer REFERENCES ${clientId}.content_detail(id) ON DELETE SET NULL,

      response int, -- e.g. 1 = "ACCEPTED" | -1 = "REJECTED" | 0 = "PENDING"
      response_at timestamp with time zone,
      response_by_id integer REFERENCES tribble.user(id) ON DELETE SET NULL,

      new_fact_id integer REFERENCES ${clientId}.embedding(id) ON DELETE SET NULL,
      task_id integer REFERENCES ${clientId}.task(id) ON DELETE SET NULL
    );
  `;
  return [createTable];
};

export const authorize_readonly_role = (clientId: string) => {
  const authorizeSchema = `
  GRANT USAGE ON SCHEMA ${clientId} TO ${DB_READONLY_ROLE};
  `;
  const tableAuthorize = `GRANT SELECT ON ALL TABLES IN SCHEMA ${clientId} TO ${DB_READONLY_ROLE}`;
  return [authorizeSchema, tableAuthorize];
};

export const nlp_task = (clientId: string) => {
  const createTable = `
  CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_NLP_TASK} (
    id SERIAL PRIMARY KEY,
    name TEXT UNIQUE,
    enabled BOOLEAN,
    version INTEGER DEFAULT 1,
    description TEXT,
    CONSTRAINT nlp_task_name_version_key UNIQUE (name, version)
  );

  INSERT INTO ${clientId}.${constants.TABLE_NLP_TASK} (name, enabled, description) VALUES
  ('NER', FALSE, 'Identifies named entities in text, such as names of people, organizations, locations, etc.'),
  ('TEXT_SUMMARIZE', FALSE, 'Generates a concise summary of a larger text document.'),
  ('TEXT_CAT_GENERAL', FALSE, 'Assigns categorical labels to text based on content.'),
  ('TEXT_CAT_RFP_QA', FALSE, 'Text categorization task for RFP questions. Uses the rfp_qa category set.'),
  ('TEXT_CAT_RFP_ANSWER_MATCH', FALSE, 'Text categorization task for RFP questions. Uses the rfp_answer_match category set.'),
  ('TEXT_CAT_RFP_ANSWER_STATUS', false, 'Task for classifying RFP questions into RFP_ANSWER_STATUS categories'),
  ('TEXT_CAT_GENERAL_ANSWER_MATCH', FALSE, 'Task for categorizing the bot answers in conversation turns. Uses the general_answer_match category set.'),
  ('TEXT_CAT_RFP_PRIMARY_USE_CASE', false, 'Task for classifying RFP questions into RFP_PRIMARY_USE_CASE categories'),
  ('TEXT_CAT_RFP_PRIMARY_BUYER', false, 'Task for classifying RFP questions into RFP_PRIMARY_BUYER categories'),
  ('TEXT_CAT_RFP_PRODUCT_AREA', false, 'Task for classifying RFP questions into RFP_PRODUCT_AREA categories'),
  ('MAP_CAT_ENT_RFP_ANSWER_MATCH', false, 'Maps entities in a content to the categories of the same content'),
  ('TEXT_CAT_CONVERSATION_ANSWER_STATUS', false, 'Task for classifying conversation turns into CONVERSATION_ANSWER_STATUS categories'),
  ('MAP_CAT_ENT_CONVERSATION_ANSWER_STATUS', false, 'Maps entities in a conversation turn to CONVERSATION_ANSWER_STATUS category of the same conversation turn.');
  `;
  return [createTable];
};

export const nlp_task_status = (clientId: string) => {
  const createTable = `
  CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_NLP_TASK_STATUS} (
    id SERIAL PRIMARY KEY,
    content_detail_id INT,
    conversation_detail_id INT,
    document_id INT,
    content_id INT,
    conversation_id INT,
    embedding_id INT,
    task_id INT,
    status TEXT,
    processed_at TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES ${clientId}.${constants.TABLE_NLP_TASK}(id),
    FOREIGN KEY (content_detail_id) REFERENCES ${clientId}.${constants.TABLE_CONTENT_DETAIL}(id) ON DELETE CASCADE,
    FOREIGN KEY (conversation_detail_id) REFERENCES ${clientId}.${constants.TABLE_CONVERSATION_DETAIL}(id) ON DELETE CASCADE,
    FOREIGN KEY (document_id) REFERENCES ${clientId}.${constants.TABLE_DOC}(id) ON DELETE CASCADE,
    FOREIGN KEY (content_id) REFERENCES ${clientId}.${constants.TABLE_CONTENT}(id) ON DELETE CASCADE,
    FOREIGN KEY (conversation_id) REFERENCES ${clientId}.${constants.TABLE_CONVERSATION}(id) ON DELETE CASCADE,
    FOREIGN KEY (embedding_id) REFERENCES ${clientId}.${constants.TABLE_EMBEDDING}(id) ON DELETE CASCADE,
    CONSTRAINT check_single_source_type CHECK (
        (content_detail_id IS NOT NULL)::INTEGER +
        (conversation_detail_id IS NOT NULL)::INTEGER +
        (document_id IS NOT NULL)::INTEGER +
        (content_id IS NOT NULL)::INTEGER +
        (conversation_id IS NOT NULL)::INTEGER +
        (embedding_id IS NOT NULL)::INTEGER = 1
    )
  )`;

  const indexTaskId = `CREATE INDEX idx_nlp_task_status_task_id ON ${clientId}.${constants.TABLE_NLP_TASK_STATUS}(task_id)`;
  const indexContentDetailId = `CREATE INDEX idx_nlp_task_status_content_detail_id ON ${clientId}.${constants.TABLE_NLP_TASK_STATUS}(content_detail_id)`;
  const indexConversationDetailId = `CREATE INDEX idx_nlp_task_status_conversation_detail_id ON ${clientId}.${constants.TABLE_NLP_TASK_STATUS}(conversation_detail_id)`;
  const indexDocumentId = `CREATE INDEX idx_nlp_task_status_document_id ON ${clientId}.${constants.TABLE_NLP_TASK_STATUS}(document_id)`;
  const indexContentId = `CREATE INDEX idx_nlp_task_status_content_id ON ${clientId}.${constants.TABLE_NLP_TASK_STATUS}(content_id)`;
  const indexConversationId = `CREATE INDEX idx_nlp_task_status_conversation_id ON ${clientId}.${constants.TABLE_NLP_TASK_STATUS}(conversation_id)`;
  const indexEmbeddingId = `CREATE INDEX idx_nlp_task_status_embedding_id ON ${clientId}.${constants.TABLE_NLP_TASK_STATUS}(embedding_id)`;

  return [
    createTable,
    indexTaskId,
    indexContentDetailId,
    indexConversationDetailId,
    indexDocumentId,
    indexContentId,
    indexConversationId,
    indexEmbeddingId,
  ];
};

export const nlp_entity = (clientId: string) => {
  const createTable = `
  CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_NLP_ENTITY} (
    id SERIAL PRIMARY KEY,
    name TEXT,
    type TEXT,
    embedding vector(1536),
    canonical_id INT REFERENCES ${clientId}.${constants.TABLE_NLP_ENTITY}(id) ON DELETE SET NULL,
    UNIQUE(name, type)
  );

  CREATE INDEX IF NOT EXISTS idx_name_trgm ON  ${clientId}.${constants.TABLE_NLP_ENTITY}  USING GIN (name gin_trgm_ops);
  CREATE INDEX IF NOT EXISTS idx_entity_embedding ON ${clientId}.${constants.TABLE_NLP_ENTITY} USING ivfflat (embedding) WITH (lists = 100);
  `;

  return [createTable];
};

export const nlp_entity_source = (clientId: string) => {
  const createTable = `
  CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_NLP_ENTITY_SOURCE} (
    id SERIAL PRIMARY KEY,
    content_detail_id INT,
    conversation_detail_id INT,
    embedding_id INT,
    entity_id INT,
    FOREIGN KEY (entity_id) REFERENCES ${clientId}.${constants.TABLE_NLP_ENTITY}(id),
    FOREIGN KEY (content_detail_id) REFERENCES ${clientId}.${constants.TABLE_CONTENT_DETAIL}(id) ON DELETE CASCADE,
    FOREIGN KEY (conversation_detail_id) REFERENCES ${clientId}.${constants.TABLE_CONVERSATION_DETAIL}(id) ON DELETE CASCADE,
    FOREIGN KEY (embedding_id) REFERENCES ${clientId}.${constants.TABLE_EMBEDDING}(id) ON DELETE CASCADE,
    CONSTRAINT check_single_source_type CHECK (
        (content_detail_id IS NOT NULL)::INTEGER +
        (conversation_detail_id IS NOT NULL)::INTEGER +
        (embedding_id IS NOT NULL)::INTEGER = 1
    )
  )`;

  const createIndices = `CREATE INDEX idx_entity_source_content_detail_id ON ${clientId}.${constants.TABLE_NLP_ENTITY_SOURCE}(content_detail_id);
    CREATE INDEX idx_entity_source_conversation_detail_id ON ${clientId}.${constants.TABLE_NLP_ENTITY_SOURCE}(conversation_detail_id);
    CREATE INDEX idx_entity_source_embedding_id ON ${clientId}.${constants.TABLE_NLP_ENTITY_SOURCE}(embedding_id);
    `;

  return [createTable, createIndices];
};

export const rfx = (clientId: string) => {
  const createTable = `
  CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_RFX} (
    id SERIAL PRIMARY KEY,
    rfx_id TEXT,
    name TEXT,
    description TEXT,
    customer_name TEXT,
    status TEXT, -- this will be deprecated in favor of project_status
    project_status TEXT,
    status_message TEXT,
    due_date DATE,
    created_by BIGINT REFERENCES ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_USER}(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_by INTEGER REFERENCES ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_USER}(id),
    modified_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    opportunity_id TEXT,
    owner_id INTEGER REFERENCES ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_USER}(id),
    is_deleted BOOLEAN DEFAULT FALSE
  )`;

  const addRfxId = `
    ALTER TABLE ${clientId}.content ADD COLUMN IF NOT EXISTS rfx_id integer REFERENCES ${clientId}.rfx(id) ON DELETE SET NULL;
  `;
  return [createTable, addRfxId];
};

export const rfx_content_user_join = (clientId: string) => {
  const createUserJoinTable = `
  CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_RFX_CONTENT_USER_JOIN} (
    id SERIAL PRIMARY KEY,
    rfx_content_id INTEGER REFERENCES ${clientId}.${constants.TABLE_RFX_CONTENT}(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_USER}(id),
    is_owner BOOLEAN DEFAULT FALSE NOT NULL,
    CONSTRAINT rfx_content_user_join_unique_rfx_content_id_user_id UNIQUE (rfx_content_id, user_id)
  )`;

  const createUserJoinIndices = `
  CREATE INDEX IF NOT EXISTS idx_rfx_content_user_join_rfx_content_id ON ${clientId}.${constants.TABLE_RFX_CONTENT_USER_JOIN} (rfx_content_id);
  CREATE INDEX IF NOT EXISTS idx_rfx_content_user_join_user_id ON ${clientId}.${constants.TABLE_RFX_CONTENT_USER_JOIN} (user_id);
  `;

  return [createUserJoinTable, createUserJoinIndices];
};

export const rfx_content = (clientId: string) => {
  const createRfxContentTable = `
  CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_RFX_CONTENT} (
    id SERIAL PRIMARY KEY,
    rfx_id INTEGER REFERENCES ${clientId}.${constants.TABLE_RFX}(id) ON DELETE CASCADE,
    content_id INTEGER REFERENCES ${clientId}.${constants.TABLE_CONTENT}(id) ON DELETE CASCADE,
    status TEXT,
    status_message TEXT,
    is_deleted BOOLEAN DEFAULT FALSE NOT NULL,
    created_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_updated TIMESTAMP WITH TIME ZONE,
    output_type TEXT,
    CONSTRAINT rfx_content_unique_rfx_id_content_id UNIQUE (rfx_id, content_id)
  )`;

  const createIndices = `
  CREATE INDEX IF NOT EXISTS idx_rfx_content_rfx_id ON ${clientId}.${constants.TABLE_RFX_CONTENT} (rfx_id);
  CREATE INDEX IF NOT EXISTS idx_rfx_content_content_id ON ${clientId}.${constants.TABLE_RFX_CONTENT} (content_id);
  `;

  return [createRfxContentTable, createIndices];
};

export const rfx_bid_packet_file_rfx_content = (clientId: string) => {
  const createTable = `
  CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_RFX_BID_PACKET_FILE_RFX_CONTENT} (
    rfx_bid_packet_file_id INTEGER REFERENCES ${clientId}.${constants.TABLE_RFX_BID_PACKET_FILE}(id) ON DELETE CASCADE,
    rfx_content_id INTEGER REFERENCES ${clientId}.${constants.TABLE_RFX_CONTENT}(id) ON DELETE CASCADE,
    PRIMARY KEY (rfx_bid_packet_file_id, rfx_content_id)
  )`;

  const createIndices = `
  CREATE INDEX IF NOT EXISTS idx_rfx_bid_packet_file_rfx_content_file_id
    ON ${clientId}.${constants.TABLE_RFX_BID_PACKET_FILE_RFX_CONTENT} (rfx_bid_packet_file_id);
  CREATE INDEX IF NOT EXISTS idx_rfx_bid_packet_file_rfx_content_content_id
    ON ${clientId}.${constants.TABLE_RFX_BID_PACKET_FILE_RFX_CONTENT} (rfx_content_id);
  `;

  return [createTable, createIndices];
};

export const rfx_criteria = (clientId: string) => {
  const createTable = `
  CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_RFX_CRITERIA} (
    id SERIAL PRIMARY KEY,
    name TEXT,
    description TEXT,
    created_by BIGINT REFERENCES tribble.user(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_by BIGINT REFERENCES tribble.user(id),
    modified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  )`;
  return [createTable];
};

export const rfx_criteria_xref_mdf = (clientId: string) => {
  const createTable = `
  CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_RFX_CRITERIA_XREF_MDF} (
    id SERIAL PRIMARY KEY,
    rfx_criteria_id BIGINT REFERENCES ${clientId}.${constants.TABLE_RFX_CRITERIA}(id) ON DELETE CASCADE,
    mdf_id BIGINT REFERENCES ${clientId}.${constants.TABLE_METADATA_FILTERS}(id) ON DELETE CASCADE,
    CONSTRAINT unique_rfx_criteria_mdf UNIQUE (rfx_criteria_id, mdf_id)
  )`;
  return [createTable];
};

export const rfx_xref_rfx_criteria = (clientId: string) => {
  const createTable = `
  CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_RFX_XREF_RFX_CRITERIA} (
    id SERIAL PRIMARY KEY,
    rfx_id BIGINT REFERENCES ${clientId}.${constants.TABLE_RFX}(id) ON DELETE CASCADE,
    rfx_criteria_id BIGINT REFERENCES ${clientId}.${constants.TABLE_RFX_CRITERIA}(id) ON DELETE CASCADE,
    CONSTRAINT unique_rfx_rfx_criteria UNIQUE (rfx_id, rfx_criteria_id)
  )`;
  return [createTable];
};

export const m365drive_asset = (clientId: string) => {
  const createTable = `
  CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_M365_DRIVE_ASSET} (
    id BIGSERIAL PRIMARY KEY,
    -- ID INFO
    item_id TEXT NOT NULL,                        -- The ID of the file/folder in M365 Drive.
    drive_id TEXT NOT NULL,         -- The ID of the Drive in which the item is located.
    -- ASSET INFO
    name TEXT,                      -- The name of the file/folder.
    mime_type TEXT,                 -- The MIME type to differentiate between files and folders.
    last_modified TIMESTAMPTZ,       -- The last time the asset was modified.
    url TEXT,                       -- The web view URL of the file/folder.
    file jsonb,                      -- The file metadata.
    parent_reference jsonb,          -- The parent folder metadata.
    folder jsonb,                   -- The folder metadata
    -- TRIBBLE INFO
    blob_id TEXT,                   -- The ID of the blob in Blob Storage.
    document_id BIGINT REFERENCES  ${clientId}.document(id) ON DELETE CASCADE,
    type TEXT,                      -- The type of the asset (document_type)
    job_id BIGINT REFERENCES  ${clientId}.job(id), -- The job that this asset belongs to.
    user_id BIGINT NOT NULL REFERENCES tribble.user(id), -- The ID of the user who owns the asset.
    watch_for_changes BOOLEAN,      -- Whether to watch for changes in the asset.
    status TEXT NOT NULL,           -- The status of the asset (e.g., 'active', 'inactive', 'deleted').
    last_checked TIMESTAMPTZ,       -- The last time the asset was checked for changes
    created_at TIMESTAMPTZ DEFAULT NOW(), -- The time this record was first created.
    updated_at TIMESTAMPTZ DEFAULT NOW(), -- The time this record was last updated.
    task_id BIGINT REFERENCES  ${clientId}.task(id) ON DELETE SET NULL, -- The task that this asset belongs to.
    UNIQUE(item_id)
)
    `;

  const userIdx = `CREATE INDEX IF NOT EXISTS idx_m365asset_user_id ON  ${clientId}.${constants.TABLE_M365_DRIVE_ASSET} (user_id);`;
  const itemIdx = `CREATE INDEX IF NOT EXISTS idx_m365asset_item_id ON  ${clientId}.${constants.TABLE_M365_DRIVE_ASSET} (item_id);`;
  const driveIdx = `CREATE INDEX IF NOT EXISTS idx_m365asset_drive_id ON  ${clientId}.${constants.TABLE_M365_DRIVE_ASSET} (drive_id);`;
  const docIdx = `CREATE INDEX IF NOT EXISTS idx_m365asset_document_id ON  ${clientId}.${constants.TABLE_M365_DRIVE_ASSET} (document_id);`;

  return [createTable, userIdx, itemIdx, driveIdx, docIdx];
};

export const box_asset = (clientId: string) => {
  const createTable = `
  CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_BOX_ASSET} (
    id BIGSERIAL PRIMARY KEY,
    file_id TEXT,
    folder_id TEXT NOT NULL,
    name TEXT,
    extension TEXT,
    last_modified TIMESTAMPTZ,
    url TEXT,
    blob_id TEXT,
    document_id BIGINT REFERENCES ${clientId}.document(id) ON DELETE CASCADE,
    type TEXT NOT NULL,
    job_id BIGINT REFERENCES ${clientId}.job(id),
    user_id BIGINT NOT NULL REFERENCES tribble.user(id),
    watch_for_changes BOOLEAN NOT NULL,
    status TEXT NOT NULL,
    last_checked TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    task_id BIGINT REFERENCES ${clientId}.task(id) ON DELETE SET NULL,
    UNIQUE(file_id, folder_id)
  )`;

  const userIdx = `CREATE INDEX IF NOT EXISTS idx_boxasset_user_id ON  ${clientId}.${constants.TABLE_BOX_ASSET} (user_id);`;
  const fileIdx = `CREATE INDEX IF NOT EXISTS idx_boxasset_file_id ON  ${clientId}.${constants.TABLE_BOX_ASSET} (file_id);`;
  const folderIdx = `CREATE INDEX IF NOT EXISTS idx_boxasset_folder_id ON  ${clientId}.${constants.TABLE_BOX_ASSET} (folder_id);`;
  const docIdx = `CREATE INDEX IF NOT EXISTS idx_boxasset_document_id ON  ${clientId}.${constants.TABLE_BOX_ASSET} (document_id);`;

  return [createTable, userIdx, fileIdx, folderIdx, docIdx];
};

export const nlp_category_set = (clientId: string) => {
  const createTable = `
  CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_NLP_CATEGORY_SET} (
    id SERIAL PRIMARY KEY,
    name TEXT UNIQUE,
    user_friendly_name TEXT,
    description TEXT,
    max_categories_per_content INT NOT NULL DEFAULT 1,
    is_user_defined BOOLEAN DEFAULT FALSE,
    version INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT nlp_category_set_name_version_key UNIQUE (name, version)
  )`;

  const insertCategorySets = `
  INSERT INTO ${clientId}.${constants.TABLE_NLP_CATEGORY_SET} (name, description, max_categories_per_content) VALUES
  ('RFP_QA', 'RFP Question Category Set', 5),
  ('RFP_ANSWER_MATCH', 'RFP Answer Category Set', 1),
  ('RFP_ANSWER_STATUS', 'Categories designed to categorize RFP QA pairs based on the answer''s completeness.', 1),
  ('GENERAL', 'Categories designed to classify documents across key areas of business operations, covering strategic, administrative, technical, and regulatory aspects', 3),
  ('GENERAL_ANSWER_MATCH', 'Categories to classify qa pairs based on answer matches', 1),
  ('CONVERSATION_ANSWER_STATUS', 'Categories designed to categorize conversation turns pairs based on the answer''s completeness.', 1);
  `;

  const insertCustomCategorySets = `
  INSERT INTO ${clientId}.${constants.TABLE_NLP_CATEGORY_SET}
  (name, description, user_friendly_name, max_categories_per_content, is_user_defined) VALUES
  ('RFP_PRODUCT_AREA', 'This category set classifies RFPs based on the product area.', 'Product Area', 3, true),
  ('RFP_PRIMARY_USE_CASE', 'This category set classifies RFPs based on the use case.', 'Primary Use Case',  1, true),
  ('RFP_PRIMARY_BUYER', 'This category set classifies RFPs based on the type of buyer.', 'Primary Buyer', 1, true);
  `;

  return [createTable, insertCategorySets, insertCustomCategorySets];
};

export const nlp_category = (clientId: string) => {
  const createTable = `
  CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_NLP_CATEGORY} (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    category_set_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_set_id) REFERENCES ${clientId}.${constants.TABLE_NLP_CATEGORY_SET}(id) ON DELETE CASCADE,
    UNIQUE (name, category_set_id)
  )`;

  const insertCategories = `
  DO $$
  DECLARE
      rfp_qa_set_id INT;
  BEGIN
      SELECT id INTO rfp_qa_set_id FROM ${clientId}.${constants.TABLE_NLP_CATEGORY_SET} WHERE name = 'RFP_QA';

      INSERT INTO ${clientId}.${constants.TABLE_NLP_CATEGORY} (name, category_set_id, description) VALUES
      ('Compliance and Regulatory', rfp_qa_set_id, 'Concerns related to compliance with laws, regulations, and standards.'),
      ('Company Information', rfp_qa_set_id, 'Background and experience of the company, including history and key achievements.'),
      ('Customization and Flexibility', rfp_qa_set_id, 'Ability to customize the product or service to meet specific needs.'),
      ('Customer Success', rfp_qa_set_id, 'Examples of customer success stories and case studies.'),
      ('Implementation and Integration', rfp_qa_set_id, 'Details on implementation and integration with existing systems.'),
      ('Pricing and Licensing', rfp_qa_set_id, 'Information on pricing models and licensing terms.'),
      ('Product Features', rfp_qa_set_id, 'Specific features and functionalities of the product.'),
      ('Security and Data Protection', rfp_qa_set_id, 'Measures to ensure data security and privacy.'),
      ('Support and Maintenance', rfp_qa_set_id, 'Support and maintenance services offered post-purchase.'),
      ('Technical Requirements', rfp_qa_set_id, 'Technical specifications and requirements necessary for operation.'),
      ('Performance and Scalability', rfp_qa_set_id, 'Performance metrics and ability to scale the solution.'),
      ('Not Applicable', rfp_qa_set_id, 'If none of the other categories apply, this category is used. When the question or answer contains irrelevant, corrupted, gibberish, or empty text, or when it does not align with any of the specified categories.');
  END $$;

  DO $$
  DECLARE
      rfp_answer_match_set_id INT;
  BEGIN
      SELECT id INTO rfp_answer_match_set_id FROM ${clientId}.${constants.TABLE_NLP_CATEGORY_SET} WHERE name = 'RFP_ANSWER_MATCH';

      INSERT INTO  ${clientId}.${constants.TABLE_NLP_CATEGORY} (name, category_set_id, description) VALUES
      ('Strong Match', rfp_answer_match_set_id, 'Indicates a strong alignment with the RFP requirements.'),
      ('Partial Match', rfp_answer_match_set_id, 'Indicates a partial match with the RFP requirements, with some gaps.'),
      ('No Match', rfp_answer_match_set_id, 'Indicates a clear response that shows no alignment with the RFP requirements or does not support the requested feature.'),
      ('No Answer', rfp_answer_match_set_id, 'Question could not be answered or the answer lacks sufficient information to determine whether the requirement is supported (fully, partially, or not at all).'),
      ('Non-Disclosure', rfp_answer_match_set_id, 'The information is known but cannot be shared due to confidentiality or policy restrictions.');
  END $$;

  DO $$
  DECLARE
    general_set_id INT;
  BEGIN
    SELECT id INTO general_set_id FROM ${clientId}.${constants.TABLE_NLP_CATEGORY_SET} WHERE name = 'GENERAL';

    INSERT INTO ${clientId}.${constants.TABLE_NLP_CATEGORY} (name, category_set_id, description) VALUES
    ('Strategy and Planning', general_set_id, 'Documents related to long-term organizational goals, decision-making processes, and strategic initiatives'),
    ('Customer Support', general_set_id, 'Materials focusing on providing assistance, guidance, and services to customers'),
    ('Finance', general_set_id, 'Documents concerning budgeting, financial planning, accounting, and investment activities'),
    ('Human Resources', general_set_id, 'Resources related to employee management, recruitment, policies, and training'),
    ('Information Technology', general_set_id, 'Content covering IT infrastructure, software development, and data management'),
    ('Legal', general_set_id, 'Documents involving legal policies, contracts, compliance, and regulatory matters'),
    ('Operations', general_set_id, 'Materials focusing on day-to-day business activities, production, and logistics'),
    ('Research and Development', general_set_id, 'Papers on innovation, product development, and experimentation'),
    ('Sales and Marketing', general_set_id, 'Content related to marketing strategies, sales tactics, and customer outreach'),
    ('Corporate Governance', general_set_id, 'Documents covering organizational structure, management practices, and internal policies'),
    ('Document Management', general_set_id, 'Procedures and systems for storing, organizing, and retrieving documents'),
    ('External Relations', general_set_id, 'Communications and interactions with external entities like partners, media, and the public'),
    ('Health and Safety', general_set_id, 'Guidelines and policies for ensuring the well-being and safety of employees'),
    ('Not Applicable', general_set_id, 'If none of the other categories apply, this category is used. When the question or answer contains irrelevant, corrupted, gibberish, or empty text, or when it does not align with any of the specified categories.');
  END $$;

  DO $$
  DECLARE
    general_answer_match_set_id INT;
  BEGIN
    SELECT id INTO general_answer_match_set_id FROM ${clientId}.${constants.TABLE_NLP_CATEGORY_SET} WHERE name = 'GENERAL_ANSWER_MATCH';

    INSERT INTO ${clientId}.${constants.TABLE_NLP_CATEGORY} (name, category_set_id, description) VALUES
    ('EXACT_MATCH', general_answer_match_set_id, 'Bot provided a precise and relevant answer to the question.'),
    ('PARTIAL_MATCH', general_answer_match_set_id, 'Bot provided a somewhat relevant answer, but it didn''t fully address the question.'),
    ('NO_MATCH', general_answer_match_set_id, 'Bot was unable to provide an answer related to the question.'),
    ('NON_QUESTION_TURN', general_answer_match_set_id, 'The user input was not a question but rather a conversational or non-inquisitive statement.');

  END $$;

  DO $$
  DECLARE
      rfp_answer_status_id INT;
  BEGIN
      SELECT id INTO rfp_answer_status_id FROM ${clientId}.${constants.TABLE_NLP_CATEGORY_SET} WHERE name = 'RFP_ANSWER_STATUS';

      INSERT INTO ${clientId}.${constants.TABLE_NLP_CATEGORY} (name, category_set_id, description) VALUES
      ('Can Answer', rfp_answer_status_id, 'A clear answer is provided, indicating whether the requirement is supported (fully, partially, or not at all).'),
      ('Cannot Answer', rfp_answer_status_id, 'The system cannot provide an answer due to insufficient knowledge, complexity, or ambiguity.'),
      ('Partial Answer', rfp_answer_status_id, 'A partial answer is provided, but more information or further clarification is needed.'),
      ('Non-Disclosure', rfp_answer_status_id, 'The information is known but cannot be shared due to confidentiality or policy restrictions.');

  END $$;

  DO $$
  DECLARE
      rfp_product_area_set_id INT;
      rfp_primary_buyer_set_id INT;
      rfp_primary_use_case_set_id INT;
  BEGIN
      SELECT id INTO rfp_product_area_set_id FROM ${clientId}.${constants.TABLE_NLP_CATEGORY_SET} WHERE name = 'RFP_PRODUCT_AREA';
      SELECT id INTO rfp_primary_buyer_set_id FROM ${clientId}.${constants.TABLE_NLP_CATEGORY_SET} WHERE name = 'RFP_PRIMARY_BUYER';
      SELECT id INTO rfp_primary_use_case_set_id FROM ${clientId}.${constants.TABLE_NLP_CATEGORY_SET} WHERE name = 'RFP_PRIMARY_USE_CASE';

      INSERT INTO ${clientId}.${constants.TABLE_NLP_CATEGORY} (name, category_set_id, description) VALUES
      ('Not Applicable', rfp_product_area_set_id, 'If none of the other categories apply, this category is used. When the question or answer contains irrelevant, corrupted, gibberish, or empty text, or when it does not align with any of the specified categories.');

      INSERT INTO ${clientId}.${constants.TABLE_NLP_CATEGORY} (name, category_set_id, description) VALUES
      ('Not Applicable', rfp_primary_buyer_set_id, 'If none of the other categories apply, this category is used. When the question or answer contains irrelevant, corrupted, gibberish, or empty text, or when it does not align with any of the specified categories.');

      INSERT INTO ${clientId}.${constants.TABLE_NLP_CATEGORY} (name, category_set_id, description) VALUES
      ('Not Applicable', rfp_primary_use_case_set_id, 'If none of the other categories apply, this category is used. When the question or answer contains irrelevant, corrupted, gibberish, or empty text, or when it does not align with any of the specified categories.');

  END $$;

  DO $$
  DECLARE
      conversation_answer_status_id INT;
  BEGIN
      SELECT id INTO conversation_answer_status_id FROM ${clientId}.${constants.TABLE_NLP_CATEGORY_SET} WHERE name = 'CONVERSATION_ANSWER_STATUS';

      INSERT INTO ${clientId}.${constants.TABLE_NLP_CATEGORY} (name, category_set_id, description) VALUES
      ('Can Answer', conversation_answer_status_id, 'A clear answer is provided, indicating whether the requirement is supported (fully, partially, or not at all).'),
      ('Cannot Answer', conversation_answer_status_id, 'The system cannot provide an answer due to insufficient knowledge, complexity, or ambiguity.'),
      ('Partial Answer', conversation_answer_status_id, 'A partial answer is provided, but more information or further clarification is needed.'),
      ('Non-Disclosure', conversation_answer_status_id, 'The information is known but cannot be shared due to confidentiality or policy restrictions.'),
      ('Not Applicable', conversation_answer_status_id, 'If none of the other categories apply, this category is used. When the question or answer contains irrelevant, corrupted, gibberish, or empty text, or when it does not align with any of the specified categories.');
  END $$;

  `;
  return [createTable, insertCategories];
};

export const nlp_category_source = (clientId: string) => {
  const createTable = `CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_NLP_CATEGORY_SOURCE} (
    id SERIAL PRIMARY KEY,
    category_id INT,
    content_detail_id INT,
    conversation_detail_id INT,
    document_id INT,
    content_id INT,
    conversation_id INT,
    embedding_id INT,
    justification TEXT,
    FOREIGN KEY (category_id) REFERENCES ${clientId}.${constants.TABLE_NLP_CATEGORY}(id) ON DELETE CASCADE,
    FOREIGN KEY (content_detail_id) REFERENCES ${clientId}.${constants.TABLE_CONTENT_DETAIL}(id) ON DELETE CASCADE,
    FOREIGN KEY (conversation_detail_id) REFERENCES ${clientId}.${constants.TABLE_CONVERSATION_DETAIL}(id) ON DELETE CASCADE,
    FOREIGN KEY (document_id) REFERENCES ${clientId}.${constants.TABLE_DOC}(id) ON DELETE CASCADE,
    FOREIGN KEY (content_id) REFERENCES ${clientId}.${constants.TABLE_CONTENT}(id) ON DELETE CASCADE,
    FOREIGN KEY (conversation_id) REFERENCES ${clientId}.${constants.TABLE_CONVERSATION}(id) ON DELETE CASCADE,
    FOREIGN KEY (embedding_id) REFERENCES ${clientId}.${constants.TABLE_EMBEDDING}(id) ON DELETE CASCADE,
    CONSTRAINT check_single_source_type CHECK (
        (content_detail_id IS NOT NULL)::INTEGER +
        (conversation_detail_id IS NOT NULL)::INTEGER +
        (document_id IS NOT NULL)::INTEGER +
        (content_id IS NOT NULL)::INTEGER +
        (conversation_id IS NOT NULL)::INTEGER +
        (embedding_id IS NOT NULL)::INTEGER = 1
    )
  )`;

  const createIndices = `CREATE INDEX idx_category_id ON ${clientId}.${constants.TABLE_NLP_CATEGORY_SOURCE}(category_id);
    CREATE INDEX idx_content_detail_id ON ${clientId}.${constants.TABLE_NLP_CATEGORY_SOURCE}(content_detail_id);
    CREATE INDEX idx_conversation_detail_id ON ${clientId}.${constants.TABLE_NLP_CATEGORY_SOURCE}(conversation_detail_id);
    CREATE INDEX idx_document_id ON ${clientId}.${constants.TABLE_NLP_CATEGORY_SOURCE}(document_id);
    CREATE INDEX idx_content_id ON ${clientId}.${constants.TABLE_NLP_CATEGORY_SOURCE}(content_id);
    CREATE INDEX idx_conversation_id ON ${clientId}.${constants.TABLE_NLP_CATEGORY_SOURCE}(conversation_id);
    CREATE INDEX idx_embedding_id ON ${clientId}.${constants.TABLE_NLP_CATEGORY_SOURCE}(embedding_id);
    `;

  return [createTable, createIndices];
};

export const nlp_summary = (clientId: string) => {
  const createTable = `CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_NLP_SUMMARY} (
    id SERIAL PRIMARY KEY,
    document_id INT NOT NULL,  -- Foreign key to document table (document id)
    summary TEXT NOT NULL,  -- The summarized text
    summary_embedding vector(1536),  -- The embedding of the summary
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- Timestamp when the summary was created
    original_length INT NOT NULL,  -- The original length of the document/content being summarized
    max_length INT NOT NULL,  -- The maximum length constraint for the summary
    model TEXT NOT NULL,  -- The model used for summarization
    FOREIGN KEY (document_id) REFERENCES ${clientId}.${constants.TABLE_DOC}(id) ON DELETE CASCADE  -- Foreign key to the document table
  )`;

  const createIndex = `CREATE INDEX IF NOT EXISTS idx_summary_embedding ON ${clientId}.${constants.TABLE_NLP_SUMMARY} USING ivfflat (summary_embedding) WITH (lists = 100);`;

  return [createTable, createIndex];
};

export const highspot_assets = (clientId: string) => {
  const createTable = `CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_HIGHSPOT_ASSET} (
    id BIGSERIAL PRIMARY KEY,
    -- ID INFO
    item_id TEXT NOT NULL,             -- The ID of the file/folder in Highspot.
    spot_id TEXT,                      -- The ID of the spot in which the item is located.
    -- ASSET INFO
    name TEXT,                      -- The title of the file/folder.
    content_name TEXT,              -- The name of the actual file
    mime_type TEXT,                 -- The MIME type to differentiate between files and folders.
    last_modified TIMESTAMPTZ,       -- The last time the asset was modified.
    -- TRIBBLE INFO
    blob_id TEXT,                   -- The ID of the blob in Blob Storage.
    document_id BIGINT REFERENCES ${clientId}.document(id) ON DELETE CASCADE,
    type TEXT,                      -- The type of the asset (document_type)
    job_id BIGINT REFERENCES ${clientId}.job(id), -- The job that this asset belongs to.
    user_id BIGINT NOT NULL REFERENCES tribble.user(id), -- The ID of the user who owns the asset.
    watch_for_changes BOOLEAN,      -- Whether to watch for changes in the asset.
    status TEXT NOT NULL,           -- The status of the asset (e.g., 'active', 'inactive', 'deleted').
    last_checked TIMESTAMPTZ,       -- The last time the asset was checked for changes
    created_at TIMESTAMPTZ DEFAULT NOW(), -- The time this record was first created.
    updated_at TIMESTAMPTZ DEFAULT NOW(), -- The time this record was last updated.
    task_id BIGINT REFERENCES ${clientId}.task(id) ON DELETE SET NULL, -- The task that this asset belongs to.
    UNIQUE(item_id)
);`,
    userIdIdx = `CREATE INDEX IF NOT EXISTS idx_highspotasset_user_id ON ${clientId}.${constants.TABLE_HIGHSPOT_ASSET} (user_id)`,
    itemIdIdx = `CREATE INDEX IF NOT EXISTS idx_highspotasset_item_id ON ${clientId}.${constants.TABLE_HIGHSPOT_ASSET} (item_id)`,
    spotIdIdx = `CREATE INDEX IF NOT EXISTS idx_highspotasset_spot_id ON ${clientId}.${constants.TABLE_HIGHSPOT_ASSET} (spot_id)`,
    documentIdIdx = `CREATE INDEX IF NOT EXISTS idx_highspotasset_document_id  ON ${clientId}.${constants.TABLE_HIGHSPOT_ASSET} (document_id)`;

  return [createTable, userIdIdx, itemIdIdx, spotIdIdx, documentIdIdx];
};

// Used for external sessions (e.g. Salesforce chat)
// There will be a "pre-flight" exchange, which will accept a user email
// (or some identifier) and exchange for a UUID. This UUID can be used
// as a proxy to a user email.
export const client_user_external_session = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.${constants.TABLE_USER_EXTERNAL_SESSION}
  (
    id SERIAL PRIMARY KEY,
    user_id integer REFERENCES ${constants.SCHEMA_TRIBBLE}.user(id),
    external_system_name TEXT,
    session_id TEXT, -- create a UUID
    created_at timestamp -- sessions expire after 24 hours
  );`;

  const userIdIdx = `CREATE INDEX IF NOT EXISTS idx_user_external_session_user_id ON ${clientId}.${constants.TABLE_USER_EXTERNAL_SESSION} (user_id)`;
  const sessionIdIdx = `CREATE INDEX IF NOT EXISTS idx_user_external_session_session_id ON ${clientId}.${constants.TABLE_USER_EXTERNAL_SESSION} (session_id)`;

  return [createTable, userIdIdx, sessionIdIdx];
};

export const salesforce_tribblytics_map = (clientId: string) => {
  const createTable = `CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_SALESFORCE_TRIBBLYTICS_MAP} (
    tribblytics_field TEXT PRIMARY KEY,
    salesforce_field TEXT NOT NULL,
    salesforce_object_name TEXT NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE
  )`;

  return [createTable];
};

export const salesforce_tribblytics = (clientId: string) => {
  const createTable = `CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_SALESFORCE_TRIBBLYTICS} (
    id SERIAL PRIMARY KEY,
    account_id TEXT NOT NULL,
    account_name TEXT NOT NULL,
    account_industry TEXT NOT NULL,
    account_region TEXT NOT NULL,
    account_country TEXT NOT NULL,
    account_system_modstamp TIMESTAMP NOT NULL,
    opportunity_id TEXT NOT NULL UNIQUE,
    opportunity_name TEXT NOT NULL,
    opportunity_amount TEXT NOT NULL,
    opportunity_stage TEXT NOT NULL,
    opportunity_type TEXT NOT NULL,
    opportunity_system_modstamp TIMESTAMP NOT NULL,
    line_items JSONB DEFAULT '{}'::jsonb
  )`;

  return [createTable];
};

export const salesforce_content = (clientId: string) => {
  const createTable = `CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_SALESFORCE_CONTENT} (
    id SERIAL PRIMARY KEY,
    salesforce_id TEXT NOT NULL UNIQUE,
    title TEXT NOT NULL,
    type TEXT NOT NULL,
    ingested_at TIMESTAMP,
    deleted_date TIMESTAMP,
    parent_salesforce_id TEXT
  )`;

  return [createTable];
};

export const nlp_entity_dedupe_result = (clientId: string) => {
  const createTable = `CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_NLP_ENTITY_DEDUPE_RESULT} (
    id SERIAL PRIMARY KEY,
    entity_id INTEGER NOT NULL,
    canonical_entity_id INTEGER NOT NULL,
    response JSONB NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (entity_id) REFERENCES ${clientId}.${constants.TABLE_NLP_ENTITY}(id),
    FOREIGN KEY (canonical_entity_id) REFERENCES ${clientId}.${constants.TABLE_NLP_ENTITY}(id)
  )`;

  const createIndex = `CREATE INDEX IF NOT EXISTS idx_nlp_entity_dedupe_result_entity_id
    ON ${clientId}.${constants.TABLE_NLP_ENTITY_DEDUPE_RESULT}(entity_id)`;

  return [createTable, createIndex];
};

export const client_profile = (clientId: string) => {
  const createProfileTable = `
    CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_PROFILE} (
      id SERIAL PRIMARY KEY,
      name TEXT NOT NULL UNIQUE,
      description TEXT
    )`;

  const createUserProfileTable = `
    CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_USER_PROFILE} (
      id SERIAL PRIMARY KEY,
      user_id INT UNIQUE REFERENCES ${constants.SCHEMA_TRIBBLE}.user(id) ON DELETE CASCADE,
      profile_id INT REFERENCES ${clientId}.${constants.TABLE_PROFILE}(id) ON DELETE CASCADE
    )`;

  const createProfileSettingTable = `
    CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_PROFILE_SETTING} (
      id SERIAL PRIMARY KEY,
      profile_id INT REFERENCES ${clientId}.${constants.TABLE_PROFILE}(id) ON DELETE CASCADE,
      setting_id INT REFERENCES ${constants.SCHEMA_TRIBBLE}.setting(id) ON DELETE CASCADE,
      value_string text,
      value_number numeric,
      value_boolean boolean,
      UNIQUE(profile_id, setting_id)
    )`;

  return [
    createProfileTable,
    createUserProfileTable,
    createProfileSettingTable,
  ];
};

export const client_block_document = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.${constants.TABLE_BLOCK_DOCUMENT} (
    id BIGSERIAL PRIMARY KEY,
    name text NOT NULL,
    created_at timestamp WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by integer NOT NULL REFERENCES ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_USER}(id),
    content_id integer REFERENCES ${clientId}.${constants.TABLE_CONTENT}(id),
    current_version int DEFAULT 1
  )`;

  const createIndex = `
  CREATE INDEX idx_block_document_created_by
  ON ${clientId}.${constants.TABLE_BLOCK_DOCUMENT} USING btree (created_by)`;

  return [createTable, createIndex];
};

export const client_block_section = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.${constants.TABLE_BLOCK_SECTION} (
    id BIGSERIAL PRIMARY KEY,
    block_document_id int REFERENCES ${clientId}.${constants.TABLE_BLOCK_DOCUMENT} (id) ON DELETE SET NULL,
    section_heading text NOT NULL,
    section_index int NOT NULL,
    created_at timestamp WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
  )`;

  return [createTable];
};

export const client_block_item = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.${constants.TABLE_BLOCK_ITEM} (
    id BIGSERIAL PRIMARY KEY,
    block_document_id int REFERENCES ${clientId}.${constants.TABLE_BLOCK_DOCUMENT} (id) ON DELETE SET NULL,
    content jsonb NOT NULL,
    position int NOT NULL,
    created_at timestamp WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    section_id int REFERENCES ${clientId}.${constants.TABLE_BLOCK_SECTION} (id) ON DELETE SET NULL,
    created_by integer NOT NULL REFERENCES ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_USER}(id) ON DELETE SET NULL,
    node_id text NOT NULL UNIQUE
  )`;

  const createIndex1 = `
  CREATE INDEX idx_block_item_doc_id
  ON ${clientId}.${constants.TABLE_BLOCK_ITEM} USING btree (block_document_id)`;

  const createIndex2 = `
  CREATE INDEX idx_block_item_section_id
  ON ${clientId}.${constants.TABLE_BLOCK_ITEM} USING btree (section_id)`;

  const createIndex3 = `
  CREATE INDEX idx_block_item_created_by
  ON ${clientId}.${constants.TABLE_BLOCK_ITEM} USING btree (created_by)`;

  return [createTable, createIndex1, createIndex2, createIndex3];
};

export const client_block_document_version = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.${constants.TABLE_BLOCK_DOCUMENT_VERSION} (
    id BIGSERIAL PRIMARY KEY,
    block_document_id int REFERENCES ${clientId}.${constants.TABLE_BLOCK_DOCUMENT} (id) ON DELETE SET NULL,
    version int NOT NULL,
    name text NOT NULL,
    created_at timestamp WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by integer NOT NULL REFERENCES ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_USER}(id) ON DELETE SET NULL,
    UNIQUE (block_document_id, version)
  )`;

  const createIndex = `
  CREATE INDEX idx_block_document_version_created_by
  ON ${clientId}.${constants.TABLE_BLOCK_DOCUMENT_VERSION} USING btree (created_by)`;

  return [createTable, createIndex];
};

export const client_block_item_version = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.${constants.TABLE_BLOCK_ITEM_VERSION} (
    id BIGSERIAL PRIMARY KEY,
    item_id int REFERENCES ${clientId}.${constants.TABLE_BLOCK_ITEM} (id) ON DELETE SET NULL,
    document_version_id int NOT NULL REFERENCES ${clientId}.${constants.TABLE_BLOCK_DOCUMENT_VERSION} (id) ON DELETE CASCADE,
    content jsonb NOT NULL,
    position int NOT NULL,
    section_id int REFERENCES ${clientId}.${constants.TABLE_BLOCK_SECTION} (id) ON DELETE SET NULL,
    created_at timestamp WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by integer NOT NULL REFERENCES ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_USER}(id) ON DELETE SET NULL,
    node_id text
  )`;

  const createIndex1 = `
  CREATE INDEX idx_block_item_version_doc_version
  ON ${clientId}.${constants.TABLE_BLOCK_ITEM_VERSION} USING btree (document_version_id)`;

  const createIndex2 = `
  CREATE INDEX idx_block_item_version_created_by
  ON ${clientId}.${constants.TABLE_BLOCK_ITEM_VERSION} USING btree (created_by)`;

  return [createTable, createIndex1, createIndex2];
};

export const client_block_doc_task_item = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.${constants.TABLE_BLOCK_DOC_TASK_ITEM} (
    id BIGSERIAL PRIMARY KEY,
    task_id int NOT NULL REFERENCES ${clientId}.${constants.TABLE_TASK}(id) ON DELETE CASCADE,
    block_item_id int NOT NULL REFERENCES ${clientId}.${constants.TABLE_BLOCK_ITEM}(id) ON DELETE CASCADE,
    status text DEFAULT 'needs_review',
    completed_by INTEGER REFERENCES ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_USER}(id) ON DELETE SET NULL,
    completed_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(task_id, block_item_id)
  )`;

  const createIndex1 = `
  CREATE INDEX idx_block_doc_task_item_task ON ${clientId}.${constants.TABLE_BLOCK_DOC_TASK_ITEM}
  USING btree (task_id)`;

  const createIndex2 = `
  CREATE INDEX idx_block_doc_task_item_block ON ${clientId}.${constants.TABLE_BLOCK_DOC_TASK_ITEM}
  USING btree (block_item_id)`;

  const createIndex3 = `
  CREATE INDEX idx_block_doc_task_item_status ON ${clientId}.${constants.TABLE_BLOCK_DOC_TASK_ITEM}
  USING btree (status)`;

  const createIndex4 = `
  CREATE INDEX idx_block_doc_task_item_completed_by ON ${clientId}.${constants.TABLE_BLOCK_DOC_TASK_ITEM}
  USING btree (completed_by)`;

  return [createTable, createIndex1, createIndex2, createIndex3, createIndex4];
};

export const client_block_document_lock = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.${constants.TABLE_BLOCK_DOCUMENT_LOCK} (
    id BIGSERIAL PRIMARY KEY,
    block_document_id INTEGER NOT NULL REFERENCES ${clientId}.${constants.TABLE_BLOCK_DOCUMENT}(id) ON DELETE CASCADE,
    locked_by INTEGER NOT NULL REFERENCES ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_USER}(id),
    locked_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    expires_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    tab_id varchar(64) NOT NULL
  )`;

  const createIndex1 = `
  CREATE INDEX idx_block_document_lock_document_id ON ${clientId}.${constants.TABLE_BLOCK_DOCUMENT_LOCK}(block_document_id)`;

  const createIndex2 = `
  CREATE INDEX idx_block_document_lock_expires_at ON ${clientId}.${constants.TABLE_BLOCK_DOCUMENT_LOCK}(expires_at)`;

  const createIndex3 = `
  CREATE INDEX idx_block_document_lock_document_expires ON ${clientId}.${constants.TABLE_BLOCK_DOCUMENT_LOCK}(block_document_id, expires_at)`;

  const createIndex4 = `
  CREATE UNIQUE INDEX idx_block_document_lock_unique_active ON ${clientId}.${constants.TABLE_BLOCK_DOCUMENT_LOCK}(block_document_id, expires_at)`;

  const createIndex5 = `
  CREATE INDEX idx_block_document_lock_user_tab ON ${clientId}.${constants.TABLE_BLOCK_DOCUMENT_LOCK}(locked_by, tab_id)`;

  const createIndex6 = `
  CREATE INDEX idx_block_document_lock_document_tab ON ${clientId}.${constants.TABLE_BLOCK_DOCUMENT_LOCK}(block_document_id, tab_id)`;

  return [
    createTable,
    createIndex1,
    createIndex2,
    createIndex3,
    createIndex4,
    createIndex5,
    createIndex6,
  ];
};

export const e2e_worksheet = (clientId: string) => {
  const workbook = `CREATE TABLE IF NOT EXISTS ${clientId}.e2e_workbook (
    id SERIAL PRIMARY KEY,
    title TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    content_id INTEGER REFERENCES ${clientId}.content(id) ON DELETE CASCADE
  )`;

  const worksheet = `CREATE TABLE IF NOT EXISTS ${clientId}.e2e_sheet (
    id SERIAL PRIMARY KEY,
    e2e_workbook_id INTEGER REFERENCES ${clientId}.e2e_workbook(id) ON DELETE CASCADE,
    title TEXT,
    title_range TEXT,
    instructions_range TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_disabled BOOLEAN,
    metadata_filter jsonb DEFAULT '{}'::jsonb,
    seq INTEGER
  )`;

  const section = `CREATE TABLE IF NOT EXISTS ${clientId}.e2e_section (
    id SERIAL PRIMARY KEY,
    e2e_sheet_id INTEGER REFERENCES ${clientId}.e2e_sheet(id) ON DELETE CASCADE,
    title TEXT,
    title_range TEXT,
    instructions TEXT,
    instructions_range TEXT,
    header_rows_range TEXT,
    seq INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
  )`;

  const block = `CREATE TABLE IF NOT EXISTS ${clientId}.e2e_block (
    id SERIAL PRIMARY KEY,
    e2e_section_id INTEGER REFERENCES ${clientId}.e2e_section(id) ON DELETE CASCADE,
    title TEXT,
    title_range TEXT,
    seq INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
  )`;

  const question = `CREATE TABLE IF NOT EXISTS ${clientId}.e2e_question_entry (
    id SERIAL PRIMARY KEY,

    -- We don't know if questions will be grouped under sections or blocks. But they must ALWAYS be at least associated to a sheet.
    e2e_block_id INTEGER REFERENCES ${clientId}.e2e_block(id) ON DELETE CASCADE,
    e2e_section_id INTEGER REFERENCES ${clientId}.e2e_section(id) ON DELETE CASCADE,
    e2e_sheet_id INTEGER NOT NULL REFERENCES ${clientId}.e2e_sheet(id) ON DELETE CASCADE,
    content_detail_id INTEGER REFERENCES ${clientId}.content_detail(id) ON DELETE SET NULL,

    question_range TEXT,
    answer_type text NOT NULL,
    answer_range TEXT,
    comments_range TEXT,

    -- If this question drives the answer of another question(s)
    dependent_questions JSONB,

    -- Sometimes a single question_range is not enough
    question_additional_range TEXT,

    -- The cell range(s) where the question_context came from.
    question_context_range TEXT,

    -- If this is a "sub-question"
    parent_question_range TEXT,

    seq INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- Can apply a tag to a specific question
    metadata_filter jsonb DEFAULT '{}'::jsonb,

    -- Store generic metadata about the question
    details JSONB
  )`;

  const answer = `
    CREATE TABLE IF NOT EXISTS ${clientId}.e2e_answer_entry (
    id BIGSERIAL PRIMARY KEY,
    e2e_question_entry_id BIGINT REFERENCES ${clientId}.e2e_question_entry(id) ON DELETE CASCADE,
    content_detail_id BIGINT REFERENCES ${clientId}.content_detail(id) ON DELETE CASCADE,
    question_string TEXT,
    answer TEXT,
    modified_text TEXT,
    type TEXT,
    answer_address TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_modified_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_modified_by INTEGER REFERENCES ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_USER}(id) ON DELETE SET NULL,
    details JSONB DEFAULT '{}'::jsonb,
    conversation_id INTEGER REFERENCES ${clientId}.conversation(id) ON DELETE SET NULL,
    loop_in_expert_sent BOOLEAN NOT NULL DEFAULT FALSE
  )`;

  const answerManual = `
    CREATE TABLE IF NOT EXISTS ${clientId}.e2e_answer_entry_manual (
    id BIGSERIAL PRIMARY KEY,
    content_id BIGINT REFERENCES ${clientId}.content(id) ON DELETE CASCADE,
    sheet_title TEXT,
    answer TEXT,
    answer_address TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_modified_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_modified_by INTEGER REFERENCES ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_USER}(id) ON DELETE SET NULL,
    UNIQUE(content_id, sheet_title, answer_address)
  )`;

  const ind1 = `CREATE INDEX IF NOT EXISTS idx_sheets_workbook_id ON ${clientId}.e2e_sheet(e2e_workbook_id)`;
  const ind2 = `CREATE INDEX IF NOT EXISTS idx_sections_sheet_id ON ${clientId}.e2e_section(e2e_sheet_id)`;
  const ind3 = `CREATE INDEX IF NOT EXISTS idx_blocks_section_id ON ${clientId}.e2e_block(e2e_section_id)`;
  const ind4 = `CREATE INDEX IF NOT EXISTS idx_question_entries_block_id ON ${clientId}.e2e_question_entry(e2e_block_id)`;
  const ind5 = `CREATE INDEX IF NOT EXISTS idx_e2e_answer_content_detail_id ON ${clientId}.e2e_answer_entry(content_detail_id)`;
  const ind6 = `CREATE INDEX IF NOT EXISTS idx_e2e_answer_entry_manual_content_id ON ${clientId}.e2e_answer_entry_manual(content_id)`;

  return [
    workbook,
    worksheet,
    section,
    block,
    question,
    answer,
    answerManual,
    ind1,
    ind2,
    ind3,
    ind4,
    ind5,
    ind6,
  ];
};
export const nlp_category_entity = (clientId: string) => {
  const createTable = `
  CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_NLP_CATEGORY_ENTITY} (
    id SERIAL PRIMARY KEY,
    category_source_id INTEGER NOT NULL,
    entity_id INTEGER NOT NULL,
    justification TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (category_source_id) REFERENCES ${clientId}.${constants.TABLE_NLP_CATEGORY_SOURCE}(id) ON DELETE CASCADE,
    FOREIGN KEY (entity_id) REFERENCES ${clientId}.${constants.TABLE_NLP_ENTITY}(id) ON DELETE CASCADE,
    CONSTRAINT nlp_category_entity_category_source_id_entity_id_unique UNIQUE (category_source_id, entity_id)
  )`;

  return [createTable];
};

export const client_block_doc_comment = (clientId: string) => {
  const createTable = `
  CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_BLOCK_DOC_COMMENT} (
    id TEXT PRIMARY KEY,
    value JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    user_id INTEGER NOT NULL REFERENCES tribble.user(id) ON DELETE CASCADE,
    parent_id TEXT REFERENCES ${clientId}.${constants.TABLE_BLOCK_DOC_COMMENT}(id) ON DELETE CASCADE,
    is_resolved BOOLEAN DEFAULT FALSE,
    block_document_id INT REFERENCES ${clientId}.${constants.TABLE_BLOCK_DOCUMENT}(id) ON DELETE CASCADE,
    notification_parent_id BIGSERIAL,
    UNIQUE(block_document_id, id)
  )`;

  const createIndex1 = `
  CREATE INDEX IF NOT EXISTS idx_block_doc_comment_user_id
    ON ${clientId}.${constants.TABLE_BLOCK_DOC_COMMENT}(user_id)`;

  const createIndex2 = `
  CREATE INDEX IF NOT EXISTS idx_block_doc_comment_parent_id
    ON ${clientId}.${constants.TABLE_BLOCK_DOC_COMMENT}(parent_id)`;

  const createIndex3 = `
  CREATE INDEX IF NOT EXISTS idx_block_doc_comment_is_resolved
    ON ${clientId}.${constants.TABLE_BLOCK_DOC_COMMENT}(is_resolved)`;

  return [createTable, createIndex1, createIndex2, createIndex3];
};

export const document_metadata_embedding = (clientId: string) => {
  const createTable = `
    CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_DOCUMENT_METADATA_EMBEDDING} (
      id SERIAL PRIMARY KEY,
      document_id INTEGER NOT NULL REFERENCES ${clientId}.document(id) ON DELETE CASCADE,
      label_embedding vector(1536) NOT NULL,
      type TEXT NOT NULL,
      CONSTRAINT document_metadata_embedding_document_id_type_unique UNIQUE (document_id, type)
  )`;

  const createIndex1 = `
    CREATE INDEX IF NOT EXISTS idx_document_metadata_embedding
      ON ${clientId}.${constants.TABLE_DOCUMENT_METADATA_EMBEDDING}
      USING ivfflat (label_embedding) WITH (lists = 100);
  `;

  const createIndex2 = `
    CREATE INDEX IF NOT EXISTS idx_document_metadata_embedding_document_id
      ON ${clientId}.${constants.TABLE_DOCUMENT_METADATA_EMBEDDING}
      USING btree (document_id ASC NULLS LAST)
      TABLESPACE pg_default;
  `;

  const createIndex3 = `
    CREATE INDEX IF NOT EXISTS idx_document_metadata_embedding_type
      ON ${clientId}.${constants.TABLE_DOCUMENT_METADATA_EMBEDDING}
      USING btree (type ASC NULLS LAST)
      TABLESPACE pg_default;
  `;

  return [createTable, createIndex1, createIndex2, createIndex3];
};

export const client_gong_calls = (clientId: string) => {
  const createTable = `
  CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_GONG_CALLS} (
    id BIGSERIAL PRIMARY KEY,
    gong_id TEXT NOT NULL UNIQUE,
    url TEXT,
    title TEXT,
    scheduled TIMESTAMP WITH TIME ZONE,
    started TIMESTAMP WITH TIME ZONE,
    duration INTEGER,
    primary_user_id VARCHAR(255),
    direction TEXT,
    system TEXT,
    scope TEXT,
    media TEXT,
    language TEXT,
    workspace_id TEXT,
    sdr_disposition TEXT,
    client_unique_id TEXT,
    custom_data TEXT,
    purpose TEXT,
    meeting_url TEXT,
    is_private BOOLEAN,
    calendar_event_id TEXT,
    last_ingested_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    crm_account_id TEXT,
    crm_opportunity_id TEXT,
    created_by INTEGER NOT NULL REFERENCES ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_USER}(id) ON DELETE CASCADE,
    processing_status VARCHAR(20),
    last_processing_started TIMESTAMP WITH TIME ZONE
  );
  `;
  const createIndex = `CREATE INDEX IF NOT EXISTS gong_calls_gong_id_idx ON ${clientId}.${constants.TABLE_GONG_CALLS}(gong_id);`;
  const createProcessingStatusIndex = `CREATE INDEX IF NOT EXISTS idx_gong_calls_processing_status ON ${clientId}.${constants.TABLE_GONG_CALLS}(processing_status);`;

  return [createTable, createIndex, createProcessingStatusIndex];
};

export const client_gong_call_transcript_sentences = (clientId: string) => {
  const createTable = `
  CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_GONG_CALL_TRANSCRIPT_SENTENCES} (
    id BIGSERIAL PRIMARY KEY,
    gong_call_id TEXT NOT NULL REFERENCES ${clientId}.${constants.TABLE_GONG_CALLS}(gong_id) ON DELETE CASCADE,
    speaker_id TEXT,
    topic TEXT,
    start_ms INTEGER,
    end_ms INTEGER,
    text TEXT,
    plain_text_speaker_name TEXT,
    last_ingested_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (gong_call_id, speaker_id, start_ms, end_ms)
  );
  `;

  const createIndex = `CREATE INDEX IF NOT EXISTS gong_call_transcript_sentences_gong_call_id_idx
    ON ${clientId}.${constants.TABLE_GONG_CALL_TRANSCRIPT_SENTENCES}(gong_call_id);`;

  return [createTable, createIndex];
};

export const client_rfx_bid_packet_file = (clientId: string) => {
  // re:content_markdown:
  // I didn't want to disrupt anything so created new markdown field.
  // The existing content field has the -----Page # ------- delimiters.
  // We could technically also just add these to the content_markdown field. Maybe for a future PR.
  const table = `CREATE TABLE IF NOT EXISTS ${clientId}.rfx_bid_packet_file (
      id SERIAL PRIMARY KEY,
      rfx_id INTEGER REFERENCES ${clientId}.rfx(id) ON DELETE CASCADE,
      blob_storage_uuid TEXT,
      file_name TEXT NOT NULL,
      paragraphs JSONB default '[]'::jsonb,
      pages JSONB default '[]'::jsonb,
      tables JSONB default '[]'::jsonb,
      content TEXT,
      content_markdown TEXT,
      mime_type TEXT,
      content_id INTEGER REFERENCES ${clientId}.content(id) ON DELETE SET NULL
    );`;
  return [table];
};

export const client_form_recognizer_output = (clientId: string) => {
  const table = `CREATE TABLE IF NOT EXISTS ${clientId}.form_recognizer_output (
      id SERIAL PRIMARY KEY,
      blob_storage_uuid TEXT,
      file_name TEXT NOT NULL,
      paragraphs JSONB default '[]'::jsonb,
      pages JSONB default '[]'::jsonb,
      tables JSONB default '[]'::jsonb,
      content TEXT,
      content_markdown TEXT,
      content_hash TEXT,
      mime_type TEXT
    );`;

  const index1 = `CREATE INDEX IF NOT EXISTS form_recognizer_output_blob_storage_uuid_idx ON ${clientId}.form_recognizer_output(blob_storage_uuid);`;
  const index2 = `CREATE INDEX IF NOT EXISTS form_recognizer_output_content_hash ON ${clientId}.form_recognizer_output(content_hash);`;
  return [table, index1, index2];
};

export const client_rfx_response_document = (clientId: string) => {
  const createTable = `CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_RFX_RESPONSE_DOCUMENT} (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    highlevel_summary TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    conversation_id INTEGER REFERENCES ${clientId}.conversation(id) ON DELETE SET NULL,
    bid_packet_file_id INTEGER REFERENCES ${clientId}.rfx_bid_packet_file(id) ON DELETE CASCADE,
    rfx_content_id INTEGER REFERENCES ${clientId}.rfx_content(id) ON DELETE CASCADE
  );`;

  return [createTable];
};

export const client_rfx_section_outline = (clientId: string) => {
  const createTable = `CREATE TABLE IF NOT EXISTS ${clientId}.${constants.TABLE_RFX_SECTION_OUTLINE} (
    id SERIAL PRIMARY KEY,
    rfx_response_document_id INTEGER REFERENCES ${clientId}.${constants.TABLE_RFX_RESPONSE_DOCUMENT}(id) ON DELETE CASCADE,
    exemplar_document_id INTEGER REFERENCES ${clientId}.document(id) ON DELETE SET NULL,
    name TEXT NOT NULL,
    content TEXT,
    source_page_start INTEGER NOT NULL,
    source_page_end INTEGER NOT NULL,
    source_text TEXT,
    seq INTEGER,
    process_as_qa BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    -- ensure page_end >= page_start
    CONSTRAINT rfx_section_outline_page_range_check CHECK (source_page_end >= source_page_start)
  );`;

  const createIndex = `CREATE INDEX IF NOT EXISTS idx_rfx_section_outline_document_id
    ON ${clientId}.${constants.TABLE_RFX_SECTION_OUTLINE} (rfx_response_document_id);`;

  return [createTable, createIndex];
};

export const client_rfx_writer_prompt = (clientId: string) => {
  const table1 = `CREATE TABLE IF NOT EXISTS  ${clientId}.rfx_writer_prompt (
    id BIGSERIAL PRIMARY KEY,
    rfx_section_outline_id int REFERENCES ${clientId}.rfx_section_outline(id) ON DELETE SET NULL,
    prompt text,
    requirements JSONB default '[]'::jsonb,
    research JSONB default '{}'::jsonb
  );`;

  const table2 = `
    CREATE TABLE IF NOT EXISTS  ${clientId}.rfx_writer_output (
      id BIGSERIAL PRIMARY KEY,
      passages JSONB default '[]'::jsonb,
      unmet_requirements JSONB default '[]'::jsonb,
      rfx_section_outline_id int REFERENCES ${clientId}.rfx_section_outline(id) ON DELETE SET NULL,
      rfx_writer_prompt_id int REFERENCES ${clientId}.rfx_writer_prompt(id) ON DELETE SET NULL,
      conversation_id INTEGER REFERENCES ${clientId}.conversation(id) ON DELETE SET NULL
    );
  `;

  const addWriterOutputRef = `
  ALTER TABLE ${clientId}.${constants.TABLE_BLOCK_SECTION}
    ADD COLUMN IF NOT EXISTS rfx_writer_output_id int REFERENCES ${clientId}.rfx_writer_output(id) ON DELETE SET NULL;`;

  return [table1, table2, addWriterOutputRef];
};

export const client_rfx_section_outline_sources = (clientId: string) => {
  const createTable = `CREATE TABLE IF NOT EXISTS ${clientId}.rfx_section_outline_sources (
    id SERIAL PRIMARY KEY,
    rfx_section_outline_id INTEGER NOT NULL REFERENCES ${clientId}.rfx_section_outline(id),
    rfx_bid_packet_file_id INTEGER NOT NULL REFERENCES ${clientId}.rfx_bid_packet_file(id),
    source_page_start INTEGER NOT NULL,
    source_page_end INTEGER NOT NULL,
    source_text TEXT NOT NULL
  )`;

  const createIndex1 = `CREATE INDEX IF NOT EXISTS idx_rfx_section_outline_sources_outline_id 
  ON ${clientId}.rfx_section_outline_sources (rfx_section_outline_id);`;

  const createIndex2 = `CREATE INDEX IF NOT EXISTS idx_rfx_section_outline_sources_file_id 
  ON ${clientId}.rfx_section_outline_sources (rfx_bid_packet_file_id);`;

  return [createTable, createIndex1, createIndex2];
};

export const client_slack_auto_reply_channel = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.${constants.TABLE_SLACK_AUTO_REPLY_CHANNEL}
  (
    id SERIAL PRIMARY KEY,
    channel_id TEXT NOT NULL UNIQUE,
    channel_name TEXT NOT NULL,
    response_delay_seconds INTEGER NOT NULL DEFAULT 300,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_by INTEGER NOT NULL REFERENCES ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_USER}(id),
    updated_by INTEGER NOT NULL REFERENCES ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_USER}(id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
  )`;

  return [createTable];
};

export const client_slack_auto_reply_message = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.${constants.TABLE_SLACK_AUTO_REPLY_MESSAGE}
  (
    id SERIAL PRIMARY KEY,
    channel_id TEXT NOT NULL,
    message_ts TEXT NOT NULL,
    platform_user_id TEXT NOT NULL,
    user_id INTEGER NOT NULL REFERENCES ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_USER}(id),
    message_text TEXT NOT NULL,
    scheduled_reply_time TIMESTAMP WITH TIME ZONE NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    response_text TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP WITH TIME ZONE,
    FOREIGN KEY (channel_id) REFERENCES ${clientId}.${constants.TABLE_SLACK_AUTO_REPLY_CHANNEL}(channel_id),
    UNIQUE(channel_id, message_ts, platform_user_id)
  )`;

  const createStatusIndex = `
  CREATE INDEX idx_slack_auto_reply_message_status
  ON ${clientId}.${constants.TABLE_SLACK_AUTO_REPLY_MESSAGE}(status)`;

  const createScheduledTimeIndex = `
  CREATE INDEX idx_slack_auto_reply_message_scheduled_time
  ON ${clientId}.${constants.TABLE_SLACK_AUTO_REPLY_MESSAGE}(scheduled_reply_time)`;

  return [createTable, createStatusIndex, createScheduledTimeIndex];
};

export const client_questionnaire_comment = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.questionnaire_comment
  (
    id SERIAL PRIMARY KEY,
    content_detail_id INTEGER NOT NULL REFERENCES ${clientId}.content_detail(id) ON DELETE CASCADE,
    created_by_id INTEGER NOT NULL REFERENCES ${constants.SCHEMA_TRIBBLE}.user(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    content JSONB NOT NULL,
    parent_id INTEGER REFERENCES ${clientId}.questionnaire_comment(id) ON DELETE CASCADE,
    is_resolved BOOLEAN DEFAULT FALSE,
    resolved_by_id INTEGER REFERENCES ${constants.SCHEMA_TRIBBLE}.user(id),
    resolved_at TIMESTAMP WITH TIME ZONE
  )`;

  const createIndex = `
  CREATE INDEX questionnaire_comment_content_detail_id_idx
  ON ${clientId}.questionnaire_comment(content_detail_id)`;

  return [createTable, createIndex];
};

export const client_slack_channel_tags = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.slack_channel_tags
  (
    id SERIAL PRIMARY KEY,
    slack_channel TEXT NOT NULL,
    tag_id SMALLINT NOT NULL REFERENCES ${clientId}.metadata_filter(id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    used_for_ingest BOOLEAN NOT NULL DEFAULT FALSE,
    used_for_answer BOOLEAN NOT NULL DEFAULT FALSE,
    UNIQUE (slack_channel, tag_id)
  )`;

  const createIndex = `
  CREATE INDEX idx_slack_channel_tags_slack_channel
  ON ${clientId}.slack_channel_tags (slack_channel)`;

  return [createTable, createIndex];
};

export const client_gong_call_extensive_data = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.gong_call_extensive_data
  (
    id SERIAL PRIMARY KEY,
    -- Foreign key to our internal gong_calls table ID
    gong_call_db_id INT NOT NULL,
    -- Gong's unique call ID (denormalized for potential lookups)
    gong_id TEXT NOT NULL,
    fetched_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    -- Store the detailed context object from the API response
    call_details JSONB NOT NULL DEFAULT '{}'::jsonb
  )`;

  const addForeignKey = `
  ALTER TABLE ${clientId}.gong_call_extensive_data
  ADD CONSTRAINT fk_gong_call_extensive_data_gong_call_db_id
  FOREIGN KEY (gong_call_db_id)
  REFERENCES ${clientId}.gong_calls(id)
  ON DELETE CASCADE`;

  const createIndexGongCallDbId = `
  CREATE INDEX idx_gong_call_extensive_data_gong_call_db_id
  ON ${clientId}.gong_call_extensive_data(gong_call_db_id)`;

  const createIndexGongId = `
  CREATE UNIQUE INDEX idx_gong_call_extensive_data_gong_id
  ON ${clientId}.gong_call_extensive_data(gong_id)`;

  const createIndexFetchedAt = `
  CREATE INDEX idx_gong_call_extensive_data_fetched_at
  ON ${clientId}.gong_call_extensive_data(fetched_at)`;

  return [
    createTable,
    addForeignKey,
    createIndexGongCallDbId,
    createIndexGongId,
    createIndexFetchedAt,
  ];
};

export const client_block_list = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.block_list_entry (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by_id INT references tribble.user(id),
    block_url text,
    CONSTRAINT block_list_entry_block_url_unique UNIQUE (block_url)
  )`;

  const index = `CREATE INDEX IF NOT EXISTS block_list_entry_block_url ON ${clientId}.block_list_entry (block_url);`;

  return [createTable, index];
};

export const client_conversation_pinned = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.conversation_pinned
  (
    id SERIAL PRIMARY KEY,
    conversation_id INTEGER,
    conversation_detail_id INTEGER,
    pinned_by INTEGER NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    image_url TEXT,
    created_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP WITH TIME ZONE,
    is_deleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (pinned_by) REFERENCES tribble.user(id) ON DELETE CASCADE,
    FOREIGN KEY (conversation_id) REFERENCES ${clientId}.conversation(id) ON DELETE CASCADE,
    FOREIGN KEY (conversation_detail_id) REFERENCES ${clientId}.conversation_detail(id) ON DELETE CASCADE
  )`;

  const createIndexPinnedBy = `
  CREATE INDEX IF NOT EXISTS idx_conversation_pinned_user_client ON ${clientId}.conversation_pinned(pinned_by);
  `;

  const createIndexCreatedDate = `
  CREATE INDEX IF NOT EXISTS idx_conversation_pinned_created_date ON ${clientId}.conversation_pinned(created_date DESC);
  `;

  return [createTable, createIndexPinnedBy, createIndexCreatedDate];
};

export const cartridge_and_tool = (clientId: string) => {
  const createCartridgeTable = `
    CREATE TABLE IF NOT EXISTS ${clientId}.cartridge (
      id SERIAL PRIMARY KEY,
      name text NOT NULL UNIQUE,
      prompt text,
      model text,
      tool_choice text,
      created_at TIMESTAMPTZ DEFAULT NOW(),
      created_by int REFERENCES tribble.user(id) ON DELETE SET NULL,
      updated_at TIMESTAMPTZ,
      eager_rag BOOLEAN NOT NULL DEFAULT FALSE,
      updated_by int REFERENCES tribble.user(id) ON DELETE SET NULL
    )`;

  const createToolTable = `
    CREATE TABLE IF NOT EXISTS ${clientId}.tool (
      id SERIAL PRIMARY KEY,
      cartridge_id int REFERENCES ${clientId}.cartridge(id) ON DELETE CASCADE,
      name text NOT NULL,
      description text,
      do_not_recurse boolean DEFAULT false,
      is_handoff boolean DEFAULT false,
      json_schema_args jsonb default '{}'::jsonb,
      
      created_at TIMESTAMPTZ DEFAULT NOW(),
      created_by int REFERENCES tribble.user(id) ON DELETE SET NULL,
      updated_at TIMESTAMPTZ,
      updated_by int REFERENCES tribble.user(id) ON DELETE SET NULL,
      CONSTRAINT unique_tool_name UNIQUE (name, cartridge_id)
    )`;

  const createCartridgeUpdatedAtTrigger = `
    CREATE OR REPLACE TRIGGER update_updated_at_before_update
    BEFORE INSERT OR UPDATE ON ${clientId}.cartridge
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column()`;

  const createToolUpdatedAtTrigger = `
    CREATE OR REPLACE TRIGGER update_updated_at_before_update
    BEFORE INSERT OR UPDATE ON ${clientId}.tool
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column()`;

  const addRef1 = `
    ALTER TABLE ${clientId}.conversation_state
    ADD COLUMN IF NOT EXISTS cartridge_id int REFERENCES ${clientId}.cartridge(id) ON DELETE SET NULL`;

  const addRef2 = `
    ALTER TABLE ${clientId}.conversation_detail
    ADD COLUMN IF NOT EXISTS cartridge_id int REFERENCES ${clientId}.tool(id) ON DELETE SET NULL`;

  return [
    createCartridgeTable,
    createToolTable,
    createCartridgeUpdatedAtTrigger,
    createToolUpdatedAtTrigger,
    addRef1,
    addRef2,
  ];
};

export const workflow_category = (clientId: string) => {
  const table = `CREATE TABLE IF NOT EXISTS ${clientId}.workflow_category (
   id SERIAL PRIMARY KEY,
      name text NOT NULL,
      label text NOT NULL,
      created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
      created_by int REFERENCES tribble.user(id) ON DELETE SET NULL,
      updated_by int REFERENCES tribble.user(id) ON DELETE SET NULL
      );`;

  const trigger = `CREATE OR REPLACE TRIGGER update_updated_at_before_update
    BEFORE INSERT OR UPDATE ON ${clientId}.workflow_category
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
  `;

  const seedCategories = `INSERT INTO ${clientId}.workflow_category (name, label)
    VALUES
      ('meet', '🤝 Meet'),
      ('build', '🏗️ Build'),
      ('analyze', '📊 Analyze'),
      ('win', '🏆 Win');
  `;
  return [table, trigger, seedCategories];
};

export const workflow = (clientId: string) => {
  const table = `CREATE TABLE IF NOT EXISTS ${clientId}.workflow (
      id SERIAL PRIMARY KEY,
      base_cartridge_id int REFERENCES ${clientId}.cartridge(id) ON DELETE SET NULL,
      base_cartridge_enum int NOT NULL DEFAULT 0,
      workflow_category_id int REFERENCES ${clientId}.workflow_category(id) ON DELETE SET NULL,
      name TEXT NOT NULL,
      display_name TEXT NOT NULL,
      description TEXT,
      is_enabled BOOLEAN DEFAULT false,
      required_integrations JSONB DEFAULT '[]'::jsonb,
      start_message TEXT,


      created_by int REFERENCES tribble.user(id) ON DELETE SET NULL,
      updated_by int REFERENCES tribble.user(id) ON DELETE SET NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );`;

  const trigger = `CREATE OR REPLACE TRIGGER update_updated_at_before_update
    BEFORE INSERT OR UPDATE ON ${clientId}.workflow
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();`;

  return [table, trigger];
};

export const client_access_sequence = (clientId: string) => {
  const createTable = `
  CREATE TABLE ${clientId}.access_sequence (
    id SERIAL PRIMARY KEY,
    tier INTEGER NOT NULL,
    type TEXT NOT NULL,
    is_single_source BOOLEAN NOT NULL DEFAULT false,
    agent_type TEXT,
    document_id INTEGER REFERENCES ${clientId}.document(id) ON DELETE CASCADE,
    value TEXT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES ${constants.SCHEMA_TRIBBLE}.user(id),
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by INTEGER REFERENCES ${constants.SCHEMA_TRIBBLE}.user(id)
  )`;

  const indexTier = `CREATE INDEX IF NOT EXISTS idx_access_sequence_tier ON ${clientId}.access_sequence (tier);`;
  const indexType = `CREATE INDEX IF NOT EXISTS idx_access_sequence_type ON ${clientId}.access_sequence (type);`;
  const indexAgentType = `CREATE INDEX IF NOT EXISTS idx_access_sequence_agent_type ON ${clientId}.access_sequence (agent_type);`;
  const indexDocumentId = `CREATE INDEX IF NOT EXISTS idx_access_sequence_document_id ON ${clientId}.access_sequence (document_id);`;
  const indexCreatedBy = `CREATE INDEX IF NOT EXISTS idx_access_sequence_created_by ON ${clientId}.access_sequence (created_by);`;
  const indexValue = `CREATE INDEX IF NOT EXISTS idx_access_sequence_value ON ${clientId}.access_sequence (value);`;
  const uniqueConstraint = `CREATE UNIQUE INDEX IF NOT EXISTS idx_access_sequence_unique_entry ON ${clientId}.access_sequence (type, value, COALESCE(agent_type, ''));`;

  return [
    createTable,
    indexTier,
    indexType,
    indexAgentType,
    indexDocumentId,
    indexCreatedBy,
    indexValue,
    uniqueConstraint,
  ];
};

export const generated_asset = (clientId: string) => {
  const table = `CREATE TABLE IF NOT EXISTS ${clientId}.generated_asset (
      id SERIAL PRIMARY KEY,
      type TEXT NOT NULL,
      label TEXT,
      description TEXT,
      is_private BOOLEAN DEFAULT true,
      can_be_shared BOOLEAN DEFAULT false,

      -- If a presentation was created, the uuid (+ext) of the pptx file uploaded to blob storage
      -- If an image, the uuid (+ext)of the image uploaded to blob storage
      -- If an account plan (some doc), likely the uuid of the markdown version of the doc      
      asset_storage_id TEXT,
      asset_mime_type TEXT,

      -- Optional: full size and thumbnail size images of the item for showing in the UI
      image_storage_id TEXT,
      image_thumbnail_storage_id TEXT,

      -- Optional: if the item was generated via code (e.g. React Code --> Image),
      -- the uuid (+ext) of the code file (likely HTML) uploaded to blob storage
      code_storage_id TEXT,

      conversation_id int REFERENCES ${clientId}.conversation(id) ON DELETE CASCADE,

      created_by int REFERENCES tribble.user(id) ON DELETE SET NULL,
      updated_by int REFERENCES tribble.user(id) ON DELETE SET NULL,
      deleted_by int REFERENCES tribble.user(id) ON DELETE SET NULL,

      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      deleted_at TIMESTAMP,

      UNIQUE (asset_storage_id, asset_mime_type)
  )`;

  const trigger = `CREATE OR REPLACE TRIGGER update_updated_at_before_update
    BEFORE INSERT OR UPDATE ON ${clientId}.generated_asset
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();`;

  const indexAssetStorageId = `CREATE INDEX IF NOT EXISTS idx_generated_asset_asset_storage_id ON ${clientId}.generated_asset (asset_storage_id);`;
  const indexCreatedBy = `CREATE INDEX IF NOT EXISTS idx_generated_asset_created_by ON ${clientId}.generated_asset (created_by);`;

  return [table, trigger, indexAssetStorageId, indexCreatedBy];
};

export const generated_asset_share = (clientId: string) => {
  const table = `CREATE TABLE IF NOT EXISTS ${clientId}.generated_asset_share (
    id SERIAL PRIMARY KEY,
    generated_asset_id int NOT NULL REFERENCES ${clientId}.generated_asset(id) ON DELETE CASCADE,
    share_id TEXT NOT NULL,
    share_hash_suffix TEXT NOT NULL,
    expiry TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by int REFERENCES tribble.user(id) ON DELETE SET NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by int REFERENCES tribble.user(id) ON DELETE SET NULL,
    deleted_at TIMESTAMP,
    deleted_by int REFERENCES tribble.user(id) ON DELETE SET NULL
  )`;

  const trigger = `CREATE OR REPLACE TRIGGER update_updated_at_before_update
    BEFORE INSERT OR UPDATE ON ${clientId}.generated_asset_share
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();`;

  const indexShareId = `CREATE INDEX IF NOT EXISTS idx_generated_asset_share_share_id ON ${clientId}.generated_asset_share (share_id);`;
  const indexGeneratedAssetId = `CREATE INDEX IF NOT EXISTS idx_generated_asset_share_generated_asset_id ON ${clientId}.generated_asset_share (generated_asset_id);`;
  const indexCreatedBy = `CREATE INDEX IF NOT EXISTS idx_generated_asset_share_created_by ON ${clientId}.generated_asset_share (created_by);`;

  return [table, trigger, indexShareId, indexGeneratedAssetId, indexCreatedBy];
};
