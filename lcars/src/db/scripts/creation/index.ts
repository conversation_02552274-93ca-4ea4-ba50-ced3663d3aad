// organize-imports-ignore
import {
  authorize_readonly_role,
  client_brand_config,
  client_client_setting,
  client_content,
  client_content_detail,
  client_content_detail_embedding,
  client_content_detail_insight,
  client_content_grouping,
  client_content_grouping_join,
  client_content_review,
  client_content_user_join,
  client_conversation,
  client_conversation_detail,
  client_conversation_detail_embedding,
  client_conversation_detail_file,
  client_conversation_state,
  client_conversation_summary,
  client_create_schema,
  client_custom_table_catalog,
  client_document,
  client_document_type,
  client_document_type_priority,
  client_embedding,
  client_embedding_history,
  client_embedding_owner,
  client_extraction_task_config,
  client_gdrive_asset,
  client_integration_asset_sheet,
  client_generated_content,
  client_integration,
  client_job,
  client_job_log,
  client_metadata_filter,
  client_metadata_filter_type,
  client_pending_query,
  client_task,
  client_task_embedding,
  client_task_owner,
  client_teams_conversation_reference,
  client_teams_thread,
  client_user_integration,
  client_user_setting,
  client_zendesk_asset,
  nlp_task,
  nlp_task_status,
  nlp_entity,
  client_task_feedback,
  nlp_entity_source,
  m365drive_asset,
  nlp_category_set,
  nlp_category,
  nlp_category_source,
  rfx,
  rfx_bid_packet_file_rfx_content,
  rfx_content,
  rfx_content_user_join,
  rfx_criteria,
  rfx_criteria_xref_mdf,
  rfx_xref_rfx_criteria,
  nlp_summary,
  highspot_assets,
  salesforce_tribblytics_map,
  salesforce_tribblytics,
  salesforce_content,
  nlp_entity_dedupe_result,
  box_asset,
  client_profile,
  client_block_document,
  client_block_section,
  client_block_item,
  client_block_document_version,
  client_block_item_version,
  client_block_doc_task_item,
  client_block_document_lock,
  e2e_worksheet,
  client_integration_asset_settings,
  client_block_doc_comment,
  nlp_category_entity,
  document_metadata_embedding,
  client_gong_calls,
  client_gong_call_extensive_data,
  client_gong_call_transcript_sentences,
  client_rfx_bid_packet_file,
  client_rfx_section_outline,
  client_rfx_response_document,
  client_rfx_writer_prompt,
  client_rfx_section_outline_sources,
  client_form_recognizer_output,
  client_questionnaire_comment,
  client_slack_auto_reply_message,
  client_slack_auto_reply_channel,
  client_task_feedback_assignee,
  client_slack_channel_tags,
  client_block_list,
  client_conversation_pinned,
  cartridge_and_tool,
  workflow,
  workflow_category,
  client_access_sequence,
  generated_asset,
  generated_asset_share,
} from './all_sql';

export const createClientDBScripts = (clientId: string) => {
  const orderedScripts = [
    client_create_schema(clientId),
    client_client_setting(clientId),
    client_user_setting(clientId),
    client_user_integration(clientId),
    client_metadata_filter_type(clientId),
    client_metadata_filter(clientId),
    client_document_type_priority(clientId),
    client_document_type(clientId),
    client_job(clientId),
    client_content(clientId),
    client_conversation(clientId),
    client_content_grouping(clientId),
    client_content_grouping_join(clientId),
    client_content_user_join(clientId),
    client_content_detail(clientId),
    client_document(clientId),
    client_embedding(clientId),
    client_embedding_history(clientId),
    client_embedding_owner(clientId),
    client_pending_query(clientId),
    client_content_review(clientId),
    client_conversation_state(clientId),
    client_conversation_detail(clientId),
    client_conversation_detail_embedding(clientId),
    client_conversation_detail_file(clientId),
    client_conversation_summary(clientId),
    client_generated_content(clientId),
    client_brand_config(clientId),
    client_job_log(clientId),
    client_extraction_task_config(clientId),
    client_content_detail_embedding(clientId),
    client_task(clientId),
    client_task_embedding(clientId),
    client_task_owner(clientId),
    client_task_feedback(clientId),
    client_teams_thread(clientId),
    client_teams_conversation_reference(clientId),
    client_custom_table_catalog(clientId),
    client_gdrive_asset(clientId),
    client_integration_asset_sheet(clientId),
    authorize_readonly_role(clientId),
    client_zendesk_asset(clientId),
    client_integration(clientId),
    nlp_task(clientId),
    nlp_entity(clientId),
    nlp_entity_source(clientId),
    nlp_task_status(clientId),
    m365drive_asset(clientId),
    nlp_category_set(clientId),
    nlp_category(clientId),
    nlp_category_source(clientId),
    nlp_summary(clientId),
    rfx(clientId),
    rfx_content(clientId),
    rfx_content_user_join(clientId),
    rfx_criteria(clientId),
    rfx_criteria_xref_mdf(clientId),
    rfx_xref_rfx_criteria(clientId),
    highspot_assets(clientId),
    salesforce_tribblytics_map(clientId),
    salesforce_tribblytics(clientId),
    salesforce_content(clientId),
    nlp_entity_dedupe_result(clientId),
    box_asset(clientId),
    client_profile(clientId),
    client_block_document(clientId),
    client_block_section(clientId),
    client_block_item(clientId),
    client_block_document_version(clientId),
    client_block_item_version(clientId),
    client_block_doc_task_item(clientId),
    client_block_document_lock(clientId),
    e2e_worksheet(clientId),
    client_integration_asset_settings(clientId),
    client_block_doc_comment(clientId),
    nlp_category_entity(clientId),
    document_metadata_embedding(clientId),
    client_gong_calls(clientId),
    client_gong_call_extensive_data(clientId),
    client_gong_call_transcript_sentences(clientId),
    client_rfx_bid_packet_file(clientId),
    client_form_recognizer_output(clientId),
    client_rfx_response_document(clientId),
    client_rfx_section_outline(clientId),
    client_rfx_writer_prompt(clientId),
    client_rfx_section_outline_sources(clientId),
    client_slack_auto_reply_channel(clientId),
    client_slack_auto_reply_message(clientId),
    client_questionnaire_comment(clientId),
    client_task_feedback_assignee(clientId),
    rfx_bid_packet_file_rfx_content(clientId),
    client_slack_channel_tags(clientId),
    client_block_list(clientId),
    client_content_detail_insight(clientId),
    client_conversation_pinned(clientId),
    cartridge_and_tool(clientId),
    workflow_category(clientId),
    workflow(clientId),
    client_access_sequence(clientId),
    generated_asset(clientId),
    generated_asset_share(clientId),
  ];

  return orderedScripts.flat().map((sql) => sql.trim());
};
