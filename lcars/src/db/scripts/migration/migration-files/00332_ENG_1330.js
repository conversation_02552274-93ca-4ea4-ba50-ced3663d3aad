export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
    CREATE TABLE IF NOT EXISTS {schema}.rfx_section_outline_sources (
      id SERIAL PRIMARY KEY,
      rfx_section_outline_id INTEGER NOT NULL REFERENCES {schema}.rfx_section_outline(id) ON DELETE CASCADE,
      rfx_bid_packet_file_id INTEGER NOT NULL REFERENCES {schema}.rfx_bid_packet_file(id) ON DELETE CASCADE,
      source_page_start INTEGER NOT NULL,
      source_page_end INTEGER NOT NULL,
      source_text TEXT
    );

    CREATE INDEX IF NOT EXISTS idx_rfx_section_outline_sources_outline_id 
      ON {schema}.rfx_section_outline_sources (rfx_section_outline_id);
    CREATE INDEX IF NOT EXISTS idx_rfx_section_outline_sources_file_id 
      ON {schema}.rfx_section_outline_sources (rfx_bid_packet_file_id);
  `;

export const down = `
    DROP INDEX IF EXISTS idx_rfx_section_outline_sources_outline_id;
    DROP INDEX IF EXISTS idx_rfx_section_outline_sources_file_id;

    DROP TABLE IF EXISTS {schema}.rfx_section_outline_sources;
  `;
