export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];
export const up = `INSERT INTO tribble.setting (name, label, description, type, is_client, tribble_editable, default_boolean)
VALUES ('respond_to_slack_bots', 'Respond to Bots in Slack', 'Can tribble respond to bots in slack?', 'boolean', true, true, false) ON CONFLICT DO NOTHING;`;
export const down = `DELETE FROM tribble.setting WHERE name = 'respond_to_slack_bots'`;
