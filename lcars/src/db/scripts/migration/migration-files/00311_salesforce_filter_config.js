export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];

export const up = `INSERT INTO tribble.setting (
  name,
  label,
  description,
  type,
  is_client,
  tribble_editable,
  default_string
) VALUES (
  'salesforce_filter_config',
  'Salesforce Filter Configuration',
  'JSON configuration for filtering Salesforce objects and fields. Defines which objects and field values should be excluded from queries.',
  'string',
  true,
  false,
  '{}'
) ON CONFLICT (name) DO NOTHING;`;

export const down = `DELETE FROM tribble.setting WHERE name = 'salesforce_filter_config';`;
