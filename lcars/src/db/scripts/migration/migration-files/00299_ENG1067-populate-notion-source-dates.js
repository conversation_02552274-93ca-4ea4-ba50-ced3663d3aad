export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
  -- First check if the notion_pages table exists for this schema
  DO $$
  BEGIN
    IF EXISTS (
      SELECT FROM information_schema.tables 
      WHERE table_schema = '{schema}_integration' 
      AND table_name = 'notion_pages'
    ) THEN
      -- Update source_created_date for Notion embeddings by joining with notion_pages table
      UPDATE {schema}.embedding e
      SET source_created_date = np.created_time
      FROM {schema}.document d
      JOIN {schema}_integration.notion_pages np ON d.metadata_json->>'externalDocSourceId' = np.id
      WHERE 
        e.document_id = d.id
        AND d.metadata_json->>'externalDocSource' = 'notion'
        AND e.content_type = 'DOC'
        AND e.source_created_date IS NULL
        AND np.created_time IS NOT NULL;

      -- Update source_modified_date for Notion embeddings by joining with notion_pages table
      UPDATE {schema}.embedding e
      SET source_modified_date = np.last_edited_time
      FROM {schema}.document d
      JOIN {schema}_integration.notion_pages np ON d.metadata_json->>'externalDocSourceId' = np.id
      WHERE 
        e.document_id = d.id
        AND d.metadata_json->>'externalDocSource' = 'notion'
        AND e.content_type = 'DOC'
        AND e.source_modified_date IS NULL
        AND d.status <> 'deleted' -- when deleted, last_edited_time is likely no longer correct
        AND np.last_edited_time IS NOT NULL;
    END IF;
  END
  $$;
`;

export const down = `
  -- Remove source_created_date for Notion embeddings
  UPDATE {schema}.embedding
  SET source_created_date = NULL, source_modified_date = NULL
  WHERE content_type = 'DOC' 
    AND (source_created_date IS NOT NULL OR source_modified_date IS NOT NULL)
    AND EXISTS (
      SELECT 1 
      FROM {schema}.document d 
      WHERE d.id = document_id 
      AND d.metadata_json->>'externalDocSource' = 'notion'
    );
`;
