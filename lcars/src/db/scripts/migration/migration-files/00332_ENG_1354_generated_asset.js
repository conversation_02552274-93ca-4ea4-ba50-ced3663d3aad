export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
    CREATE TABLE IF NOT EXISTS {schema}.generated_asset (
      id SERIAL PRIMARY KEY,
      type TEXT NOT NULL,
      label TEXT,
      description TEXT,
      is_private BOOLEAN DEFAULT true,
      can_be_shared BOOLEAN DEFAULT false,

      -- If a presentation was created, the uuid of the pptx file uploaded to blob storage
      -- If an image, the uuid of the image uploaded to blob storage
      -- If an account plan (some doc), likely the uuid of the markdown version of the doc
      asset_storage_id TEXT,
      asset_mime_type TEXT,

      -- Optional: full size and thumbnail size images of the item for showing in the UI
      image_storage_id TEXT,
      image_thumbnail_storage_id TEXT,

      -- Optional: if the item was generated via code (e.g. React Code --> Image),
      -- the uuid of the code file (likely HTML) uploaded to blob storage
      code_storage_id TEXT,

      conversation_id bigint REFERENCES {schema}.conversation(id) ON DELETE CASCADE,

      deleted_date TIMESTAMP,
      deleted_by int REFERENCES tribble.user(id) ON DELETE SET NULL,

      created_by int REFERENCES tribble.user(id) ON DELETE SET NULL,
      updated_by int REFERENCES tribble.user(id) ON DELETE SET NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

      UNIQUE (asset_storage_id, asset_mime_type)
    );

    CREATE OR REPLACE TRIGGER update_updated_at_before_update
    BEFORE INSERT OR UPDATE ON {schema}.generated_asset
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

    CREATE INDEX IF NOT EXISTS idx_generated_asset_asset_storage_id ON {schema}.generated_asset (asset_storage_id);
    CREATE INDEX IF NOT EXISTS idx_generated_asset_created_by ON {schema}.generated_asset (created_by);

    CREATE TABLE IF NOT EXISTS {schema}.generated_asset_share (
      id SERIAL PRIMARY KEY,
      generated_asset_id int NOT NULL REFERENCES {schema}.generated_asset(id) ON DELETE CASCADE,
      share_id TEXT NOT NULL,
      share_hash_suffix TEXT NOT NULL,
      expiry TIMESTAMP,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      created_by int REFERENCES tribble.user(id) ON DELETE SET NULL,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_by int REFERENCES tribble.user(id) ON DELETE SET NULL,
      deleted_at TIMESTAMP,
      deleted_by int REFERENCES tribble.user(id) ON DELETE SET NULL
    );

    CREATE OR REPLACE TRIGGER update_updated_at_before_update
    BEFORE INSERT OR UPDATE ON {schema}.generated_asset_share
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();    

    CREATE INDEX IF NOT EXISTS idx_generated_asset_share_share_id ON {schema}.generated_asset_share (share_id);
    CREATE INDEX IF NOT EXISTS idx_generated_asset_share_generated_asset_id ON {schema}.generated_asset_share (generated_asset_id);
    CREATE INDEX IF NOT EXISTS idx_generated_asset_share_created_by ON {schema}.generated_asset_share (created_by);
`;

export const down = `
  DROP TABLE IF EXISTS {schema}.generated_asset_share;
  DROP TABLE IF EXISTS {schema}.generated_asset;

  DROP TABLE IF EXISTS {schema}.generated_project_share;
  DROP TABLE IF EXISTS {schema}.generated_project;
`;
