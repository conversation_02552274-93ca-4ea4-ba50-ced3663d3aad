export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
  -- Update the exclude_from_interaction_count field to match the is_background_agent_message field.
  -- Only update if the vaules are different.
  UPDATE tribble.activity_log al
  SET exclude_from_interaction_count = cd.is_background_agent_message
  FROM {schema}.conversation c
  JOIN {schema}.conversation_detail cd ON c.id = cd.conversation_id
  JOIN tribble.client tc ON tc.database_schema_id = '{schema}'
  -- All the conditions...
  WHERE al.source_id = cd.id
  AND al.client_id = tc.id  -- Ensure the correct client_id is matched!
  AND c.system = 'ANSWER_RFP'
  AND c.channel = 'questionnaire'
  AND al.type = 'conversation'
  AND al.source_table = 'conversation_detail'
  AND al.event = 'answer_rfp'
  AND al.exclude_from_interaction_count != cd.is_background_agent_message;
`;

export const down = `
  -- NO UNDO!
  -- No specific down migration as reverting would require knowing the previous values
  -- which could vary across records
`;
