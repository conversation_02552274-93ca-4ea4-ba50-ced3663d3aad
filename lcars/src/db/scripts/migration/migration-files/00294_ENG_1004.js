export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
  ALTER TABLE {schema}.e2e_question_entry 
  ADD COLUMN IF NOT EXISTS question_context_range TEXT;

  ALTER TABLE {schema}.e2e_question_entry 
  DROP COLUMN IF EXISTS question_context;
`;

export const down = `
  ALTER TABLE {schema}.e2e_question_entry 
  DROP COLUMN IF EXISTS question_context_range;

  ALTER TABLE {schema}.e2e_question_entry 
  ADD COLUMN IF NOT EXISTS question_context TEXT;
`;
