export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
  ALTER TABLE {schema}.conversation_detail ADD COLUMN IF NOT EXISTS is_background_agent_message BOOLEAN DEFAULT FALSE;

  -- Any previously set ignoreConversationToSeq records: set is_background_agent_message to true
  UPDATE {schema}.conversation_detail
  SET is_background_agent_message = TRUE
  WHERE id IN (
    SELECT cd.id
    FROM {schema}.content c
    JOIN {schema}.rfx_bid_packet_file rbpf ON rbpf.content_id = c.id
    JOIN {schema}.rfx_response_document rrd ON rrd.bid_packet_file_id = rbpf.id
    JOIN {schema}.conversation_detail cd ON cd.conversation_id = rrd.conversation_id
    WHERE c.details->'agentParams'->'ignoreConversationToSeq' IS NOT NULL
    AND cd.seq <= (c.details->'agentParams'->>'ignoreConversationToSeq')::int
  );

  -- If conversation.system = UNKNOWN, set is_background_agent_message to true
  UPDATE {schema}.conversation_detail
  SET is_background_agent_message = TRUE
  WHERE conversation_id IN (
    SELECT id
    FROM {schema}.conversation
    WHERE system = 'UNKNOWN'
  );

  CREATE INDEX IF NOT EXISTS idx_conversation_detail_is_background_agent_msg
  ON {schema}.conversation_detail USING btree
  (is_background_agent_message ASC NULLS LAST)
  TABLESPACE pg_default;
  `;

export const down = `
  ALTER TABLE {schema}.conversation_detail DROP COLUMN IF EXISTS is_background_agent_message;
`;
