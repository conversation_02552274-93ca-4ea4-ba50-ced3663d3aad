export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
  -- Migration: Update rfx_bid_packet_file blob_storage_uuid with correct values
  WITH update_data AS (
      SELECT 
          rfx_bid_packet_file_id,
          -- if blobUuid is null, use rfpId
          COALESCE(blobUuid, rfpId) as new_blob_storage_uuid
      FROM (
          SELECT
              c.id as content_id,
              details -> 'files' as files,
              details ->> 'rfpId' as rfpId,
              details -> 'files' -> 0 ->> 'blobUuid' as blobUuid,
              rbpf.id as rfx_bid_packet_file_id,
              rbpf.blob_storage_uuid as rbpf_blob_storage_uuid,
              c.is_deleted as c_is_deleted
          FROM
              {schema}.content c
              JOIN {schema}.rfx_bid_packet_file rbpf ON rbpf.content_id = c.id
          -- Might as well update the deleted ones too
          -- WHERE COALESCE(c.is_deleted, false) != true
      ) subquery
  )
  UPDATE {schema}.rfx_bid_packet_file rbpf
  SET blob_storage_uuid = update_data.new_blob_storage_uuid
  FROM update_data
  WHERE rbpf.id = update_data.rfx_bid_packet_file_id
    AND update_data.new_blob_storage_uuid IS NOT NULL;
  `;

export const down = ``;
