export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];
export const up = `
  -- Create the user_proactive tables

  -- Create the messages table
  CREATE TABLE IF NOT EXISTS tribble.message (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    message TEXT NOT NULL,
    type TEXT NOT NULL,
    client_id INTEGER NOT NULL REFERENCES tribble.client(id),
    sender_id INTEGER NOT NULL REFERENCES tribble.user(id),
    is_test BOOLEAN NOT NULL DEFAULT FALSE,
    invoke_tribble BOOLEAN NOT NULL DEFAULT FALSE
  );

  -- Table to track the recipients of the messages
  CREATE TABLE IF NOT EXISTS tribble.message_recipient (
    id BIGSERIAL PRIMARY KEY,
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    message_id BIGINT NOT NULL REFERENCES tribble.message(id),
    recipient_id INTEGER NOT NULL REFERENCES tribble.user(id),
    status TEXT NOT NULL
  );

  ALTER TABLE tribble.user
  ADD COLUMN opted_out_at TIMESTAMP WITH TIME ZONE;
  `;

export const down = `
  ALTER TABLE tribble.user
  DROP COLUMN IF EXISTS opted_out_at;

  DROP TABLE IF EXISTS tribble.message_recipient;
  DROP TABLE IF EXISTS tribble.message;
  `;
