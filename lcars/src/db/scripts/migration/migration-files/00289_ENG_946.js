export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `
    CREATE TABLE IF NOT EXISTS {schema}.task_feedback_assignee (
        id SERIAL PRIMARY KEY,
        task_feedback_id INTEGER NOT NULL REFERENCES {schema}.task_feedback(id),
        assignee_id INTEGER NOT NULL REFERENCES tribble.user(id),
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        UNIQUE (task_feedback_id, assignee_id)
    );

    CREATE INDEX IF NOT EXISTS idx_task_feedback_assignee_task_feedback_id ON {schema}.task_feedback_assignee (task_feedback_id);
    CREATE INDEX IF NOT EXISTS idx_task_feedback_assignee_assignee_id ON {schema}.task_feedback_assignee (assignee_id);

    ALTER TABLE IF EXISTS {schema}.task_feedback
    ADD COLUMN IF NOT EXISTS category TEXT;

    ALTER TABLE IF EXISTS {schema}.task
    ADD COLUMN IF NOT EXISTS resolution_reason TEXT;

  DO $$
  DECLARE
    client_record RECORD;
    activity_record RECORD;
    new_task_id INTEGER;
  BEGIN
    -- Get client record to find the correct schema
    SELECT * INTO client_record 
    FROM tribble.client 
    WHERE database_schema_id = '{schema}';
    
    -- Loop through each qualifying activity log
    FOR activity_record IN (
        SELECT 
            al.id,
            al.user_id,
            al.source_id,
            cd.message->>'content' as answer,
            cd.input->>'content' as question
        FROM tribble.activity_log al
        JOIN {schema}.conversation_detail cd ON cd.id = al.source_id
        WHERE al.client_id = client_record.id
          AND al.source_table = 'conversation_detail'
          AND al.type = 'feedback'
          AND al.event = 'reaction'
          AND (al.details->>'reaction') = '-1'
          AND NOT EXISTS (
              -- Exclude records already processed into task_feedback
              SELECT 1
              FROM {schema}.task_feedback tf
              WHERE tf.source_conversation_detail_id = al.source_id
          )
    ) LOOP
        -- Create a new task
        INSERT INTO {schema}.task (
            label,
            created_by_id,
            status,
            origin,
            created_at
        ) VALUES (
            'Feedback: No details provided',
            activity_record.user_id,
            'not_started',
            'slack',
            CURRENT_TIMESTAMP
        ) RETURNING id INTO new_task_id;
        
        -- Create a task_feedback record linked to the new task
        INSERT INTO {schema}.task_feedback (
            task_id,
            source_conversation_detail_id,
            question,
            answer,
            score_enum,
            response
        ) VALUES (
            new_task_id,
            activity_record.source_id,
            activity_record.question,
            activity_record.answer,
            2,
            0
        );
        
    END LOOP;
    
    -- Log completion
    RAISE NOTICE 'Migration completed: Created tasks and feedback records for negative reactions';
  END $$;
  `;

export const down = `
    DROP TABLE IF EXISTS {schema}.task_feedback_assignee;

    DROP INDEX IF EXISTS {schema}.idx_task_feedback_assignee_task_feedback_id;
    DROP INDEX IF EXISTS {schema}.idx_task_feedback_assignee_assignee_id;

    ALTER TABLE IF EXISTS {schema}.task_feedback
    DROP COLUMN IF EXISTS category;

    ALTER TABLE IF EXISTS {schema}.task
    DROP COLUMN IF EXISTS resolution_reason;
  `;
