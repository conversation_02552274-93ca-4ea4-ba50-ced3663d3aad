export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `
    ALTER TABLE IF EXISTS {schema}.e2e_workbook ADD COLUMN IF NOT EXISTS content_id integer REFERENCES {schema}.content(id) ON DELETE CASCADE;
    ALTER TABLE IF EXISTS {schema}.e2e_sheet ADD COLUMN IF NOT EXISTS seq integer;
  `;
export const down = `
    ALTER TABLE IF EXISTS {schema}.e2e_workbook DROP COLUMN IF EXISTS content_id;
    ALTER TABLE IF EXISTS {schema}.e2e_sheet DROP COLUMN IF EXISTS seq;
  `;
