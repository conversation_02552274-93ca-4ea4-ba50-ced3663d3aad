export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `
    ALTER TABLE IF EXISTS {schema}.nlp_summary
    ADD COLUMN IF NOT EXISTS summary_embedding vector(1536);

    -- Add index on embedding for vector search
   CREATE INDEX IF NOT EXISTS idx_summary_embedding ON {schema}.nlp_summary USING ivfflat (summary_embedding) WITH (lists = 100);

   CREATE TABLE IF NOT EXISTS {schema}.document_metadata_embedding (
    id SERIAL PRIMARY KEY,
    document_id INT NOT NULL UNIQUE,
    label_embedding vector(1536) NOT NULL,
    FOREIGN KEY (document_id) REFERENCES {schema}.document(id) ON DELETE CASCADE
  );

  CREATE INDEX IF NOT EXISTS idx_document_metadata_embedding ON {schema}.document_metadata_embedding USING ivfflat (label_embedding) WITH (lists = 100);

  `;

export const down = `
    ALTER TABLE IF EXISTS {schema}.nlp_summary
    DROP COLUMN IF EXISTS summary_embedding;

    DROP INDEX IF EXISTS {schema}.idx_summary_embedding;

    DROP TABLE IF EXISTS {schema}.document_metadata_embedding;

    DROP INDEX IF EXISTS {schema}.idx_document_metadata_embedding;
  `;
