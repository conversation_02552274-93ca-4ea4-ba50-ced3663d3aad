export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
  -- Populate source_created_date for Gong embeddings by extracting timestamps from content_meta
  -- Similar to what was done for Slack in migration 00297
  
  -- For Gong calls, the timestamp is stored in the content_meta.call_date field as an ISO string
  UPDATE {schema}.embedding
  SET source_created_date = (content_meta->>'call_date')::timestamptz
  WHERE 
    content_type = 'GONG' 
    AND source_created_date IS NULL 
    AND content_meta->>'call_date' IS NOT NULL;
`;

export const down = `
  -- Remove source_created_date for Gong embeddings
  UPDATE {schema}.embedding
  SET source_created_date = NULL
  WHERE content_type = 'GONG' 
    AND source_created_date IS NOT NULL;
`;
