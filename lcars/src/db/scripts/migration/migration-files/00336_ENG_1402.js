// no more model overrides
export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `
    DELETE FROM {schema}.user_setting where setting_id in (select id from tribble.setting where name in ('user_rfp_model'));
    DELETE FROM {schema}.client_setting where setting_id in (select id from tribble.setting where name in ('client_agentic_model'));

    -- Upsert some settings
    -- Hintpills:
    INSERT INTO {schema}.client_setting (setting_id, value_boolean)
    VALUES
    ((SELECT id FROM tribble.setting WHERE name = 'enable_hint_pills'), true)
    ON CONFLICT (setting_id) DO NOTHING;

    -- Agent and tribblytics:
    INSERT INTO {schema}.client_setting (setting_id, value_boolean)
    VALUES
        ((SELECT id FROM tribble.setting WHERE name = 'client_uses_agentic_slackbot'), true),
        ((SELECT id FROM tribble.setting WHERE name = 'enable_tribblytics'), true)
    
    ON CONFLICT (setting_id) 
    DO UPDATE SET value_boolean = true;
    
  `;
export const down = `
  SELECT 'No down migration for this file';
  `;
