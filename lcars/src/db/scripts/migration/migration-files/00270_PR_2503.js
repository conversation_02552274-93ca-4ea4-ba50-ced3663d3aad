export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];
export const up = `
  INSERT INTO tribble.setting 
    (name, label, description, type, is_client, tribble_editable, default_string)
  VALUES 
    ('logger_toggles', 'Logger Toggles', 'Control which types of logs are enabled for debugging and monitoring', 'string', true, true, '{"default": true, "system": true}') 
  ON CONFLICT DO NOTHING;
`;
export const down = `
  DELETE FROM tribble.setting WHERE name = 'logger_toggles';
`;
