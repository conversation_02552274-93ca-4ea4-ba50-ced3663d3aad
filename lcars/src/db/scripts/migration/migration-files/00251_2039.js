export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];
export const up = `
  INSERT INTO tribble.setting 
    (name, label, description, type, is_client, tribble_editable, default_string)
  VALUES 
    ('project_fact_learning_pref', 'Project Fact Learning Preference', 'learn_none | learn_all | learn_approved', 'string', true, false, 'learn_none') 
  ON CONFLICT DO NOTHING;  
`;
export const down = `
  DELETE FROM tribble.setting WHERE name = 'project_fact_learning_pref';
`;
