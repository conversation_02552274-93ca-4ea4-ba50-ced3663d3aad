export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `
  ALTER TABLE {schema}.gong_calls
  ADD COLUMN IF NOT EXISTS processing_status VARCHAR(20),
  ADD COLUMN IF NOT EXISTS last_processing_started TIMESTAMP WITH TIME ZONE;

  -- Set default values based on last_ingested_at
  UPDATE {schema}.gong_calls 
  SET processing_status = CASE 
      WHEN last_ingested_at IS NOT NULL THEN 'completed'
      ELSE 'pending'
    END 
  WHERE processing_status IS NULL;

  CREATE INDEX IF NOT EXISTS idx_gong_calls_processing_status ON {schema}.gong_calls(processing_status);

  COMMENT ON COLUMN {schema}.gong_calls.processing_status IS 'Status of call processing: pending, processing, completed, or failed';
  COMMENT ON COLUMN {schema}.gong_calls.last_processing_started IS 'Timestamp when processing was last started';
  `;

export const down = `
  DROP INDEX IF EXISTS {schema}.idx_gong_calls_processing_status;
  
  ALTER TABLE {schema}.gong_calls
  DROP COLUMN IF EXISTS processing_status,
  DROP COLUMN IF EXISTS last_processing_started;
  `;
