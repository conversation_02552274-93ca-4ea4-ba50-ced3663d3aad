export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];
export const up = `INSERT INTO tribble.setting 
    (name, label, description, type, is_client, tribble_editable, default_boolean)
  VALUES 
    ('client_uses_sso', 'Client uses SSO', 'i.e. Do not include password reset link in welcome email', 'boolean', true, true, false) 
  ON CONFLICT DO NOTHING;`;

export const down = `DELETE FROM tribble.setting WHERE name = 'client_uses_sso';`;
