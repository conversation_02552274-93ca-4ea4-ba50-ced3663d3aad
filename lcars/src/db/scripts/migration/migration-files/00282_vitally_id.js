export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];
export const up = `ALTER TABLE tribble.client
    ADD COLUMN IF NOT EXISTS vitally_id UUID;
    
    
    ALTER TABLE tribble.user
    ADD COLUMN IF NOT EXISTS vitally_id UUID;
    
    CREATE INDEX IF NOT EXISTS idx_user_vitally_id ON tribble.user(vitally_id);`;

export const down = `
    ALTER TABLE tribble.client
    DROP COLUMN IF EXISTS vitally_id;
    ALTER TABLE tribble.user
    DROP COLUMN IF EXISTS vitally_id;
    
    DROP INDEX IF EXISTS idx_user_vitally_id;
`;
