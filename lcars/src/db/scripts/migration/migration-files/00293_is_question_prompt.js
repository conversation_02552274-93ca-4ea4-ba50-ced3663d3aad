export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];
export const up = `INSERT INTO tribble.setting (name, label, description, type, is_client, tribble_editable)
VALUES ('client_is_question_prompt', 'Is Question Prompt', 'The system prompt used to determine if a message is a question', 'string', true, true) ON CONFLICT DO NOTHING;`;
export const down = `DELETE FROM tribble.setting WHERE name = 'client_is_question_prompt';`;
