export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `
    ALTER TABLE IF EXISTS {schema}.nlp_category_source
      DROP CONSTRAINT IF EXISTS check_single_source_type;

    ALTER TABLE IF EXISTS {schema}.nlp_category_source
        ADD CONSTRAINT check_single_source_type CHECK (
          (content_detail_id IS NOT NULL)::INTEGER + 
          (conversation_detail_id IS NOT NULL)::INTEGER + 
          (document_id IS NOT NULL)::INTEGER + 
          (content_id IS NOT NULL)::INTEGER + 
          (conversation_id IS NOT NULL)::INTEGER + 
          (embedding_id IS NOT NULL)::INTEGER = 1
        );
  `;
