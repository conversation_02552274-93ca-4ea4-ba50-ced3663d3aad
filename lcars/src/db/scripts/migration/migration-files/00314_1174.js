export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];
export const up = `
  -- Step 1: Add the new columns
ALTER TABLE tribble.client_teleportation 
ADD COLUMN source_org_id int REFERENCES tribble.client(id) ON DELETE CASCADE,
ADD COLUMN target_org_id int REFERENCES tribble.client(id) ON DELETE CASCADE;

-- Step 2: Populate the new columns with data for both directions
-- First, insert the original direction (org_id_1 -> org_id_2)
UPDATE tribble.client_teleportation 
SET source_org_id = org_id_1, 
    target_org_id = org_id_2;

-- Step 3: Insert the reverse direction as new rows
-- We need to temporarily disable the primary key constraint
ALTER TABLE tribble.client_teleportation 
DROP CONSTRAINT client_teleportation_pkey,
DROP CONSTRAINT IF EXISTS client_teleportation_check;

-- Insert reverse direction records
INSERT INTO tribble.client_teleportation (org_id_1, org_id_2, source_org_id, target_org_id, created_at)
SELECT org_id_2, org_id_1, org_id_2, org_id_1, created_at
FROM tribble.client_teleportation
WHERE source_org_id IS NOT NULL;

-- Step 4: Make the new columns NOT NULL
ALTER TABLE tribble.client_teleportation 
ALTER COLUMN source_org_id SET NOT NULL,
ALTER COLUMN target_org_id SET NOT NULL;

-- Step 6: Add unique constraint on (source_org_id, target_org_id)
ALTER TABLE tribble.client_teleportation
ADD CONSTRAINT unique_source_target UNIQUE (source_org_id, target_org_id);

-- Step 7: Drop the old columns and constraints
ALTER TABLE tribble.client_teleportation 
DROP CONSTRAINT IF EXISTS tribble_client_teleportation_org_id_1_fkey,
DROP CONSTRAINT IF EXISTS tribble_client_teleportation_org_id_2_fkey,
DROP COLUMN org_id_1,
DROP COLUMN org_id_2;

-- Step 8: Create a new primary key on id (since we removed the composite primary key)
ALTER TABLE tribble.client_teleportation
ADD PRIMARY KEY (id);
  `;
