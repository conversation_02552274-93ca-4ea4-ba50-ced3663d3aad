export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
  CREATE TABLE IF NOT EXISTS {schema}.slack_auto_reply_message
  (
    id SERIAL PRIMARY KEY,
    channel_id TEXT NOT NULL,
    message_ts TEXT NOT NULL,
    platform_user_id TEXT NOT NULL,
    user_id INTEGER NOT NULL REFERENCES tribble.user(id),
    message_text TEXT NOT NULL,
    scheduled_reply_time TIMESTAMP WITH TIME ZONE NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    response_text TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP WITH TIME ZONE,
    FOREIGN KEY (channel_id) REFERENCES {schema}.slack_auto_reply_channel(channel_id),
    UNIQUE(channel_id, message_ts, platform_user_id)
  );

  CREATE INDEX IF NOT EXISTS idx_slack_auto_reply_message_status 
  ON {schema}.slack_auto_reply_message(status);

  CREATE INDEX IF NOT EXISTS idx_slack_auto_reply_message_scheduled_time 
  ON {schema}.slack_auto_reply_message(scheduled_reply_time);
`;

export const down = `
  DROP TABLE IF EXISTS {schema}.slack_auto_reply_message;
`;
