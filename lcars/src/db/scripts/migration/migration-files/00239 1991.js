export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `
    CREATE TABLE IF NOT EXISTS {schema}.rfx_bid_packet_file (
      id SERIAL PRIMARY KEY,
      blob_storage_uuid TEXT,
      file_name TEXT NOT NULL,
      paragraphs JSONB default '[]'::jsonb,
      pages JSONB default '[]'::jsonb,
      content TEXT,
      mime_type TEXT
    );

    ALTER TABLE {schema}.conversation_state ADD COLUMN IF NOT EXISTS 
    files JSONB default '[]'::jsonb;
  `;

export const down = `
    DROP TABLE IF EXISTS {schema}.rfx_bid_packet_file;
    ALTER TABLE {schema}.conversation_state DROP COLUMN IF EXISTS files;
  `;
