export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
    CREATE TABLE IF NOT EXISTS {schema}.access_sequence (
        id SERIAL PRIMARY KEY,
        tier INTEGER NOT NULL,
        type TEXT NOT NULL,
        value TEXT NOT NULL,
        is_single_source BOOLEAN NOT NULL DEFAULT false,
        agent_type TEXT,
        document_id INTEGER REFERENCES {schema}.document(id),
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        created_by INTEGER REFERENCES tribble.user(id),
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_by INTEGER REFERENCES tribble.user(id)
    );

    CREATE INDEX IF NOT EXISTS idx_access_sequence_tier ON {schema}.access_sequence (tier);
    CREATE INDEX IF NOT EXISTS idx_access_sequence_type ON {schema}.access_sequence (type);
    CREATE INDEX IF NOT EXISTS idx_access_sequence_value ON {schema}.access_sequence (value);
    CREATE INDEX IF NOT EXISTS idx_access_sequence_agent_type ON {schema}.access_sequence (agent_type);
    CREATE INDEX IF NOT EXISTS idx_access_sequence_document_id ON {schema}.access_sequence (document_id);
    CREATE INDEX IF NOT EXISTS idx_access_sequence_created_by ON {schema}.access_sequence (created_by);
`;

export const down = `
    DROP INDEX IF EXISTS {schema}.idx_access_sequence_created_by;
    DROP INDEX IF EXISTS {schema}.idx_access_sequence_document_id;
    DROP INDEX IF EXISTS {schema}.idx_access_sequence_agent_type;
    DROP INDEX IF EXISTS {schema}.idx_access_sequence_value;
    DROP INDEX IF EXISTS {schema}.idx_access_sequence_type;
    DROP INDEX IF EXISTS {schema}.idx_access_sequence_tier;
    DROP TABLE IF EXISTS {schema}.access_sequence;
`;
