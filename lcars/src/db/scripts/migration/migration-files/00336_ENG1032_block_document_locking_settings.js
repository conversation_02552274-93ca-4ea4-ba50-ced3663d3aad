export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];

export const up = `
    -- Add locking settings to tribble settings
    INSERT INTO tribble.setting (name, label, description, type, is_client, tribble_editable, default_boolean)
    VALUES ('block_doc.locking.enabled', 'Block Document Locking', 'Enable document locking to prevent editing conflicts', 'boolean', true, true, true) 
    ON CONFLICT (name) DO NOTHING;

    INSERT INTO tribble.setting (name, label, description, type, is_client, tribble_editable, default_number)
    VALUES ('block_doc.locking.timeout_milliseconds', 'Block Document Lock Timeout', 'Duration in milliseconds before a document lock expires', 'number', true, true, 300000) 
    ON CONFLICT (name) DO NOTHING;
`;

export const down = `
    DELETE FROM tribble.setting WHERE name IN (
        'block_doc.locking.enabled',
        'block_doc.locking.timeout_milliseconds'
    );
`;
