// Add client setting for controlling scheduled Gong sync
export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];
export const up = `INSERT INTO tribble.setting (name, label, description, type, is_client, tribble_editable, default_boolean)
VALUES ('client_gong_scheduled_sync_enabled', 'Gong Scheduled Sync Enabled', 'Whether scheduled Gong integration sync is enabled for this client', 'boolean', true, false, false) ON CONFLICT DO NOTHING;`;
export const down = `DELETE FROM tribble.setting WHERE name = 'client_gong_scheduled_sync_enabled';`;
