export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
  UPDATE {schema}.job 
  SET status = 'queued', 
      error = NULL,
      updated_at = CURRENT_TIMESTAMP
  WHERE status = 'error' 
    AND error LIKE 'Parallel processing failed for document%'
    AND created_date >= CURRENT_DATE - INTERVAL '10 days';
`;

export const down = `
  -- Cannot reliably rollback without storing original error messages
  -- This would require a more complex migration with temporary storage
  SELECT 'No rollback available for this migration' as message;
`;
