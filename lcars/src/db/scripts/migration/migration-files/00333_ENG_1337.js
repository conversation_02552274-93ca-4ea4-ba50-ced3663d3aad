// Insert hard coded categories

export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `
    INSERT INTO {schema}.workflow_category (name, label)
    VALUES
      ('meet', '🤝 Meet'),
      ('build', '🏗️ Build'),
      ('analyze', '📊 Analyze'),
      ('win', '🏆 Win');

      -- Update existing workflows to have a category
      UPDATE {schema}.workflow 
      SET workflow_category_id = (SELECT id FROM {schema}.workflow_category WHERE name = 'meet')
      WHERE workflow_category_id IS NULL;
  `;

export const down = `
    DELETE FROM {schema}.workflow_category
    WHERE name IN ('meet', 'build', 'analyze', 'win');`;
