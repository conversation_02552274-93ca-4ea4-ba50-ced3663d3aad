export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
    -- Update embedding records with source dates from zendesk_asset
    -- Using za.last_modified as the source_modified_date, since that seems to have come from article.updated_at
    WITH source_data AS (
      SELECT 
        e.id as embedding_id,
        za.last_modified
      FROM {schema}.embedding e
      JOIN {schema}.document d ON e.document_id = d.id
      JOIN {schema}.zendesk_asset za ON d.id = za.document_id
      WHERE e.source_modified_date IS NULL
        AND (za.last_modified IS NOT NULL)
    )
    UPDATE {schema}.embedding e
    SET 
      source_modified_date = sd.last_modified
    FROM source_data sd
    WHERE e.id = sd.embedding_id;
  `;

export const down = `
    -- No down migration needed - this is a data migration
  `;
