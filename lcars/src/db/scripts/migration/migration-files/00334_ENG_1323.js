export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
    UPDATE {schema}.rfx_content 
    SET output_type = CASE 
        -- very very likely longform
        WHEN c.details->'agentParams'->>'longForm' = 'true' THEN 'longform'
        -- very very likely spreadsheet
        WHEN c.details->>'fileName' LIKE '%.xlsx' THEN 'spreadsheet'
        -- unsure? leave NULL
        ELSE NULL
    END
    FROM {schema}.content c
    WHERE rfx_content.content_id = c.id
      AND rfx_content.output_type IS NULL;
  `;

export const down = `
    -- This data migration is sort of irreversible.
    -- Although if we really want to, everything created before June 30 2025 should have output_type = NULL
  `;
