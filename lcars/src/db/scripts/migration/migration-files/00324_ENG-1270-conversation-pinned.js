export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
  ALTER TABLE IF EXISTS {schema}.conversation_pinned
    ADD COLUMN IF NOT EXISTS conversation_detail_id INTEGER;
  
  ALTER TABLE IF EXISTS {schema}.conversation_pinned
    ADD CONSTRAINT fk_conversation_pinned_conversation_detail_id 
      FOREIGN KEY (conversation_detail_id) REFERENCES {schema}.conversation_detail(id) ON DELETE CASCADE;
`;

export const down = `
  ALTER TABLE IF EXISTS {schema}.conversation_pinned
  DROP COLUMN IF EXISTS conversation_detail_id;
  DROP CONSTRAINT IF EXISTS fk_conversation_pinned_conversation_detail_id;
`;
