export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];
export const up = `INSERT INTO tribble.setting (name, label, description, type, is_client, tribble_editable, default_string)
VALUES ('gong_batch_sizes', 'Batch Sizes', 'JSON configuration for Gong batch processing sizes', 'string', true, true, '{"callBatchSize": 25, "callQueryLimit": 25, "maxApiDurationMs": 900000, "vectorConcurrency": 3, "embeddingConcurrency": 5}') ON CONFLICT DO NOTHING;`;
export const down = `DELETE from tribble.setting where name = 'gong_batch_sizes'`;
