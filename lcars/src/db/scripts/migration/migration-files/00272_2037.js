export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
  ALTER TABLE IF EXISTS {schema}.rfx
    ADD COLUMN IF NOT EXISTS opportunity_id TEXT,
    ADD COLUMN IF NOT EXISTS modified_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ADD COLUMN IF NOT EXISTS modified_by INTEGER REFERENCES tribble.user(id);
`;

export const down = `
  ALTER TABLE IF EXISTS {schema}.rfx
    DROP COLUMN IF EXISTS opportunity_id,
    DROP COLUMN IF EXISTS modified_at,
    DROP COLUMN IF EXISTS modified_by;
`;
