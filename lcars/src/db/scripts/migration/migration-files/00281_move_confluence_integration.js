export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `
  -- Get the details of the most recent Confluence user_integration for each schema
  WITH most_recent_confluence_integrations AS (
    SELECT 
      ui.*,
      ROW_NUMBER() OVER (PARTITION BY integration_system_id ORDER BY created_at DESC) as rn
    FROM {schema}.user_integration ui
    JOIN tribble.integration_system is_sys ON ui.integration_system_id = is_sys.id
    WHERE is_sys.system_name = 'confluence'
  ),
  
  -- Insert into client_integration if one doesn't already exist
  inserted_records AS (
    INSERT INTO {schema}.client_integration 
      (integration_system_id, auth_details, created_by, created_at)
    SELECT 
      integration_system_id, 
      auth_details, 
      user_id AS created_by, 
      created_at
    FROM most_recent_confluence_integrations
    WHERE rn = 1
    AND NOT EXISTS (
      SELECT 1 FROM {schema}.client_integration ci 
      WHERE ci.integration_system_id = most_recent_confluence_integrations.integration_system_id
    )
    RETURNING integration_system_id
  )
  
  -- Delete all Confluence user_integration records
  DELETE FROM {schema}.user_integration
  WHERE id IN (
    SELECT ui.id 
    FROM {schema}.user_integration ui
    JOIN tribble.integration_system is_sys ON ui.integration_system_id = is_sys.id
    WHERE is_sys.system_name = 'confluence'
  );
`;

export const down = `
  -- This migration is not easily reversible as we can't determine which client_integration records were created by this migration
  -- and the deleted user_integration records are permanently lost
  -- No down migration provided to prevent accidental data loss
`;
