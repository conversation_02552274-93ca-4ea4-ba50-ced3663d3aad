export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
  ALTER TABLE {schema}.teams_conversation_reference 
  ADD COLUMN IF NOT EXISTS user_id integer REFERENCES tribble.user(id);

  CREATE INDEX IF NOT EXISTS idx_teams_conversation_reference_user_id 
  ON {schema}.teams_conversation_reference(user_id);
`;

export const down = `
  DROP INDEX IF EXISTS {schema}.idx_teams_conversation_reference_user_id;
  ALTER TABLE {schema}.teams_conversation_reference 
  DROP COLUMN IF EXISTS user_id;
`;
