export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];
export const up = `INSERT INTO tribble.setting (name, label, description, type, is_client, tribble_editable, default_string)
VALUES ('kg_entity_types', 'Entity Types', 'JSON array of entity types to recognize in the knowledge graph.', 'string', true, true, '["person", "organization", "product", "location", "concept", "technology", "event", "opportunity"]') ON CONFLICT DO NOTHING;`;
export const down = `
DO $$
DECLARE 
    schema_record record;
    query text;
BEGIN
    FOR schema_record IN (
        SELECT schema_name 
        FROM information_schema.schemata 
        WHERE schema_name LIKE 'c00%'
        AND schema_name NOT LIKE '%_custom'
        AND schema_name NOT LIKE '%_integration'
    )
    LOOP
        query := format(
            'DELETE FROM  %1$s.client_setting where setting_id in (select id from tribble.setting where name = ''kg_entity_types'')',
            schema_record.schema_name
        );
        EXECUTE query;
    END LOOP;
END
$$;

DELETE FROM tribble.setting WHERE name = 'kg_entity_types';`;
