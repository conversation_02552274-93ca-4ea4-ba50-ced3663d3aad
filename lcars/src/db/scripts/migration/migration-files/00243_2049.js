export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
  ALTER TABLE {schema}.rfx_response_document
  ADD COLUMN IF NOT EXISTS conversation_id INTEGER REFERENCES {schema}.conversation(id) ON DELETE SET NULL,
  ADD COLUMN IF NOT EXISTS bid_packet_file_id INTEGER REFERENCES {schema}.rfx_bid_packet_file(id) ON DELETE CASCADE;

  -- Update existing foreign key on rfx_section_outline to include ON DELETE CASCADE
  ALTER TABLE {schema}.rfx_section_outline 
  DROP CONSTRAINT IF EXISTS rfx_section_outline_rfx_response_document_id_fkey,
  ADD CONSTRAINT rfx_section_outline_rfx_response_document_id_fkey 
    FOREIGN KEY (rfx_response_document_id) 
    REFERENCES {schema}.rfx_response_document(id) 
    ON DELETE CASCADE;
`;

export const down = `
  ALTER TABLE {schema}.rfx_response_document
  DROP COLUMN IF EXISTS conversation_id,
  DROP COLUMN IF EXISTS bid_packet_file_id;

  -- without ON DELETE CASCADE
  ALTER TABLE {schema}.rfx_section_outline 
  DROP CONSTRAINT IF EXISTS rfx_section_outline_rfx_response_document_id_fkey,
  ADD CONSTRAINT rfx_section_outline_rfx_response_document_id_fkey 
    FOREIGN KEY (rfx_response_document_id) 
    REFERENCES {schema}.rfx_response_document(id);
`;
