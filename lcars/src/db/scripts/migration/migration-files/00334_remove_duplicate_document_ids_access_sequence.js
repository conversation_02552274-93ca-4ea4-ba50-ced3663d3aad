export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

// Remove duplicate document_ids in access_sequence table and add unique constraint
// Keep the earliest created entry for each unique combination of (type, value, agent_type)

export const up = `
    -- Remove duplicates in access_sequence table
    -- Keep only the earliest created entry for each unique combination of (type, value, agent_type)
    -- Use COALESCE to treat NULL agent_type the same as empty string, consistent with the unique constraint
    DELETE FROM {schema}.access_sequence
    WHERE id NOT IN (
        SELECT DISTINCT ON (type, value, COALESCE(agent_type, '')) id
        FROM {schema}.access_sequence
        ORDER BY type, value, COALESCE(agent_type, ''), created_at ASC
    );
    
    -- Add unique constraint to prevent future duplicates
    -- This ensures unique combination of (type, value, agent_type)
    -- Note: agent_type can be NULL, so we need to handle that in the constraint
    CREATE UNIQUE INDEX IF NOT EXISTS idx_access_sequence_unique_entry 
    ON {schema}.access_sequence (type, value, COALESCE(agent_type, ''));
`;

export const down = `
    -- Remove the unique constraint
    DROP INDEX IF EXISTS {schema}.idx_access_sequence_unique_entry;
    
    -- This migration cannot be reversed as we're deleting duplicate data
    -- Manual intervention would be required to restore duplicates if needed
`;
