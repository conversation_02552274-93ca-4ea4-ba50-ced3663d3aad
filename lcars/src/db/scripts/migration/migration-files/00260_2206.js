export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
  CREATE TABLE IF NOT EXISTS {schema}.slack_auto_reply_channel (
      id SERIAL PRIMARY KEY,
      channel_id TEXT NOT NULL UNIQUE,
      channel_name TEXT NOT NULL,
      response_delay_seconds INTEGER NOT NULL DEFAULT 300,
      is_active BOOLEAN NOT NULL DEFAULT true,
      created_by INTEGER NOT NULL REFERENCES tribble.user(id),
      updated_by INTEGER NOT NULL REFERENCES tribble.user(id),
      created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
  );
`;

export const down = `
  DROP TABLE IF EXISTS {schema}.slack_auto_reply_channel;
`;
