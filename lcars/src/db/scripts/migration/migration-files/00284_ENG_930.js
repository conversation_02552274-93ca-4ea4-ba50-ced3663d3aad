export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
  ALTER TABLE {schema}.document ADD COLUMN IF NOT EXISTS description TEXT;
  ALTER TABLE {schema}.document_metadata_embedding ADD COLUMN IF NOT EXISTS type TEXT;

  -- Migrate all existing metadata_embedding records to use "label" as the type
  UPDATE {schema}.document_metadata_embedding
  SET type = 'label'
  WHERE type IS NULL;

  -- Drop the unique constraint on document_id
  ALTER TABLE IF EXISTS {schema}.document_metadata_embedding
    DROP CONSTRAINT IF EXISTS document_metadata_embedding_document_id_key;

  -- Drop the constraint if it exists, then add it
  ALTER TABLE IF EXISTS {schema}.document_metadata_embedding
    DROP CONSTRAINT IF EXISTS document_metadata_embedding_document_id_type_unique;
  ALTER TABLE IF EXISTS {schema}.document_metadata_embedding
    ADD CONSTRAINT document_metadata_embedding_document_id_type_unique UNIQUE (document_id, type);

  CREATE INDEX IF NOT EXISTS idx_document_metadata_embedding_document_id
    ON {schema}.document_metadata_embedding USING btree
    (document_id ASC NULLS LAST)
    TABLESPACE pg_default;

  CREATE INDEX IF NOT EXISTS idx_document_metadata_embedding_type
    ON {schema}.document_metadata_embedding USING btree
    (type ASC NULLS LAST)
    TABLESPACE pg_default;
`;

export const down = `
  ALTER TABLE {schema}.document DROP COLUMN IF EXISTS description;
  ALTER TABLE {schema}.document_metadata_embedding DROP COLUMN IF EXISTS type;

  -- Re-drop as there's no ADD CONSTRAINT IF NOT EXISTS... possible
  ALTER TABLE IF EXISTS {schema}.document_metadata_embedding
    DROP CONSTRAINT IF EXISTS document_metadata_embedding_document_id_key;

  ALTER TABLE IF EXISTS {schema}.document_metadata_embedding
    ADD CONSTRAINT document_metadata_embedding_document_id_key UNIQUE (document_id);

  ALTER TABLE IF EXISTS {schema}.document_metadata_embedding
    DROP CONSTRAINT IF EXISTS document_metadata_embedding_document_id_type_unique;
`;
