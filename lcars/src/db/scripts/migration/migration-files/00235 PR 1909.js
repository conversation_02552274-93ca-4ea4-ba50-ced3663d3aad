export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];
export const up = `
  INSERT INTO tribble.setting 
    (name, label, description, type, is_client, tribble_editable, default_string)
  VALUES 
    ('welcome_email_message', 'Welcome Email Message', 'Welcome email message for newly created users', 'string', true, true, 'Welcome to Tribble!
    
    A new user account has been created for you in the Tribble platform.
    
    With Tribble, you can generate responses to RFP questions, ask about product features, and more!
    
    Your user is registered with the following email: {{email}}.
    To login, visit: {{tribble_url}}.
    
    Regards,
    Tribble') 
  ON CONFLICT DO NOTHING;
`;
export const down = `
  DELETE FROM tribble.setting WHERE name = 'welcome_email_message';
`;
