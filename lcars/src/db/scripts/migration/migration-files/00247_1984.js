export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `
    CREATE TABLE IF NOT EXISTS  {schema}.rfx_writer_output (
      id BIGSERIAL PRIMARY KEY,
      passages JSONB default '[]'::jsonb,
      unmet_requirements JSONB default '[]'::jsonb,
      rfx_section_outline_id int REFERENCES {schema}.rfx_section_outline(id) ON DELETE SET NULL,
      rfx_writer_prompt_id int REFERENCES {schema}.rfx_writer_prompt(id) ON DELETE SET NULL
    )
  `;

export const down = `DROP TABLE IF EXISTS {schema}.rfx_writer_output`;
