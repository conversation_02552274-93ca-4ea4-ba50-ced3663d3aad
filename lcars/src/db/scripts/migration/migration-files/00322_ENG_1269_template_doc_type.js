export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

// Note: special document type for Corporate Template assets.
// They will always have document.use_for_generation = false, as well as embedding records the same.
export const up = `
    INSERT INTO {schema}.document_type(name, priority_id, is_rfp, is_system)
    SELECT 'Corporate Template', dtp.id, false, true
    FROM   {schema}.document_type_priority dtp
    WHERE  dtp.name = 'DEFAULT'
      AND NOT EXISTS (SELECT 1 FROM {schema}.document_type WHERE name='Corporate Template');
  `;

export const down = `
    DELETE FROM {schema}.document_type
    WHERE name = 'Corporate Template';
    `;
