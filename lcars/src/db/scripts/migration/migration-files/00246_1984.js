export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `
    CREATE TABLE IF NOT EXISTS  {schema}.rfx_writer_prompt (
      id BIGSERIAL PRIMARY KEY,
      rfx_section_outline_id int REFERENCES {schema}.rfx_section_outline(id) ON DELETE SET NULL,
      prompt text,
      requirements JSONB default '[]'::jsonb
    )
  `;

export const down = `DROP TABLE IF EXISTS {schema}.rfx_writer_prompt;`;
