export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];

export const up = `
  INSERT INTO tribble.setting 
    (name, label, description, type, is_client, tribble_editable, default_string)
  VALUES 
    ('slack_auto_reply_tags', 'Auto-Reply Tags', 'When <PERSON><PERSON> automatically responds to messages in Slack, these tags will be used to focus the response', 'string', true, false, '')
  ON CONFLICT DO NOTHING;
`;

export const down = `
  DELETE FROM tribble.setting WHERE name = 'slack_auto_reply_tags';
`;
