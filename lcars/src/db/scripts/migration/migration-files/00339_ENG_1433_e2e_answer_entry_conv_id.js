export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
  ALTER TABLE IF EXISTS {schema}.e2e_answer_entry 
  ADD COLUMN IF NOT EXISTS question_string TEXT,
  ADD COLUMN IF NOT EXISTS conversation_id INT REFERENCES {schema}.conversation(id) ON DELETE SET NULL;
`;

export const down = `
  ALTER TABLE IF EXISTS {schema}.e2e_answer_entry 
  DROP COLUMN IF EXISTS question_string,
  DROP COLUMN IF EXISTS conversation_id;
`;
