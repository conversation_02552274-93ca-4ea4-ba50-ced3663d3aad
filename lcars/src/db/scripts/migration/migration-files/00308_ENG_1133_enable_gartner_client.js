export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];
export const up = `INSERT INTO tribble.setting (name, label, description, type, default_boolean, is_client, tribble_editable)
VALUES ('enable_gartner_e2e', 'Enable Gartner Style E2E XLSX', 'When enabled, client users can analyze an XLSX as a Gartner-style file (this should be set only for applicable clients)', 'boolean', false, true, true) ON CONFLICT DO NOTHING;`;
export const down = `DELETE FROM tribble.setting WHERE name = 'enable_gartner_e2e';`;
