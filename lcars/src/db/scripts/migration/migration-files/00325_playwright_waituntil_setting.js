export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];

export const up = `
INSERT INTO tribble.setting (name, label, description, type, default_string, is_client, tribble_editable)
VALUES (
  'browser_waituntil_option', 
  'Browser WaitUntil Option', 
  'Specifies the wait condition for browser page loads (Playwright/Puppeteer). Options: "networkidle" (wait for network to be idle) or "load" (wait for load event).', 
  'string', 
  'networkidle', 
  true, 
  true
) ON CONFLICT DO NOTHING;`;

export const down = `DELETE FROM tribble.setting WHERE name = 'browser_waituntil_option';`;
