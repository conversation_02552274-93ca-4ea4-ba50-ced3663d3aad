export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
    ALTER TABLE IF EXISTS {schema}.e2e_answer_entry
      ADD COLUMN IF NOT EXISTS modified_text TEXT,
      ADD COLUMN IF NOT EXISTS last_modified_by INTEGER REFERENCES tribble.user(id) ON DELETE SET NULL;
  `;

export const down = `
    ALTER TABLE IF EXISTS {schema}.e2e_answer_entry
      DROP COLUMN IF EXISTS modified_text,
      DROP COLUMN IF EXISTS last_modified_by;
  `;
