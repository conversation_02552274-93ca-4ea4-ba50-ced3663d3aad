export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `

  INSERT INTO {schema}.nlp_task (name, enabled, description) 
  VALUES ('TEXT_CAT_CONVERSATION_ANSWER_STATUS', false, 'Task for classifying conversation turns into CONVERSATION_ANSWER_STATUS categories')
  ON CONFLICT (name, version) DO NOTHING;

  INSERT INTO {schema}.nlp_category_set (name, description, max_categories_per_content)
  VALUES ('CONVERSATION_ANSWER_STATUS', 'Categories designed to categorize conversation turns based on the answer''s completeness.', 1)  
  ON CONFLICT (name, version) DO NOTHING;

  -- Get the id of the 'CONVERSATION_ANSWER_STATUS' category set and add categories with descriptions
  DO $$
  DECLARE
      conversation_answer_status_id INT;
  BEGIN
      SELECT id INTO conversation_answer_status_id FROM {schema}.nlp_category_set WHERE name = 'CONVERSATION_ANSWER_STATUS';  

      INSERT INTO {schema}.nlp_category (name, category_set_id, description) VALUES
      ('Can Answer', conversation_answer_status_id, 'A clear answer is provided, indicating whether the requirement is supported (fully, partially, or not at all).'),
      ('Cannot Answer', conversation_answer_status_id, 'The system cannot provide an answer due to insufficient knowledge, complexity, or ambiguity.'),
      ('Partial Answer', conversation_answer_status_id, 'A partial answer is provided, but more information or further clarification is needed.'),
      ('Non-Disclosure', conversation_answer_status_id, 'The information is known but cannot be shared due to confidentiality or policy restrictions.'),
      ('Not Applicable', conversation_answer_status_id, 'If none of the other categories apply, this category is used. When the question or answer contains irrelevant, corrupted, gibberish, or empty text, or when it does not align with any of the specified categories.')
      ON CONFLICT (name, category_set_id) DO NOTHING;
  END $$;
  
`;

export const down = `
  -- Remove categories associated with the 'CONVERSATION_ANSWER_STATUS' category set
  DO $$
  DECLARE
      conversation_answer_status_id INT;
  BEGIN
      SELECT id INTO conversation_answer_status_id FROM {schema}.nlp_category_set WHERE name = 'CONVERSATION_ANSWER_STATUS';

      IF conversation_answer_status_id IS NOT NULL THEN
          DELETE FROM {schema}.nlp_category WHERE category_set_id = conversation_answer_status_id;
      END IF;
  END $$;

  -- Remove the 'CONVERSATION_ANSWER_STATUS' category set
  DELETE FROM {schema}.nlp_category_set WHERE name = 'CONVERSATION_ANSWER_STATUS';

  -- Remove the 'TEXT_CAT_CONVERSATION_ANSWER_STATUS' NLP task
  DELETE FROM {schema}.nlp_task WHERE name = 'TEXT_CAT_CONVERSATION_ANSWER_STATUS';
`;
