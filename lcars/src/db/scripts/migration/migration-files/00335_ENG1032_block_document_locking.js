export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
    CREATE TABLE IF NOT EXISTS {schema}.block_document_lock (
        id BIGSERIAL PRIMARY KEY,
        block_document_id INTEGER NOT NULL REFERENCES {schema}.block_document(id) ON DELETE CASCADE,
        locked_by INTEGER NOT NULL REFERENCES tribble.user(id),
        locked_at TIMESTAMPTZ NOT NULL DEFAULT now(),
        expires_at TIMESTAMPTZ NOT NULL,
        created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
        updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
    );

    CREATE INDEX IF NOT EXISTS idx_block_document_lock_document_id ON {schema}.block_document_lock(block_document_id);
    CREATE INDEX IF NOT EXISTS idx_block_document_lock_expires_at ON {schema}.block_document_lock(expires_at);
    CREATE INDEX IF NOT EXISTS idx_block_document_lock_document_expires ON {schema}.block_document_lock(block_document_id, expires_at);
`;

export const down = `
    DROP TABLE IF EXISTS {schema}.block_document_lock;
`;
