export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
  -- Populate source_created_date for Slack embeddings by extracting timestamps from content_meta
  -- This is similar to what was done for Google Drive in migration 00295
  
  -- For Slack messages, the timestamp (ts) is stored in the content_meta.messages array
  -- Although sometimes it is not reliably the first message in the array...
  -- But perhaps this is the best we can do for now...
  UPDATE {schema}.embedding
  SET source_created_date = to_timestamp((content_meta -> 'messages' -> 0 ->> 'float_ts')::float)
  WHERE 
    content_type = 'SLACK' 
    AND source_created_date IS NULL 
    AND content_meta -> 'messages' -> 0 ->> 'float_ts' IS NOT NULL;
`;

export const down = `
  -- Remove source_created_date for Slack embeddings
  UPDATE {schema}.embedding
  SET source_created_date = NULL
  WHERE content_type = 'SLACK' 
    AND source_created_date IS NOT NULL;
`;
