export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];

export const up = `
  -- ============================================================================
  -- MIGRATION DISABLED - RUN MANUALLY
  -- ============================================================================
  -- Uncomment the SQL statements below when ready to run manually
  
  -- -- ============================================================================
  -- -- INDEX 1: Composite index for analytics queries (HIGHEST PRIORITY)
  -- -- ============================================================================
  -- -- Purpose: Optimizes the most common query pattern in the analytics endpoint
  -- -- Query pattern it helps:
  -- --   WHERE client_id = ? AND date_timestamp >= ? AND date_timestamp <= ?
  -- --   AND source_table = ? AND event IN (?) AND exclude_from_interaction_count = false
  -- -- 
  -- -- Benefits:
  -- -- 1. Reduces query time from O(n) full table scan to O(log n) index scan
  -- -- 2. Column order (client_id, date_timestamp, source_table, event) matches the
  -- --    selectivity pattern - client_id is most selective, then date range
  -- -- 3. Partial index (WHERE exclude_from_interaction_count = false) reduces 
  -- --    index size by ~30% based on typical data patterns
  -- -- 4. For a message with 1,000 recipients, this can reduce analytics query 
  -- --    time from 6+ seconds to <100ms
  -- CREATE INDEX IF NOT EXISTS idx_activity_log_analytics 
  -- ON tribble.activity_log (client_id, date_timestamp, source_table, event)
  -- WHERE exclude_from_interaction_count = false;

  -- -- ============================================================================
  -- -- INDEX 2: User-specific activity queries
  -- -- ============================================================================
  -- -- Purpose: Optimizes the refactored analytics query that fetches all user
  -- -- activities in a single query instead of 6 separate queries
  -- -- Query pattern it helps:
  -- --   WHERE user_id IN (1000 recipient ids) AND date_timestamp BETWEEN ? AND ?
  -- --
  -- -- Benefits:
  -- -- 1. Supports efficient IN clause lookups for multiple user_ids
  -- -- 2. Date range filtering is covered after user_id filtering
  -- -- 3. Eliminates the N+1 query problem where N = number of time periods (6)
  -- -- 4. Expected performance gain: 6x reduction in database round trips
  -- CREATE INDEX IF NOT EXISTS idx_activity_log_user_activity 
  -- ON tribble.activity_log (client_id, user_id, date_timestamp, source_table);

  -- -- ============================================================================
  -- -- INDEX 3: Message recipient status lookups
  -- -- ============================================================================
  -- -- Purpose: Optimizes the initial status count query and recipient fetching
  -- -- Query patterns it helps:
  -- --   1. SELECT status, COUNT(*) WHERE message_id = ? GROUP BY status
  -- --   2. SELECT * WHERE message_id = ? AND status = 'sent'
  -- --
  -- -- Benefits:
  -- -- 1. Covering index for status aggregation queries - no table access needed
  -- -- 2. Reduces query time for counting recipients by status
  -- -- 3. For messages with 1,000+ recipients, reduces aggregation time from
  -- --    ~200ms to <10ms
  -- CREATE INDEX IF NOT EXISTS idx_message_recipient_message_status 
  -- ON tribble.message_recipient(message_id, status);

  -- -- ============================================================================
  -- -- INDEX 4: Sent recipients lookup (partial index)
  -- -- ============================================================================
  -- -- Purpose: Specifically optimizes the query that finds all users who received
  -- -- the message (status = 'sent')
  -- -- Query pattern it helps:
  -- --   SELECT recipient_id WHERE message_id = ? AND status = 'sent'
  -- --
  -- -- Benefits:
  -- -- 1. Partial index only includes 'sent' status records, reducing index size
  -- --    by ~75% (assuming 25% of messages are in 'sent' status)
  -- -- 2. Faster lookup for the specific case of finding sent recipients
  -- -- 3. Smaller index = better cache utilization and faster scans
  -- -- 4. Critical for the refactored query that fetches all activity logs at once
  -- CREATE INDEX IF NOT EXISTS idx_message_recipient_status_recipient 
  -- ON tribble.message_recipient(status, recipient_id) 
  -- WHERE status = 'sent';

  -- -- Note: These indexes will lock the tables briefly during creation.
  -- -- For production deployments with high traffic, consider creating these
  -- -- indexes manually with CONCURRENTLY outside of a transaction.
`;

export const down = `
  -- ============================================================================
  -- ROLLBACK DISABLED - RUN MANUALLY
  -- ============================================================================
  -- Uncomment the SQL statements below when ready to run manually
  
  -- -- Drop indexes in reverse order of creation
  -- DROP INDEX IF EXISTS tribble.idx_message_recipient_status_recipient;
  -- DROP INDEX IF EXISTS tribble.idx_message_recipient_message_status;
  -- DROP INDEX IF EXISTS tribble.idx_activity_log_user_activity;
  -- DROP INDEX IF EXISTS tribble.idx_activity_log_analytics;
`;
