export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `

    -- Add indexes to nlp_category_source table for foreign key columns
    CREATE INDEX IF NOT EXISTS idx_nlp_category_source_category_id ON {schema}.nlp_category_source (category_id);
    CREATE INDEX IF NOT EXISTS idx_nlp_category_source_content_detail_id ON {schema}.nlp_category_source (content_detail_id);
    CREATE INDEX IF NOT EXISTS idx_nlp_category_source_conversation_detail_id ON {schema}.nlp_category_source (conversation_detail_id);
    CREATE INDEX IF NOT EXISTS idx_nlp_category_source_document_id ON {schema}.nlp_category_source (document_id);
    CREATE INDEX IF NOT EXISTS idx_nlp_category_source_content_id ON {schema}.nlp_category_source (content_id);
    CREATE INDEX IF NOT EXISTS idx_nlp_category_source_conversation_id ON {schema}.nlp_category_source (conversation_id);
    CREATE INDEX IF NOT EXISTS idx_nlp_category_source_embedding_id ON {schema}.nlp_category_source (embedding_id);

    -- Add indexes to nlp_task_status table for foreign key columns
    CREATE INDEX IF NOT EXISTS idx_nlp_task_status_content_detail_id ON {schema}.nlp_task_status (content_detail_id);
    CREATE INDEX IF NOT EXISTS idx_nlp_task_status_conversation_detail_id ON {schema}.nlp_task_status (conversation_detail_id);
    CREATE INDEX IF NOT EXISTS idx_nlp_task_status_document_id ON {schema}.nlp_task_status (document_id);
    CREATE INDEX IF NOT EXISTS idx_nlp_task_status_content_id ON {schema}.nlp_task_status (content_id);
    CREATE INDEX IF NOT EXISTS idx_nlp_task_status_conversation_id ON {schema}.nlp_task_status (conversation_id);
    CREATE INDEX IF NOT EXISTS idx_nlp_task_status_embedding_id ON {schema}.nlp_task_status (embedding_id);
    CREATE INDEX IF NOT EXISTS idx_nlp_task_status_task_id ON {schema}.nlp_task_status (task_id);
`;

export const down = `

    -- Drop indexes from nlp_category_source for foreign key columns
    DROP INDEX IF EXISTS {schema}.idx_nlp_category_source_category_id;
    DROP INDEX IF EXISTS {schema}.idx_nlp_category_source_content_detail_id;
    DROP INDEX IF EXISTS {schema}.idx_nlp_category_source_conversation_detail_id;
    DROP INDEX IF EXISTS {schema}.idx_nlp_category_source_document_id;
    DROP INDEX IF EXISTS {schema}.idx_nlp_category_source_content_id;
    DROP INDEX IF EXISTS {schema}.idx_nlp_category_source_conversation_id;
    DROP INDEX IF EXISTS {schema}.idx_nlp_category_source_embedding_id;

    -- Drop indexes from nlp_task_status for foreign key columns
    DROP INDEX IF EXISTS {schema}.idx_nlp_task_status_content_detail_id;
    DROP INDEX IF EXISTS {schema}.idx_nlp_task_status_conversation_detail_id;
    DROP INDEX IF EXISTS {schema}.idx_nlp_task_status_document_id;
    DROP INDEX IF EXISTS {schema}.idx_nlp_task_status_content_id;
    DROP INDEX IF EXISTS {schema}.idx_nlp_task_status_conversation_id;
    DROP INDEX IF EXISTS {schema}.idx_nlp_task_status_embedding_id;
    DROP INDEX IF EXISTS {schema}.idx_nlp_task_status_task_id;
`;
