// Add tab_id to block_document_lock table for server-side tab validation
// This allows the server to validate which specific browser tab owns a lock

export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
-- Add tab_id column to block_document_lock table
ALTER TABLE {schema}.block_document_lock 
ADD COLUMN tab_id varchar(64) NOT NULL DEFAULT '';

-- Backfill existing locks with 'legacy' tab_id
UPDATE {schema}.block_document_lock 
SET tab_id = 'legacy' 
WHERE tab_id = '' OR tab_id IS NULL;

-- Remove default after backfill
ALTER TABLE {schema}.block_document_lock 
ALTER COLUMN tab_id DROP DEFAULT;

-- Update unique constraint to include tab_id
-- Drop existing unique constraint first
DROP INDEX IF EXISTS {schema}.idx_block_document_lock_unique_active;

-- Create new unique constraint that includes tab_id
-- This allows multiple tabs from same user to attempt locks, but only one can succeed
-- Note: removed WHERE clause to avoid immutability issues, will handle in application logic
CREATE UNIQUE INDEX idx_block_document_lock_unique_active 
ON {schema}.block_document_lock(block_document_id, expires_at);

-- Add index for efficient lookups by (user_id, tab_id)
CREATE INDEX IF NOT EXISTS idx_block_document_lock_user_tab 
ON {schema}.block_document_lock(locked_by, tab_id);

-- Add index for efficient lookups by (document_id, tab_id)
CREATE INDEX IF NOT EXISTS idx_block_document_lock_document_tab 
ON {schema}.block_document_lock(block_document_id, tab_id);
`;

export const down = `
-- Remove tab_id related indexes
DROP INDEX IF EXISTS {schema}.idx_block_document_lock_user_tab;
DROP INDEX IF EXISTS {schema}.idx_block_document_lock_document_tab;

-- Restore original unique constraint
DROP INDEX IF EXISTS {schema}.idx_block_document_lock_unique_active;
CREATE UNIQUE INDEX idx_block_document_lock_unique_active 
ON {schema}.block_document_lock(block_document_id, expires_at);

-- Remove tab_id column
ALTER TABLE {schema}.block_document_lock 
DROP COLUMN IF EXISTS tab_id;
`;
