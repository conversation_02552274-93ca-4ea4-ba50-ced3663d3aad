export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
  -- First get the client_id for the given schema
  WITH client_mapping AS (
    SELECT id AS client_id
    FROM tribble.client 
    WHERE database_schema_id = '{schema}'
  )

  UPDATE tribble.activity_log al
  SET event = 'answer_rfp'
  FROM {schema}.conversation c
  JOIN {schema}.conversation_detail cd ON c.id = cd.conversation_id
  CROSS JOIN client_mapping cm
  -- All the conditions...
  WHERE al.source_id = cd.id
  AND al.client_id = cm.client_id  -- Ensure the correct client_id is matched!
  AND c.system = 'ANSWER_RFP'
  AND c.channel = 'questionnaire'
  AND al.type = 'conversation'
  AND al.source_table = 'conversation_detail'
  AND al.event = 'slack_message';
`;

export const down = `
  -- NO UNDO!
  -- No specific down migration as reverting would require knowing the previous values
  -- which could vary across records
`;
