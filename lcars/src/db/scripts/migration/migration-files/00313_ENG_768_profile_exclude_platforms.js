export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];

export const up = `
INSERT INTO tribble.setting (name, label, description, type, default_boolean, is_client, tribble_editable)
VALUES (
  'can_exclude_source_platforms', 
  'Can Exclude Source Platforms', 
  'When enabled, users with this profile can exclude specific source platforms (<PERSON>lack, Gong) when creating projects', 
  'boolean', 
  false, 
  false, 
  false
) ON CONFLICT DO NOTHING;`;

export const down = `DELETE FROM tribble.setting WHERE name = 'can_exclude_source_platforms';`;
