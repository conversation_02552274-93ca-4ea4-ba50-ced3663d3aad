export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
    -- Update embedding records with source dates from m365drive_asset
    -- Using last_modified from m365drive_asset
    WITH source_data AS (
      SELECT 
        e.id as embedding_id,
        mda.last_modified as modified_date
      FROM {schema}.embedding e
      JOIN {schema}.document d ON e.document_id = d.id
      JOIN {schema}.m365drive_asset mda ON d.id = mda.document_id
      WHERE e.source_modified_date IS NULL
        AND mda.last_modified IS NOT NULL
        AND d.status <> 'deleted'
        AND e.use_for_generation = true
    )
    UPDATE {schema}.embedding e
    SET 
      source_modified_date = sd.modified_date
    FROM source_data sd
    WHERE e.id = sd.embedding_id;
  `;

export const down = `
    -- No down migration needed - this is a data migration
  `;
