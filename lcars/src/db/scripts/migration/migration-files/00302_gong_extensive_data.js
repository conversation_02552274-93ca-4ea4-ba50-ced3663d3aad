export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
    CREATE TABLE {schema}.gong_call_extensive_data (
        id SERIAL PRIMARY KEY,
        -- Foreign key to our internal gong_calls table ID
        gong_call_db_id INT NOT NULL,
        -- <PERSON>'s unique call ID (denormalized for potential lookups)
        gong_id TEXT NOT NULL,
        fetched_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        -- Store the detailed context object from the API response
        call_details JSONB NOT NULL DEFAULT '{}'::jsonb
    );

    ALTER TABLE {schema}.gong_call_extensive_data DROP CONSTRAINT IF EXISTS fk_gong_call_extensive_data_gong_call_db_id;
    
    ALTER TABLE {schema}.gong_call_extensive_data ADD CONSTRAINT fk_gong_call_extensive_data_gong_call_db_id
      FOREIGN KEY (gong_call_db_id) REFERENCES {schema}.gong_calls(id) ON DELETE CASCADE;

    -- Index for joining back to gong_calls during processing
    CREATE INDEX IF NOT EXISTS idx_gong_call_extensive_data_gong_call_db_id ON {schema}.gong_call_extensive_data(gong_call_db_id);

    -- Index for potential direct lookups by gong_id (if needed outside processing)
    CREATE UNIQUE INDEX IF NOT EXISTS idx_gong_call_extensive_data_gong_id ON {schema}.gong_call_extensive_data(gong_id);

    -- Index for querying by fetch time if needed
    CREATE INDEX IF NOT EXISTS idx_gong_call_extensive_data_fetched_at ON {schema}.gong_call_extensive_data(fetched_at);
`;

export const down = `
    DROP INDEX IF EXISTS {schema}.idx_gong_call_extensive_data_fetched_at;
    DROP INDEX IF EXISTS {schema}.idx_gong_call_extensive_data_gong_id;
    DROP INDEX IF EXISTS {schema}.idx_gong_call_extensive_data_gong_call_db_id;

    ALTER TABLE {schema}.gong_call_extensive_data DROP CONSTRAINT IF EXISTS fk_gong_call_extensive_data_gong_call_db_id;

    DROP TABLE IF EXISTS {schema}.gong_call_extensive_data;
`;
