export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `
    ALTER TABLE {schema}.slack_channel_tags ADD COLUMN IF NOT EXISTS used_for_ingest BOOLEAN NOT NULL DEFAULT FALSE;
    ALTER TABLE {schema}.slack_channel_tags ADD COLUMN IF NOT EXISTS used_for_answer BOOLEAN NOT NULL DEFAULT FALSE;

    UPDATE {schema}.slack_channel_tags SET used_for_answer = TRUE;
  `;

export const down = `
    ALTER TABLE {schema}.slack_channel_tags DROP COLUMN IF EXISTS used_for_ingest;
    ALTER TABLE {schema}.slack_channel_tags DROP COLUMN IF EXISTS used_for_answer;
  `;
