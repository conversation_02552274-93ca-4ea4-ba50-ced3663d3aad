export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];
export const up = `
  CREATE TABLE IF NOT EXISTS tribble.allowed_bot (
    id BIGSERIAL PRIMARY KEY,
    slack_bot_id text, 
    slack_user_id text,
    slack_team_id text,
    tribble_user_id integer references tribble.user(id) on delete cascade,
    tribble_client_id integer references tribble.client(id) on delete cascade,
    created_by_tribble_user_id integer references tribble.user(id) on delete set null,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
  );
`;

export const down = `
  DROP TABLE IF EXISTS tribble.allowed_bot;
`;
