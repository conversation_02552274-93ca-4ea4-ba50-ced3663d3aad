export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];
export const up = `INSERT INTO tribble.setting (name, label, description, type, is_client, tribble_editable, default_string)
VALUES ('client_rfp_writer_model', 'The model to use for long form rfps', 'Used for writing, and researching', 'string', true, true, '') ON CONFLICT DO NOTHING;`;
export const down = `DELETE FROM tribble.setting WHERE name = 'client_rfp_writer_model'`;
