export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `
    CREATE TABLE IF NOT EXISTS {schema}.gong_call_transcript_sentences (
        id BIGSERIAL PRIMARY KEY,
        gong_call_id TEXT NOT NULL REFERENCES {schema}.gong_calls(gong_id) ON DELETE CASCADE,
        speaker_id TEXT,
        topic TEXT,
        start_ms INTEGER,
        end_ms INTEGER,
        text TEXT,
        last_ingested_at TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        UNIQUE (gong_call_id, speaker_id, start_ms, end_ms)
    );

    -- Create indexes for common query patterns
    CREATE INDEX IF NOT EXISTS gong_call_transcript_sentences_gong_call_id_idx 
        ON {schema}.gong_call_transcript_sentences(gong_call_id);
`;

export const down = `
    DROP TABLE IF EXISTS {schema}.gong_call_transcript_sentences;
`;
