export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
  INSERT INTO tribble.setting 
    (name, label, description, type, is_client, tribble_editable, default_boolean)
  VALUES 
    ('slack_auto_reply_enabled', 'Enable Slack Auto-Reply', 'When enabled, Tribble will automatically reply to messages in configured Slack channels', 'boolean', true, true, false) 
  ON CONFLICT DO NOTHING;
`;

export const down = `
  DELETE FROM tribble.setting WHERE name = 'slack_auto_reply_enabled';
`;
