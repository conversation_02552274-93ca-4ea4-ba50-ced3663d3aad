export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];
export const up = `
  CREATE TABLE tribble.user_teleportation (
    id SERIAL,
    user_id INTEGER NOT NULL,
    client_id INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by_id INTEGER NOT NULL,
    
    -- Primary key on both columns
    PRIMARY KEY (user_id, client_id),
        
    -- Foreign key constraints assuming you have an organizations table
    FOREIGN KEY (user_id) REFERENCES tribble.user(id) ON DELETE CASCADE,
    FOREIGN KEY (client_id) REFERENCES tribble.client(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by_id) REFERENCES tribble.user(id) ON DELETE SET NULL);

  CREATE INDEX IF NOT EXISTS idx_user_teleportation_user_id ON tribble.user_teleportation (user_id);
`;
export const down = `
  DROP INDEX IF EXISTS idx_user_teleportation_user_id;
  DROP TABLE IF EXISTS tribble.user_teleportation;
  `;
