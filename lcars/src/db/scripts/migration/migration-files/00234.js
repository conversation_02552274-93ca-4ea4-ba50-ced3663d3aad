export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `
    ALTER TABLE IF EXISTS {schema}.content_detail
    ADD COLUMN IF NOT EXISTS modified_at TIMESTAMP WITH TIME ZONE;

    -- Backfill all modified records (from activity_log) to populate modified_at
    WITH al_modified AS (
        select source_id, max(date_timestamp) ts
        from tribble.activity_log al, tribble.client c
        where al.client_id = c.id
        and c.database_schema_id = '{schema}'
        and source_table='content_detail'
        and type = 'modify_answer'
        group by source_id
    )
    UPDATE {schema}.content_detail cd
    SET modified_at = al_modified.ts
    FROM al_modified
    WHERE cd.id = al_modified.source_id;    
`;

export const down = `
    ALTER TABLE IF EXISTS {schema}.content_detail
    DROP COLUMN IF EXISTS modified_at;
`;
