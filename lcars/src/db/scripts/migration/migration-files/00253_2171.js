export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

// Migration to fix any schemas that have the wrong rfx_writer_output_id column
export const up = `
  ALTER TABLE {schema}.block_document DROP COLUMN IF EXISTS rfx_writer_output_id;

  ALTER TABLE {schema}.block_section ADD COLUMN IF NOT EXISTS 
    rfx_writer_output_id int REFERENCES {schema}.rfx_writer_output(id) ON DELETE SET NULL;
  `;

export const down = `
  ALTER TABLE {schema}.block_document ADD COLUMN IF NOT EXISTS 
    rfx_writer_output_id int REFERENCES {schema}.rfx_writer_output(id) ON DELETE SET NULL;

  ALTER TABLE {schema}.block_section DROP COLUMN IF EXISTS rfx_writer_output_id;
`;
