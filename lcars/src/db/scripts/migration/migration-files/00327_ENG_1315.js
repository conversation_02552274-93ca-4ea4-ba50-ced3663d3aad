// Good bye KG settings
export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `
  DELETE FROM {schema}.client_setting where setting_id in 
  (select id from tribble.setting where name in (
    'kg_enable_multi_hop_reasoning', 
    'kg_max_hop_depth', 
    'kg_detect_bridge_nodes', 
    'kg_calculate_centrality'
    'kg_entity_types',
    'kg_relationship_extraction',
    'kg_minimum_confidence',
    'client_enable_knowledge_graph'
    ));
  `;
export const down = `
  select 'No down migration for this script';
  `;
