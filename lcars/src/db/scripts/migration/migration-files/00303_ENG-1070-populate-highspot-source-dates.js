export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
    -- Update embedding records with source dates from highspot_asset
    WITH source_data AS (
      SELECT 
        e.id as embedding_id,
        ha.last_modified as modified_date
      FROM {schema}.embedding e
      JOIN {schema}.document d ON e.document_id = d.id
      JOIN {schema}.highspot_asset ha ON d.id = ha.document_id
      WHERE 
        d.status <> 'deleted'
        AND e.use_for_generation = true
        AND e.source_modified_date IS NULL
        AND ha.last_modified IS NOT NULL
    )
    UPDATE {schema}.embedding e
    SET source_modified_date = sd.modified_date
    FROM source_data sd
    WHERE e.id = sd.embedding_id;
  `;

export const down = `
    -- No down migration needed - this is a data migration
  `;
