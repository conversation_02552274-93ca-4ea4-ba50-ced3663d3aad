export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
DO $$
DECLARE
    gong_start_date_setting TEXT;
    gong_start_date TIMESTAMP WITH TIME ZONE;
    old_gong_calls_count INTEGER;
    failed_gong_calls_count INTEGER;
    total_calls_to_cleanup INTEGER;
    deleted_documents_count INTEGER;
    deleted_embeddings_count INTEGER;
    deleted_calls_count INTEGER;
    schema_name TEXT := '{schema}';
BEGIN
    -- Get the gong ingestion start date setting
    SELECT cs.value_string INTO gong_start_date_setting
    FROM tribble.setting s
    JOIN {schema}.client_setting cs ON cs.setting_id = s.id
    WHERE s.name = 'gong_ingestion_start_date'
    AND cs.value_string IS NOT NULL
    AND cs.value_string != '';
    
    -- Count failed calls regardless of date setting
    SELECT COUNT(*) INTO failed_gong_calls_count
    FROM {schema}.gong_calls gc
    WHERE gc.processing_status = 'failed';
    
    -- If no start date is configured, only clean up failed calls
    IF gong_start_date_setting IS NULL THEN
        IF failed_gong_calls_count = 0 THEN
            -- No gong ingestion start date configured and no failed calls found - skip cleanup
            RETURN;
        ELSE
            -- No gong ingestion start date configured - will only clean up failed calls
            gong_start_date := NULL; -- Will be handled in the WHERE clause
        END IF;
    ELSE
        -- Parse the start date
        BEGIN
            gong_start_date := gong_start_date_setting::TIMESTAMP WITH TIME ZONE;
        EXCEPTION WHEN OTHERS THEN
            -- Invalid gong ingestion start date format - will only clean up failed calls
            gong_start_date := NULL;
        END;
        
        -- Count old calls if we have a valid start date
        IF gong_start_date IS NOT NULL THEN
            SELECT COUNT(*) INTO old_gong_calls_count
            FROM {schema}.gong_calls gc
            WHERE gc.started < gong_start_date;
        ELSE
            old_gong_calls_count := 0;
        END IF;
    END IF;

    -- Count total calls to be cleaned up
    SELECT COUNT(*) INTO total_calls_to_cleanup
    FROM {schema}.gong_calls gc
    WHERE (gong_start_date IS NOT NULL AND gc.started < gong_start_date)
       OR gc.processing_status = 'failed';
    
    IF total_calls_to_cleanup = 0 THEN
        -- No Gong calls found for cleanup
        RETURN;
    END IF;
    
    -- Starting cleanup - will process old calls (if date configured) and failed calls

    -- Delete embeddings associated with old/failed gong documents
    WITH calls_to_cleanup AS (
        SELECT gc.gong_id
        FROM {schema}.gong_calls gc
        WHERE (gong_start_date IS NOT NULL AND gc.started < gong_start_date)
           OR gc.processing_status = 'failed'
    ),
    gong_documents_to_cleanup AS (
        SELECT d.id as document_id
        FROM {schema}.document d
        WHERE d.metadata_json->>'external_doc_source' = 'gong'
        AND d.metadata_json->>'external_doc_source_id' IN (
            SELECT gong_id FROM calls_to_cleanup
        )
        AND d.deleted_date IS NULL
    ),
    deleted_embeddings AS (
        DELETE FROM {schema}.embedding e
        WHERE e.document_id IN (SELECT document_id FROM gong_documents_to_cleanup)
        RETURNING e.id
    )
    SELECT COUNT(*) INTO deleted_embeddings_count FROM deleted_embeddings;
    
    -- Deleted embeddings associated with old/failed Gong documents

    -- Soft delete documents associated with old/failed gong calls
    WITH calls_to_cleanup AS (
        SELECT gc.gong_id
        FROM {schema}.gong_calls gc
        WHERE (gong_start_date IS NOT NULL AND gc.started < gong_start_date)
           OR gc.processing_status = 'failed'
    ),
    deleted_documents AS (
        UPDATE {schema}.document d
        SET 
            deleted_date = CURRENT_DATE,
            deleted_by_id = (
                SELECT u.id 
                FROM tribble.user u 
                WHERE u.email = '<EMAIL>' 
                LIMIT 1
            )
        WHERE d.metadata_json->>'external_doc_source' = 'gong'
        AND d.metadata_json->>'external_doc_source_id' IN (
            SELECT gong_id FROM calls_to_cleanup
        )
        AND d.deleted_date IS NULL
        RETURNING d.id
    )
    SELECT COUNT(*) INTO deleted_documents_count FROM deleted_documents;
    
    -- Soft deleted documents associated with old/failed Gong calls

    -- Delete old/failed gong calls (this will cascade to transcript sentences due to FK)
    WITH deleted_calls AS (
        DELETE FROM {schema}.gong_calls gc
        WHERE (gong_start_date IS NOT NULL AND gc.started < gong_start_date)
           OR gc.processing_status = 'failed'
        RETURNING gc.id, gc.processing_status, gc.started
    )
    SELECT COUNT(*) INTO deleted_calls_count FROM deleted_calls;
    
    -- Deleted Gong calls and their associated transcript sentences
    -- Cleanup completed - deleted calls, soft deleted documents, and deleted embeddings
    
END $$;
`;

export const down = `
-- This migration cannot be safely reversed as it deletes data
-- The down migration is intentionally left empty
-- If reversal is needed, restore from backup
SELECT 1;
`;
