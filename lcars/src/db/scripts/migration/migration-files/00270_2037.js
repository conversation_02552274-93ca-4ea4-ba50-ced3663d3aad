export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
  -- Enhance the rfx table with fields needed to be a project container
  ALTER TABLE IF EXISTS {schema}.rfx
    ADD COLUMN IF NOT EXISTS description TEXT,
    ADD COLUMN IF NOT EXISTS customer_name TEXT,
    ADD COLUMN IF NOT EXISTS project_status TEXT,
    ADD COLUMN IF NOT EXISTS due_date DATE,
    ADD COLUMN IF NOT EXISTS owner_id INTEGER REFERENCES tribble.user(id),
    ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN DEFAULT FALSE;

  -- Create rfx_content table
  CREATE TABLE IF NOT EXISTS {schema}.rfx_content (
    id SERIAL PRIMARY KEY,
    rfx_id INTEGER REFERENCES {schema}.rfx(id) ON DELETE CASCADE,
    content_id INTEGER REFERENCES {schema}.content(id) ON DELETE CASCADE,
    status TEXT,
    status_message TEXT,
    is_deleted BOOLEAN DEFAULT FALSE NOT NULL,
    CONSTRAINT rfx_content_unique_rfx_id_content_id UNIQUE (rfx_id, content_id)
  );

  -- Create user association table for rfx projects
  CREATE TABLE IF NOT EXISTS {schema}.rfx_content_user_join (
    id SERIAL PRIMARY KEY,
    rfx_content_id INTEGER REFERENCES {schema}.rfx_content(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES tribble.user(id),
    is_owner BOOLEAN DEFAULT FALSE NOT NULL,
    CONSTRAINT rfx_content_user_join_unique_rfx_content_id_user_id UNIQUE (rfx_content_id, user_id)
  );

  -- Add rfx_content_id to rfx_response_document
  ALTER TABLE IF EXISTS {schema}.rfx_response_document
    ADD COLUMN IF NOT EXISTS rfx_content_id INTEGER REFERENCES {schema}.rfx_content(id) ON DELETE CASCADE;

  -- Create rfx_bid_packet_file_rfx_content join table
  CREATE TABLE IF NOT EXISTS {schema}.rfx_bid_packet_file_rfx_content (
    rfx_bid_packet_file_id INTEGER REFERENCES {schema}.rfx_bid_packet_file(id) ON DELETE CASCADE,
    rfx_content_id INTEGER REFERENCES {schema}.rfx_content(id) ON DELETE CASCADE,
    PRIMARY KEY (rfx_bid_packet_file_id, rfx_content_id)
  );

  -- Create indexes on rfx_content_user_join
  CREATE INDEX IF NOT EXISTS idx_rfx_content_user_join_rfx_content_id
    ON {schema}.rfx_content_user_join (rfx_content_id);
  CREATE INDEX IF NOT EXISTS idx_rfx_content_user_join_user_id
    ON {schema}.rfx_content_user_join (user_id);

  -- Create indexes on rfx_content
  CREATE INDEX IF NOT EXISTS idx_rfx_content_rfx_id
    ON {schema}.rfx_content (rfx_id);
  CREATE INDEX IF NOT EXISTS idx_rfx_content_content_id
    ON {schema}.rfx_content (content_id);
    
  -- Create indexes on rfx_bid_packet_file_rfx_content
  CREATE INDEX IF NOT EXISTS idx_rfx_bid_packet_file_rfx_content_file_id
    ON {schema}.rfx_bid_packet_file_rfx_content (rfx_bid_packet_file_id);
  CREATE INDEX IF NOT EXISTS idx_rfx_bid_packet_file_rfx_content_content_id
    ON {schema}.rfx_bid_packet_file_rfx_content (rfx_content_id);
`;

export const down = `
  ALTER TABLE IF EXISTS {schema}.rfx_response_document
    DROP COLUMN IF EXISTS rfx_content_id;

  DROP TABLE IF EXISTS {schema}.rfx_bid_packet_file_rfx_content;

  -- Drop indexes
  DROP INDEX IF EXISTS idx_rfx_content_user_join_user_id;
  DROP INDEX IF EXISTS idx_rfx_content_user_join_rfx_content_id;
  DROP INDEX IF EXISTS idx_rfx_content_content_id;
  DROP INDEX IF EXISTS idx_rfx_content_rfx_id;
  DROP INDEX IF EXISTS idx_rfx_bid_packet_file_rfx_content_content_id;
  DROP INDEX IF EXISTS idx_rfx_bid_packet_file_rfx_content_file_id;

  DROP TABLE IF EXISTS {schema}.rfx_content_user_join;
  DROP TABLE IF EXISTS {schema}.rfx_content;
  
  ALTER TABLE IF EXISTS {schema}.rfx
    DROP COLUMN IF EXISTS description,
    DROP COLUMN IF EXISTS customer_name,
    DROP COLUMN IF EXISTS project_status,
    DROP COLUMN IF EXISTS due_date,
    DROP COLUMN IF EXISTS owner_id,
    DROP COLUMN IF EXISTS is_deleted;
`;
