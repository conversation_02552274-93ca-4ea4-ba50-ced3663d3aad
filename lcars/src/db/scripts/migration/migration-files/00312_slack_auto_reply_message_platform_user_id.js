export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
  -- Add platform_user_id column if it doesn't exist
  ALTER TABLE {schema}.slack_auto_reply_message 
  ADD COLUMN IF NOT EXISTS platform_user_id TEXT;

  -- Update existing rows to have a default platform_user_id (assuming Slack for existing data)
  UPDATE {schema}.slack_auto_reply_message 
  SET platform_user_id = 'slack_user_' || user_id::text 
  WHERE platform_user_id IS NULL;

  -- Make the column NOT NULL after populating it
  ALTER TABLE {schema}.slack_auto_reply_message 
  ALTER COLUMN platform_user_id SET NOT NULL;

  -- Drop the old unique constraint
  ALTER TABLE {schema}.slack_auto_reply_message 
  DROP CONSTRAINT IF EXISTS slack_auto_reply_message_channel_id_message_ts_key;

  -- Add the new unique constraint including platform_user_id
  ALTER TABLE {schema}.slack_auto_reply_message 
  ADD CONSTRAINT slack_auto_reply_message_channel_id_message_ts_platform_user_key 
  UNIQUE (channel_id, message_ts, platform_user_id);
`;

export const down = `
  -- Drop the new unique constraint
  ALTER TABLE {schema}.slack_auto_reply_message 
  DROP CONSTRAINT IF EXISTS slack_auto_reply_message_channel_id_message_ts_platform_user_key;

  -- Re-add the old unique constraint
  ALTER TABLE {schema}.slack_auto_reply_message 
  ADD CONSTRAINT slack_auto_reply_message_channel_id_message_ts_key 
  UNIQUE (channel_id, message_ts);

  -- Drop the platform_user_id column
  ALTER TABLE {schema}.slack_auto_reply_message 
  DROP COLUMN IF EXISTS platform_user_id;
`;
