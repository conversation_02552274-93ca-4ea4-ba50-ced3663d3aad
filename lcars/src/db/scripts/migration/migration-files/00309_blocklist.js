export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `
  CREATE TABLE IF NOT EXISTS {schema}.block_list_entry (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by_id INT references tribble.user(id),
    block_url text,
    -- add more fields as needed e.g. block_google_asset_id
    CONSTRAINT block_list_entry_block_url_unique UNIQUE (block_url)
);
CREATE INDEX IF NOT EXISTS block_list_entry_block_url ON {schema}.block_list_entry (block_url);
  `;
export const down = `
DROP INDEX IF EXISTS {schema}.block_list_entry_block_url;
DROP TABLE IF EXISTS {schema}.block_list_entry;
  `;
