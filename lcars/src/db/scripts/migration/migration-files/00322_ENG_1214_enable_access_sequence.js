export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];

export const up = `
INSERT INTO tribble.setting (name, label, description, type, default_boolean, is_client, tribble_editable)
VALUES (
  'enable_access_sequence', 
  'Enable Access Sequence', 
  'Allow prioritization of data source access sequence for how the Agent uses data sources', 
  'boolean', 
  false, 
  true, 
  true
) ON CONFLICT DO NOTHING;`;

export const down = `DELETE FROM tribble.setting WHERE name = 'enable_access_sequence';`;
