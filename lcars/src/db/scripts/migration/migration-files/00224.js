export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `  
  CREATE TABLE IF NOT EXISTS {schema}.nlp_category_entity (
    id SERIAL PRIMARY KEY,
    category_source_id INTEGER NOT NULL,
    entity_id INTEGER NOT NULL,
    justification TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (category_source_id) REFERENCES {schema}.nlp_category_source(id) ON DELETE CASCADE,
    FOREIGN KEY (entity_id) REFERENCES {schema}.nlp_entity(id) ON DELETE CASCADE,
    CONSTRAINT nlp_category_entity_category_source_id_entity_id_unique UNIQUE (category_source_id, entity_id)
  );

  INSERT INTO {schema}.nlp_task (name, enabled, description) 
  VALUES ('MAP_CAT_ENT_RFP_ANSWER_MATCH', false, 'Maps entities in a content to the categories of the same content')
  ON CONFLICT (name, version) DO NOTHING;

`;

export const down = `
  DROP TABLE IF EXISTS {schema}.nlp_category_entity;
  
  DELETE FROM {schema}.nlp_task WHERE name = 'MAP_CAT_ENT_RFP_ANSWER_MATCH';
`;
