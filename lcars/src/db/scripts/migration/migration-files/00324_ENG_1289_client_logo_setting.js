export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];

export const up = `
INSERT INTO tribble.setting (name, label, description, type, default_string, is_client, tribble_editable)
VALUES (
  'client_logo_blob_id', 
  'Client Logo Blob Id', 
  'Storage account blob id for the uploaded client logo.', 
  'string', 
  '', 
  true, 
  true
) ON CONFLICT DO NOTHING;`;

export const down = `DELETE FROM tribble.setting WHERE name = 'client_logo_blob_id';`;
