export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = /*sql*/ `
CREATE TABLE IF NOT EXISTS {schema}.block_doc_comment (
  id TEXT PRIMARY KEY,
  value JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  user_id INTEGER NOT NULL REFERENCES tribble.user(id) ON DELETE CASCADE,
  parent_id TEXT REFERENCES {schema}.block_doc_comment(id) ON DELETE CASCADE,
  is_resolved BOOLEAN DEFAULT FALSE,
  block_document_id INT REFERENCES {schema}.block_document(id) ON DELETE CASCADE,
  UNIQUE(block_document_id, id)
);

-- Index on userId for looking up user's comments
CREATE INDEX IF NOT EXISTS idx_block_doc_comment_user_id
  ON {schema}.block_doc_comment(user_id);

-- Index on parentId for finding replies
CREATE INDEX IF NOT EXISTS idx_block_doc_comment_parent_id
  ON {schema}.block_doc_comment(parent_id);

-- Index on isResolved for filtering resolved/unresolved
CREATE INDEX IF NOT EXISTS idx_block_doc_comment_is_resolved
  ON {schema}.block_doc_comment(is_resolved);
`;

export const down = /*sql*/ `
DROP INDEX IF EXISTS idx_block_doc_comment_user_id;
DROP INDEX IF EXISTS idx_block_doc_comment_parent_id;
DROP INDEX IF EXISTS idx_block_doc_comment_is_resolved;
DROP TABLE IF EXISTS {schema}.block_doc_comment;
`;
