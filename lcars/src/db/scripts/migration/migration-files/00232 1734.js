export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `
    CREATE TABLE IF NOT EXISTS {schema}.e2e_answer_entry (
    id BIGSERIAL PRIMARY KEY,
    e2e_question_entry_id BIGINT REFERENCES {schema}.e2e_question_entry(id) ON DELETE CASCADE,
    content_detail_id BIGINT REFERENCES {schema}.content_detail(id) ON DELETE CASCADE,
    answer TEXT,
    type TEXT,
    answer_address TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_modified_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );
    CREATE INDEX IF NOT EXISTS idx_e2e_answer_content_detail_id ON {schema}.e2e_answer_entry(content_detail_id);
  `;

export const down = `
DROP INDEX IF EXISTS {schema}.idx_e2e_answer_content_detail_id;  
DROP TABLE IF EXISTS {schema}.e2e_answer_entry;

`;
