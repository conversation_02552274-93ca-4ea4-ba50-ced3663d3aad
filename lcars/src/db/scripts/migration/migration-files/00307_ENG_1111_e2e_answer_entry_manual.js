export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `
    CREATE TABLE IF NOT EXISTS {schema}.e2e_answer_entry_manual (
    id BIGSERIAL PRIMARY KEY,
    content_id BIGINT REFERENCES {schema}.content(id) ON DELETE CASCADE,
    sheet_title TEXT,
    answer TEXT,
    answer_address TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_modified_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_modified_by INTEGER REFERENCES tribble.user(id) ON DELETE SET NULL,
    UNIQUE(content_id, sheet_title,answer_address)
    );

    CREATE INDEX IF NOT EXISTS idx_e2e_answer_entry_manual_content_id ON {schema}.e2e_answer_entry_manual(content_id);
  `;

export const down = `
DROP INDEX IF EXISTS {schema}.idx_e2e_answer_entry_manual_content_id;
DROP TABLE IF EXISTS {schema}.e2e_answer_entry_manual;
`;
