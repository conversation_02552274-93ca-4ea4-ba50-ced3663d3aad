export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];
export const up = `
  CREATE TABLE tribble.client_teleportation (
    id SERIAL,
    org_id_1 INTEGER NOT NULL,
    org_id_2 INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Primary key on both columns
    PRIMARY KEY (org_id_1, org_id_2),
    
    -- Ensure org_id_1 is always less than org_id_2 to prevent duplicate relationships
    CHECK (org_id_1 < org_id_2),
    
    -- Foreign key constraints assuming you have an organizations table
    FOREIGN KEY (org_id_1) REFERENCES tribble.client(id) ON DELETE CASCADE,
    FOREIGN KEY (org_id_2) REFERENCES tribble.client(id) ON DELETE CASCADE
);`;

export const down = `
  DROP TABLE IF EXISTS tribble.client_teleportation;
  `;
