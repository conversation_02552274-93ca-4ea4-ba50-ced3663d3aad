export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = /*sql*/ `
ALTER TABLE IF EXISTS {schema}.block_item_version
ADD COLUMN IF NOT EXISTS document_version_id int NOT NULL REFERENCES {schema}.block_document_version (id) ON DELETE CASCADE;

-- Copy existing values from document_version to document_version_id only if the column exists
DO $$ 
BEGIN
    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = '{schema}' 
        AND table_name = 'block_item_version' 
        AND column_name = 'document_version'
    ) THEN
        EXECUTE 'UPDATE {schema}.block_item_version 
                SET document_version_id = document_version 
                WHERE document_version IS NOT NULL';
    END IF;
END $$;

-- Drop the old column if it exists
ALTER TABLE IF EXISTS {schema}.block_item_version
DROP COLUMN IF EXISTS document_version;

CREATE INDEX IF NOT EXISTS idx_block_item_version_doc_version_id ON {schema}.block_item_version(document_version_id);



`;
