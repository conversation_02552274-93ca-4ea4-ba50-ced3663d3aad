export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
  -- Functional Index for TRIM(content) Length
  CREATE INDEX idx_embedding_content_trim ON {schema}.embedding
    (LENGTH(TRIM(content))) WHERE length(trim(content)) > 0;

  -- Composite Index for Common Filtering Conditions
  CREATE INDEX IF NOT EXISTS idx_embedding_combined_filters
  ON {schema}.embedding(origin, content_type, use_for_generation);

  -- Combined Document Metadata Index
  CREATE INDEX IF NOT EXISTS idx_document_metadata
  ON {schema}.document(id, use_for_generation)
  WHERE deleted_date IS NULL;

  -- Metadata Filter Index
  CREATE INDEX IF NOT EXISTS idx_embedding_metadata_filter
  ON {schema}.embedding USING gin (metadata_filter);
`;

export const down = `
  DROP INDEX IF EXISTS {schema}.idx_embedding_content_trim;
  DROP INDEX IF EXISTS {schema}.idx_embedding_combined_filters;
  DROP INDEX IF EXISTS {schema}.idx_document_metadata;
  DROP INDEX IF EXISTS {schema}.idx_embedding_metadata_filter;
`;
