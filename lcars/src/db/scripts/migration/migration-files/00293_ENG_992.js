export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `
    CREATE TABLE IF NOT EXISTS {schema}.slack_channel_tags (  
      id SERIAL PRIMARY KEY,
      slack_channel TEXT NOT NULL,
      tag_id SMALLINT NOT NULL REFERENCES {schema}.metadata_filter(id),
      created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
      UNIQUE (slack_channel, tag_id)
    );

    CREATE INDEX IF NOT EXISTS idx_slack_channel_tags_slack_channel ON {schema}.slack_channel_tags (slack_channel);  
  `;

export const down = `
    DROP TABLE IF EXISTS {schema}.slack_channel_tags;
  `;
