export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `
    ALTER TABLE {schema}.graph_nodes
    ADD COLUMN IF NOT EXISTS source_embedding_id int references {schema}.embedding(id) on delete set null;
    CREATE INDEX IF NOT EXISTS idx_graph_nodes_source_embedding_id ON {schema}.graph_nodes (source_embedding_id);
  `;

export const down = `
    ALTER TABLE {schema}.graph_nodes
    DROP COLUMN IF EXISTS source_embedding_id;
    DROP INDEX IF EXISTS {schema}.idx_graph_nodes_source_embedding_id;
  `;
