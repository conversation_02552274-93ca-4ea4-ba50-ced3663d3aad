export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `
    CREATE TABLE IF NOT EXISTS {schema}.cartridge (
      id SERIAL PRIMARY KEY,
      name text NOT NULL UNIQUE,
      prompt text,
      model text,
      tool_choice text,
      created_at TIMESTAMPTZ DEFAULT NOW(),
      created_by int REFERENCES tribble.user(id) ON DELETE SET NULL,
      updated_at TIMESTAMPTZ,
      updated_by int REFERENCES tribble.user(id) ON DELETE SET NULL
    );
    
    CREATE TABLE IF NOT EXISTS {schema}.tool (
      id SERIAL PRIMARY KEY,
      cartridge_id int REFERENCES {schema}.cartridge(id) ON DELETE CASCADE,
      name text NOT NULL,
      description text,
      do_not_recurse boolean DEFAULT false,
      is_handoff boolean DEFAULT false,
      json_schema_args jsonb default '{}'::jsonb,
      
      created_at TIMESTAMPTZ DEFAULT NOW(),
      created_by int REFERENCES tribble.user(id) ON DELETE SET NULL,
      updated_at TIMESTAMPTZ,
      updated_by int REFERENCES tribble.user(id) ON DELETE SET NULL,
      CONSTRAINT unique_tool_name UNIQUE (name, cartridge_id)
    );

    CREATE OR REPLACE TRIGGER update_updated_at_before_update
    BEFORE INSERT OR UPDATE ON {schema}.cartridge
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

    CREATE OR REPLACE TRIGGER update_updated_at_before_update
    BEFORE INSERT OR UPDATE ON {schema}.tool
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
    `;

export const down = `
    DROP TABLE IF EXISTS {schema}.tool CASCADE;
    DROP TABLE IF EXISTS {schema}.cartridge CASCADE;
  `;
