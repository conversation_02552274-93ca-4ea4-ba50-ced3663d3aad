export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];
export const up = `INSERT INTO tribble.salesforce_tribblytics_default (tribblytics_name, object_name, field_name, label, is_customizable)
SELECT 'account_region', 'Account', 'BillingState', 'State', TRUE
WHERE NOT EXISTS (
  SELECT 1 FROM tribble.salesforce_tribblytics_default WHERE tribblytics_name = 'account_region'
);

INSERT INTO tribble.salesforce_tribblytics_default (tribblytics_name, object_name, field_name, label, is_customizable)
SELECT 'account_country', 'Account', 'BillingCountry', 'Country', TRUE
WHERE NOT EXISTS (
  SELECT 1 FROM tribble.salesforce_tribblytics_default WHERE tribblytics_name = 'account_country'
);
DELETE FROM tribble.salesforce_tribblytics_default
WHERE tribblytics_name = 'account_address';`;
