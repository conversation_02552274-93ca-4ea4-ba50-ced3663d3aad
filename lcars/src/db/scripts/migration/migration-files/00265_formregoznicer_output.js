export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `
      CREATE TABLE IF NOT EXISTS {schema}.form_recognizer_output (
      id SERIAL PRIMARY KEY,
      blob_storage_uuid TEXT,
      file_name TEXT NOT NULL,
      paragraphs JSONB default '[]'::jsonb,
      pages JSONB default '[]'::jsonb,
      tables JSONB default '[]'::jsonb,
      content TEXT,
      content_hash TEXT,
      mime_type TEXT
    );

    CREATE INDEX IF NOT EXISTS form_recognizer_output_blob_storage_uuid_idx ON {schema}.form_recognizer_output(blob_storage_uuid);
    CREATE INDEX IF NOT EXISTS form_recognizer_output_content_hash ON {schema}.form_recognizer_output(content_hash);
  `;

export const down = `
      DROP INDEX IF EXISTS {schema}.form_recognizer_output_blob_storage_uuid_idx;
      DROP INDEX IF EXISTS {schema}.form_recognizer_output_content_hash;
      DROP TABLE IF EXISTS {schema}.form_recognizer_output;
  `;
