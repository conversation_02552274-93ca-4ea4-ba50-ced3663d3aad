//
export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `

  ALTER TABLE IF EXISTS {schema}.workflow ADD COLUMN IF NOT EXISTS cartridge_id INT; -- To make sure the migration can be rerun after reverting. We drop this below... 
  
  ALTER TABLE IF EXISTS {schema}.workflow ADD COLUMN IF NOT EXISTS name TEXT NOT NULL DEFAULT '_';
  ALTER TABLE IF EXISTS {schema}.workflow ADD COLUMN IF NOT EXISTS start_message TEXT; -- this is what will hold the custom start message.
  ALTER TABLE IF EXISTS {schema}.workflow ADD COLUMN IF NOT EXISTS base_cartridge_enum int default 0 NOT NULL;

  -- Now put the prompt into the start_message column for existing workflows
  UPDATE {schema}.workflow
  SET start_message = (
    SELECT prompt FROM {schema}.cartridge WHERE id = cartridge_id
  ),
  name = (
      SELECT name FROM {schema}.cartridge WHERE id = workflow.cartridge_id
    )

  WHERE start_message IS NULL AND cartridge_id IS NOT NULL;


  ALTER TABLE {schema}.workflow ADD COLUMN IF NOT EXISTS base_cartridge_id int REFERENCES {schema}.cartridge(id) ON DELETE SET NULL;  
  DELETE FROM {schema}.cartridge WHERE id in (select cartridge_id from {schema}.workflow where cartridge_id is not null);
  ALTER TABLE {schema}.workflow DROP COLUMN IF EXISTS cartridge_id; -- this is to ensure that all workflows have a cartridge.
  `;

export const down = `
  ALTER TABLE {schema}.workflow DROP COLUMN IF EXISTS start_message;
  ALTER TABLE {schema}.workflow DROP COLUMN IF EXISTS name;
  ALTER TABLE {schema}.workflow DROP COLUMN IF EXISTS base_cartridge_enum;
  ALTER TABLE {schema}.workflow DROP COLUMN IF EXISTS base_cartridge_id; -- this is to ensure that all workflows have a cartridge.
  `;
