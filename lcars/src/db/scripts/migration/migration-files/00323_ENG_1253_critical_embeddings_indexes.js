export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
  -- -- Index 1: Composite index for embedding-document joins filtered by origin
  -- -- This index speeds up the join between embedding and document tables in filtered_embeddings CTE
  -- -- Supports both AZURE and OPENAI origins
  -- CREATE INDEX IF NOT EXISTS idx_embedding_origin_document_join
  -- ON {schema}.embedding (origin, document_id);

  -- -- Index 2: Composite index for metadata filter JSONB queries
  -- -- This significantly speeds up the complex JSONB operations in embedding_metadata CTE
  -- -- Specifically targets the metadata_filter -> 'type_id' access pattern for learned facts
  -- CREATE INDEX IF NOT EXISTS idx_embedding_metadata_filter_type_id
  -- ON {schema}.embedding ((metadata_filter -> 'type_id'))
  -- WHERE metadata_filter -> 'values' IS NOT NULL;

  -- -- Index 3: Covering index for usage stats calculation
  -- -- This index includes created_at to avoid table lookups when calculating usage stats
  -- CREATE INDEX IF NOT EXISTS idx_content_detail_embedding_usage_stats
  -- ON {schema}.content_detail_embedding (embedding_id, created_at);

  -- -- Index 4: Partial index on document for active documents
  -- -- Speeds up the join and filter on document table for non-deleted documents
  -- CREATE INDEX IF NOT EXISTS idx_document_active_label
  -- ON {schema}.document (id, label, document_type_id)
  -- WHERE deleted_date IS NULL;
`;

export const down = `
  -- DROP INDEX IF EXISTS {schema}.idx_embedding_origin_document_join;
  -- DROP INDEX IF EXISTS {schema}.idx_embedding_metadata_filter_type_id;
  -- DROP INDEX IF EXISTS {schema}.idx_content_detail_embedding_usage_stats;
  -- DROP INDEX IF EXISTS {schema}.idx_document_active_label;
`;
