export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
  ALTER TABLE {schema}.e2e_question_entry 
  ADD COLUMN IF NOT EXISTS dependent_questions JSONB;

  ALTER TABLE {schema}.e2e_question_entry 
  ADD COLUMN IF NOT EXISTS question_additional_range TEXT;

  ALTER TABLE {schema}.e2e_question_entry 
  ADD COLUMN IF NOT EXISTS question_context TEXT;

  ALTER TABLE {schema}.e2e_question_entry 
  ADD COLUMN IF NOT EXISTS parent_question_range TEXT;
`;

export const down = `
  ALTER TABLE {schema}.e2e_question_entry 
  DROP COLUMN IF EXISTS dependent_questions;

  ALTER TABLE {schema}.e2e_question_entry 
  DROP COLUMN IF EXISTS question_additional_range;

  ALTER TABLE {schema}.e2e_question_entry 
  DROP COLUMN IF EXISTS question_context;

  ALTER TABLE {schema}.e2e_question_entry 
  DROP COLUMN IF EXISTS parent_question_range;
`;
