export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];
export const up = `INSERT INTO tribble.setting (name, label, description, type, default_boolean, is_client, tribble_editable)
VALUES ('enable_gong_in_brain_search', 'Enable Gong sources in BrainSearch', 'When enabled, gong will be used in brain search', 'boolean', false, true, true) ON CONFLICT DO NOTHING;`;
export const down = `DELETE FROM tribble.setting WHERE name = 'enable_gong_in_brain_search';`;
