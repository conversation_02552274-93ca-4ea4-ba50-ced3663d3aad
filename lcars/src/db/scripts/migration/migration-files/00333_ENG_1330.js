export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
  -- Data migration to populate rfx_section_outline_sources table
  -- This migration extracts source information from rfx_section_outline 
  INSERT INTO {schema}.rfx_section_outline_sources (
      rfx_section_outline_id,
      rfx_bid_packet_file_id,
      source_page_start,
      source_page_end,
      source_text
  )
  SELECT DISTINCT
      rso.id as rfx_section_outline_id,
      rbpf.id as rfx_bid_packet_file_id,
      rso.source_page_start,
      rso.source_page_end,
      rso.source_text
  FROM {schema}.rfx_section_outline rso
  INNER JOIN {schema}.rfx_response_document rrd ON rso.rfx_response_document_id = rrd.id
  INNER JOIN {schema}.rfx_bid_packet_file rbpf ON rrd.bid_packet_file_id = rbpf.id
  LEFT JOIN {schema}.rfx_section_outline_sources existing_rsos ON existing_rsos.rfx_section_outline_id = rso.id
  WHERE rso.source_page_start IS NOT NULL
      AND rso.source_page_end IS NOT NULL
      AND existing_rsos.rfx_section_outline_id IS NULL
  ORDER BY rso.id;
  `;

export const down = `
  -- No down migration available
`;
