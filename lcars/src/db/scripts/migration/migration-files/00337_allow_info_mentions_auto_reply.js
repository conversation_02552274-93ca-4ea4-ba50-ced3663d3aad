export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];

export const up = `
    INSERT INTO tribble.setting (name, label, description, type, default_boolean, is_client, tribble_editable)
    VALUES (
        'allow_info_mentions_auto_reply', 
        'Allow Info Mentions Auto Reply', 
        'Allow auto-reply for informational @-mentions like FYI and CC', 
        'boolean', 
        true, 
        true, 
        true
    ) ON CONFLICT (name) DO NOTHING;
`;

export const down = `
    DELETE FROM tribble.setting WHERE name = 'allow_info_mentions_auto_reply';
`;
