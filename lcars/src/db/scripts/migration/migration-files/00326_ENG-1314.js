export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `
    CREATE TABLE IF NOT EXISTS {schema}.workflow (
      id SERIAL PRIMARY KEY,
      cartridge_id int NOT NULL REFERENCES {schema}.cartridge(id) ON DELETE CASCADE,
      display_name TEXT NOT NULL,
      description TEXT,
      is_enabled BOOLEAN DEFAULT false,

      created_by int REFERENCES tribble.user(id) ON DELETE SET NULL,
      updated_by int REFERENCES tribble.user(id) ON DELETE SET NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    CREATE OR REPLACE TRIGGER update_updated_at_before_update
    BEFORE INSERT OR UPDATE ON {schema}.workflow
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
  `;
export const down = `
  DROP TABLE IF EXISTS {schema}.workflow;
  `;
