export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];

export const up = `INSERT INTO tribble.setting (
  name,
  label,
  description,
  type,
  is_client,
  tribble_editable,
  default_string
) VALUES (
  'gong_ingestion_start_date',
  'Gong Ingestion Start Date',
  'Filter Gong calls to only ingest those created on or after this date (UTC). Leave blank to ingest all.',
  'string',
  true,
  false,
  null
) ON CONFLICT (name) DO NOTHING;`;

export const down = `DELETE FROM tribble.setting WHERE name = 'gong_ingestion_start_date';`;
