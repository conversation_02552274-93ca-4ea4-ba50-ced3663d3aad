export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
  ALTER TABLE {schema}.conversation_detail ADD COLUMN IF NOT EXISTS is_injected_message BOOLEAN DEFAULT FALSE;

  CREATE INDEX IF NOT EXISTS idx_conversation_detail_is_injected_msg
  ON {schema}.conversation_detail USING btree
  (is_injected_message ASC NULLS LAST)
  TABLESPACE pg_default;
`;

export const down = `
  ALTER TABLE {schema}.conversation_detail DROP COLUMN IF EXISTS is_injected_message;
`;
