export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `
  CREATE TABLE IF NOT EXISTS {schema}.integration_asset_setting (
    id BIGSERIAL PRIMARY KEY,
    integration_id BIGINT REFERENCES tribble.integration_system(id),
    asset_id BIGINT,
    metadata_tags JSONB DEFAULT '{}'::jsonb, -- The metadata tags associated with the asset.
    privacy TEXT, -- The default privacy setting for the asset (e.g., 'private', 'public').
    UNIQUE(integration_id, asset_id)
  );
  
  CREATE INDEX IF NOT EXISTS 
    idx_integration_asset_setting_int_asset_id ON {schema}.integration_asset_setting(integration_id, asset_id);
`;

export const down = `
  DROP TABLE IF EXISTS {schema}.integration_asset_setting;
`;
