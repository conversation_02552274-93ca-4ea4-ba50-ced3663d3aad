export const allCustomers = false;
export const justTribble = false;
export const justTheseSchema = ['c000189'];

export const up = `
DO $$
BEGIN
    -- Perform the update only if the schema exists
    IF EXISTS (SELECT 1 FROM information_schema.schemata WHERE schema_name = '{schema}') THEN
        -- Separately identified these ids as the affected ones
        -- Controlling the ids to ensure we don't update any other records
        WITH content_detail_ids AS (
            SELECT unnest(ARRAY[465,537,538,539,540,541,542,544,546,549,550,556,559,560,561,563,564,565,566,568,569,571,572,573,574,575,577,578,579,580,581,583,584,585,587,588,589,590,591,592,594,595,597,598,599,600,601,603,606,607,608,609,610,613,616,617,619,621,622,623,625,626,627,628,633,635,636,637,638,639,640,642,643,644,645,647,649]) AS id  
        )
        UPDATE {schema}.e2e_answer_entry eae
        SET answer = cd.output_modified->>'text'
        FROM {schema}.content_detail cd
        WHERE eae.content_detail_id = cd.id
            AND cd.id IN (SELECT id FROM content_detail_ids)
            AND cd.output_modified->>'text' IS NOT NULL
            AND eae.answer IS NOT NULL
            AND eae.type = 'free_text';
    END IF;
EXCEPTION
    WHEN undefined_table THEN
        RAISE NOTICE 'Schema or tables do not exist, no update performed';
    WHEN OTHERS THEN
        RAISE NOTICE 'An error occurred: %', SQLERRM;
END;
$$;
`;

export const down = `
    -- This is a one-way migration.
`;
