export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `
    CREATE TABLE IF NOT EXISTS {schema}.gong_calls (
        id BIGSERIAL PRIMARY KEY,
        gong_id TEXT NOT NULL UNIQUE,
        url TEXT,
        title TEXT,
        scheduled TIMESTAMP WITH TIME ZONE,
        started TIMESTAMP WITH TIME ZONE,
        duration INTEGER,
        primary_user_id VARCHAR(255),
        direction TEXT,
        system TEXT,
        scope TEXT,
        media TEXT,
        language TEXT,
        workspace_id TEXT,
        sdr_disposition TEXT,
        client_unique_id TEXT,
        custom_data TEXT,
        purpose TEXT,
        meeting_url TEXT,
        is_private BOOLEAN,
        calendar_event_id TEXT,
        last_ingested_at TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        created_by INTEGER NOT NULL REFERENCES tribble.user(id) ON DELETE CASCADE
    );

    -- Create an index on gong_id since it will likely be used for lookups
    CREATE INDEX IF NOT EXISTS gong_calls_gong_id_idx ON {schema}.gong_calls(gong_id);
`;

export const down = `
    DROP TABLE IF EXISTS {schema}.gong_calls;
`;
