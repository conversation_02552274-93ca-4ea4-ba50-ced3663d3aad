export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = /*sql*/ `
  -- First remove any duplicate node_ids if they exist
  WITH duplicates AS (
    SELECT node_id
    FROM (
      SELECT node_id, COUNT(*) 
      FROM {schema}.block_item
      WHERE node_id IS NOT NULL
      GROUP BY node_id
      HAVING COUNT(*) > 1
    ) t
  )
  UPDATE {schema}.block_item
  SET node_id = NULL
  WHERE node_id IN (SELECT node_id FROM duplicates);

  -- Drop existing constraint if it exists
  ALTER TABLE {schema}.block_item
  DROP CONSTRAINT IF EXISTS block_item_node_id_unique;

  -- Generate UUIDs for any NULL node_ids
  UPDATE {schema}.block_item
  SET node_id = gen_random_uuid()::text
  WHERE node_id IS NULL;

  -- Add NOT NULL constraint
  ALTER TABLE {schema}.block_item
  ALTER COLUMN node_id SET NOT NULL;

  -- Then add the unique constraint
  ALTER TABLE {schema}.block_item
  ADD CONSTRAINT block_item_node_id_unique UNIQUE (node_id);
`;

export const down = /*sql*/ `
  ALTER TABLE {schema}.block_item
  ALTER COLUMN node_id DROP NOT NULL;

  ALTER TABLE {schema}.block_item
  DROP CONSTRAINT IF EXISTS block_item_node_id_unique;
`;
