export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
    -- Convert existing tribble_knowledge_base entries from grouped_documents to document type
    UPDATE {schema}.access_sequence 
    SET 
        type = 'document',
        is_single_source = true,
        document_id = (
            SELECT id 
            FROM {schema}.document 
            WHERE file_name = 'tribble_knowledge_base' 
              AND deleted_date IS NULL
            LIMIT 1
        ),
        value = (
            SELECT id::text 
            FROM {schema}.document 
            WHERE file_name = 'tribble_knowledge_base' 
              AND deleted_date IS NULL
            LIMIT 1
        )
    WHERE type = 'grouped_documents' 
      AND value = 'tribble_knowledge_base'
      AND EXISTS (
          SELECT 1 
          FROM {schema}.document 
          WHERE file_name = 'tribble_knowledge_base' 
            AND deleted_date IS NULL
      );
`;

export const down = `
    -- Revert tribble_knowledge_base entries back to grouped_documents type
    UPDATE {schema}.access_sequence 
    SET 
        type = 'grouped_documents',
        is_single_source = false,
        document_id = NULL,
        value = 'tribble_knowledge_base'
    WHERE type = 'document' 
      AND document_id = (
          SELECT id 
          FROM {schema}.document 
          WHERE file_name = 'tribble_knowledge_base' 
            AND deleted_date IS NULL
          LIMIT 1
      );
`;
