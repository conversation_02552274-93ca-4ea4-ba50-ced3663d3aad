export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = /*sql*/ `
    CREATE TABLE IF NOT EXISTS {schema}.questionnaire_comment (
      id SERIAL PRIMARY KEY,
      content_detail_id INTEGER NOT NULL REFERENCES {schema}.content_detail(id) ON DELETE CASCADE,
      created_by_id INTEGER NOT NULL REFERENCES tribble.user(id),
      created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
      content JSONB NOT NULL,
      parent_id INTEGER REFERENCES {schema}.questionnaire_comment(id) ON DELETE CASCADE,
      is_resolved BOOLEAN DEFAULT FALSE,
      resolved_by_id INTEGER REFERENCES tribble.user(id),
      resolved_at TIMESTAMP WITH TIME ZONE
    );

    CREATE INDEX IF NOT EXISTS questionnaire_comment_content_detail_id_idx ON {schema}.questionnaire_comment(content_detail_id);
`;

export const down = /*sql*/ `
    DROP INDEX IF EXISTS questionnaire_comment_content_detail_id_idx;
    DROP TABLE IF EXISTS {schema}.questionnaire_comment;
`;
