export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
  CREATE TABLE IF NOT EXISTS {schema}.rfx_response_document (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    highlevel_summary TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    content_id INTEGER,
    CONSTRAINT rfx_response_document_content_id_fkey FOREIGN KEY (content_id)
        REFERENCES {schema}.content (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        -- Maybe we don't want to cascade deletes here?
        ON DELETE SET NULL
  );

  CREATE TABLE IF NOT EXISTS {schema}.rfx_section_outline (
    id SERIAL PRIMARY KEY,
    rfx_response_document_id INTEGER REFERENCES {schema}.rfx_response_document(id),
    name TEXT NOT NULL,
    content TEXT,
    source_page_start INTEGER NOT NULL,
    source_page_end INTEGER NOT NULL,
    source_text TEXT,
    seq INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    -- ensure page_end >= page_start
    CONSTRAINT rfx_section_outline_page_range_check CHECK (source_page_end >= source_page_start)
  );

  CREATE INDEX IF NOT EXISTS idx_rfx_section_outline_document_id
    ON {schema}.rfx_section_outline (rfx_response_document_id);
`;

export const down = `
  DROP INDEX IF EXISTS {schema}.idx_rfx_section_outline_document_id;

  DROP TABLE IF EXISTS {schema}.rfx_section_outline;
  DROP TABLE IF EXISTS {schema}.rfx_response_document;
`;
