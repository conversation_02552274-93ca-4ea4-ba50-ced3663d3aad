export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];
export const up = `INSERT into tribble.integration_system 
    (system_name, label,  description, auth_type, is_airbyte, hide_from_integration_modal) 
    VALUES ('external_content_management', 'External Content Management', 'Token can be used for managing content in the Tribble Brain via HTTP requests.', 'APIKEY', false, true)
    on conflict (system_name) do nothing;
    `;

export const down = `DELETE FROM tribble.integration_system WHERE system_name = 'external_content_management';`;
