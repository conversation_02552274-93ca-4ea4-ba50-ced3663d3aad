export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];
export const up = `INSERT INTO tribble.setting (name, label, description, type, is_client, tribble_editable, default_boolean)
VALUES ('kg_enable_multi_hop_reasoning', 'Enable Multi-Hop Reasoning', 'Enable multi-hop reasoning in knowledge graph to discover indirect relationships between entities.', 'boolean', true, true, false) ON CONFLICT DO NOTHING;`;
export const down = `
DO $$
DECLARE 
    schema_record record;
    query text;
BEGIN
    FOR schema_record IN (
        SELECT schema_name 
        FROM information_schema.schemata 
        WHERE schema_name LIKE 'c00%'
        AND schema_name NOT LIKE '%_custom'
        AND schema_name NOT LIKE '%_integration'
    )
    LOOP
        query := format(
            'DELETE FROM  %1$s.client_setting where setting_id in (select id from tribble.setting where name = ''kg_enable_multi_hop_reasoning'')',
            schema_record.schema_name
        );
        EXECUTE query;
    END LOOP;
END
$$;

DELETE FROM tribble.setting WHERE name = 'kg_enable_multi_hop_reasoning';`;
