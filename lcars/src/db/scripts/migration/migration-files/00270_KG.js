export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `
    -- Create graph_nodes table
  CREATE TABLE IF NOT EXISTS {schema}.graph_nodes (
    id BIGSERIAL PRIMARY KEY,
    content TEXT NOT NULL,
    source_document_id INT REFERENCES {schema}.document(id) ON DELETE CASCADE,
    metadata JSONB NOT NULL DEFAULT '{}'::jsonb,
    embedding vector(1536),
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    deleted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
  );

  -- Indexes for graph_nodes
  CREATE INDEX IF NOT EXISTS idx_graph_nodes_embedding
  ON {schema}.graph_nodes USING ivfflat (embedding) WITH (lists = 100);

  CREATE INDEX IF NOT EXISTS idx_graph_nodes_source_document
  ON {schema}.graph_nodes(source_document_id);

  CREATE INDEX IF NOT EXISTS idx_graph_nodes_is_deleted
  ON {schema}.graph_nodes(is_deleted);

  CREATE INDEX IF NOT EXISTS idx_graph_nodes_content_ilike 
  ON {schema}.graph_nodes USING GIN (content gin_trgm_ops);

  CREATE INDEX IF NOT EXISTS idx_graph_nodes_content_similarity
  ON {schema}.graph_nodes USING GIN (LOWER(content) gin_trgm_ops);

  -- Create graph_edges table
  CREATE TABLE IF NOT EXISTS {schema}.graph_edges (
    id BIGSERIAL PRIMARY KEY,
    source_id INT NOT NULL REFERENCES {schema}.graph_nodes(id) ON DELETE CASCADE,
    target_id INT NOT NULL REFERENCES {schema}.graph_nodes(id) ON DELETE CASCADE,
    relation TEXT,
    domain TEXT,
    weight FLOAT DEFAULT 1.0,
    metadata JSONB NOT NULL DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
  );

  -- Indexes for graph_edges
  CREATE INDEX IF NOT EXISTS idx_graph_edges_source
  ON {schema}.graph_edges(source_id);

  CREATE INDEX IF NOT EXISTS idx_graph_edges_target
  ON {schema}.graph_edges(target_id);

  CREATE INDEX IF NOT EXISTS idx_graph_edges_relation
  ON {schema}.graph_edges(relation);

  CREATE INDEX IF NOT EXISTS idx_graph_edges_domain
  ON {schema}.graph_edges(domain);

  CREATE INDEX IF NOT EXISTS idx_graph_edges_updated_at
  ON {schema}.graph_edges(updated_at);

  -- Create graph_feedback table
  CREATE TABLE IF NOT EXISTS {schema}.graph_feedback (
    id BIGSERIAL PRIMARY KEY,
    query TEXT NOT NULL,
    node_id INT REFERENCES {schema}.graph_nodes(id) ON DELETE SET NULL,
    edge_id INT REFERENCES {schema}.graph_edges(id) ON DELETE SET NULL,
    feedback_score INTEGER NOT NULL,
    user_id int REFERENCES tribble.user(id) ON DELETE SET NULL,
    metadata JSONB NOT NULL DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
  );

  -- Indexes for graph_feedback
  CREATE INDEX IF NOT EXISTS idx_graph_feedback_node
  ON {schema}.graph_feedback(node_id);

  CREATE INDEX IF NOT EXISTS idx_graph_feedback_edge
  ON {schema}.graph_feedback(edge_id);
  `;

export const down = `
  DROP TABLE IF EXISTS {schema}.graph_feedback;
  DROP TABLE IF EXISTS {schema}.graph_edges;
  DROP TABLE IF EXISTS {schema}.graph_nodes;
  
  -- No need to explicitly drop indexes as they're dropped when the table is dropped
`;
