export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
    CREATE TABLE IF NOT EXISTS {schema}.content_detail_insight (
        content_detail_id BIGINT NOT NULL REFERENCES {schema}.content_detail(id) ON DELETE CASCADE,
        insight_id BIGINT NOT NULL REFERENCES {schema}.embedding(id) ON DELETE CASCADE,
        PRIMARY KEY (content_detail_id, insight_id)
    );

    CREATE INDEX IF NOT EXISTS idx_content_detail_insight_content_detail_id
    ON {schema}.content_detail_insight(content_detail_id);

    CREATE INDEX IF NOT EXISTS idx_content_detail_insight_insight_id
    ON {schema}.content_detail_insight(insight_id);
`;

export const down = `
    DROP TABLE IF EXISTS {schema}.content_detail_insight;
`;
