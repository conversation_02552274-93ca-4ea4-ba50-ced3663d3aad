export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
  -----------------------------------------------------------------------------
  -- 1. Migrate E2E Longform records (agentParams.longForm === true)
  -- These already have RFX records, so just need to add RFX_CONTENT record and associate
  -----------------------------------------------------------------------------
  WITH longform_content AS (
    SELECT 
      c.id as content_id,
      c.details->>'customer_name' as customer_name,
      c.details->>'owner_id' as owner_id,
      c.details->>'project_name' as name,
      c.details->>'opportunity_id' as opportunity_id,
      c.due_date as due_date,
      r.id as rfx_id,
      -- For this case, the status for rfx_content is simply copying the status of the rfx record
      r.status as status,
      CASE 
        WHEN j.status = 'finished' THEN 'finished'
        WHEN j.status = 'rejected' THEN 'rejected'
        ELSE 'new'
      END as new_rfx_status,
      COALESCE(
        r.created_at,
        j.created_date, 
        (c.details->'agentParams'->>'lastUpdated')::timestamp
      ) AS created_date,
      COALESCE(c.is_deleted, false) as is_deleted
    FROM {schema}.content c
    JOIN {schema}.rfx_bid_packet_file rbpf ON rbpf.content_id = c.id
    JOIN {schema}.rfx r ON r.id = rbpf.rfx_id
    LEFT JOIN {schema}.job j ON c.job_id = j.id
    WHERE 
      c.details ? 'agentParams'
      AND (c.details->'agentParams'->>'longForm')::boolean = true
      AND NOT EXISTS (
        SELECT 1 FROM {schema}.rfx_content rc 
        WHERE rc.content_id = c.id
      )
  ),
  inserted_rfx_content AS (
    INSERT INTO {schema}.rfx_content 
      (rfx_id, content_id, status, is_deleted, created_date)
    SELECT 
      rfx_id, content_id, status, is_deleted, created_date
    FROM longform_content
    RETURNING id, content_id, rfx_id
  ),
  rfx_updates AS (
    UPDATE {schema}.rfx
    SET 
      customer_name = lc.customer_name,
      due_date = lc.due_date,
      owner_id = (lc.owner_id)::integer,
      is_deleted = lc.is_deleted,
      opportunity_id = lc.opportunity_id,
      project_status = lc.new_rfx_status,
      name = COALESCE(lc.name, {schema}.rfx.name)
    FROM longform_content lc
    WHERE {schema}.rfx.id = lc.rfx_id
      -- Ensure owner exists before updating
      AND EXISTS (SELECT 1 FROM tribble.user u WHERE u.id = (lc.owner_id)::integer)
  ),
  insert_rfx_bid_packet_file_join AS (
    INSERT INTO {schema}.rfx_bid_packet_file_rfx_content 
      (rfx_bid_packet_file_id, rfx_content_id)
    SELECT 
      rbpf.id, irc.id
    FROM inserted_rfx_content irc
    JOIN {schema}.rfx_bid_packet_file rbpf ON rbpf.content_id = irc.content_id
    ON CONFLICT DO NOTHING
  ),
  -- Add owner user to rfx_content_user_join for longform
  insert_rfx_content_owner AS (
    INSERT INTO {schema}.rfx_content_user_join
      (rfx_content_id, user_id, is_owner)
    SELECT 
      irc.id, 
      (lc.owner_id)::integer, 
      TRUE
    FROM inserted_rfx_content irc
    JOIN longform_content lc ON lc.content_id = irc.content_id
    WHERE lc.owner_id IS NOT NULL
      -- Ensure owner exists before inserting join record
      AND EXISTS (SELECT 1 FROM tribble.user u WHERE u.id = (lc.owner_id)::integer)
    ON CONFLICT DO NOTHING
  ),
  -- Update rfx_response_document records with rfx_content_id for longform
  update_rfx_response_docs AS (
    UPDATE {schema}.rfx_response_document rrd
    SET rfx_content_id = irc.id
    FROM inserted_rfx_content irc
    JOIN {schema}.rfx_bid_packet_file rbpf ON rbpf.content_id = irc.content_id
    WHERE rrd.bid_packet_file_id = rbpf.id
      AND rrd.rfx_content_id IS NULL
  )
  -- Empty select to finalize the WITH chain
  SELECT 1;

  -----------------------------------------------------------------------------
  -- 2. Process Questionnaire records (agentParams.longForm === false)
  -- AND
  -- 3. Process Chrome created shell records (rfpId is null)
  -- But basically filtering all these as agentParams.longForm !== true
  -- These need both RFX and RFX_CONTENT records
  -----------------------------------------------------------------------------

  -- Add temporary column
  ALTER TABLE {schema}.rfx ADD COLUMN IF NOT EXISTS temp_content_id INTEGER;

  WITH questionnaire_content AS (
    SELECT DISTINCT
      c.id as content_id,
      c.details->>'customer_name' as customer_name,
      c.details->>'owner_id' as owner_id,
      COALESCE(
	  	  c.details->>'project_name',
		    d.label
	    ) as name,
      c.details->>'opportunity_id' as opportunity_id,
      c.due_date as due_date,
      COALESCE(
        j.created_date, 
        (c.details->'agentParams'->>'lastUpdated')::timestamp,
		    e.created_at
      ) AS created_date,
      COALESCE(c.is_deleted, false) as is_deleted,
      j.status as job_status,
      -- Mapping job status to the new rfx_content status
      CASE 
        WHEN j.status = 'finished' THEN 'finished'
        WHEN j.status = 'processing' AND j.type = 'analyzed_excel' THEN 'spreadsheet_confirm_analysis'
        WHEN j.status IS NULL AND j.type IS NULL AND j.id IS NULL THEN 'spreadsheet_confirm_analysis'
        WHEN j.status = 'processing' AND j.type = 'analyze_excel' THEN 'analyzing'
        WHEN j.status = 'processed' AND j.type = 'answer_rfp' THEN 'finished'
        WHEN j.status = 'processing' AND j.type = 'answer_rfp' THEN 'in_review'
        ELSE j.status
      END as new_rfx_content_status,
      CASE 
        WHEN j.status = 'finished' THEN 'finished'
        ELSE 'new'
      END as new_rfx_status
    FROM {schema}.content c
    LEFT JOIN {schema}.job j ON c.job_id = j.id
    LEFT JOIN {schema}.document d ON c.job_id = d.job_id
    LEFT JOIN {schema}.e2e_workbook e ON c.id = e.content_id
    -- attempting to filter questionnaire records...(??)
    WHERE ((c.details->'agentParams'->>'longForm')::boolean IS NOT TRUE 
      OR NOT (c.details ? 'agentParams'))
      -- ASSUME if no project name and no document label then it's not valid
      AND COALESCE(
	  	   c.details->>'project_name',
		     d.label
	    ) IS NOT NULL
      AND NOT EXISTS (
        SELECT 1 FROM {schema}.rfx_content rc 
        WHERE rc.content_id = c.id
      )
  ),
  inserted_rfx AS (
    INSERT INTO {schema}.rfx 
      (name, created_at, customer_name, due_date, owner_id, is_deleted, opportunity_id, project_status, temp_content_id)
    SELECT 
      COALESCE(name, 'Project #' || content_id), 
      created_date,
      customer_name,
      due_date,
      (owner_id)::integer,
      is_deleted,
      opportunity_id,
      new_rfx_status,
      content_id  -- Store content_id in temp column
    FROM questionnaire_content
    -- Ensure owner exists before inserting RFX record
    WHERE EXISTS (SELECT 1 FROM tribble.user u WHERE u.id = (questionnaire_content.owner_id)::integer)
    RETURNING id, temp_content_id
  ),
  inserted_rfx_content AS (
    INSERT INTO {schema}.rfx_content 
      (rfx_id, content_id, status, is_deleted, created_date)
    SELECT 
      ir.id, 
      ir.temp_content_id, 
      qc.new_rfx_content_status, 
      qc.is_deleted,
      qc.created_date
    FROM inserted_rfx ir
    JOIN questionnaire_content qc ON qc.content_id = ir.temp_content_id
    RETURNING id, content_id, rfx_id
  ),
  -- For questionnaire content, add any existing rfx_bid_packet_file connections
  insert_rfx_bid_packet_file_join AS (
    INSERT INTO {schema}.rfx_bid_packet_file_rfx_content 
      (rfx_bid_packet_file_id, rfx_content_id)
    SELECT 
      rbpf.id, irc.id
    FROM inserted_rfx_content irc
    JOIN {schema}.rfx_bid_packet_file rbpf ON rbpf.content_id = irc.content_id
    ON CONFLICT DO NOTHING
  ),
  -- Add owner user to rfx_content_user_join for questionnaires
  insert_rfx_content_owner AS (
    INSERT INTO {schema}.rfx_content_user_join
      (rfx_content_id, user_id, is_owner)
    SELECT 
      irc.id, 
      r.owner_id, 
      TRUE
    FROM inserted_rfx_content irc
    JOIN {schema}.rfx r ON r.id = irc.rfx_id
    WHERE r.owner_id IS NOT NULL
      -- Ensure owner exists before inserting join record (owner_id comes from rfx here)
      AND EXISTS (SELECT 1 FROM tribble.user u WHERE u.id = r.owner_id)
    ON CONFLICT DO NOTHING
  ),
  -- Update rfx_response_document records with rfx_content_id for questionnaires
  update_rfx_response_docs AS (
    UPDATE {schema}.rfx_response_document rrd
    SET rfx_content_id = irc.id
    FROM inserted_rfx_content irc
    JOIN {schema}.rfx_bid_packet_file rbpf ON rbpf.content_id = irc.content_id
    WHERE rrd.bid_packet_file_id = rbpf.id
      AND rrd.rfx_content_id IS NULL
  )
  -- Empty select to finalize the WITH chain
  SELECT 1;

  -- Remove temporary column
  ALTER TABLE {schema}.rfx DROP COLUMN IF EXISTS temp_content_id;

  -----------------------------------------------------------------------------
  -- 4. Migrate any remaining rfx_response_document records
  -----------------------------------------------------------------------------
  UPDATE {schema}.rfx_response_document rrd
  SET rfx_content_id = rc.id
  FROM {schema}.rfx_content rc
  JOIN {schema}.rfx_bid_packet_file rbpf ON rbpf.content_id = rc.content_id
  WHERE rrd.bid_packet_file_id = rbpf.id
    AND rrd.rfx_content_id IS NULL;

  -----------------------------------------------------------------------------
  -- 5. Add any remaining owner users from rfx to rfx_content_user_join 
  -----------------------------------------------------------------------------
  INSERT INTO {schema}.rfx_content_user_join (rfx_content_id, user_id, is_owner)
  SELECT 
    rc.id, 
    r.owner_id, 
    TRUE
  FROM {schema}.rfx_content rc
  JOIN {schema}.rfx r ON r.id = rc.rfx_id
  WHERE r.owner_id IS NOT NULL
    AND NOT EXISTS (
      SELECT 1 FROM {schema}.rfx_content_user_join rcuj
      WHERE rcuj.rfx_content_id = rc.id
        AND rcuj.user_id = r.owner_id
    )
    -- Ensure owner exists before inserting join record
    AND EXISTS (SELECT 1 FROM tribble.user u WHERE u.id = r.owner_id)
  ON CONFLICT DO NOTHING;
`;

export const down = `
  -- No direct way to reverse these data migrations without losing data
  -- This is intentionally empty as data migrations typically don't have clean reversions
  -- Manual intervention would be required to undo these changes
  -- We can drop the new tables, but new rfx records would need to be deleted in a more targeted way
  --   * maybe can look for pg commit timestamp within the migration period
`;
