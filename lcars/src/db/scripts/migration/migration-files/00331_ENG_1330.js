export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

// Migration to fix "hanging" RFX records that have no content records
export const up = `
  UPDATE {schema}.rfx 
  SET is_deleted = true
  WHERE id IN (
    SELECT 
      r.id
    FROM {schema}.rfx r
    LEFT JOIN {schema}.rfx_content rc ON r.id = rc.rfx_id
    WHERE COALESCE(r.is_deleted, FALSE) != TRUE
      AND r.project_status IS NOT NULL
    GROUP BY r.id
    HAVING 
      -- No rfx_content records
      COUNT(rc.id) = 0 
      -- All rfx_content records are deleted
      OR COUNT(rc.id) = COUNT(CASE WHEN COALESCE(rc.is_deleted, FALSE) = TRUE THEN 1 END)
  );
  `;

export const down = `
  -- No down migration
`;
