export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];

export const up = `
  CREATE TABLE IF NOT EXISTS {schema}.conversation_pinned (
    id SERIAL PRIMARY KEY,
    conversation_id INTEGER,
    pinned_by INTEGER NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    image_name TEXT,
    created_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP WITH TIME ZONE,
    is_deleted BOOLEAN DEFAULT FALSE,

    -- Foreign key constraints
    FOREIGN KEY (pinned_by) REFERENCES tribble.user(id) ON DELETE CASCADE,
    FOREIGN KEY (conversation_id) REFERENCES {schema}.conversation(id) ON DELETE CASCADE
  );
  
  -- Index for performance on common queries
  CREATE INDEX IF NOT EXISTS idx_conversation_pinned_user_client ON {schema}.conversation_pinned(pinned_by);
  CREATE INDEX IF NOT EXISTS idx_conversation_pinned_created_date ON {schema}.conversation_pinned(created_date DESC);
`;

export const down = `
  DROP TABLE IF EXISTS {schema}.conversation_pinned;
  DROP INDEX IF EXISTS idx_conversation_pinned_user_client;
  DROP INDEX IF EXISTS idx_conversation_pinned_message_id;
  DROP INDEX IF EXISTS idx_conversation_pinned_created_date;
`;
