export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `
  -- box asset
  ALTER TABLE IF EXISTS {schema}.box_asset ALTER COLUMN name DROP NOT NULL;

  --DocumentType
  -- First drop the existing constraint
  ALTER TABLE IF EXISTS {schema}.document_type 
  DROP CONSTRAINT IF EXISTS document_type_priority_id_fkey;
  ALTER TABLE IF EXISTS {schema}.document_type
  ADD CONSTRAINT document_type_priority_id_fkey 
  FOREIGN KEY (priority_id) 
  REFERENCES {schema}.document_type_priority(id)
  ON DELETE SET NULL;
 
 --embedding
  ALTER TABLE IF EXISTS {schema}.embedding
  DROP COLUMN IF EXISTS source_effective_date;

-- GDriveAsset

ALTER TABLE IF EXISTS {schema}.gdrive_asset 
  DROP CONSTRAINT IF EXISTS gdrive_asset_document_id_fkey
  , DROP CONSTRAINT IF EXISTS gdrive_asset_job_id_fkey
  , DROP CONSTRAINT IF EXISTS gdrive_asset_task_id_fkey;
  
  ALTER TABLE IF EXISTS {schema}.gdrive_asset
  
  ADD CONSTRAINT gdrive_asset_document_id_fkey 
  FOREIGN KEY (document_id) 
  REFERENCES {schema}.document(id)
  ON DELETE CASCADE,
  
  ADD CONSTRAINT gdrive_asset_job_id_fkey
  FOREIGN KEY (job_id)
  REFERENCES {schema}.job(id),

  ADD CONSTRAINT gdrive_asset_task_id_fkey
  FOREIGN KEY (task_id) 
  REFERENCES {schema}.task(id)
  ON DELETE SET NULL;
  
  -- GraphFeedback
  ALTER TABLE IF EXISTS {schema}.graph_feedback
  DROP CONSTRAINT IF EXISTS graph_feedback_edge_id_fkey,
  DROP CONSTRAINT IF EXISTS graph_feedback_node_id_fkey;
  
  ALTER TABLE IF EXISTS {schema}.graph_feedback
  ADD CONSTRAINT graph_feedback_edge_id_fkey
  FOREIGN KEY (edge_id)
  REFERENCES {schema}.graph_edges(id)
  ON DELETE SET NULL,
  ADD CONSTRAINT graph_feedback_node_id_fkey
  FOREIGN KEY (node_id)
  REFERENCES {schema}.graph_nodes(id)
  ON DELETE SET NULL;
  
  --Task Owner
  ALTER TABLE IF EXISTS {schema}.task_owner
  DROP CONSTRAINT IF EXISTS task_owner_task_embedding_id_fkey,
  DROP CONSTRAINT IF EXISTS task_owner_task_id_fkey;

  ALTER TABLE IF EXISTS {schema}.task_owner
  ADD CONSTRAINT task_owner_task_embedding_id_fkey
  FOREIGN KEY (task_embedding_id)
  REFERENCES {schema}.task_embedding(id)
  ON DELETE CASCADE,
  ADD CONSTRAINT task_owner_task_id_fkey
  FOREIGN KEY (task_id)
  REFERENCES {schema}.task(id)
  ON DELETE CASCADE;


  --TeamsThread
  ALTER TABLE IF EXISTS {schema}.teams_thread ALTER COLUMN reply_to_id DROP NOT NULL;
  
  
  --ZendeskAsset
  ALTER TABLE IF EXISTS {schema}.zendesk_asset ALTER COLUMN blob_id SET NOT NULL;
  
  `;

export const down = `
  select 1 as down_not_implemented;
  `;
