export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];
export const up = `INSERT INTO tribble.setting (name, label, description, type, is_client, tribble_editable, default_string)
VALUES ('client_highspot_api_base_url', 'Highspot API Base URL', 'The API URL for the customer for Highspot (default: https://api.highspot.com/v1.0)', 'string', true, true, '') ON CONFLICT DO NOTHING;`;
export const down = `DELETE FROM tribble.setting WHERE name = 'client_highspot_api_base_url';`;
