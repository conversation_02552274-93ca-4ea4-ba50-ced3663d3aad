// Categories
export const allCustomers = true;
export const justTribble = false;
export const justTheseSchema = [];
export const up = `
    CREATE TABLE IF NOT EXISTS {schema}.workflow_category (
      id SERIAL PRIMARY KEY,
      name text NOT NULL,
      label text NOT NULL,
      created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
      created_by int REFERENCES tribble.user(id) ON DELETE SET NULL,
      updated_by int REFERENCES tribble.user(id) ON DELETE SET NULL
    );

    CREATE OR REPLACE TRIGGER update_updated_at_before_update
    BEFORE INSERT OR UPDATE ON {schema}.workflow_category
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

    ALTER TABLE {schema}.workflow
    ADD COLUMN IF NOT EXISTS workflow_category_id int REFERENCES {schema}.workflow_category(id) ON DELETE SET NULL;
  `;

export const down = `
    ALTER TABLE {schema}.workflow
    DROP COLUMN IF EXISTS workflow_category_id;
    DROP TABLE IF EXISTS {schema}.workflow_category;
  `;
