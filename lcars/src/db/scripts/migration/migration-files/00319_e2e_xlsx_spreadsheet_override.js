export const allCustomers = false;
export const justTribble = true;
export const justTheseSchema = [];

export const up = `
INSERT INTO tribble.setting (name, label, description, type, default_boolean, is_client, tribble_editable)
VALUES (
  'use_spreadsheet_ui_override', 
  'E2E XLSX Always Use Spreadsheet UI', 
  'If true, the E2E XLSX will always use the Spreadsheet UI, even if the project is not a Gartner MQ', 
  'boolean', 
  false, 
  true, 
  true
) ON CONFLICT DO NOTHING;`;

export const down = `DELETE FROM tribble.setting WHERE name = 'use_spreadsheet_ui_override';`;
