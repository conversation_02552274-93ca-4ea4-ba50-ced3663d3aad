name: PR Title Check

on:
  pull_request:
    types: [opened, reopened, edited, synchronize]

jobs:
  check_pr_title:
    runs-on: ubuntu-latest
    steps:
      - name: Check PR Title Starts with ENG-
        run: |
          PR_TITLE="${{ github.event.pull_request.title }}"
          if [[ "$PR_TITLE" != ENG-* ]]; then
            echo "::error::PR title must start with 'ENG-'. Current title: '$PR_TITLE'"
            exit 1
          fi
          echo "PR title is valid."