name: Check Schema Changes

on:
  pull_request:
    paths:
      - 'lcars/src/db/scripts/migration/migration-files/**.js'
      - 'lcars/src/db/scripts/creation/all_sql.ts'

jobs:
  check-schema-changes:
    runs-on: ubuntu-latest
    env: # Environment variables
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      GITHUB_REPOSITORY: ${{ github.repository }}
      PR_NUMBER: ${{ github.event.number }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.x'

      - name: Install dependencies
        run: pip install PyGithub

      - name: Run schema check script
        run: python .github/scripts/check_schema_changes.py
