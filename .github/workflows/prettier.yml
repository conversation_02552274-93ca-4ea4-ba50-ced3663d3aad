name: Check Prettier Formatting

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

# Prevent concurrent runs of the same workflow
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  prettier_check:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    # Find all package-lock.json files and create a hash of their contents
    - name: Generate hash for all package-lock.json files
      id: package-locks
      run: echo "hash=$(find . -name "package-lock.json" -type f | grep -v "node_modules" | sort | xargs cat | shasum -a 256 | cut -d ' ' -f 1)" >> $GITHUB_OUTPUT
    
    # Cache all node_modules directories in the monorepo
    - name: Cache node_modules
      uses: actions/cache@v3
      id: node-modules-cache
      with:
        path: |
          **/node_modules
          !**/node_modules/.cache
        key: ${{ runner.os }}-modules-${{ steps.package-locks.outputs.hash }}
    
    # For cache hits, just ensure we use the right node version without the heavy setup action
    - name: Use preinstalled Node.js
      if: steps.node-modules-cache.outputs.cache-hit == 'true'
      run: |
        NODE_VERSION=$(node -v)
        if [[ $NODE_VERSION != v20* ]]; then
          echo "Using preinstalled Node.js $NODE_VERSION instead of v20"
        fi

    # Skip installation if we had a cache hit on all node_modules
    - name: Install Dependencies
      if: steps.node-modules-cache.outputs.cache-hit != 'true'
      run: npm ci --prefer-offline --no-audit --progress=false

    - name: Check Prettier Formatting
      run: |
        if [ "${{ github.event_name }}" = "pull_request" ]; then
          echo "Event: pull_request. Base ref: ${{ github.base_ref }}. Head SHA: ${{ github.sha }}"
          # Attempt to fetch the base reference with more depth to aid diff accuracy.
          # Default checkout is depth 1.
          echo "Fetching origin/${{ github.base_ref }} with depth 50..."
          git fetch origin ${{ github.base_ref }} --depth=50 || echo "Failed to fetch base_ref with depth, continuing with available history."

          # Get the list of files changed compared to the base branch
          FILES_IN_DIFF=$(git diff --name-only origin/${{ github.base_ref }} HEAD)

          if [ -z "$FILES_IN_DIFF" ]; then
            echo "No files found in diff between origin/${{ github.base_ref }} and HEAD."
          else
            echo "Files found in diff:"
            echo "${FILES_IN_DIFF}"

            # Create an array to store file paths
            declare -a FILES_TO_CHECK
            # Iterate over each file reported by git diff and check if it actually exists
            while IFS= read -r file_path; do
              if [ -z "$file_path" ]; then # Skip empty lines, if any
                continue
              fi
              if [ -f "${file_path}" ]; then
                FILES_TO_CHECK+=("${file_path}")
              else
                echo "Skipping non-existent file listed in diff: '${file_path}'"
              fi
            done <<< "${FILES_IN_DIFF}"
            
            printf "\n"

            if [ ${#FILES_TO_CHECK[@]} -gt 0 ]; then
              echo "Checking formatting of existing files from diff:"
              printf '%s\n' "${FILES_TO_CHECK[@]}"
              
              # Explicitly specify the plugins to ensure organize-imports is included
              npx prettier --check \
                "${FILES_TO_CHECK[@]}" 
            else
              echo "No existing files to format after filtering the diff."
            fi
          fi
        else
          # Check all files for pushes to other events (e.g., main branch)
          echo "Event: ${{ github.event_name }}. Checking all files in the repository."
          npx prettier --check --ignore-unknown .
        fi
