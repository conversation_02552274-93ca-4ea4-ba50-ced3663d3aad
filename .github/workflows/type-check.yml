name: Type check LCARS

on: 
  pull_request:
    paths:
      - 'lcars/**.ts'

# Prevent concurrent runs of the same workflow
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs: 
  type-check:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 1

    - name: Use Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'

    - name: Prune LCARS
      id: prune
      run: |
        npx -yes turbo@1.13.3 prune lcars
        echo "hash=$(find out -name "package-lock.json" -type f | grep -v "node_modules" | sort | xargs cat | shasum -a 256 | cut -d ' ' -f 1)" >> $GITHUB_OUTPUT

    # Cache all node_modules directories in the pruned output
    - name: Cache node modules
      uses: actions/cache@v3
      id: node-modules-cache
      with:
        path: 'out/**/node_modules'
        key: ${{ runner.os }}-lcars-modules-${{ steps.prune.outputs.hash }}

    # Skip installation if we had a cache hit on all node_modules
    - name: Install Dependencies
      if: steps.node-modules-cache.outputs.cache-hit != 'true'
      env:
        NODE_OPTIONS: "--max-old-space-size=4096"
      run: npm i --prefer-offline --no-audit --progress=false
      working-directory: out

    # Always run the build and type check
    - name: Run Build
      env:
        NODE_OPTIONS: "--max-old-space-size=4096"
      run: npm run build
      working-directory: out

    - name: Run TypeScript Compiler
      run: npx tsc --noEmit  
      working-directory: out/lcars