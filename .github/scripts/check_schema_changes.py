import os
import re

from github import Github

# Initialize GitHub API
github_token = os.environ["GITHUB_TOKEN"]
repo_name = os.environ["GITHUB_REPOSITORY"]
pr_number = os.environ["PR_NUMBER"]
g = Github(github_token)
repo = g.get_repo(repo_name)
pr = repo.get_pull(int(pr_number))

# Check for modified files
migration_modified = False
client_schema_table_created = False
creation_script_modified = False
all_sql_modified = False
schema_placeholder_present = False
all_customers_flag_set = False

TRIBBLE = "tribble"

for f in pr.get_files():
    if f.filename.startswith("lcars/src/db/scripts/migration/migration-files/"):
        # Fetch the file content
        file_content = repo.get_contents(
            f.filename, ref=pr.head.sha
        ).decoded_content.decode("utf-8")

        # Check for export const allCustomers = true; (case insensitive, flexible whitespace)
        if re.search(
            r"export\s+const\s+allCustomers\s*=\s*true;", file_content, re.IGNORECASE
        ):
            all_customers_flag_set = True  # Set the flag if the line is found

        if all_customers_flag_set and re.search(r"CREATE TABLE", file_content):
            client_schema_table_created = True

    elif f.filename == "lcars/src/db/scripts/creation/all_sql.ts":
        creation_script_modified = True

        # Check each line for the presence of "{schema}"
        file_content = repo.get_contents(
            f.filename, ref=pr.head.sha
        ).decoded_content.decode("utf-8")
        if re.search(r"\{schema\}", file_content):
            schema_placeholder_present = True

    elif f.filename.startswith("lcars/src/db/scripts/creation/index.ts"):
        all_sql_modified = True

comment_text = ""
# Check conditions and leave comments if needed
if all_customers_flag_set and not creation_script_modified:
    comment_text += "⚠️ Migration file(s) added but creation script not updated. Please double check.\n"

if creation_script_modified and not all_customers_flag_set:
    comment_text += "⚠️ Creation script modified but no migration files found. Please double check.\n"

if client_schema_table_created and not all_sql_modified:
    comment_text += "⚠️ Migration with new table added but imports from all_sql not updated. Please double check.\n"

if schema_placeholder_present:
    pr.create_issue_comment(
        "⚠️ You used '\{schema\}' in the creation script. Did you mean '\{clientId\}'?"
    )

if len(comment_text) > 0:
    pr.create_issue_comment(comment_text)
