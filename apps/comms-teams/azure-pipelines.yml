# Node.js Express Web App to Linux on Azure
# Build a Node.js Express app and deploy it to Azure as a Linux web app.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript

trigger:
  branches:
    include:
      - alonzo/comms-teams
  # This is important in order to trigger the fild only for the current folder. See root-level README.
  paths:
    include:
      - 'apps/comms-teams/*'
      - 'packages/*'

pr: none

variables:
  # Agent VM image name
  vmImageName: 'ubuntu-latest'

stages:
  - stage: Build
    displayName: Build
    jobs:
      - job: Build_App_Service
        displayName: Build Bot App
        pool:
          vmImage: $(vmImageName)

        steps:
          - task: NodeTool@0
            inputs:
              versionSpec: '20.19'
            displayName: 'Install Node.js'

          - script: |
              node $(Build.SourcesDirectory)/scripts/generate-version.js $(Build.SourcesDirectory)/apps/comms-teams
            displayName: 'Generate Version Info'

          - script: |
              npx -yes turbo@1.13.3 prune @tribble/comms-teams
            displayName: "Separate @tribble/comms-teams from the monorepo"
            workingDirectory: $(Build.SourcesDirectory)

          - script: |
              cp $(Build.SourcesDirectory)/apps/comms-teams/version.json $(Build.SourcesDirectory)/out/apps/comms-teams/
            displayName: 'Copy version.json after turbo prune'

          - script: |
              npm install --silent --include=optional
              npm run build
            displayName: 'npm install and build'
            failOnStderr: true
            workingDirectory: $(Build.SourcesDirectory)/out


          - script: |
              find . -type d \( -name dist -o -name node_modules \) -exec zip -r $(Build.ArtifactStagingDirectory)/$(Build.BuildId)_comms_teams.zip {} +;
              find . -type f \( -name package.json -o -name package-lock.json \) -exec zip -ur $(Build.ArtifactStagingDirectory)/$(Build.BuildId)_comms_teams.zip {} +;
              [ -f turbo.json ] && zip -ur $(Build.ArtifactStagingDirectory)/$(Build.BuildId)_comms_teams.zip turbo.json;
            displayName: 'Create release zip'
            workingDirectory: $(Build.SourcesDirectory)/out

          - task: PublishBuildArtifacts@1
            condition: 'true'
            displayName: 'Publish Artifact'
            inputs:
              PathtoPublish: $(Build.ArtifactStagingDirectory)/$(Build.BuildId)_comms_teams.zip
              ArtifactName: release_test
