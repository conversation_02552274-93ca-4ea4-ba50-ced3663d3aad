function capitalizeFirstLetter(string) {
  return string.charAt(0).toUpperCase() + string.slice(1);
}

export function createWelcomeCard(command?: string) {
  return {
    type: 'AdaptiveCard',
    body: [
      {
        type: 'TextBlock',
        size: 'extralarge',
        weight: 'bolder',
        text:
          (command ? capitalizeFirstLetter(command.toLowerCase()) + ', ' : '') +
          'Welcome to the Tribble Agent!',
        wrap: true,
        style: 'heading',
        horizontalAlignment: 'Center',
      },
      {
        type: 'TextBlock',
        text: 'A Digital Sales Engineer at Your Fingertips',
        wrap: true,
        weight: 'Bolder',
        horizontalAlignment: 'Center',
        spacing: 'Small',
      },
      {
        type: 'TextBlock',
        text: 'Revolutionize your sales strategy with AI-powered insights and web intelligence. The Tribble Sales Engineer Agent provides data-driven answers from your company data and real-time web searches.',
        wrap: true,
        spacing: 'Medium',
      },
      {
        type: 'FactSet',
        facts: [
          {
            title: 'Data-Driven Insights:',
            value: 'Tailored answers leveraging your company data.',
          },
          {
            title: 'Web Intelligence:',
            value: 'Up-to-date information with real-time web search.',
          },
          {
            title: 'Seamless Integration:',
            value: 'A natural fit within Microsoft Teams.',
          },
          {
            title: 'Empower Your Team:',
            value: 'Instant knowledge for informed decisions.',
          },
        ],
      },
      {
        type: 'TextBlock',
        text: 'Try asking questions like:',
        wrap: true,
        weight: 'Bolder',
        spacing: 'Medium',
      },
      {
        type: 'TextBlock',
        text: '"How do we encrypt data at rest?"  \n"What languages does our platform support?"  \n"Research our competitor <company name>"',
        wrap: true,
        spacing: 'Small',
        fontType: 'Monospace',
      },
      {
        type: 'TextBlock',
        text: 'Tribble is a paid service. You must have a valid subscription to use the Tribble Agent. For more information about signing up, please visit [tribble.ai](https://tribble.ai).',
        wrap: true,
        spacing: 'Medium',
      },
      {
        type: 'TextBlock',
        text: 'If you have a Tribble subscription and this is your first time using the Tribble Agent, type the "Help" button below for more connecting the teams app to your Tribble account.',
        wrap: true,
        spacing: 'Medium',
      },
      {
        type: 'TextBlock',
        text: 'Your experience matters to us. If you encounter any issues, please let us know at [<EMAIL>](mailto:<EMAIL>).',
        wrap: true,
        spacing: 'Medium',
      },
    ],
    msteams: {
      width: 'full',
    },
    $schema: 'http://adaptivecards.io/schemas/adaptive-card.json',
    version: '1.6',
  };
}

export function createHelpCard() {
  return {
    type: 'AdaptiveCard',
    body: [
      {
        type: 'TextBlock',
        size: 'extralarge',
        weight: 'bolder',
        text: 'Need Help?',
        wrap: true,
        style: 'heading',
        horizontalAlignment: 'Center',
      },
      {
        type: 'TextBlock',
        text: `If the Tribble Agent is unable to process your request, click [Connect with Tribble](${process.env.APP_PATH + '/client/settings/integrations'}) to be taken to the Tribble Admin Dashboard. From there, click 'Connect to Teams' to link your Teams app to the Tribble Agent. Return to the conversation and start asking questions.`,
        wrap: true,
        weight: 'Bolder',
        horizontalAlignment: 'Left',
        spacing: 'Small',
      },
      {
        type: 'TextBlock',
        text: 'If your Teams app is already linked to Tribble, starting asking questions to the Tribble Agent. For example:',
        wrap: true,
        spacing: 'Medium',
      },
      {
        type: 'TextBlock',
        text: '"How do we encrypt data at rest?"  \n"What languages does our platform support?"  \n"Research our competitor <company name>"',
        wrap: true,
        spacing: 'Small',
        fontType: 'Monospace',
      },
      {
        type: 'TextBlock',
        text: 'Your experience matters to us. If you encounter any issues, please let us know at [<EMAIL>](mailto:<EMAIL>).',
        wrap: true,
        spacing: 'Medium',
      },
    ],
    msteams: {
      width: 'full',
    },
    $schema: 'http://adaptivecards.io/schemas/adaptive-card.json',
    version: '1.6',
  };
}
