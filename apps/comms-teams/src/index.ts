import dotenv from 'dotenv';
dotenv.config();

import { createVersionHandler } from '@tribble/common-utils';
import { initializePool } from '@tribble/tribble-db/db_ops';
import express from 'express';
import path from 'path';
import { apiHandler, botHandler } from './botHandler';
import { getLogger } from './utils';

const app = express();

app.use(express.json());

app.use('/api/messages', botHandler);
app.use('/api/proactive', apiHandler);
app.get('/hello', (req, res) => {
  res.send('Hello World');
});

app.get('/api/version', createVersionHandler());

app.use('/public', express.static(path.resolve(__dirname, '../public')));

app.get('*', (req, res) => {
  res.json({ error: 'Route not found' });
});

export const port = process.env.port || process.env.PORT || 3978;

initializePool()
  .then(() => getLogger().initialize(undefined, true, 60000))
  .then(() =>
    app.listen(port, () => {
      console.log(`Bot/ME Server listening on http://localhost:${port}`);
    }),
  );
