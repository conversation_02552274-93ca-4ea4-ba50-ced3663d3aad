// Copyright (c) Microsoft Corporation. All rights reserved.
// Licensed under the MIT License.
import { BlobContainer, downloadBlob, getBlobUrl } from '@tribble/azure-helper';
import {
  ActionsHandler,
  System,
  UnifiedCommsMiddleware,
  attachSettings,
  createLoggerMiddleware,
  createTeamsConversationReference,
  createUnifiedMessageHandlerFactory,
  fetchConversationRecord,
  fileHandler,
  getClientByBotTeamId,
  getHelperFunckey,
  getInferenceTags,
  getMetadataFilters,
  getParameters,
  onlySpeakWhenSpokenToOrInDM,
  registerTeamsActions,
  registeredUsersOnly,
  runAgentIfFlag,
} from '@tribble/comms-shared';
import { getDB } from '@tribble/tribble-db/db_ops';
import axios from 'axios';
import {
  ActivityHandler,
  ActivityTypes,
  CardFactory,
  MessageFactory,
  TeamsActivityHandler,
  TurnContext,
} from 'botbuilder';
import {
  createHelpCard,
  createWelcomeCard,
} from '../adaptiveCards/welcomeCard';
import { getLogger, getMatchingUser } from '../utils';

export class DialogBot extends TeamsActivityHandler {
  actionsHandler: ActionsHandler;

  constructor() {
    super();
    this.actionsHandler = new ActionsHandler();
    registerTeamsActions(this.actionsHandler);
  }

  async onInstallationUpdateAddActivity(turnContext: TurnContext) {
    const cardObject = createWelcomeCard();
    const card = MessageFactory.attachment(
      CardFactory.adaptiveCard(cardObject),
    );
    await turnContext.sendActivity(card);

    const matchingUser = await getMatchingUser(turnContext);
    let client = matchingUser?.client;
    if (!client) {
      client = await getClientByBotTeamId(
        turnContext.activity.conversation.tenantId!,
      );
    }
    const conversationReference = TurnContext.getConversationReference(
      turnContext.activity,
    );
    const db = await getDB(client.database_schema_id);
    await createTeamsConversationReference(
      db,
      turnContext.activity.conversation.id,
      conversationReference,
      matchingUser?.user.id,
    );
  }

  async onMessageActivity(teamsContext: TurnContext) {
    const teamsMessageHandler = createUnifiedMessageHandlerFactory({
      prepend: [teamsLoginCheckSetClientByInstall],
      append: [saveConversationReference],
    });
    return teamsMessageHandler(
      createLoggerMiddleware(getLogger() as any),
      getParameters,
      registeredUsersOnly,
      onlySpeakWhenSpokenToOrInDM,
      getHelperFunckey,
      attachSettings,
      fetchConversationRecord,
      getMetadataFilters,
      fileHandler,
      getInferenceTags,
      runAgentIfFlag,
    )(teamsContext);
  }

  async onConversationUpdateActivity(context: TurnContext) {
    const matchingUser = await getMatchingUser(context);
    let client = matchingUser?.client;
    if (!client) {
      client = await getClientByBotTeamId(
        context.activity.conversation.tenantId!,
      );
    }
    const conversationReference = TurnContext.getConversationReference(
      context.activity,
    );
    const db = await getDB(client.database_schema_id);
    await createTeamsConversationReference(
      db,
      context.activity.conversation.id,
      conversationReference,
      matchingUser?.user.id,
    );
  }

  async onInvokeActivity(teamsContext: TurnContext) {
    if (teamsContext.activity.name === 'fileConsent/invoke') {
      return ActivityHandler.createInvokeResponse(
        await this.handleTeamsFileConsent(
          teamsContext,
          teamsContext.activity.value,
        ),
      );
    }
    return this.actionsHandler.run(teamsContext);
  }

  async handleTeamsFileConsentAccept(context, fileConsentCardResponse) {
    try {
      const { uuid, schema } = fileConsentCardResponse.context;
      const blobUrl = getBlobUrl(schema);
      const blob = await downloadBlob(blobUrl, BlobContainer.DOCS, uuid);

      await axios.put(fileConsentCardResponse.uploadInfo.uploadUrl, blob, {
        headers: {
          'Content-Type': 'image/png',
          'Content-Length': blob.length,
          'Content-Range': `bytes 0-${blob.length - 1}/${blob.length}`,
        },
      });

      await this.fileUploadCompleted(context, fileConsentCardResponse);
    } catch (e) {
      await this.fileUploadFailed(context, e.message);
    }
  }

  async handleTeamsFileConsentDecline(context, fileConsentCardResponse) {
    const reply = MessageFactory.text(
      `Declined. We won't upload file <b>${fileConsentCardResponse.context.filename}</b>.`,
    );
    reply.textFormat = 'xml';
    await context.sendActivity(reply);
  }

  async fileUploadCompleted(context, fileConsentCardResponse) {
    const downloadCard = {
      uniqueId: fileConsentCardResponse.uploadInfo.uniqueId,
      fileType: fileConsentCardResponse.uploadInfo.fileType,
    };
    const asAttachment = {
      content: downloadCard,
      contentType: 'application/vnd.microsoft.teams.card.file.info',
      name: fileConsentCardResponse.uploadInfo.name,
      contentUrl: fileConsentCardResponse.uploadInfo.contentUrl,
    };
    const reply = MessageFactory.text(
      `<b>File uploaded.</b> Your file <b>${fileConsentCardResponse.uploadInfo.name}</b> is ready to download`,
    );
    reply.textFormat = 'xml';
    reply.attachments = [asAttachment];
    await context.sendActivity(reply);
  }

  async fileUploadFailed(context, error) {
    const reply = MessageFactory.text(
      `<b>File upload failed.</b> Error: <pre>${error}</pre>`,
    );
    reply.textFormat = 'xml';
    await context.sendActivity(reply);
  }

  async run(context) {
    await super.run(context);
  }
}

async function teamsLoginCheckSetClientByInstall(params) {
  const { next, system } = params;
  if (system === System.Teams) {
    const context = params.teams;
    if (context.activity.type === ActivityTypes.Message) {
      const matchingUser = await getMatchingUser(context);
      let client = matchingUser?.client;
      if (!client) {
        client = await getClientByBotTeamId(
          context.activity.conversation.tenantId!,
        );
      }

      if (
        !context.activity.replyToId &&
        ['hello', 'hi', 'hey'].includes(context.activity.text?.toLowerCase())
      ) {
        const cardObject = createWelcomeCard(
          context.activity.text?.toLowerCase(),
        );
        const card = MessageFactory.attachment(
          CardFactory.adaptiveCard(cardObject),
        );
        await context.sendActivity(card);
        const conversationReference = TurnContext.getConversationReference(
          context.activity,
        );
        const db = await getDB(client.database_schema_id);
        await createTeamsConversationReference(
          db,
          context.activity.conversation.id,
          conversationReference,
          matchingUser?.user.id,
        );
        return;
      }
      if (
        !context.activity.replyToId &&
        ['help'].includes(context.activity.text?.toLowerCase())
      ) {
        const cardObject = createHelpCard();
        const card = MessageFactory.attachment(
          CardFactory.adaptiveCard(cardObject),
        );
        await context.sendActivity(card);
        return;
      }
      if (!client) {
        await context.sendActivity(
          `Your query cannot be processed by the Tribble agent.
                Your Teams tenant is not currently linked to the Tribble agent.
                Type 'help' to learn how to connect your Teams tenant to the Tribble agent.`,
        );
        return;
      }
      params.client = client;
    }
  }
  next();
}

const saveConversationReference: UnifiedCommsMiddleware = async (params) => {
  const { next, system } = params;
  if (system === System.Teams) {
    const context = params.teams;
    const conversationReference = TurnContext.getConversationReference(
      context.activity,
    );
    const db = await getDB(params.client.database_schema_id);
    await createTeamsConversationReference(
      db,
      context.activity.conversation.id,
      conversationReference,
      params.tribbleUser?.id,
    );
  }
  next();
};
