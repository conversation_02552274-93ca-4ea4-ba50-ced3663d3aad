import {
  CloudAdapter,
  ConfigurationBotFrameworkAuthentication,
  ConfigurationServiceClientCredentialFactory,
} from 'botbuilder';

import { getTeamsConversationReference } from '@tribble/comms-shared';
import {
  createDeleteTeamsMessage,
  createTeamsFileUpload,
  createTeamsUpsertMessage,
} from '@tribble/comms-shared/dist/handler/unifiedMiddleware/sendMessages';
import { getDB } from '@tribble/tribble-db/db_ops';
import pRetry from 'p-retry';
import { DialogBot } from './bots/dialogBot';
import config from './config';
import { getMatchingUser } from './utils';

const credentialsFactory = new ConfigurationServiceClientCredentialFactory({
  MicrosoftAppId: config.botId,
  MicrosoftAppPassword: config.botPassword,
  MicrosoftAppType: 'MultiTenant',
});

const botFrameworkAuthentication = new ConfigurationBotFrameworkAuthentication(
  {},
  credentialsFactory,
);

const adapter = new CloudAdapter(botFrameworkAuthentication);

// Catch-all for errors.
adapter.onTurnError = async (context, error) => {
  // This check writes out errors to console log .vs. app insights.
  // NOTE: In production environment, you should consider logging this to Azure
  //       application insights.
  console.error(
    `\n [onTurnError] context:${JSON.stringify(context, null, 2)} unhandled error: ${error}`,
  );

  // Send a trace activity, which will be displayed in Bot Framework Emulator
  await context.sendTraceActivity(
    'OnTurnError Trace',
    `${error}`,
    'https://www.botframework.com/schemas/error',
    'TurnError',
  );

  // Send a message to the user
  await context.sendActivity('The bot encounted an error or bug.');
};

const bot = new DialogBot();

export function botHandler(req, res) {
  adapter.process(req, res, async (context) => {
    await bot.run(context);
  });
}

export async function apiHandler(req, res) {
  try {
    const { data, schema, type } = req.body;
    const db = await getDB(schema);
    const conversationReferenceRecord = await getTeamsConversationReference(
      db,
      data.channel,
    );
    await pRetry(
      () =>
        adapter.continueConversationAsync(
          config.botId,
          conversationReferenceRecord.conversation_reference,
          async (context) => {
            try {
              if (type === 'message') {
                const upsertMessage = await createTeamsUpsertMessage({
                  teamsContext: context,
                  schema,
                });
                const newMessage = await upsertMessage(data);
                res.status(200).json(newMessage);
              } else if (type === 'file') {
                const user = await getMatchingUser(context);
                const uploadFile = createTeamsFileUpload(
                  context,
                  schema,
                  user.user.id,
                );
                const response = await uploadFile({
                  ...data,
                  file: Buffer.from(data.file, 'base64'),
                });
                res.status(200).json(response);
              } else if (type === 'delete') {
                const deleteMessage = await createDeleteTeamsMessage({
                  teamsContext: context,
                  schema,
                });
                await deleteMessage({
                  id: data.id,
                  threadId: data.threadId,
                });
                res.status(200).json({ success: true });
              }
            } catch (error) {
              console.error('Error sending proactive message:', error);
              console.error('Type:', type);
              console.error('Data:', data);
              console.error('Schema:', schema);
              res.status(500).send('Proactive message failed.');
            }
          },
        ),
      {
        retries: 3,
        minTimeout: 500,
        maxTimeout: 1000,
        factor: 1.5,
      },
    );
  } catch (error) {
    console.error(error);
    res.status(500).send('Proactive message failed.');
  }
}
