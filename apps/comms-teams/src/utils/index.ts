import { Logger } from '@tribble/common-utils';
import {
  UserClient,
  getUserAndClientByEmail,
  logDebugMessage,
} from '@tribble/comms-shared';
import { TeamsInfo, TurnContext } from 'botbuilder';

export async function getMatchingUser(
  turnContext: TurnContext,
): Promise<UserClient | null> {
  const teamsUser = await TeamsInfo.getMember(
    turnContext,
    turnContext.activity.from.id,
  );
  const matchingUser = await getUserAndClientByEmail(
    (teamsUser.email ?? teamsUser.userPrincipalName)!.toLocaleLowerCase(),
  );

  logDebugMessage(
    `[messageUserCheck] TribbleUser: ${JSON.stringify(matchingUser)}, platformUserId: ${turnContext.activity.from.id}, Platform: "TEAMS", Activity: ${JSON.stringify(
      turnContext.activity,
      null,
      2,
    )}`,
  );

  if (matchingUser && !matchingUser.user.is_deleted) {
    return matchingUser;
  }
  return null;
}

const logger = new Logger({
  appName: 'comms-teams',
});

export function getLogger() {
  return logger;
}
