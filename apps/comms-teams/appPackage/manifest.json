{"$schema": "https://developer.microsoft.com/en-us/json-schemas/teams/v1.16/MicrosoftTeams.schema.json", "manifestVersion": "1.16", "version": "1.0.2", "id": "${{TEAMS_APP_ID}}", "packageName": "com.microsoft.teams.extension", "developer": {"name": "Tribble, Inc.", "websiteUrl": "https://tribble.ai", "privacyUrl": "https://tribble.ai/terms-of-service", "termsOfUseUrl": "https://tribble.ai/privacy/"}, "icons": {"color": "tribble_logo.png", "outline": "tribble_outline.png"}, "name": {"short": "Tribble (${{APP_NAME_SUFFIX}})", "full": "Tribble (${{APP_NAME_SUFFIX}})"}, "description": {"short": "Questionnaire automation trained on your documents", "full": "Our Sales Engineer Agent helps you answer questions about your company and products, guide you through deals, and bridge knowledge gaps about your solutions - all within Microsoft Teams."}, "accentColor": "#FFFFFF", "bots": [{"botId": "${{BOT_ID}}", "scopes": ["personal"], "supportsFiles": true, "isNotificationOnly": false}], "composeExtensions": [], "configurableTabs": [], "staticTabs": [], "permissions": ["identity", "messageTeamMembers"], "validDomains": ["token.botframework.com"]}