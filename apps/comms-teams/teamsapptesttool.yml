# yaml-language-server: $schema=https://aka.ms/teams-app-test-tool-config/0.1.0/config.schema.json
# Visit https://aka.ms/teams-app-test-tool-config-guide for more details on this file.

# This configuration file customizes the Teams context information like chats, teams, and users.
# It contains mock data for testing Bot Framework APIs or Bot Builder SDK methods such as TeamsInfo.getTeamMembers().
# You can customize this file to change API response if your bot code uses these APIs.
version: "0.1.0"
tenantId: 5b546ca3-7a6d-4e5a-9f69-73461b81e864
bot:
  id: 00000000-0000-0000-0000-00000000000011
  name: Test Bot
currentUser:
  id: user-id-0
  name: <PERSON><PERSON><PERSON> Thomas
  userPrincipleName: <EMAIL>
  aadObjectId: 00000000-0000-0000-0000-0000000000020
  givenName: Alex
  surname: Wilber
  email: <EMAIL>
users:
  - id: user-id-1
    name: <PERSON>
    userPrincipleName: <EMAIL>
    aadObjectId: 00000000-0000-0000-0000-0000000000021
    givenName: Megan
    surname: Bowen
    email: <EMAIL>
  - id: user-id-2
    name: Adele Vance
    userPrincipleName: <EMAIL>
    aadObjectId: 00000000-0000-0000-0000-0000000000022
    givenName: Adele
    surname: Vance
    email: <EMAIL>
  - id: user-id-3
    name: Isaiah Langer
    userPrincipleName: <EMAIL>
    aadObjectId: 00000000-0000-0000-0000-0000000000023
    givenName: Isaiah
    surname: Langer
    email: <EMAIL>
  - id: user-id-4
    name: Patti Fernandez
    userPrincipleName: <EMAIL>
    aadObjectId: 00000000-0000-0000-0000-0000000000024
    givenName: Patti
    surname: Fernandez
    email: <EMAIL>
  - id: user-id-5
    name: Lynne Robbins
    userPrincipleName: <EMAIL>
    aadObjectId: 00000000-0000-0000-0000-0000000000025
    givenName: Lynne
    surname: Robbins
    email: <EMAIL>
personalChat:
  id: personal-chat-id
groupChat:
  id: group-chat-id
  name: Group Chat
team:
  id: team-id
  name: My Team
  aadGroupId: 00000000-0000-0000-0000-000000000031
  channels:
    - id: channel-announcements-id
      name: Announcements