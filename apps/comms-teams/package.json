{"name": "@tribble/comms-teams", "version": "1.0.0", "description": "Tribble teams chat bot", "engines": {"node": "20"}, "author": "Microsoft", "license": "MIT", "main": "./dist/index.js", "scripts": {"dev:teamsfx": "env-cmd --silent -f .localConfigs npm run dev", "dev:teamsfx:testtool": "env-cmd --silent -f env/.env.testtool -- npm run dev && npm run dev:teamsfx:launch-testtool", "dev:teamsfx:launch-testtool": "teamsapptester start", "dev": "nodemon --exec node --inspect=9239 --signal SIGINT -r ts-node/register src/index.ts", "build": "tsc --build && npm run copy-version", "copy-version": "if [ -f version.json ]; then cp version.json dist/; fi", "start": "node ./dist/index.js", "watch": "nodemon --exec \"npm run start\"", "test": "echo \"Error: no test specified\" && exit 1", "format": "prettier --write \"src/**/*.ts\""}, "prettier": "@tribble/prettier", "repository": {"type": "git", "url": "https://github.com"}, "dependencies": {"@tribble/common-utils": "^0.0.1", "@tribble/comms-shared": "*", "@tribble/tribble-db": "*", "axios": "^1.7.2", "botbuilder": "^4.20.0", "botbuilder-dialogs": "^4.21.4", "dotenv": "^16.4.7", "express": "^4.18.2", "kysely": "^0.27.2", "p-retry": "4.6.1"}, "devDependencies": {"@microsoft/teams-app-test-tool": "^0.1.0-beta.5", "@tribble/prettier": "*", "@types/node": "^14.18.63", "@types/restify": "^8.5.5", "env-cmd": "^10.1.0", "nodemon": "^2.0.7", "prettier": "3.2.4", "shx": "^0.3.3", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}