// This file is automatically generated by Teams Toolkit.
// The teamsfx tasks defined in this file require Teams Toolkit version >= 5.0.0.
// See https://aka.ms/teamsfx-tasks for details on how to customize each task.
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Start Teams App (Test Tool)",
      "dependsOn": [
        "Validate prerequisites (Test Tool)",
        "Deploy (Test Tool)",
        "Start application (Test Tool)",
        "Start Test Tool"
      ],
      "dependsOrder": "sequence"
    },
    {
      // Check all required prerequisites.
      // See https://aka.ms/teamsfx-tasks/check-prerequisites to know the details and how to customize the args.
      "label": "Validate prerequisites (Test Tool)",
      "type": "teamsfx",
      "command": "debug-check-prerequisites",
      "args": {
        "prerequisites": [
          "nodejs", // Validate if Node.js is installed.
          "portOccupancy" // Validate available ports to ensure those debug ones are not occupied.
        ],
        "portOccupancy": [
          3978, // app service port
          9239, // app inspector port for Node.js debugger
          56150 // test tool port
        ]
      }
    },
    {
      // Build project.
      // See https://aka.ms/teamsfx-tasks/deploy to know the details and how to customize the args.
      "label": "Deploy (Test Tool)",
      "type": "teamsfx",
      "command": "deploy",
      "args": {
        "env": "testtool"
      }
    },
    {
      "label": "Start application (Test Tool)",
      "type": "shell",
      "command": "npm run dev:teamsfx:testtool",
      "isBackground": true,
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "problemMatcher": {
        "pattern": [
          {
            "regexp": "^.*$",
            "file": 0,
            "location": 1,
            "message": 2
          }
        ],
        "background": {
          "activeOnStart": true,
          "beginsPattern": "[nodemon] starting",
          "endsPattern": "restify listening to|Bot/ME service listening at|[nodemon] app crashed"
        }
      }
    },
    {
      "label": "Start Test Tool",
      "type": "shell",
      "command": "npm run dev:teamsfx:launch-testtool",
      "isBackground": true,
      "options": {
        "env": {
          "PATH": "${workspaceFolder}/devTools/teamsapptester/node_modules/.bin:${env:PATH}"
        }
      },
      "windows": {
        "options": {
          "env": {
            "PATH": "${workspaceFolder}/devTools/teamsapptester/node_modules/.bin;${env:PATH}"
          }
        }
      },
      "problemMatcher": {
        "pattern": [
          {
            "regexp": "^.*$",
            "file": 0,
            "location": 1,
            "message": 2
          }
        ],
        "background": {
          "activeOnStart": true,
          "beginsPattern": ".*",
          "endsPattern": "Listening on"
        }
      },
      "presentation": {
        "panel": "dedicated",
        "reveal": "silent"
      }
    },
    {
      "label": "Start Teams App Locally",
      "dependsOn": [
        "Validate prerequisites",
        "Start local tunnel",
        "Provision",
        "Deploy",
        "Start application"
      ],
      "dependsOrder": "sequence"
    },
    {
      // Check all required prerequisites.
      // See https://aka.ms/teamsfx-tasks/check-prerequisites to know the details and how to customize the args.
      "label": "Validate prerequisites",
      "type": "teamsfx",
      "command": "debug-check-prerequisites",
      "args": {
        "prerequisites": [
          "nodejs", // Validate if Node.js is installed.
          "m365Account", // Sign-in prompt for Microsoft 365 account, then validate if the account enables the sideloading permission.
          "portOccupancy" // Validate available ports to ensure those debug ones are not occupied.
        ],
        "portOccupancy": [
          3978, // app service port
          9239 // app inspector port for Node.js debugger
        ]
      }
    },
    {
      // Start the local tunnel service to forward public URL to local port and inspect traffic.
      // See https://aka.ms/teamsfx-tasks/local-tunnel for the detailed args definitions.
      "label": "Start local tunnel",
      "type": "teamsfx",
      "command": "debug-start-local-tunnel",
      "args": {
        "type": "dev-tunnel",
        "ports": [
          {
            "portNumber": 3978,
            "protocol": "http",
            "access": "public",
            "writeToEnvironmentFile": {
              "endpoint": "BOT_ENDPOINT", // output tunnel endpoint as BOT_ENDPOINT
              "domain": "BOT_DOMAIN" // output tunnel domain as BOT_DOMAIN
            }
          }
        ],
        "env": "local"
      },
      "isBackground": true,
      "problemMatcher": "$teamsfx-local-tunnel-watch"
    },
    {
      // Create the debug resources.
      // See https://aka.ms/teamsfx-tasks/provision to know the details and how to customize the args.
      "label": "Provision",
      "type": "teamsfx",
      "command": "provision",
      "args": {
        "env": "local"
      }
    },
    {
      // Build project.
      // See https://aka.ms/teamsfx-tasks/deploy to know the details and how to customize the args.
      "label": "Deploy",
      "type": "teamsfx",
      "command": "deploy",
      "args": {
        "env": "local"
      }
    },
    {
      "label": "Start application",
      "type": "shell",
      "command": "npm run dev:teamsfx",
      "isBackground": true,
      "options": {
        "cwd": "${workspaceFolder}"
      },
      "problemMatcher": {
        "pattern": [
          {
            "regexp": "^.*$",
            "file": 0,
            "location": 1,
            "message": 2
          }
        ],
        "background": {
          "activeOnStart": true,
          "beginsPattern": "[nodemon] starting",
          "endsPattern": "restify listening to|Bot/ME service listening at|[nodemon] app crashed"
        }
      }
    }
  ]
}
