/**
 * Handler that will be called during the execution of a PostLogin flow.
 *
 * @param {Event} event - Details about the user and the context in which they are logging in.
 * @param {PostLoginAPI} api - Interface whose methods can be used to change the behavior of the login.
 */
exports.onExecutePostLogin = async (event, api) => {
  if (event.authentication?.riskAssessment?.assessments.NewDevice) {
    // Example condition: prompt MFA only based on the NewDevice
    // confidence level, this will prompt for MFA when a user is logging in
    // from an unknown device.
    let shouldPromptMfa;

    switch (
      event.authentication.riskAssessment.assessments.NewDevice.confidence
    ) {
      case 'low':
      case 'medium':
        shouldPromptMfa = true;
        break;
      case 'high':
        shouldPromptMfa = false;
        break;
      case 'neutral':
        // When this assessor has no useful information about the confidence,
        // do not prompt MFA.
        shouldPromptMfa = false;
        break;
    }

    if (event.user.user_metadata) {
      shouldPromptMfa = event.user.user_metadata.use_mfa;
    }

    // It only makes sense to prompt for MFA when the user has at least one
    // enrolled MFA factor.
    const canPromptMfa =
      event.user.enrolledFactors && event.user.enrolledFactors.length > 0;

    if (shouldPromptMfa && canPromptMfa) {
      const enrolledFactors =
        event.user.enrolledFactors?.map((f) => ({ type: f.type })) ?? [];
      api.authentication.challengeWith(
        { type: 'otp' },
        { additionalFactors: enrolledFactors },
      );
    }
  }
};

/**
 * Handler that will be invoked when this action is resuming after an external redirect. If your
 * onExecutePostLogin function does not perform a redirect, this function can be safely ignored.
 *
 * @param {Event} event - Details about the user and the context in which they are logging in.
 * @param {PostLoginAPI} api - Interface whose methods can be used to change the behavior of the login.
 */
// exports.onContinuePostLogin = async (event, api) => {
// };
