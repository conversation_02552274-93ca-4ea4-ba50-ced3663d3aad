/**
 * Handler that will be called during the execution of a PostLogin flow.
 *
 * @param {Event} event - Details about the user and the context in which they are logging in.
 * @param {PostLoginAPI} api - Interface whose methods can be used to change the behavior of the login.
 */
exports.onExecutePostLogin = async (event, api) => {
  const ManagementClient = require('auth0').ManagementClient;
  const management = new ManagementClient({
    domain: event.secrets.domain,
    clientId: event.secrets.clientId,
    clientSecret: event.secrets.clientSecret,
    scope: 'read:roles update:users create:role_members',
  });

  const params = {
    search_engine: 'v3',
    q: `email:"${event.user.email}"`,
  };

  try {
    const usersByEmailResp = await management.usersByEmail.getByEmail({
      email: event.user.email,
    });

    const lowerCaseEmail = (event.user.email ?? '').toLowerCase();
    const usersByEmailRespLower = await management.usersByEmail.getByEmail({
      email: lowerCaseEmail,
    });

    // Don't use this one as the search endpoint needs to be indexed before a new user can be searched.
    // In Prod, this started being in the "couple minutes" latency range. usersByEmail should be immediate.
    // const usersResp = await management.users.getAll(params);
    const users = usersByEmailResp.data ?? [];
    const usersLower = usersByEmailRespLower.data ?? [];
    for (const userLower of usersLower) {
      if (!users.find((u) => u.user_id == userLower.user_id)) {
        users.push(userLower);
      }
    }

    if (
      users.length == 0 ||
      !users.find((u) => u.app_metadata?.is_tribble_user === true)
    ) {
      management.users.delete({ id: event.user.user_id });
      api.access.deny(
        "Attempting to log in with an unregistered email. Please check with your company's Tribble admin.",
      );
      return;
    }

    //Add main user roles to idToken for front end, and add user permissions to access
    //token for backend
    const mainUser = users.find(
      (u) => u.app_metadata?.is_tribble_user === true,
    );
    const mainUserRolesResp = await management.users.getRoles({
      id: mainUser.user_id,
    });
    const mainUserRoles = mainUserRolesResp.data ?? [];

    if (event.authorization) {
      const roleNames = mainUserRoles.map((r) => r.name);
      api.idToken.setCustomClaim(`${event.secrets.namespace}/roles`, roleNames);
      const mainUserPermissionsResp = await management.users.getPermissions({
        id: mainUser.user_id,
      });
      const mainUserPermissions = mainUserPermissionsResp.data ?? [];

      const permNames = mainUserPermissions.map((perm) => perm.permission_name);
      api.accessToken.setCustomClaim(
        `${event.secrets.namespace}/permissions`,
        permNames,
      );
    }

    if (users.length > 1) {
      //If there's more than one user account, make sure the roles from the main (Username/Password) account are applied to all others.
      const otherUsers = users.filter((u) => u.user_id != mainUser.user_id);

      otherUsers.forEach(async (user) => {
        //Remove existing roles
        const removeRolesResp = await management.users.getRoles({
          id: user.user_id,
        });
        const removeRoles = removeRolesResp.data ?? [];

        if (removeRoles.length) {
          await management.users.deleteRoles(
            { id: user.user_id },
            { roles: removeRoles.map((r) => r.id) },
          );
        }

        //Add main user roles
        const mainUserRolesResp = await management.users.getRoles({
          id: mainUser.user_id,
        });
        const mainUserRoles = mainUserRolesResp.data ?? [];

        if (mainUserRoles.length) {
          const roleIds = mainUserRoles.map((role) => role.id);
          await management.users.assignRoles(
            { id: user.user_id },
            { roles: roleIds },
          );
        }
      });
    }
  } catch (e) {
    console.log(e);
    api.access.deny('Error logging in. Please try again.');
  }
};

/**
 * Handler that will be invoked when this action is resuming after an external redirect. If your
 * onExecutePostLogin function does not perform a redirect, this function can be safely ignored.
 *
 * @param {Event} event - Details about the user and the context in which they are logging in.
 * @param {PostLoginAPI} api - Interface whose methods can be used to change the behavior of the login.
 */
// exports.onContinuePostLogin = async (event, api) => {
// };
