# Agent Evals

Agent Evals is for evaluating and benchmarking our agents' performance across various tasks.

## Getting Started
Copy .env.sample -> .env and put in proper values. `DATABASEURL` should be your current local db url. `DATABASEURLEVALS` should be the url to the evals DB.

Get typescript setup:

From `apps/agent-evals`, install dependencies: `npm install` and build `turbo run build`

Get python setup:

1. Ensure you have Python 3+ (it works with 3.10)
2. From apps/agent-evals, setup a virtual env of your choice. (eg. `python3.10 -m venv .venv` then `source .venv/bin/activate`)
3. Navigate to the `python_evals/` directory
4. Install dependencies: `pip install -r requirements.txt`

## SSH
If you want to use the evals database in staging, (which has a few good approximations of prod data) follow these steps:

1. Tunnel into staging-pg-vm:
 - Get the .pem file for vm-staging-pm-tunnel
 - Make sure your ip is allow-listed for ssh.
 - Run a tunnel on your local machine
```
ssh -N -i ~/somewhere/on/your/machine/vm-staging-pg-tunnel.pem -L 15432:tribble-staging.postgres.database.azure.com:5432 tribble-staging-vm@************
```
###### Note you are tunneling from port 15432 on your local.
2. Go to ds9/scripts and run `./setDbEnv.sh evals ON`.
Later on don't forget to run `./setDbEnv.sh evals OFF` to switch back.

## Orchestrator 

The orchestrator can prepare data for evals by **collecting ground truths** and can run that prepared data through the agent service. 
![alt text](image.png)

- Run `npm run setup:new` to create a list of "ground truth questions" by fetching all the thumbs up questions for a particular client. 

- Then run those questions through agent service with `npm run job`

## TruLens Evaluation

The TruLens evaluation notebook is located in the `python_evals/` directory.

Run it from your favourite notebook environment. 

Or do this: `jupyter notebook trulens_notebook.ipynb`
Set the job_run_id you made with the orchestrator and off you go. 

## Slack Agent

If you want to use the Slack Agent with the evals db, go to settings -> integrations and click the `Reinstall` button on Slack integration. 