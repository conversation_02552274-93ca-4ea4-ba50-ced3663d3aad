{
  "ts-node": {},
  "compilerOptions": {
    "target": "es2022",
    "module": "NodeNext",
    "moduleResolution": "nodenext",
    "declaration": true,
    "lib": ["ES2020"],
    "strict": false,
    "baseUrl": ".",
    "noEmit": true,
    "incremental": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "rootDir": "./src",
    "outDir": "./dist",
    "composite": true,
    "allowJs": true,
    "resolveJsonModule": true,
    "allowImportingTsExtensions": true,
    "jsx": "react-jsx",
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"],
}
