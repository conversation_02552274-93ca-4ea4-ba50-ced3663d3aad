{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# !pip install -r requirements.txt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 📓 TruLens\n", "\n", "You must already have run your questions through the agent-service via the orchestrator to be here.\n", "That would have yielded a job_run_id, which you set here:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from typing import List, TypedDict\n", "from questions import QuestionResult\n", "from rag import RagShell\n", "\n", "class JobRunConfig(TypedDict):\n", "    job_run_id: int\n", "    name: str\n", "    version: str\n", "    results: List[QuestionResult]  # Added after initialization\n", "    feedbacks: list  # Added after initialization\n", "    rag_app: RagShell# Added after initialization"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["job_run_configs = [{\"job_run_id\": 60, \"name\": \"demo2\", \"version\": \"ironclad_subset 1.0\"},{\"job_run_id\": 59, \"name\": \"demo2\", \"version\": \"ironclad_small 1.0\"}]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dotenv import load_dotenv\n", "from questions import gather_job_run_results, QuestionResult, ConversationMessage, ConversationMessageHelper\n", "from feedback import f_qa_relevance, f_groundtruth, f_context_relevance, f_groundedness\n", "load_dotenv()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for config in job_run_configs:\n", "    results = gather_job_run_results(config[\"job_run_id\"])\n", "    config[\"results\"] = results\n", "    config[\"feedbacks\"] = [\n", "    # f_qa_relevance(), \n", "    f_groundtruth(results), \n", "    # f_context_relevance(), # Expensive!\n", "    # f_groundedness()\n", "    ]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here is a \"fake\" implementation of our app. Since we already have our questions answers and tool_calls, this app just pretends to generate/retrieve things. It passes our already retrieved / generated stuff back to trulens in a way it expects. "]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["\n", "for config in job_run_configs:\n", "    rag = RagShell(config[\"results\"])\n", "    config[\"rag_app\"] = rag"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from trulens.apps.custom import TruCustomApp\n", "from trulens.core import TruSession\n", "from trulens.dashboard import run_dashboard\n", "from trulens.core.database.connector.default import DefaultDBConnector\n", "from utils import get_db_conn_string\n", "\n", "connector = DefaultDBConnector(database_url= get_db_conn_string())\n", "\n", "session = TruSession(connector=connector)\n", "\n", "for config in job_run_configs:\n", "    rag = config[\"rag_app\"]\n", "    \n", "    tru_rag = TruCustomApp(\n", "        app_name=config[\"name\"],\n", "        app_version=config[\"version\"],\n", "        app=rag,\n", "        feedbacks=config[\"feedbacks\"])\n", "\n", "    with tru_rag as recording:\n", "        for result in config[\"results\"]:\n", "            rag.query(result.ground_truth.question)\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_dashboard(session)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# session.reset_database()\n", "# session.start_evaluator()\n", "# session.stop_evaluator() # stop if needed"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["# Deleter \n", "from utils import delete_trulens_records\n", "# delete_trulens_records('base')\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 4}