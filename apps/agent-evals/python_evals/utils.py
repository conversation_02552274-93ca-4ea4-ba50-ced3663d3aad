import os
import re
from dotenv import load_dotenv
import pandas as pd
from sqlalchemy.orm import Session
from sqlalchemy import create_engine
from typing import List, Dict
from sqlalchemy import text

# Load environment variables
load_dotenv()

def get_db_conn_string():
    connection_string = os.getenv("DATABASEURL")
    return connection_string.replace("postgres://", "postgresql+psycopg2://").replace(
        "postgresql://", "postgresql+psycopg2://"
    )


def get_db_connection():
    """
    Create a database connection using the connection string from .env file
    """
    return create_engine(get_db_conn_string(), echo=True)

def load_evaluation_results() -> pd.DataFrame:
    """
    Load evaluation results from the PostgreSQL database into a pandas DataFrame.
    """
    engine = get_db_connection()
    query = """
    SELECT * FROM evals.job_run_question
    JOIN evals.ground_truth_question ON job_run_question.ground_truth_question_id = ground_truth_question.id
    """
    return pd.read_sql(query, engine)

def process_answer_citations(answer: str, remove_citations: bool = False) -> str:
    if not answer:
        return ""

    def process_answer_helper(answer: str, remove_citations: bool) -> str:
        if "^" in answer:
            if remove_citations:
                # Remove all citation patterns
                answer = re.sub(r"\^salesforce_[a-zA-Z0-9]+", "", answer)
                answer = re.sub(r"\^fetch_url[a-zA-Z0-9_]*", "", answer)
                answer = re.sub(r"\^struct_\[[a-zA-Z0-9_]*\]", "", answer)
                answer = re.sub(r"[\^]\d{1,}", "", answer)
                answer = re.sub(r"[^\._]\d{1,}[\^]", "", answer)
                answer = re.sub(r"[\^]", "", answer)
                answer = re.sub(r"[,]+\.", ".", answer)
                return answer

            # Check citation style (^1 vs ^1^)
            replace_citation = re.sub(r"[\^]\d{1,}", "", answer)

            if len(replace_citation) < len(answer) and "^" in replace_citation:
                # Handle ^1^ style
                matches = re.finditer(r"[\^]\d{1,}[\^],*", answer)
                for match in matches:
                    citation = match.group()
                    replacement = " " + citation.replace("^", "[", 1).replace(
                        "^", "]", 1
                    ).replace(",", "")
                    answer = answer.replace(citation, replacement)
            else:
                # Handle ^1 style
                matches_prefix = re.finditer(r"[\^]\d{1,},*", answer)
                matches_suffix = re.finditer(r"\d{1,}[\^],*", answer)

                # Process prefix matches (^1)
                for match in matches_prefix:
                    citation = match.group()
                    replacement = (
                        " " + citation.replace("^", "[", 1).replace(",", "") + "]"
                    )
                    answer = answer.replace(citation, replacement)

                # Process suffix matches (1^)
                for match in matches_suffix:
                    citation = match.group()
                    replacement = "[" + citation.replace("^", "]", 1).replace(",", "")
                    answer = answer.replace(citation, replacement)

            # Handle corner cases
            # Case 1: "]2." -> "][2]."
            for match in re.finditer(r"](\d+)[.]", answer):
                fixed_citation = match.group().replace("]", "][").replace(".", "].")
                answer = answer.replace(match.group(), fixed_citation)

            # Case 2: "]2 [" -> "][2]["
            for match in re.finditer(r"](\d+)\s\[", answer):
                fixed_citation = match.group().replace("]", "][").replace(" [", "][")
                answer = answer.replace(match.group(), fixed_citation)

            # Replace "[1] [2]" with "[1][2]"
            answer = re.sub(r"]\s\[", "][", answer)

        return answer

    processed_answer = process_answer_helper(answer, remove_citations)

    # Run process again if there are remaining '^' characters
    if "^" in processed_answer:
        processed_answer = process_answer_helper(processed_answer, remove_citations)

    return processed_answer

def delete_trulens_records(app_name: str):
    engine = get_db_connection()
    with Session(engine) as session:
        query = text(f"DELETE FROM public.trulens_records WHERE app_id IN (SELECT app_id FROM public.trulens_apps WHERE app_name = '{app_name}')")
        session.execute(query)
        query = text(f"DELETE FROM public.trulens_feedbacks WHERE record_id IN (SELECT record_id FROM public.trulens_records WHERE app_id IN (SELECT app_id FROM public.trulens_apps WHERE app_name = '{app_name}'))")
        session.execute(query)
        query = text(f"DELETE FROM public.trulens_apps WHERE app_name = '{app_name}'")
        session.execute(query)
        session.commit()
        session.close()
