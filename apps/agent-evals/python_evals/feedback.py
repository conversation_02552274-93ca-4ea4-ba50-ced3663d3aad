from trulens.core import Select
from trulens.core import Feedback
from trulens.feedback import GroundTruthAgreement
from trulens.providers.openai import AzureOpenAI
from typing import List

from questions import QuestionResult


# Initialize provider class
provider = AzureOpenAI(deployment_name="gpt-4o-5")


def f_groundtruth(results: List[QuestionResult]):
    golden_set = [
        {
            "query": r.ground_truth.question,
            "expected_response": r.ground_truth.expected_answer,
        }
        for r in results
    ]
    ground_truth_agreement = GroundTruthAgreement(
        ground_truth=golden_set, provider=provider
    )

    # Create a wrapper function
    def ground_truth_wrapper(input_text, output_text):
        return ground_truth_agreement.agreement_measure(input_text, output_text)

    return Feedback(ground_truth_wrapper, name="Ground Truth").on_input_output()


# Question/statement relevance between question and each context chunk.
def f_context_relevance():
    return (
        Feedback(
            provider.context_relevance_with_cot_reasons, name="Per chunk relevance"
        )
        .on(Select.RecordCalls.retreive.args.query)
        .on(Select.RecordCalls.retreive.rets[:])
    )


# Define a groundedness feedback function
def f_groundedness():
    return (
        Feedback(provider.groundedness_measure_with_cot_reasons, name="Groundedness")
        .on(Select.RecordCalls.retreive.rets[:])
        .on(Select.RecordCalls.query.rets)
    )


# Question/answer relevance between overall question and answer.
def f_qa_relevance():
    return (
        Feedback(provider.relevance_with_cot_reasons, name="Answer Relevance")
        .on_input()
        .on_output()
    )
