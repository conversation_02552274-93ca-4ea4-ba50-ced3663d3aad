import json
from typing import List
from trulens.apps.custom import instrument

from questions import ConversationMessage, ConversationMessageHelper, QuestionResult
from utils import process_answer_citations

class RagShell:
    def __init__(self, results: List[QuestionResult]):
        self.results = results

    @instrument
    def retreive(self, query: str) -> List[str]:
        results = []
        # Find the record in self.results where the question matches the query
        matching_question = next((result for result in self.results if result.ground_truth.question == query), None)
        
        if matching_question:
            messages = [ConversationMessage.from_json(m) for m in matching_question.tool_used]
            helper = ConversationMessageHelper(messages)
            all_tool_responses = helper.get_all_tool_call_responses()
            
            for tool_response in all_tool_responses:
                try:
                    content_dict = json.loads(tool_response.content)
                    # If this is an array of contexts from brain search, add them all as strings
                    if isinstance(content_dict, list):
                        results.extend([json.dumps(x) for x in content_dict])
                    # Maybe it's a single context like from structured search
                    else:
                        results.append(json.dumps(content_dict))
                except:
                    print ("Could not decode json", tool_response.content)
                    results.append(tool_response.content)
        return results
        
    @instrument
    def generate_completion(self, query: str, context_str: list) -> str:
        matching_question = next((result for result in self.results if result.ground_truth.question == query), None)
        if matching_question:
            processed = process_answer_citations(matching_question.answer, True)
            return processed
        else:
            return "No answer"
    
    @instrument
    def query( self, query: str) -> str:
        contexts = self.retreive(query)
        return self.generate_completion(query, contexts)
