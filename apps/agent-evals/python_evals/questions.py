from dataclasses import field
from datetime import datetime
from typing import Optional, List
from utils import get_db_connection

from sqlalchemy.orm import Session
from sqlalchemy import text


class GroundTruthQuestion:
    def __init__(
        self,
        question: str,
        expected_answer: str,
        created_date: datetime,
        client_id: int,
        client_schema: str,
        conversation_id: Optional[int] = None,
        conversation_detail_id: Optional[int] = None,
        id: Optional[int] = None,
    ):
        self.question = question
        self.expected_answer = expected_answer
        self.created_date = created_date
        self.client_id = client_id
        self.client_schema = client_schema
        self.conversation_id = conversation_id
        self.conversation_detail_id = conversation_detail_id
        self.id = id


class FunctionCall:
    name: str
    arguments: str


class ToolCall:
    id: str
    type: str
    function: FunctionCall
    do_not_recurse: Optional[bool] = field(default=None)


class ConversationMessage:
    def __init__(
        self,
        conversation_detail_id: int,
        role: str,
        content: Optional[str] = None,
        name: Optional[str] = None,
        tool_calls: Optional[List[ToolCall]] = field(default=None),
        tool_call_id: Optional[str] = None,
    ):
        self.conversation_detail_id = conversation_detail_id
        self.role = role
        self.content = content
        self.name = name
        self.tool_calls = tool_calls
        self.tool_call_id = tool_call_id

    @classmethod
    def from_json(cls, data: dict):
        return cls(
            conversation_detail_id=data["conversation_detail_id"],
            role=data["role"],
            content=data.get("content"),
            name=data.get("name"),
            tool_calls=data.get("tool_calls"),
            tool_call_id=data.get("tool_call_id"),
        )


class ConversationMessageHelper:
    def __init__(self, messages: List[ConversationMessage]):
        self.messages = messages

    def get_all_tool_call_responses(self) -> List[ToolCall]:
        return [m for m in self.messages if m.role == "tool"]

    def get_tool_calls(self) -> List[ToolCall]:
        all_tools = []
        messages_with_tools = [
            m for m in self.messages if m.get("tool_calls", None) is not None
        ]
        for message in messages_with_tools:
            for tool_call in message.get("tool_calls"):
                all_tools.append(tool_call)
        return all_tools

    def get_tool_response_for_id(self, tool_call_id: str):
        matching_responses = [
            m
            for m in self.messages
            if m.role == "tool" and m.tool_call_id == tool_call_id
        ]
        if len(matching_responses) > 0:
            return matching_responses[0]
        return None

    def get_eager_rag_response(self):
        for tool in self.get_tool_calls():
            if tool.id == "call_ncc1701":
                return self.get_tool_response(tool.id)
        return None


class QuestionResult:
    def __init__(
        self,
        conversation_id: int,
        tool_used: List[ConversationMessage],
        answer: str,
        ground_truth: GroundTruthQuestion,
    ):
        # self.ground_truth = ground_truth
        self.conversation_id = conversation_id
        self.tool_used = tool_used
        self.answer = answer
        self.ground_truth = ground_truth


def gather_job_run_results(job_run_id: int) -> List[QuestionResult]:
    rets = []
    engine = get_db_connection()
    try:

        query = text("""
            SELECT 
                job_run_question.generated_answer,
                job_run_question.tool_used,
                job_run_question.id as job_run_question_id,
                job_run_question.conversation_id as job_run_conversation_id,
                gtq.question,
                gtq.answer,
                gtq.conversation_id as source_conversation_id,
                gtq.conversation_detail_id as source_conversation_detail_id,
                gtq.created_at,
                gtq.client_id,
                job_def.options
            FROM evals.job_run_question
            JOIN evals.ground_truth_question gtq
            ON job_run_question.ground_truth_question_id = gtq.id
            JOIN evals.job_definition job_def ON gtq.job_definition_id = job_def.id
            WHERE job_run_id = :job_run_id
        """)
        with Session(engine) as session:
            result = session.execute(query, {"job_run_id": job_run_id}).fetchall()
        for row in result:
            row_dict = row._asdict()
            options = row_dict["options"]
            schema = options["schema"]

            ground_truth_question = GroundTruthQuestion(
                client_schema=schema,
                client_id=row_dict["client_id"],
                question=row_dict["question"],
                expected_answer=row_dict["answer"],
                conversation_detail_id=row_dict["source_conversation_detail_id"],
                created_date=row_dict["created_at"],
                conversation_id=row_dict["source_conversation_id"],
                id=row_dict["job_run_question_id"],
            )
            question_result = QuestionResult(
                answer=row_dict["generated_answer"],
                tool_used=row_dict["tool_used"],
                conversation_id=row_dict["job_run_conversation_id"],
                ground_truth=ground_truth_question,
            )
            rets.append(question_result)
    except Exception as e:
        print(e)
    finally:
        engine.dispose()
    return rets