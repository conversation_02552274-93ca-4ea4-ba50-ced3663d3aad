alembic==1.13.3
altair==4.2.2
annotated-types==0.7.0
anyio==4.6.2.post1
appnope==0.1.4
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
arrow==1.3.0
asttokens==2.4.1
async-lru==2.0.4
attrs==24.2.0
babel==2.16.0
beautifulsoup4==4.12.3
bleach==6.1.0
blinker==1.8.2
cachetools==5.5.0
certifi==2024.8.30
cffi==1.17.1
charset-normalizer==3.4.0
click==8.1.7
comm==0.2.2
contourpy==1.3.0
cycler==0.12.1
debugpy==1.8.7
decorator==5.1.1
defusedxml==0.7.1
dill==0.3.9
entrypoints==0.4
exceptiongroup==1.2.2
executing==2.1.0
Faker==30.3.0
fastjsonschema==2.20.0
favicon==0.7.0
fonttools==4.54.1
fqdn==1.5.1
gitdb==4.0.11
GitPython==3.1.43
h11==0.14.0
htbuilder==0.6.2
httpcore==1.0.6
httpx==0.27.2
idna==3.10
ipykernel==6.29.5
ipython==8.28.0
ipywidgets==8.1.5
isoduration==20.11.0
jedi==0.19.1
Jinja2==3.1.4
joblib==1.4.2
json5==0.9.25
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
jupyter==1.1.1
jupyter-console==6.6.3
jupyter-events==0.10.0
jupyter-lsp==2.2.5
jupyter_client==8.6.3
jupyter_core==5.7.2
jupyter_server==2.14.2
jupyter_server_terminals==0.5.3
jupyterlab==4.2.5
jupyterlab_pygments==0.3.0
jupyterlab_server==2.27.3
jupyterlab_widgets==3.0.13
kiwisolver==1.4.7
lxml==5.3.0
Mako==1.3.5
Markdown==3.7
markdown-it-py==3.0.0
markdownlit==0.0.7
MarkupSafe==3.0.1
matplotlib==3.9.2
matplotlib-inline==0.1.7
mdurl==0.1.2
mistune==3.0.2
more-itertools==10.5.0
munch==2.5.0
nbclient==0.10.0
nbconvert==7.16.4
nbformat==5.10.4
nest-asyncio==1.6.0
nltk==3.9.1
notebook==7.2.2
notebook_shim==0.2.4
numpy==2.1.2
overrides==7.7.0
packaging==24.1
pandas==2.2.3
pandocfilters==1.5.1
parso==0.8.4
pexpect==4.9.0
pillow==10.4.0
platformdirs==4.3.6
plotly==5.24.1
prometheus_client==0.21.0
prompt_toolkit==3.0.48
protobuf==5.28.2
psutil==5.9.8
psycopg2-binary==2.9.9
ptyprocess==0.7.0
pure_eval==0.2.3
pyarrow==17.0.0
pycparser==2.22
pydantic==2.9.2
pydantic_core==2.23.4
pydeck==0.9.1
Pygments==2.18.0
pymdown-extensions==10.11.2
pyparsing==3.2.0
python-dateutil==2.9.0.post0
python-decouple==3.8
python-dotenv==1.0.1
python-json-logger==2.0.7
pytz==2024.2
PyYAML==6.0.2
pyzmq==26.2.0
referencing==0.35.1
regex==2024.9.11
requests==2.32.3
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rich==13.9.2
rpds-py==0.20.0
scikit-learn==1.5.2
scipy==1.14.1
Send2Trash==1.8.3
six==1.16.0
smmap==5.0.1
sniffio==1.3.1
soupsieve==2.6
SQLAlchemy==2.0.35
st-annotated-text==4.0.1
st-theme==1.2.3
stack-data==0.6.3
streamlit==1.39.0
streamlit-aggrid==1.0.5
streamlit-camera-input-live==0.2.0
streamlit-card==1.0.2
streamlit-embedcode==0.1.2
streamlit-extras==0.5.0
streamlit-faker==0.0.3
streamlit-image-coordinates==0.1.9
streamlit-keyup==0.2.4
streamlit-pills==0.3.0
streamlit-toggle-switch==1.0.2
streamlit-vertical-slider==2.5.5
tenacity==9.0.0
terminado==0.18.1
threadpoolctl==3.5.0
tinycss2==1.3.0
toml==0.10.2
tomli==2.0.2
toolz==1.0.0
tornado==6.4.1
tqdm==4.66.5
traitlets==5.14.3
trulens==1.1.0
trulens-core==1.1.0
trulens-dashboard==1.1.0
trulens-feedback==1.1.0
trulens_eval==1.1.0
types-python-dateutil==2.9.0.20241003
typing_extensions==4.12.2
tzdata==2024.2
uri-template==1.3.0
urllib3==2.2.3
validators==0.34.0
wcwidth==0.2.13
webcolors==24.8.0
webencodings==0.5.1
websocket-client==1.8.0
widgetsnbextension==4.0.13
