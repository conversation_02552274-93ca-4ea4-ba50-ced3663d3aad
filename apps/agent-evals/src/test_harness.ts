import { EvaluationJobDef, GroundTruthQuestion } from './evaluation.ts';

const testSchema = process.env.TEST_SCHEMA || 'c000001'; //Make sure this an existing schema in your local db. If you want to runs structured tests, make sure they have custom structured data.
const testUserId = process.env.TRIBBLE_USER_ID
  ? parseInt(process.env.TRIBBLE_USER_ID)
  : 1;

export function createMockEvalJobDef(questions: string[]): EvaluationJobDef {
  const mockGroundTruthQuestions: GroundTruthQuestion[] = questions.map(
    (question, index) => ({
      question: question,
      expected_answer: 'Mock answer',
      created_date: new Date(),
      client_id: 1,
      client_schema: testSchema as `c${string}`,
      conversation_id: 123,
      conversation_detail_id: 456,
      id: index,
    }),
  );

  const mockEvalJobDef: EvaluationJobDef = {
    schema: testSchema as `c${string}`,
    bot_user_id: process.env.BOT_USER_ID || 'mock_bot_user_id',
    client_id: 1,
    tribble_user_id: testUserId,
    id: 999,
    infer_tags: false,
    questions_to_run: mockGroundTruthQuestions,
  };

  return mockEvalJobDef;
}
