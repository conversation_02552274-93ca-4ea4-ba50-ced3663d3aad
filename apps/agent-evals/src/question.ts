import { ConversationId } from '@tribble/tribble-db/types';

import { getDB } from '@tribble/tribble-db/db_ops';
import { ConversationMessage, GroundTruthQuestion } from './evaluation.ts';

export class QuestionResult {
  ground_truth: GroundTruthQuestion;
  conversation_id: ConversationId;

  messages: ConversationMessage[];
  answer: string;

  constructor(args: {
    ground_truth: GroundTruthQuestion;
    conversation_id: ConversationId;
  }) {
    this.ground_truth = args.ground_truth;
    this.conversation_id = args.conversation_id;
  }

  // Grab all the messages, and extract out the answer.
  async populateMessages() {
    this.messages = (await this.fetchMessages()).map((message) => {
      return {
        ...(message.message as ConversationMessage),
        conversation_detail_id: message.id,
      };
    });

    this.answer = this.getLastAssistantMessageContent();
  }

  //The last assistant message's content is presumably the answer!
  getLastAssistantMessageContent() {
    const assistantMessages = this.messages?.filter(
      (message) => message.role === 'assistant',
    );

    if (assistantMessages.length > 0) {
      return assistantMessages[assistantMessages.length - 1].content;
    }
    return '';
  }

  async fetchMessages() {
    const schema = this.ground_truth.client_schema;
    const db = await getDB(schema);
    return await db
      .selectFrom('conversation_detail')
      .select(['message', 'seq', 'id'])
      .where('conversation_id', '=', this.conversation_id)
      .orderBy('seq', 'asc')
      .execute();
  }
}
