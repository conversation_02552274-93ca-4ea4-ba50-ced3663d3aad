// Just for testing agent-service
import dotenv from 'dotenv';
dotenv.config();

import { initializePool } from '@tribble/tribble-db/db_ops';
import { EvalutionJobRunner } from '../evaluation.ts';
import { createMockEvalJobDef } from '../test_harness.ts';

let pool;

beforeAll(async () => {
  pool = await initializePool();
});

afterAll(async () => {
  await pool.end();
});

test('create mock objects', () => {
  const jobDef = createMockEvalJobDef(['How are you?']);
  expect(jobDef).toBeDefined();
  const runner = new EvalutionJobRunner(jobDef);
  expect(runner).toBeDefined();
});

test.concurrent(
  'simple question, conversation detail messages are present',
  async () => {
    const jobDef = createMockEvalJobDef(['How are you?']);
    const runner = new EvalutionJobRunner(jobDef);
    await runner.run();
    expect(runner.results).toBeDefined();
    expect(runner.results.length).toBe(1);

    const result = runner.results[0];
    const messages = result.messages;
    expect(Array.isArray(messages)).toBe(true);
    expect(messages.length).toBeGreaterThan(1);

    const userMessage = messages.find((message) => message.role === 'user');
    const toolMessage = messages.find((message) => message.role === 'tool');
    const assistantMessage = messages.find(
      (message) => message.role === 'assistant',
    );
    const eagerRagTool = messages.find(
      (message) => message.tool_call_id === 'call_ncc1701',
    );

    expect(userMessage).toBeDefined();
    expect(toolMessage).toBeDefined();
    expect(assistantMessage).toBeDefined();
    expect(eagerRagTool).toBeDefined();
  },
  20000,
);

test.concurrent(
  'structured query',
  async () => {
    const jobDef = createMockEvalJobDef([
      'Search our custom tables for spreadsheets related to video assets. ',
    ]);
    const runner = new EvalutionJobRunner(jobDef);
    await runner.run();

    const result = runner.results[0];
    const messages = result.messages;

    const toolCall = messages.find((message) =>
      message.tool_calls?.find(
        (toolcall) => toolcall.function?.name == 'search_custom_tables',
      ),
    );

    expect(toolCall).toBeDefined();
  },
  20000,
);

test.concurrent(
  'should search web and use citations',
  async () => {
    const jobDef = createMockEvalJobDef([
      'Research our competitor Tribble online and give me a summary. 20 words or less please.',
    ]);
    const runner = new EvalutionJobRunner(jobDef);
    await runner.run();

    const result = runner.results[0];
    const messages = result.messages;

    const toolCall = messages.find((message) =>
      message.tool_calls?.find(
        (toolcall) => toolcall.function?.name == 'web_search',
      ),
    );
    expect(toolCall).toBeDefined();

    const responseWithCitations = messages.find((message) =>
      message.content?.includes('^web_search_'),
    );
    expect(responseWithCitations).toBeDefined();
  },
  30000,
);

// Uncomment this test if you have a valid salesforce user connected to your tribble_user_id in your current environment.
// test.concurrent(
//   'should check salesforce',
//   async () => {
//     const jobDef = createMockEvalJobDef([
//       'Who is our oldest account in salesforce?',
//     ]);
//     const runner = new EvalutionJobRunner(jobDef);
//     await runner.run();

//     const result = runner.results[0];
//     const messages = result.messages;

//     const toolCall = messages.find((message) =>
//       message.tool_calls?.find(
//         (toolcall) => toolcall.function?.name == 'salesforce_query',
//       ),
//     );

//     expect(toolCall).toBeDefined();
//   },
//   30000,
// );
