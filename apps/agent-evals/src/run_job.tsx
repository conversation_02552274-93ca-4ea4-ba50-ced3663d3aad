import dotenv from 'dotenv';
dotenv.config();

import { ClientSchema } from '@tribble/tribble-db';
import { getDB } from '@tribble/tribble-db/db_ops';
import { Box, render, Text } from 'ink';
import path from 'path';
import React, { FC, useEffect, useState } from 'react';

import {
  ClientId,
  GroundTruthQuestionId,
  JobDefinitionId,
  UserId,
} from '@tribble/tribble-db/types';
import { spawn as childSpawn, execSync, ExecSyncOptions } from 'child_process';
import { Table } from './components/table.tsx';
import { TruLensSetup } from './components/trulens_setup.tsx';
import { EvaluationJobDef, EvalutionJobRunner } from './evaluation.ts';

import { dirname } from 'path';
import { fileURLToPath } from 'url';

// Get the equivalent of __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

interface JobRow {
  id: number;
  description: string;
  count: number;
}

const App: FC = () => {
  const [jobRows, setJobRows] = useState<JobRow[]>([]);
  const [selectedJobs, setSelectedJobs] = useState<number[]>(null);
  const [status, setStatus] = useState<string>('loading');
  const [evalsJobName, setEvalsJobName] = useState('My evals job');
  const [evalsJobVersion, setEvalsJobVersion] = useState('1.0');

  const [message, setMessage] = useState('');
  const [cmdOutput, setCmdOutput] = useState<string[]>([]);

  useEffect(() => {
    const loadJobs = async () => {
      const db = await getDB();
      const recentJobDefinitions = await db
        .withSchema('evals')
        .selectFrom('evals.job_definition')
        .selectAll()
        .orderBy('id', 'desc')
        .limit(100)
        .execute();

      const questionCounts = await db
        .withSchema('evals')
        .selectFrom('evals.ground_truth_question')
        .select([
          'job_definition_id',
          (eb) => eb.fn.count('job_definition_id').as('count'),
        ])
        .where(
          'job_definition_id',
          'in',
          recentJobDefinitions.map((job) => job.id),
        )
        .groupBy('job_definition_id')
        .execute();

      const rows = recentJobDefinitions.map((job) => ({
        id: job.id as number,
        description: job.description,
        count: (questionCounts.find((q) => q.job_definition_id == job.id)
          ?.count || 0) as number,
      }));

      setJobRows(rows);
      setStatus('select_job');
    };

    loadJobs();
  }, []);

  const runJob = async (
    jobIds: number[],
    runTruLens: boolean,
    jobName: string,
    jobVersion: string,
  ) => {
    setStatus('running');

    let jobRunsToEvaluate: number[] = [];
    const db = await getDB();
    for (let i = 0; i < jobIds.length; i++) {
      const jobId = jobIds[i];
      const rawJobDef = await db
        .withSchema('evals')
        .selectFrom('evals.job_definition')
        .selectAll()
        .where('id', '=', jobId as JobDefinitionId)
        .executeTakeFirstOrThrow();

      const rawQuestions = await db
        .selectFrom('evals.ground_truth_question as q')
        .leftJoin('tribble.client as c', 'c.id', 'q.client_id')
        .select([
          'q.id',
          'q.answer',
          'q.client_id',
          'q.question',
          'c.database_schema_id as schema',
          'q.conversation_id',
          'q.conversation_detail_id',
          'q.created_at',
        ])
        .where('job_definition_id', '=', rawJobDef.id)
        .execute();

      const questions = rawQuestions.map((question) => ({
        client_id: question.client_id,
        client_schema: question.schema as ClientSchema,
        created_date: question.created_at,
        expected_answer: question.answer,
        question: question.question,
        conversation_detail_id: question.conversation_detail_id,
        conversation_id: question.conversation_id,
        id: question.id,
      }));

      const jobDef: EvaluationJobDef = {
        id: rawJobDef.id,
        bot_user_id: (rawJobDef.options as any).bot_user_id,
        client_id: (rawJobDef.options as any).client_id,
        infer_tags: (rawJobDef.options as any).infer_tags,
        schema: (rawJobDef.options as any).schema,
        tribble_user_id: (rawJobDef.options as any).tribble_user_id,
        questions_to_run: questions,
      };
      await db
        .updateTable('tribble.user')
        .set('client_id', jobDef.client_id as ClientId)
        .where('id', '=', jobDef.tribble_user_id as UserId)
        .execute();

      const runner = new EvalutionJobRunner(jobDef);
      setMessage(
        `Running job ${i + 1} of ${jobIds.length}: (${jobDef.schema})`,
      );
      await runner.run();

      const jobRun = await db
        .withSchema('evals')
        .insertInto('evals.job_run')
        .values({
          start_time: runner.start_date,
          end_time: runner.end_date,
          errors: runner.errors,
          job_definition_id: jobDef.id as JobDefinitionId,
          status: runner.status,
        })
        .returning('id')
        .executeTakeFirst();
      jobRunsToEvaluate.push(jobRun.id);
      for (const result of runner.results) {
        await db
          .withSchema('evals')
          .insertInto('evals.job_run_question')
          .values({
            job_run_id: jobRun.id,
            conversation_id: result.conversation_id as unknown as number,
            generated_answer: result.answer,
            ground_truth_question_id: result.ground_truth
              .id as GroundTruthQuestionId,
            tool_used: JSON.stringify(result.messages),
          })
          .execute();
      }
    }
    setMessage('');
    if (runTruLens) {
      const pathToNotebook = path.join(
        'python_evals',
        'trulens_notebook.ipynb',
      );
      runCommand(
        `jq ' ` +
          `(.cells[] | select(.metadata.tags? == ["parameters"]) | .source) |= [` +
          `"NAME_OF_THIS_RUN = \\"${jobName}\\"\n",` +
          `"job_run_configs = [` +
          jobRunsToEvaluate
            .map((jobId, i) => {
              const jobDef = jobRows[selectedJobs[i]];
              return `{\\\"job_run_id\\\": ${jobId}, \\\"name\\\": \\\"${jobName}\\\", \\\"version\\\": \\\"${jobDef.description} ${jobVersion}\\\"}`;
            })
            .join(',') +
          `]\n"` +
          `]` +
          `' ${pathToNotebook} > temp.ipynb && mv temp.ipynb ${pathToNotebook}`,
      );
      setMessage('Getting trulens ready...');
      const pythonEvalsPath = path.join(__dirname, '..', 'python_evals');

      runCommand(
        'jupyter nbconvert --to python --execute trulens_notebook.ipynb --output out.py',
        { cwd: pythonEvalsPath },
      );
      setMessage('');
      const pythonProcess = childSpawn('python', ['out.py'], {
        stdio: ['pipe', 'pipe', 'pipe'],
        cwd: pythonEvalsPath,
        shell: true,
      });

      pythonProcess.stdout.on('data', (data) => {
        setCmdOutput((prev) => [...prev, data.toString()]);
      });

      pythonProcess.stderr.on('data', (data) => {
        setCmdOutput((prev) => [...prev, `Error: ${data.toString()}`]);
      });

      pythonProcess.on('close', (code) => {
        setCmdOutput((prev) => [...prev, `Process exited with code ${code}`]);
      });
    }

    // setStatus('complete');
  };

  const runCommand = (cmd: string, options: ExecSyncOptions = {}) => {
    try {
      const output = execSync(cmd, {
        ...options,
        encoding: 'utf-8',
        stdio: 'pipe',
      });
      setCmdOutput((prev) => [...prev, `$ ${cmd}`, output]);
    } catch (error) {
      setCmdOutput((prev) => [
        ...prev,
        `$ ${cmd}`,
        `Error: ${error.message}`,
        error.stderr,
      ]);
      throw error;
    }
  };

  if (status === 'loading') {
    return <Text>Loading jobs...</Text>;
  }

  if (status === 'select_job') {
    return (
      <Box flexDirection="column">
        <Text> Use ⬆/⬇ and SPACEBAR to select jobs. </Text>
        <Table
          rows={jobRows}
          onComplete={(rows) => {
            if (rows.length > 0) {
              setSelectedJobs(rows);
              setStatus('confirm_trulens');
            }
          }}
        />
        <Text>Press enter when ready</Text>
      </Box>
    );
  }

  if (status === 'confirm_trulens') {
    return (
      <TruLensSetup
        jobName={evalsJobName}
        jobVersion={evalsJobVersion}
        onComplete={async (executeTruLens, jobName, jobVersion) => {
          setEvalsJobName(jobName);
          setEvalsJobVersion(jobVersion);
          if (selectedJobs && selectedJobs.length > 0) {
            const jobIds = selectedJobs.map((jobId) => jobRows[jobId].id);
            await runJob(jobIds, executeTruLens, jobName, jobVersion);
          }
        }}
      />
    );
  }

  if (status === 'running') {
    return (
      <Box flexDirection="column">
        <Text>Running job...</Text>
        <Text>{message}</Text>
        {cmdOutput.map((line, i) => (
          <Text key={i} color={line.startsWith('Error') ? 'red' : 'white'}>
            {line}
          </Text>
        ))}
      </Box>
    );
  }

  return <Text>Job complete!</Text>;
};

render(<App />);
