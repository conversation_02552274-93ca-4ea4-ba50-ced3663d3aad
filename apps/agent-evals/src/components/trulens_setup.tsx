import { Box, Text, useInput } from 'ink';
import React, { FC } from 'react';

interface TruLensSetupProps {
  jobName: string;
  jobVersion: string;
  onComplete: (
    executeTruLens: boolean,
    jobName: string,
    jobVersion: string,
  ) => void;
}

export const TruLensSetup: FC<TruLensSetupProps> = ({
  jobName,
  jobVersion,
  onComplete,
}) => {
  const [step, setStep] = React.useState<'confirm' | 'name' | 'version'>(
    'confirm',
  );
  const [newJobName, setNewJobName] = React.useState(jobName);
  const [newJobVersion, setNewJobVersion] = React.useState(jobVersion);
  const [totalInput, setInput] = React.useState('');

  useInput((input, key) => {
    if (step === 'confirm') {
      if (input.toLowerCase() === 'y') {
        setStep('name');
      } else if (input.toLowerCase() === 'n') {
        onComplete(false, jobName, jobVersion);
      }
      return;
    }

    if (key.return) {
      if (step === 'name') {
        setNewJobName(totalInput || newJobName);
        setInput('');
        setStep('version');
      } else if (step === 'version') {
        setNewJobVersion(totalInput || newJobVersion);
        onComplete(true, newJobName, totalInput || newJobVersion);
      }
      return;
    }

    if (key.backspace || key.delete) {
      setInput(input.slice(0, -1));
      return;
    }

    setInput(totalInput + input);
  });

  if (step === 'confirm') {
    return (
      <Box flexDirection="column">
        <Text>Run TruLens immediately after generating answers? (y/n)</Text>
      </Box>
    );
  }

  if (step === 'name') {
    return (
      <Box flexDirection="column">
        <Text>Enter job name [{newJobName}]:</Text>
        <Text>{totalInput || newJobName}</Text>
      </Box>
    );
  }

  return (
    <Box flexDirection="column">
      <Text>Enter job version [{newJobVersion}]:</Text>
      <Text>{totalInput || newJobVersion}</Text>
    </Box>
  );
};
