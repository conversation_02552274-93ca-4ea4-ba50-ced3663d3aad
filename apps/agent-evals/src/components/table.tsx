import { Box, Text, useInput } from 'ink';
import React, { useState } from 'react';

interface TableProps {
  rows: {
    id: number;
    description: string;
    count: number;
  }[];
  onComplete: (rows: number[]) => void;
}
export const Table = ({ rows, onComplete }: TableProps) => {
  const [selectedRow, setSelectedRow] = useState(0);
  const [selectedRows, setSelectedRows] = useState<number[]>([]);

  useInput((input, key) => {
    if (key.return) {
      onComplete(selectedRows);
    }
    if (key.upArrow) {
      setSelectedRow((prev) => (prev > 0 ? prev - 1 : rows.length - 1));
    }

    if (key.downArrow) {
      setSelectedRow((prev) => (prev < rows.length - 1 ? prev + 1 : 0));
    }

    if (input === ' ') {
      setSelectedRows(
        (prev) =>
          prev.includes(selectedRow)
            ? prev.filter((index) => index !== selectedRow) // Deselect if already selected
            : [...prev, selectedRow], // Add to selected rows
      );
    }
  });

  return (
    <Box flexDirection="column" borderStyle="classic">
      <Box>
        <Box width={2}>
          <Text bold> </Text>
        </Box>
        <Box width={20}>
          <Text bold>Description</Text>
        </Box>
        <Box width={20}>
          <Text bold>Count</Text>
        </Box>
      </Box>
      {rows.map((row, index) => (
        <Box key={index}>
          <Box width={2}>
            {selectedRows.includes(index) ? <Text>X</Text> : <Text> </Text>}
          </Box>
          <Box width={20}>
            <Text
              backgroundColor={index === selectedRow ? 'blue' : undefined}
              color={selectedRows.includes(index) ? 'green' : undefined}
            >
              {row.description}
            </Text>
          </Box>
          <Box width={20}>
            <Text
              backgroundColor={index === selectedRow ? 'blue' : undefined}
              color={selectedRows.includes(index) ? 'green' : undefined}
            >
              {row.count}
            </Text>
          </Box>
        </Box>
      ))}
    </Box>
  );
};
