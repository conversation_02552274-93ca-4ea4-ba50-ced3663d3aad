import {
  ConversationClient,
  ConversationMessagePart,
} from '@tribble/conversation-service';
import { ClientSchema } from '@tribble/tribble-db';

import { ConversationId } from '@tribble/tribble-db/types';
import { ConversationEventType as EventType } from '@tribble/types-shared';
import { v4 as uuidv4 } from 'uuid';
import { QuestionResult } from './question.ts';

export interface GroundTruthQuestion {
  question: string;
  expected_answer: string;
  created_date: Date;
  client_id: number;
  client_schema: ClientSchema;
  conversation_id?: number;
  conversation_detail_id?: number;
  id?: number;

  //Not sure if i need these yet.
  //eval_job_definition_id?: number;
  //tools_should_run: string[];???
}
export interface EvaluationJobDef {
  schema: ClientSchema; //The target schema to run the evaluation against
  bot_user_id: string;
  client_id: number;
  tribble_user_id: number;
  id: number;

  infer_tags: boolean; //Should run tag inference prior to evaluation

  questions_to_run: GroundTruthQuestion[];
}

/**
 * Runs the evaluation job by iterating over the questions
 * defined in the job definition and sending them to the agent service.
 * When each question is done the answer and all the tool messages are collected
 * from the db
 */
export class EvalutionJobRunner {
  private job_definition: EvaluationJobDef;
  private botId: string;

  start_date: Date;
  end_date: Date;
  status: 'not_started' | 'started' | 'completed';
  conversation_client: ConversationClient;

  errors: {
    message: string;
    ground_truth_question_id?: number;
    conversation_id?: ConversationId;
  }[] = [];

  results: QuestionResult[] = [];

  constructor(def: EvaluationJobDef) {
    this.job_definition = def;
    this.status = 'not_started';
    this.botId = this.job_definition.bot_user_id || 'EVALS_BOT_ID';
    this.conversation_client = new ConversationClient(
      process.env.CONVERSATION_SERVICE_ADDRESS,
      def.schema,
      def.tribble_user_id,
    );
  }

  async run() {
    this.status = 'started';
    this.start_date = new Date();

    //Agent service expects these values for telemetry,
    //so we'll just hardcode them.
    const system = 'EVALS_SYSTEM',
      channel = 'EVALS_CHANNEL',
      channelType = 'EVALS_CHANNEL_TYPE',
      isDm = false;

    //TODO: Batch these up
    for (let i = 0; i < this.job_definition.questions_to_run.length; i++) {
      const question = this.job_definition.questions_to_run[i],
        messageId = uuidv4(),
        questionContent = question.question;

      let inferredTags = [];
      if (this.job_definition.infer_tags) {
        //TODO: Run tag inference
      }

      const conversationId =
        await this.conversation_client.findOrCreateConversation(
          system,
          channel,
          messageId,
          this.job_definition.tribble_user_id.toString(),
          inferredTags,
        );

      await this.conversation_client.sendMessage(
        {
          conversationId,
          messageText: questionContent,
          botId: this.botId,
          timeoutMs: 90000,
          isDm,
          inferredTags,
          channelType,
          platformUserId: this.job_definition.tribble_user_id.toString(),
          correlationId: messageId,
          event: EventType.EVALS,
        },
        (chunk: ConversationMessagePart) => {
          //I'm blowing chunks!
        },
      );

      const result = new QuestionResult({
        ground_truth: question,
        conversation_id: conversationId as ConversationId,
      });

      await result.populateMessages();
      this.results.push(result);
      console.log(`Q ${i}/${this.job_definition.questions_to_run.length}`);
    }

    this.end_date = new Date();
    this.status = 'completed';
  }
}

export interface ConversationMessage {
  conversation_detail_id: number;
  role: 'assistant' | 'user' | 'tool' | 'system';
  content?: string;
  name?: string;
  tool_calls?: {
    id: string;
    type: string;
    function: {
      name: string;
      arguments: string;
    };
    do_not_recurse?: boolean;
  }[];
  tool_call_id?: string; //If a tool call is made, this is the id of the response
}
