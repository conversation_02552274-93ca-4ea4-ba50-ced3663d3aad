import { ClientSchema } from '@tribble/tribble-db';
import { getDB } from '@tribble/tribble-db/db_ops';
import { ClientId, ConversationDetailId } from '@tribble/tribble-db/types';
import { sql } from 'kysely';
import { GroundTruthQuestion } from '../evaluation.ts';

interface Message {
  content: string | null;
  role: 'assistant' | 'user' | 'tool';
}
//Get all the thumbs up message by conv.detail.id
//{schema: [message_id, message_id]}
const getThumbsUps = async (client_id?: number) => {
  const db = await getDB('tribble');

  let thumbsUpsQuery = db
    .selectFrom('tribble.activity_log as a1')
    .leftJoin('tribble.client as c', 'a1.client_id', 'c.id')
    .select([
      'a1.client_id',
      'a1.source_id',
      'a1.user_id',
      'c.database_schema_id as schema',
    ])
    .where('a1.type', '=', 'feedback')
    .where(sql`a1.details->> 'type'`, '=', 'reaction_added')
    .where(sql`a1.details->> 'reaction'`, '=', '+1');

  if (client_id) {
    thumbsUpsQuery = thumbsUpsQuery.where(
      'a1.client_id',
      '=',
      client_id as ClientId,
    );
  }

  const thumbsUp = await thumbsUpsQuery.execute();

  const thumbsUpMap: Record<string, number[]> = {};

  for (const reaction of thumbsUp) {
    if (!thumbsUpMap[reaction.schema]) {
      thumbsUpMap[reaction.schema] = [];
    }
    thumbsUpMap[reaction.schema].push(parseInt(reaction.source_id));
  }

  return thumbsUpMap;
};

//Get thumbs ups and fetch corresponding messages.
//Return a list of GroundTruth questions.
export const createGroundTruthsFromThumbsUps = async (client_id?: number) => {
  const thumbsUpMessageMap = await getThumbsUps(client_id);
  let groundTruths: GroundTruthQuestion[] = [];
  const db = await getDB();
  for (const entry of Object.entries(thumbsUpMessageMap)) {
    const schema = entry[0],
      thumbsUpMessageIds = entry[1];

    let ambiguousInput = false;

    const conversationDetails = await fetchConversationDetails(
      thumbsUpMessageIds,
      schema,
      ambiguousInput,
    );

    for (const detail of conversationDetails) {
      groundTruths.push({
        question: (detail.input as Message)?.content,
        expected_answer: (detail.message as Message)?.content,
        client_id: detail.client_id as ClientId,
        conversation_id: detail.conversation_id as unknown as number,
        conversation_detail_id: detail.id,
        client_schema: schema as ClientSchema,
        created_date: new Date(),
      });
    }

    //Some of our message might not have input set, so we go spelunking.
    ambiguousInput = true;
    const ambiguousConversationDetails = await fetchConversationDetails(
      thumbsUpMessageIds,
      schema,
      ambiguousInput,
    );

    for (const detail of ambiguousConversationDetails) {
      const conversationId = detail.conversation_id;
      //This logic needs work to find the CORRECT human message.
      const allMessages = await db
        .withSchema(schema)
        .selectFrom('conversation_detail')
        .select(['message', 'input', 'seq', 'id'])
        .where('conversation_id', '=', conversationId)
        .where('seq', '<', detail.seq)
        .orderBy('id', 'asc')
        .execute();

      const humanMessages = allMessages.filter((m) => {
        return (m.message as Message).role === 'user';
      });
      const firstHumanMessage = humanMessages[0];

      groundTruths.push({
        client_id: detail.client_id as ClientId,
        client_schema: schema as ClientSchema,
        created_date: new Date(),
        expected_answer: (detail.message as Message)?.content,
        question: (firstHumanMessage.message as Message)?.content,
        conversation_detail_id: detail.id,
        conversation_id: conversationId as unknown as number,
      });
    }
  }
  return groundTruths;
};

const fetchConversationDetails = async (
  messageIds: number[],
  schema: string,
  ambiguousInput: boolean = false, //If true, fetches messages with null input
) => {
  const db = await getDB(schema);
  return await db
    .selectFrom('conversation_detail')
    .leftJoin(
      'conversation',
      'conversation.id',
      'conversation_detail.conversation_id',
    )
    .leftJoin('tribble.user as u', 'u.id', 'conversation.user_id')
    .leftJoin('tribble.client as c', 'c.id', 'u.client_id')
    .select([
      'conversation_detail.id',
      'conversation_detail.seq',
      'conversation_id',
      'conversation.user_id',
      'c.id as client_id',
      'message',
      'input',
      'statistics',
    ])
    .where('conversation_detail.id', 'in', messageIds as ConversationDetailId[])
    .where('type', '=', 'agent')
    .where(sql`input ->> 'content'`, ambiguousInput ? 'is' : 'is not', null)
    .execute();
};
