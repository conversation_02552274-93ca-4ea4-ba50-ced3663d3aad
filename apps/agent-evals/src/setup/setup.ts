import { initializePool } from '@tribble/tribble-db/db_ops';
import dotenv from 'dotenv';
import { EvaluationJobDef } from '../evaluation.ts';
import { askQuestion } from '../util.ts';
import { createGroundTruthsFromThumbsUps } from './find_ground_truths.ts';
dotenv.config();

const setupNewEvalJob = async () => {
  let trxStarted = false;
  console.log(
    `Let's fetch some thumbs up'd messages and create ground truths.`,
  );
  let client_id_raw = await askQuestion('Which client id?');
  const client_id = parseInt(client_id_raw);

  const pool = await initializePool(),
    pg_client = await pool.connect(),
    rawClient = await pool.query('SELECT * FROM tribble.client WHERE id = $1', [
      client_id,
    ]),
    client = rawClient.rows[0],
    schema = client.database_schema_id,
    name = client.name;

  try {
    console.log(`Client: ${name}, Schema: ${schema}`);
    const confirmRaw = await askQuestion('Is this correct? (y/n)', 'n');
    const confirm = confirmRaw.toLowerCase().startsWith('y');

    if (!confirm) {
      console.log('Exiting.');
      process.exit(0);
    }

    const inferTagsRaw = await askQuestion(
      'Infer tags? (Run tag inference as part of eval?) (y/n)',
      'n',
    );
    const inferTags = inferTagsRaw.toLowerCase().startsWith('y');

    const description = await askQuestion(
      'Description: (optional)',
      `${schema}:${name} - ${new Date().toISOString()}`,
    );

    const botUserId = await askQuestion(
      `Bot User ID: (${process.env.BOT_USER_ID})`,
      process.env.BOT_USER_ID,
    );

    const tribbleUserId = await askQuestion(
      `Tribble User ID: (${process.env.TRIBBLE_USER_ID})`,
      process.env.TRIBBLE_USER_ID,
    );

    const evalDefinition: Partial<EvaluationJobDef> = {
      client_id: client_id,
      bot_user_id: botUserId,
      infer_tags: inferTags,
      schema: schema,
      tribble_user_id: parseInt(tribbleUserId),
    };

    const thumbsUpMessages = await createGroundTruthsFromThumbsUps(client_id);

    console.log(`Found ${thumbsUpMessages.length} messages. Saving...`);

    await pg_client.query('BEGIN');
    trxStarted = true;
    const rawJob = await pg_client.query(
      `INSERT INTO evals.job_definition 
            (description, options) VALUES ($1, $2) RETURNING id`,
      [description, JSON.stringify(evalDefinition)],
    );
    const jobId = rawJob.rows[0].id;
    evalDefinition.id = jobId;

    for (const message of thumbsUpMessages) {
      await pg_client.query(
        `INSERT INTO evals.ground_truth_question 
              (job_definition_id, question, answer, created_at, client_id, conversation_id, conversation_detail_id) VALUES ($1, $2, $3, $4, $5, $6, $7)`,
        [
          jobId,
          message.question,
          message.expected_answer,
          message.created_date,
          message.client_id,
          message.conversation_id,
          message.conversation_detail_id,
        ],
      );
    }

    await pg_client.query('COMMIT');
    console.log(`New job definition created id: (${jobId})`);
  } catch (err) {
    if (trxStarted) {
      await pg_client.query('ROLLBACK');
    }
    console.error(err);
  } finally {
    pg_client.release();
    pool.end();
    process.exit(0);
  }
};

setupNewEvalJob().catch((e) => {
  console.error(e);
  process.exit(1);
});
