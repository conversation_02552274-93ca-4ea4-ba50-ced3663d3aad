ENVIRONMENT=development

DATABASEURL= # the database url you will run evals against
CONVERSATION_SERVICE_ADDRESS=localhost:50061

TRIBBLE_USER_ID=1 # the user id that will be running the agent questions
BOT_USER_ID=U05HPK35RM3 # the bot user id for the environment you're testing against

# TRULENS:
AZURE_OPENAI_ENDPOINT="https://openai-tribble-5.openai.azure.com"
AZURE_OPENAI_API_KEY=[get from azure portal]
OPENAI_API_VERSION=2024-08-01-preview

# Test Harness
TEST_SCHEMA=c000001 # Make sure this an existing schema in your local db. If you want to runs structured tests, make sure they have custom structured data.
