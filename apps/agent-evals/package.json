{"name": "agent-evals", "version": "1.0.0", "description": "Agent evaluation app", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "job": "node --loader ts-node/esm src/run_job.tsx", "setup:new": "ts-node src/setup/setup.ts", "test": "jest"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^18.x", "@types/uuid": "^10.0.0", "jest": "^29.7.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.1", "typescript": "^5.6.2"}, "dependencies": {"@grpc/grpc-js": "^1.9.14", "@tribble/chat-completion-service": "^0.0.1", "@tribble/conversation-service": "^0.0.1", "@tribble/tribble-db": "^0.0.0", "@tribble/types-shared": "^0.0.1", "dotenv": "^16.4.5", "ink": "^5.0.1", "uuid": "^10.0.0"}}