import glob from 'glob';
import ncpModule from 'ncp';
import path, { dirname } from 'path';
import { fileURLToPath } from 'url';

const ncp = ncpModule.ncp;
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const sourceDir = path.join(__dirname, '../src');
const outputDir = path.join(__dirname, '../dist');

glob('**/*.hbs', { cwd: sourceDir }, (err, files) => {
  if (err) {
    console.error(err);
    return;
  }

  files.forEach((file) => {
    const source = path.join(sourceDir, file);
    const destination = path.join(outputDir, file);

    ncp(source, destination, (err) => {
      if (err) {
        console.error(`Error copying ${file}:`, err);
      } else {
        console.log(`Copied ${file}`);
      }
    });
  });
});
