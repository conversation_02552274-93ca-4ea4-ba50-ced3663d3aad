module.exports = {
  presets: [
    ['@babel/preset-env', { targets: { node: 'current' }, modules: false }], // Set target to current Node.js version
    '@babel/preset-typescript', // Enable Babel to handle TypeScript
  ],
  plugins: [
    [
      'module-resolver',
      {
        extensions: ['.ts', '.js'],
        resolvePath(sourcePath, currentFile, opts) {
          // Only adjust if it's a .ts file
          if (sourcePath.endsWith('.ts')) {
            return sourcePath.replace(/\.ts$/, '.js'); // Replace .ts with .js
          }
          return sourcePath;
        },
      },
    ],
  ],
  ignore: ['**/*.d.ts'], // Ignore declaration files
};
