#!/bin/bash
set -e

echo "Building proto files..."

protoc-gen-grpc-ts \
  --ts_out=grpc_js:../../packages/conversation-service/src \
  --proto_path ./ \
  --proto_path ../../node_modules/grpc-tools/bin \
service.proto 

grpc_tools_node_protoc \
  --js_out=import_style=commonjs,binary:../../packages/conversation-service/src \
  --grpc_out=grpc_js:../../packages/conversation-service/src \
  --plugin=protoc-gen-grpc=`which grpc_tools_node_protoc_plugin` \
  --proto_path=. \
  --proto_path ../../node_modules/grpc-tools/bin \
service.proto

echo "Proto files built successfully!"

turbo build --filter=conversation-service

# Build Python packages
echo "Building Python packages..."
cd ../../packages/conversation-service-python

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    echo "Error: Python virtual environment not found at .venv"
    exit 1
fi

# Activate the virtual environment
source .venv/bin/activate

# Run the proto generation script
python generate_protos.py

# Copy the generated files to positronic-answer_rfp
echo "Copying generated files to positronic-answer_rfp..."
cp -r dist/* ../../positronic-answer_rfp/

echo "Python proto files built and copied successfully!"
