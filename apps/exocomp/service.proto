syntax = "proto3";

import "google/protobuf/duration.proto";

package exocomp;

enum ClientType {
  CLIENT_TYPE_UNKNOWN = 0;
  CLIENT_TYPE_SLACK = 1;
  CLIENT_TYPE_TEAMS = 2;
  CLIENT_TYPE_WHATSAPP = 3;
  CLIENT_TYPE_EXTENSION = 4;
  CLIENT_TYPE_WEBCHAT = 5;
  CLIENT_TYPE_ANSWER_RFP = 6;
}

message ConversationRequest {
  string schema = 1;  // The DB schema (aka client) for this conversation.
  int64 user_id = 2; // Tribble user ID of the user starting the conversation.
  ClientType client_type = 3;  // The client type for this conversation.
  string channel = 4;  // The channel for this conversation.
  string message_id = 5;  // ID of the message that started the conversation.
  string platform_user_id = 6;  // ID of the user on the platform (ie Slack).
  repeated int64 metadata_filter_ids = 7;  // IDs of metadata filters for this conversation.
}

message ConversationResponse {
  string conversation_id = 1;  // ID of the conversation
  bool is_new = 2;  // True if this is a new conversation
}

message ConversationFile {
 string name = 1;  // Name of the file
 string mime_type = 2;  // MIME type of the file
 string url = 3;  // URL of the file
}

// Message containing image data for RAG processing
message ImageInfo {
  string data_url = 1;  // Base64 data URL of the image (e.g., data:image/png;base64,...)
  string mimetype = 2;  // MIME type of the image
}

enum Cartridge {
  DEFAULT = 0;
  RFP_WRITER = 1;
  RFP_PROMPT_GENERATOR = 2; //This guy writes prompts for the RFP writer
  ANSWER_RFP = 3; //This guy answers RFP questions
  PADD_CHAT = 4; //This guy answers questions about RFP Responses or suggests edits
  RFP_RESEARCHER = 5; //This guy finds sources for longform RFP proposals
  RFP_REQUIREMENTS_EDITOR = 6; //This guy edits the requirements for RFPs
  MEETING_PREP = 7; //This guy helps you prep for meetings
  TRIBBLYTICS = 8; //This guy generates analytics insights and visualizations
  CUSTOM = 9; //This guy is a custom cartridge that can be used for anything
}

message ResponseFormat {
  // For structured outputs from openai
  string type = 1; // Example: "json_schema"
  string json_schema = 2; // See: "https://platform.openai.com/docs/guides/structured-outputs"
}

message ConversationMessageRequest {
  string schema = 1;  // The DB schema (aka client) for this conversation.
  string conversation_id = 2;  // ID of the conversation
  int64 user_id = 3; // Tribble user ID of the user starting the conversation.
  string message_text = 4;  // Message content
  string bot_id = 5;  // ID of the bot sending the message
  repeated ConversationFile files = 6;  // Files attached to the message
  google.protobuf.Duration timeout = 7;  // Timeout for first byte from LLM.
  bool is_dm = 8;  // True if this is a direct message.
  repeated int64 inferred_tags = 9;  // Inferred tag ids for this message.
  string platform_user_id = 10;  // ID of the user on the platform (ie Slack).
  string channel_type = 11;  // Type of the channel.
  Cartridge cartridge = 12;  // The cartridge for this conversation.
  ResponseFormat response_format = 13;  // The response format for this conversation.
  string prompt_override = 14;  // Pass in a prompt to use instead of whatever the cartridge supplies. 
  bool exclude_from_interaction_count = 15;  // Whether to exclude this interaction from activity_log counts. defaults to false when not set.
  string correlation_id = 16;  // ID of the correlation for this conversation.
  string event = 17;  // The event for this conversation. E.g. "slack_message", "teams_message", "webchat_message", "answer_rfp", etc.
  repeated ImageInfo image_infos = 18;  // Image data URLs and mimetypes for multimodal RAG processing
  int64 custom_cartridge_id = 19;  // ID of the custom cartridge to use for this conversation. Only used when cartridge is CUSTOM.
}

message AnswerUpdate {
  string answer_part = 1; // Any update to the text of the answer.
  string blocks = 2; // JSON string containing blocks to be appended.
  bool is_complete = 3;  // True if this is the final update for this answer.
  int64 conversation_detail_id = 4;  // ID of the conversation detail.
  bool is_hybrid = 5;  // True if this answer contains both text and a toolcall
  string sources = 6;  // JSON string containing sources for the answer.
}

message ToolStatusUpdate {
  string tool_call_id = 1;  // ID of the tool call.  
  string status = 2;  // Status message for the tool call.
}                                                   

// ConversationMessageResponse is a partial answer response returned to the 
// caller in a stream.
message ConversationMessageResponse {
  bool message_queued = 1; // When a message is queue, no answers will come.
  AnswerUpdate answer_update = 2;
  ToolStatusUpdate tool_status_update = 3;
  bool did_consume_queue = 4;  // True if the queue was consumed, and more messages are coming over the wire. 
}

// Version-related messages
message VersionRequest {
  // Empty request - no parameters needed
}

message VersionResponse {
  string service_name = 1;
  string version = 2;
  string commit_sha = 3;
  string commit_short = 4;
  string commit_date = 5;
  string build_date = 6;
  string build_id = 7;
  string branch = 8;
  string environment = 9;
  string build_url = 10; // optional
}

service ConversationService {
  // Find or create a new conversation anchors to a given channel and message.
  rpc FindOrCreateConversation(ConversationRequest) returns (ConversationResponse);

  // Append a message to an existing conversation
  rpc SendMessage(ConversationMessageRequest) returns (stream ConversationMessageResponse);
  
  // Get version information
  rpc GetVersion(VersionRequest) returns (VersionResponse);
}

