ENV_PREFIX=test # test | staging | empty for prod
ENVIRONMENT=development
DEBUG=true

#Needed for local only. Same as LCARS setup
AZURE_CLIENT_ID=
AZURE_TENANT_ID=
AZURE_CLIENT_SECRET=

APPLICATIONINSIGHTS_CONNECTION_STRING=InstrumentationKey=29d0ebe7-7b21-4bd2-aea3-6d7653b0dfb2;IngestionEndpoint=https://eastus-8.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/
APPLICATION_INSIGHTS_DISABLE=true #if you want to disable telemetry. use for local only. 

AZURE_KEY_VAULT_NAME=kv-tribble-test

DATABASEURL=postgres://postgres:postgres@localhost:5432/postgres

CHAT_COMPLETION_SERVER_ADDRESS=localhost:50051

GRPC_SERVER_ADDRESS=localhost:50061

LLM_SYSTEM=AZURE
# Default timeout for calls to Q
# Recommend setting to 60000
LLM_Q_TIMEOUT=60000
AGENT_API_VERSION_LATEST=2024-10-21

AGENT_MODEL=gpt-4.1
AGENT_TEMP=0
AGENT_TIMEOUT=120000

HISTORY_MAX_LENGTH=8192
HISTORY_CONTEXT_LENGTH=3072
HISTORY_SUMMARY_LENGTH=600
HISTORY_SUMMARY_MODEL=gpt-4.1
HISTORY_SUMMARY_TEMP=0.2
PAGE_SUMMARY_LENGTH=500
PAGE_SUMMARY_MODEL=gpt-4.1
PAGE_SUMMARY_TEMP=0.3

SCRAPE_WEB_PAGE_ENDPOINT="https://functribbletesthelper.azurewebsites.net/api/scrape-web-page"
POSITRONIC_HELPERS_URL=http://localhost:7077 # Optional to run things locally. defaults to https://functribbletesthelper.azurewebsites.net from keyvault

SERP_API_KEY="" // Get one of these.
APP_PATH=http://localhost:5173

#Needed for local only. Same as LCARS setup
AZURE_CLIENT_ID=
AZURE_TENANT_ID=
AZURE_CLIENT_SECRET=

#Needed for auth0 role fetching. Same as LCARS setup
AUTH0_M2M_CLIENT_ID=
AUTH0_M2M_CLIENT_SECRET=
AUTH0_M2M_DOMAIN=tribble-dev.us.auth0.com

# Needed for local if using salesforce tools. Same as LCARS setup
SALESFORCE_REDIRECT_URI=http://localhost:5173/api/salesforce/oauth2/callback