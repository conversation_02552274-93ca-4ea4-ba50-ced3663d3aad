{"name": "exocomp", "version": "1.0.0", "description": "A conversational LLM agent service.", "main": "index.js", "scripts": {"type-check": "tsc --noEmit", "build": "npm run type-check && babel src --out-dir dist --extensions '.ts' && node scripts/copy-prompts.mjs && npm run copy-version", "copy-version": "if [ -f version.json ]; then cp version.json dist/; fi", "test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon --watch 'src/**/*.ts' --exec node --loader ts-node/esm src/app.ts", "start": "node dist/app.js"}, "type": "module", "author": "", "license": "ISC", "dependencies": {"@azure/identity": "^4.4.1", "@azure/keyvault-secrets": "^4.8.0", "@azure/openai": "^2.0.0", "@slack/bolt": "^3.15.0", "@tribble/auth-helper": "^0.0.1", "@tribble/cartridge-helper": "^0.0.1", "@tribble/chat-completion-service": "^0.0.1", "@tribble/common-utils": "^0.0.1", "@tribble/conversation-service": "^0.0.1", "@tribble/file-helper": "^0.0.1", "@tribble/formrecognizer": "^0.0.1", "@tribble/gong-service": "^0.0.1", "@tribble/hubspot": "^0.0.1", "@tribble/redis": "^0.0.0", "@tribble/salesforce": "^0.0.1", "@tribble/tribble-db": "^0.0.0", "@tribble/types-shared": "^0.0.1", "@types/node": "^22.5.4", "applicationinsights": "^2.9.6", "date-fns": "^2.25.0", "dotenv": "^16.4.5", "handlebars": "^4.7.8", "jsforce": "^2.0.0-beta.29", "jsonrepair": "^3.12.0", "kysely": "^0.27.4", "ncp": "^2.0.0", "openai": "^4.77.0", "tiktoken": "^1.0.16", "uuid": "^10.0.0"}, "devDependencies": {"@babel/cli": "^7.25.6", "@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.4", "@babel/preset-typescript": "^7.24.7", "babel-plugin-module-resolver": "^5.0.2"}}