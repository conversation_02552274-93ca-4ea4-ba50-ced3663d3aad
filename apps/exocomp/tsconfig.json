{
  "extends": "@tribble/tsconfig/base.json",
  "compilerOptions": {
    "outDir": "dist",
    "rootDir": "src",
    "declaration": true,
    "declarationMap": true,
    "module": "nodenext",
    "moduleResolution": "nodenext",
    "noEmit": true,
    "allowImportingTsExtensions": true,
    "paths": {
      "~/*": ["./src/*"],
    },
    "target": "esnext",
    "esModuleInterop": true,
    "strict": false,
  },
  "ts-node": {
    "esm": true,
  },
}
