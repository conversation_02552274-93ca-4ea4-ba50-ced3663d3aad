import 'dotenv/config';
import http from 'http';
import './telemetry.ts';

import { Server, ServerCredentials } from '@grpc/grpc-js';
import { Logger } from '@tribble/common-utils';
import { ConversationServiceService } from '@tribble/conversation-service';
import { conversationServer } from './conversation_service.ts';

// Create global logger instance
export const logger = new Logger({ appName: 'exocomp' });

async function startServer() {
  // Initialize Logger with auto refresh
  await logger.initialize('exocomp', true, 60000);

  const server = new Server();

  server.addService(ConversationServiceService, conversationServer);

  // Specify the server address and port
  const serverAddress = process.env.GRPC_SERVER_ADDRESS || 'localhost:50061';

  // Start the server
  server.bindAsync(
    serverAddress,
    ServerCredentials.createInsecure(),
    (error, port) => {
      if (error) {
        console.error(`Server error: ${error.message}`);
      } else {
        console.log(`Server listening on port ${port}`);
      }
    },
  );

  // Run a simple HTTP 1.1 server that response to / with 200.
  const simpleServer = http.createServer((req, res) => {
    // Default response for all paths
    res.writeHead(200);
    res.end();
  });

  simpleServer.listen(process.env.PORT || 8080, () => {
    console.log(
      `Azure HC server listening on port ${process.env.PORT || 8080}`,
    );
  });
}

startServer().catch(console.error);
