import { schemaNames, tableNames } from '@tribble/tribble-db/constants';
import { getDB, query } from '@tribble/tribble-db/db_ops';
import { UserId } from '@tribble/tribble-db/types';

export interface User {
  id: number;
  client_id: number;
  external_id?: string;
  external_system: string;
  name: string;
  is_admin: boolean;
  is_tribble: boolean;
  is_deleted: boolean;
  deleted_by_id?: number;
  deleted_date?: Date;
  status?: 'active' | 'terminated' | 'unknown';
  email: string;
}

export async function getUserById(id: number): Promise<User | null> {
  const db = await getDB();
  const detail = await db
    .selectFrom('tribble.user as users')
    .where('users.id', '=', id as UserId)
    .select([
      'users.id as user_id',
      'users.client_id',
      'users.name as user_name',
      'users.external_id',
      'users.status',
      'users.email',
      'users.external_system',
      'users.is_admin',
      'users.is_tribble',
      'users.is_deleted',
    ])
    .executeTakeFirst();

  if (!detail) {
    return null;
  }

  let status: 'active' | 'terminated' | 'unknown' = 'unknown';
  switch (detail.status) {
    case 'active':
    case 'terminated':
      status = detail.status;
      break;
  }

  return {
    id: id,
    client_id: detail.client_id,
    external_system: detail.external_system,
    name: detail.user_name,
    status: status,
    email: detail.email.toLowerCase(),
    is_admin: detail.is_admin || false,
    is_tribble: detail.is_tribble || false,
    is_deleted: detail.is_deleted,
  };
}

export async function getUserByEmail(email: string): Promise<User | null> {
  const db = await getDB();
  const detail = await db
    .selectFrom('tribble.user as users')
    .where('users.email', '=', email)
    .select([
      'users.id as user_id',
      'users.client_id',
      'users.name as user_name',
      'users.external_id',
      'users.status',
      'users.external_system',
      'users.is_admin',
      'users.is_tribble',
      'users.is_deleted',
    ])
    .executeTakeFirst();

  if (!detail) {
    return null;
  }

  let status: 'active' | 'terminated' | 'unknown' = 'unknown';
  switch (detail.status) {
    case 'active':
    case 'terminated':
      status = detail.status;
      break;
  }

  return {
    id: detail.user_id,
    client_id: detail.client_id,
    external_system: detail.external_system,
    name: detail.user_name,
    status: status,
    email: email.toLowerCase(),
    is_admin: detail.is_admin || false,
    is_tribble: detail.is_tribble || false,
    is_deleted: detail.is_deleted,
  };
}

export interface UserSetting {
  id: number;
  setting_name: string;
  setting_type: string;
  value: string;
}

export async function getUserSettings(
  schema: string,
  user_id: number,
): Promise<UserSetting[]> {
  try {
    const queryString = `
      SELECT  
        us.id,
        s.name as setting_name,
        s.type as setting_type,
        CASE 
          WHEN s.type = 'string'  THEN   us.value_string
          WHEN s.type = 'number'  THEN   us.value_number::text
          WHEN s.type = 'boolean' THEN   us.value_boolean::text
        END AS "value"
        FROM    ${schema}.${tableNames.TABLE_USER_SETTING} us 
          JOIN    ${schemaNames.SCHEMA_TRIBBLE}.${tableNames.TABLE_SETTING} s
          ON    us.setting_id = s.id
        WHERE   us.user_id = $1
        `;
    const result = await query(queryString, [user_id]);
    if (result && result.rows) {
      return result.rows;
    }
    return [];
  } catch (err) {
    console.error(`[getUserSettings] ${err.message}`);
    throw new Error('DB Error failed to query user settings');
  }
}
