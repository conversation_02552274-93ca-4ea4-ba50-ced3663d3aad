import { ConversationSystem } from './conversations/conversation.ts';
import { processAnswerCitations } from './views/answer_citations.ts';
import { clampText } from './views/clamp_text.ts';

export class AgentResponse {
  private system: ConversationSystem;

  answer: string = '';

  private toolUpdates: { id: string; msg: string }[];

  constructor(system: ConversationSystem) {
    this.toolUpdates = [];
    this.system = system;
  }

  async updateToolStatus(id: string, msg: string) {
    const idx = this.toolUpdates.findIndex((e) => e.id === id);
    if (idx !== -1) {
      this.toolUpdates[idx].msg = msg;
    } else {
      this.toolUpdates.push({ id, msg });
    }
  }

  appendAnswerChunk(chunk: string) {
    this.answer += chunk;
  }

  extractSources(): number[] {
    const regex: RegExp = /\[(\d+)\]/g;
    let matches: RegExpExecArray | null;
    const ids: number[] = [];

    while ((matches = regex.exec(this.answer)) !== null) {
      const id = parseInt(matches[1], 10);
      if (!isNaN(id)) {
        ids.push(id);
      }
    }

    return ids;
  }

  setAnswer(answer: string) {
    this.answer = answer;
  }

  refinedAnswerText(): string {
    let refined = convertMarkdownLinksToSlack(this.answer);

    // In case any double asterisks around text, convert to single
    refined = refined.replace(/\*\*/g, '*');

    // Remove any "[^1^]"-like citations from text
    // Just in case the source text has [2] text in it, don't remove those.
    // Just stuff that has a caret is likely ours
    refined = refined
      .replace(/\[\^+\d{1,}\^*\]/g, '')
      .replace(/\[\^*\d{1,}\^+\]/g, '')
      .replace(/[*]\s[:]\s/g, '* ')
      .replaceAll('web_search_', ''); // little extra clean-up

    // Remove any remaining citations
    refined = processAnswerCitations(refined, true);

    // Literally saw a citation like [ [1]^], which left a [] at the very end.
    // So clean these up.
    refined = refined.replaceAll('[]', '');

    return clampText(refined, 20_000);
  }
}

function convertMarkdownLinksToSlack(message: string): string {
  // Regular expression to match Markdown links
  // This regex captures the link text and URL in two different groups
  const markdownLinkRegex = /\[([^\]]+)\]\((http[s]?:\/\/[^)]+)\)/g;

  // Replace each Markdown link with Slack's link format
  const convertedMessage = message.replace(
    markdownLinkRegex,
    (_, linkText, url) => {
      return `<${url}|${linkText}>`;
    },
  );

  return convertedMessage;
}
