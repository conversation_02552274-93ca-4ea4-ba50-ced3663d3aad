import { query } from '@tribble/tribble-db/db_ops';
import { constants } from './brain.ts';

export interface DocumentSummaryParams {
  schema: string;
  document_ids: string[];
}

export interface DocumentSummaryResult {
  document_id: number;
  file_name: string;
  label: string;
  type: string;
  summary: string;
}

export const DocumentSummary = async (
  params: DocumentSummaryParams,
): Promise<DocumentSummaryResult[]> => {
  const { schema, document_ids } = params;

  const queryString = `
    SELECT
      d.id,
      d.file_name,
      d.label,
      COALESCE(dt.name, 'Document') as type,
      ns.summary 
    FROM ${schema}.${constants.TABLE_NLP_SUMMARY} ns
    JOIN ${schema}.${constants.TABLE_DOC} d ON ns.document_id = d.id
    LEFT JOIN ${schema}.${constants.TABLE_DOCUMENT_TYPE} dt ON d.document_type_id = dt.id
    WHERE d.id IN (${document_ids.map((id) => `'${id}'`).join(',')})
    ORDER BY document_id 
    LIMIT 5
`;

  const result = await query(queryString);

  if (result && result.rows) {
    return result.rows.map((row: any) => {
      return {
        id: row.id,
        file_name: row.file_name,
        label: row.label,
        type: row.type,
        summary: row.summary,
      };
    });
  }

  return [];
};
