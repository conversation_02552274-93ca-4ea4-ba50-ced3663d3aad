import { getDB, query } from '@tribble/tribble-db/db_ops';
import { ClientId } from '@tribble/tribble-db/types';

export interface Client {
  id: number;
  name: string;
  cloud_service?: string; // We query these values but dont use them.
  cloud_instance?: string;
  storage_id: string;
  database_id: string;
  database_schema_id: string;
  airbyte_workspace_id: string;
}

export async function getClientById(clientId: number): Promise<Client | null> {
  const db = await getDB();

  return await db
    .selectFrom('tribble.client as c')
    .where('c.id', '=', clientId as ClientId)
    .select([
      'c.id',
      'c.name',
      'c.cloud_service',
      'c.cloud_instance',
      'c.storage_id',
      'c.database_id',
      'c.database_schema_id',
      'c.airbyte_workspace_id',
    ])
    .executeTakeFirst();
}

export async function getClientBySchema(
  schema: string,
): Promise<Client | null> {
  const db = await getDB();

  return await db
    .selectFrom('tribble.client as c')
    .where('c.database_schema_id', '=', schema)
    .select([
      'c.id',
      'c.name',
      'c.cloud_service',
      'c.cloud_instance',
      'c.storage_id',
      'c.database_id',
      'c.database_schema_id',
      'c.airbyte_workspace_id',
    ])
    .executeTakeFirst();
}

export interface ClientSetting {
  name: string;
  label: string;
  description: string;
  type: string;
  tribble_editable: boolean;
  value: string;
}

export async function getClientSettings(
  schema: string,
): Promise<ClientSetting[]> {
  try {
    const getQueryString = (type: string) => `
      SELECT 	s.id
      ,		s.name
      ,		s.label
      ,		s.description
      ,   s.type
      ,   COALESCE(s.tribble_editable, false) tribble_editable
      ,		CAST(COALESCE(value_${type}, default_${type}) as text) AS "value"
      FROM	tribble.setting s
      LEFT JOIN
          ${schema}.client_setting cs
          ON s.id = cs.setting_id
      WHERE s.type = '${type}'
    `;

    const queryParts = [
      getQueryString('string'),
      getQueryString('number'),
      getQueryString('boolean'),
    ];
    const results = await query(queryParts.join(' UNION '), []);

    if (results && results.rows) {
      return results.rows;
    }
    return [];
  } catch (err) {
    console.error(`[db.loadClientSettings] ${err.message}`);
    return null;
  }
}

export async function getClientDocumentTypes(
  schema: string,
): Promise<string[]> {
  try {
    const getQueryString = `
    SELECT name as type 
    FROM ${schema}.document_type dt
    WHERE EXISTS (
      SELECT 1 
      FROM ${schema}.document d 
      WHERE d.document_type_id = dt.id
      LIMIT 1
    )
    ORDER BY dt.id ASC
  `;

    const results = await query(getQueryString, []);

    if (results && results.rows) {
      return results.rows.map((row: any) => row.type);
    }

    return [];
  } catch (err) {
    console.error(`[db.loadClientDocumentTypes] ${err.message}`);
    return null;
  }
}
