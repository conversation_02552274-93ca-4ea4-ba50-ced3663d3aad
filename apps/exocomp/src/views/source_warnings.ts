import { ClientSchema } from '@tribble/tribble-db';
import { getClientSettings } from '../clients.ts';
import { ContentType } from '../conversations/conversation_context.ts';
import { clampText } from './clamp_text.ts';

export const getPrivacyWarningMessage = async (
  schema: ClientSchema,
): Promise<string> => {
  //default message:
  let privacyWarning =
    'One or more sources belongs to a document that has been marked "private". Please consult with legal and/or an appropriate manager before sharing this answer with any 3rd parties.';

  try {
    const clientSettings = await getClientSettings(schema);
    const privacyWarningSetting = clientSettings.find(
      (setting) => setting.name === 'source_privacy_warning',
    );
    if (privacyWarningSetting && privacyWarningSetting.value) {
      privacyWarning = clampText(privacyWarningSetting.value, 300);
    }
  } catch (err) {
    console.warn(`[slackapp/getPrivacyWarningMessage] ${err}`);
  } finally {
    return privacyWarning;
  }
};

//Get the Slack source warning message from the client setting
export const getSlackSourceWarningMessage = async (schema: ClientSchema) => {
  //default message (blank)
  let slackSourceWarning = '';

  try {
    const clientSettings = await getClientSettings(schema);
    const slackSourceWarningSetting = clientSettings.find(
      (setting) => setting.name === 'source_slack_warning',
    );
    if (slackSourceWarningSetting && slackSourceWarningSetting.value) {
      slackSourceWarning = clampText(slackSourceWarningSetting.value, 300);
    }
  } catch (err) {
    console.warn(`[slackapp/getSlackSourceWarningMessage] ${err}`);
  } finally {
    return slackSourceWarning;
  }
};

//Get privacy warnings for private sources and slack sources.
export const getPrivacyWarningForSources = async (
  schema: string,
  sources: { content_type: ContentType; privacy: 'public' | 'private' }[],
) => {
  const hasSlack = sources.some((source) => source.content_type === 'SLACK'),
    hasPrivate = sources.some(
      (source) =>
        source.privacy === 'private' && source.content_type !== 'SLACK',
    );

  const slackWarning = hasSlack
      ? await getSlackSourceWarningMessage(schema as ClientSchema)
      : '',
    privacyWarning = hasPrivate
      ? await getPrivacyWarningMessage(schema as ClientSchema)
      : '';

  return `${slackWarning ? `• ${slackWarning}` + '\n' : ''}${privacyWarning ? `• ${privacyWarning}` : ''}`;
};
