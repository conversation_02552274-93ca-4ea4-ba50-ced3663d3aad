// Pretty up the answer text by either removing citations, or making them nice in pretty square brackets.
// Copy pasted from chrome ext
export const processAnswerCitations = (
  answer: string,
  removeCitations = false,
): string => {
  if (!answer) return '';

  const processAnswer = (answer: string, removeCitations: boolean) => {
    // GPT either does citations like ^1 or ^1^ (yeah, it's weird)
    // Need to find out which one. Then change the citation to [1] square brackets style
    if (answer.indexOf('^') > -1) {
      if (removeCitations) {
        // If just removing citations, then easy: replace all '^1' patterns, and then remove any leftover '^'.
        // Also, sometimes citations separated by commas because...why not.
        return answer
          .replace(/\^salesforce_[a-zA-Z0-9]+/g, '')
          .replace(/\^fetch_url[a-zA-Z0-9_]*/g, '')
          .replace(/\^struct_\[[a-zA-Z0-9_]*\]/g, '')
          .replace(/\^get_meeting_summaries[a-zA-Z0-9_]+/g, '')
          .replace(/[\^]\d{1,}/g, '')
          .replace(/[^\._]\d{1,}[\^]/g, '')
          .replace(/[\^]/g, '')
          .replace(/[,]+\./g, '.');
      } else {
        // Check if ^1 style. Assume there's no '^' in the actual answer, which feels reasonable.
        // If you replace all the instances of ^(digit) and there's still a ^ left, then it's ^1^ style.
        const replaceCitation = answer.replace(/[\^]\d{1,}/g, '');

        if (
          replaceCitation.length < answer.length &&
          replaceCitation.indexOf('^') > -1
        ) {
          // ^1^ style
          // Watch out for commas separating multi-context citation (^1^,^2^)
          const matches = answer.match(/[\^]\d{1,}[\^],*/g);

          // Sanity check
          if (matches != null) {
            matches.forEach((citation: string) => {
              // TODO: replace each citation instance with a <span> with tooltip
              // How to do this without dangerouslyInsertHTML-like construct?
              answer = answer.replace(
                citation,
                ' ' +
                  citation.replace('^', '[').replace('^', ']').replace(',', ''),
              );
            });
          }
        } else {
          // ^1 style
          // Watch out for commas separating multi-context citation (^1,^2)
          const matchesPrefix = answer.match(/[\^]\d{1,},*/g);

          // Look for 1^ style
          const matchesSuffix = answer.match(/\d{1,}[\^],*/g);

          // Sanity check
          if (matchesPrefix != null) {
            matchesPrefix.forEach((citation: string) => {
              answer = answer.replace(
                citation,
                ' ' + citation.replace('^', '[').replace(',', '') + ']',
              );
            });
          } else if (matchesSuffix != null) {
            matchesSuffix.forEach((citation: string) => {
              answer = answer.replace(
                citation,
                '[' + citation.replace('^', ']').replace(',', ''),
              );
            });
          }
        }

        // Corner-case: GPT seems to SOMETIMES answer with BOTH ^1 and ^1^ formats, across different sentences.
        // The above can lead to straglers like "...[1]2. Blah blah..."
        // Look for "](digits)." Regex and surround those (digits) with [] too. Sigh.
        const cornerCaseRegex = /](\d+)[\.]/g;
        let match;
        while ((match = cornerCaseRegex.exec(answer))) {
          let fixedCitation = match[0].replace(']', '][').replace('.', '].');
          answer = answer.replace(match[0], fixedCitation);
        }

        // Another corner-case whack a mole: "...[1]2 [3][4]..."
        const cornerCase2Regex = /](\d+)\s\[/g;
        let match2;
        while ((match2 = cornerCase2Regex.exec(answer))) {
          let fixedCitation = match2[0].replace(']', '][').replace(' [', '][');
          answer = answer.replace(match2[0], fixedCitation);
        }

        // Replace any "[1] [2]" --> "[1][2]"
        answer = answer.replace(/\]\s\[/g, '][');
      }
    }
    return answer;
  };

  let processedAnswer = processAnswer(answer, removeCitations);

  // Seeing sometimes [1]^2 after the above is run...so...run the thing again...
  // Note: seems to be caused by citations like this: ^1^,^2  ...because...why the heck GPT
  if (processedAnswer.indexOf('^') > -1) {
    processedAnswer = processAnswer(processedAnswer, removeCitations);
  }

  return processedAnswer;
};

// copied from chrome extension
// Citations simply map to indexes to contexts as they were presented to LLM.
// Reorder so that instead of say [1], [4], [5] we have [1], [2], and [3] instead.
// And in the sources page, we show [1], [4], [5] first and then can separately present
// the other contexts that weren't used. We'll just need to juggle accordingly,
// especially if we regenerate.
export const reorderCitations = (
  processedAnswer: string,
  originalAnswer: string,
): {
  usedCitationIndexes: number[];
  renumberedProcessedAnswer: string;
} => {
  const regexExtractCitationNumbers = /(\[\d+\])/g;

  let allCitationIndexes = {};
  let match;
  while ((match = regexExtractCitationNumbers.exec(processedAnswer))) {
    allCitationIndexes[match[0].replace('[', '').replace(']', '')] = null;
  }

  let usedCitationIndexes = Object.keys(allCitationIndexes)
    .map((index) => Number(index))
    .sort();

  // Renumber the citations based on their order in the above sorted array.
  // E.g. [1], [2], [5], [7] --> [1], [2], [3], [4]
  let renumberedProcessedAnswer = processedAnswer;
  usedCitationIndexes.forEach((origIndex, idx) => {
    const replaceRegex = new RegExp('\\[' + origIndex + '\\]', 'g');
    renumberedProcessedAnswer = renumberedProcessedAnswer.replace(
      replaceRegex,
      '[' + (idx + 1) + ']',
    );
  });

  // If there's no citations, but, [0] was in the original, then
  // a [1] citation was probably/likely incorrected tagged as [0]
  if (usedCitationIndexes.length == 0 && originalAnswer.indexOf('[0]') > -1) {
    renumberedProcessedAnswer = originalAnswer.replace('[0]', '[1]');
    usedCitationIndexes = [1];
  }

  return {
    usedCitationIndexes,
    renumberedProcessedAnswer,
  };
};
