import { ActionsBlock, Button, ContextBlock, KnownBlock } from '@slack/bolt';
import { ConversationSystem } from '../conversations/conversation.ts';
import { ConversationSourceFormat } from '../conversations/conversation_context.ts';

const constants = {
  SLACK_ACTION_ID_VIEW_CONVERSATION_DETAIL_SOURCES_BUTTON:
    'view_conversation_detail_sources_button',
  SLACK_ACTION_ID_THUMBS_UP: 'thumbs_up_action',
  SLACK_ACTION_ID_THUMBS_DOWN: 'thumbs_down_action',
};

//Change text into section block kit and add thumbs buttons
//and warning message.
export function footerBlocksWithThumbsButtons(
  channel_id: string,
  schema: string,
  conversation_detail_id: number,
  conversation_id: number,
  system: string,
  allUsedSources: { [key in string]: Partial<ConversationSourceFormat>[] },
  warningMessage: string = '',
): KnownBlock[] {
  const warningSection: ContextBlock = {
    type: 'context',
    elements: [
      {
        type: 'mrkdwn',
        text: warningMessage,
      },
    ],
  };

  const actionsBlock = thumbsButtons(
    channel_id,
    schema,
    conversation_detail_id,
    conversation_id,
  );

  let blocks: KnownBlock[] = [];

  if (warningMessage) {
    blocks.push(warningSection);
  }

  if (system === ConversationSystem.SLACK) {
    blocks.push(actionsBlock);
  }

  if (Object.keys(allUsedSources).length > 0) {
    let sourceItems = [];

    // For non-web sources, just display the name (and consolidate multiple references)
    let citedNonWebSources = {};

    Object.keys(allUsedSources).forEach((toolName) => {
      (allUsedSources[toolName] ?? []).forEach((source) => {
        let name = source.file_name;
        // I saw an example of a website label/file_name that had a linebreak for whatever reason
        name = name.replace(/[\n\t]/g, ' ').replace(/\s{2, }/g, ' ');

        // Linking to an internal source: just show the name and details will be in the modal
        if (source.url.startsWith(process.env.APP_PATH)) {
          if (!citedNonWebSources[name]) {
            sourceItems.push(`• ${name}`);
            citedNonWebSources[name] = true;
          }
        } else {
          sourceItems.push(`• <${source.url}|*${name}*> (${source.reference})`);
        }
      });
    });

    if (sourceItems.length > 0) {
      blocks.push({
        type: 'context',
        elements: [
          {
            type: 'mrkdwn',
            text: '*SOURCES*',
          },
        ],
      });
      blocks.push({
        type: 'context',
        elements: [
          {
            type: 'mrkdwn',
            text: sourceItems.join('\n'),
          },
        ],
      });
      blocks.push({
        type: 'actions',
        elements: [
          {
            type: 'button',
            action_id:
              constants.SLACK_ACTION_ID_VIEW_CONVERSATION_DETAIL_SOURCES_BUTTON,
            text: {
              text: 'View source details',
              type: 'plain_text',
            },
            value: JSON.stringify({ conversation_detail_id, schema }),
            accessibility_label: 'View source details',
          },
        ],
      });
    }
  }

  return blocks;
}

interface ThumbButtonValue {
  need: string[];
  channel_id: string;
  conversation_detail_id: number;
  conversation_id?: number;
  schema: string;
  vote: 'up' | 'down';
}

export function thumbsButtons(
  channel_id: string,
  schema: string,
  conversation_detail_id: number,
  conversation_id: number,
): ActionsBlock {
  const upValue: ThumbButtonValue = {
    need: ['message_ts'],
    vote: 'up',
    channel_id,
    conversation_detail_id,
    conversation_id,
    schema,
  };

  const downValue: ThumbButtonValue = {
    need: ['message_ts'],
    vote: 'down',
    channel_id,
    schema,
    conversation_detail_id,
    conversation_id,
  };

  const thumbsUp: Button = {
    type: 'button',
    text: {
      type: 'plain_text',
      text: '👍',
    },
    action_id: constants.SLACK_ACTION_ID_THUMBS_UP,
    value: JSON.stringify(upValue),
  };
  const thumbsDown: Button = {
    type: 'button',
    text: {
      type: 'plain_text',
      text: '👎',
    },
    action_id: constants.SLACK_ACTION_ID_THUMBS_DOWN,
    value: JSON.stringify(downValue),
  };
  return {
    type: 'actions',
    elements: [thumbsUp, thumbsDown],
  };
}
