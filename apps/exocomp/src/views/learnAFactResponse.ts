import {
  Actions<PERSON>lock,
  Button,
  Divider<PERSON>lock,
  KnownBlock,
  SectionBlock,
} from '@slack/bolt';
import { Redis, constants as expiries } from '@tribble/redis';
import { clampText } from './clamp_text.ts';

const constants = {
  SLACK_ACTION_ID_ADD_TO_BRAIN_ACCEPT: 'add_to_brain_accept_id',
  SLACK_ACTION_ID_ADD_TO_BRAIN_CHANGE: 'add_to_brain_change_id',
  SLACK_BLOCK_ID_ADD_TO_BRAIN_MSG_TEXT: 'add_to_brain_msg_block_id',
  SLACK_ACTION_ID_ADD_TO_BRAIN_DISMISS: 'add_to_brain_dismiss_id',
};

export const learnFactResponseBlocks = (
  newData: string,
  schema: string,
  client_id: number,
  conversation_detail_id: number,
  tool_call_id: string,
  metadata_filter_ids: number[] = [],
  isContentMod: boolean,
): KnownBlock[] => {
  const payload = {
    client_id,
    conversation_detail_id,
    schema,
    tool_call_id,
    data: newData,
    metadata_filter_ids,
    is_content_mod: isContentMod,
  };

  const redis = new Redis();
  const cacheKey = `${client_id}-${conversation_detail_id}-${tool_call_id}`;
  redis.set(cacheKey, newData, expiries.EX_7_DAYS);

  let stringifiedPayload = JSON.stringify(payload);
  if (stringifiedPayload.length > 2000) {
    const overage = stringifiedPayload.length - 2000;
    payload.data = clampText(newData, newData.length - overage);
    stringifiedPayload = JSON.stringify(payload);
  }

  //Action values have a max length of 2000 characters, so let's clamp.

  const introText = isContentMod
    ? '_The following will be learned by Tribble:_'
    : `_Submit this correction to your company's Tribble Admin:_`;

  const introSection: SectionBlock = {
    type: 'section',
    text: {
      type: 'mrkdwn',
      text: introText,
    },
  };

  const textBlock: SectionBlock = {
    type: 'section',
    block_id: constants.SLACK_BLOCK_ID_ADD_TO_BRAIN_MSG_TEXT,
    text: {
      type: 'mrkdwn',
      text: clampText(newData, 3000),
    },
  };
  const acceptButton: Button = {
    type: 'button',
    action_id: constants.SLACK_ACTION_ID_ADD_TO_BRAIN_ACCEPT,
    accessibility_label: 'Accept',
    style: 'primary',
    text: {
      type: 'plain_text',
      text: 'Accept',
    },
    value: stringifiedPayload,
  };

  const changeButton: Button = {
    type: 'button',
    action_id: constants.SLACK_ACTION_ID_ADD_TO_BRAIN_CHANGE,
    accessibility_label: 'Change',
    text: {
      type: 'plain_text',
      text: 'Change',
    },
    value: stringifiedPayload,
  };

  const dismissButton: Button = {
    type: 'button',
    action_id: constants.SLACK_ACTION_ID_ADD_TO_BRAIN_DISMISS,
    accessibility_label: 'Dismiss',
    style: 'danger',
    text: {
      type: 'plain_text',
      text: 'Dismiss',
    },
    value: stringifiedPayload,
  };

  const actions: ActionsBlock = {
    elements: [acceptButton, changeButton, dismissButton],
    type: 'actions',
  };

  const divider: DividerBlock = {
    type: 'divider',
  };

  return [introSection, divider, textBlock, divider, actions];
};
