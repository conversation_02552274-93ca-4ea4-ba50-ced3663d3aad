import appInsights from 'applicationinsights';

appInsights.setup(process.env.APPLICATIONINSIGHTS_CONNECTION_STRING).start();
let telemetryClient: appInsights.TelemetryClient = appInsights.defaultClient;

const modes = process.env.MODES;
const env_prefix = process.env.ENV_PREFIX ? `${process.env.ENV_PREFIX}|` : '';

const environment = `${env_prefix}${modes}`; // STAGING|APP

telemetryClient.commonProperties = {
  environment,
};

const disabled = process.env.APPLICATION_INSIGHTS_DISABLE;
if (disabled && disabled == 'true') {
  telemetryClient.config.disableAppInsights = true;
}

export { telemetryClient };
