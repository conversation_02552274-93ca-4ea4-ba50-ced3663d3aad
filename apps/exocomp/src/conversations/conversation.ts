import { Cartridge } from '@tribble/conversation-service';
import { ClientSchema } from '@tribble/tribble-db';
import { tableNames } from '@tribble/tribble-db/constants';
import { getDB, query } from '@tribble/tribble-db/db_ops';
import {
  CartridgeId,
  ConversationId,
  ConversationSummary,
  NewConversation,
} from '@tribble/tribble-db/types';
import { get_encoding } from 'tiktoken';
import {
  MetadataFilterValue,
  getMetadataFilterRecords,
} from '../metadata_filters.ts';
import { ConversationContext } from './conversation_context.ts';
import {
  ConversationState,
  getConversationState,
  updateConversationStateCartridge,
} from './conversation_state.ts';
import {
  ConversationMessage,
  getMessagesAfterSeq,
  textContent,
} from './message.ts';
import {
  getLatestSummaryByConversationId,
  insertConversationSummary,
  summarizeHistory,
} from './summaries.ts';

interface ConversationOptions {
  id: string;
  system: string;
  channel: string;
  message_id: string;
  embedding_ids: number[];
  created_date: Date;
  last_activity_date: Date;
}

export enum ConversationSystem {
  SLACK = 'SLACK',
  TEAMS = 'TEAMS',
  EXTENSION = 'EXTENSION',
  WEBCHAT = 'WEBCHAT',
  ANSWER_RFP = 'ANSWER_RFP',
}

export class Conversation {
  schema: ClientSchema;

  id: number;
  system: ConversationSystem;
  channel: string;
  messageId: string;
  embeddingIds: number[];
  createdDate: Date;
  lastActivityDate: Date;

  state: ConversationState;
  contexts: ConversationContext[] = [];
  metadataFilters: MetadataFilterValue[] = [];

  seq: number = 0;
  messages: ConversationMessage[] = [];
  lastSummarySeq: number = 0;
  summary: ConversationSummary = null;

  constructor(schema: string, options: ConversationOptions) {
    this.schema = schema as ClientSchema;
    this.id = parseInt(options.id);
    this.channel = options.channel;
    this.messageId = options.message_id;
    this.embeddingIds = options.embedding_ids;
    this.createdDate = options.created_date;
    this.lastActivityDate = options.last_activity_date;

    switch (options.system) {
      case ConversationSystem.SLACK:
        this.system = ConversationSystem.SLACK;
        break;
      case ConversationSystem.TEAMS:
        this.system = ConversationSystem.TEAMS;
        break;
      case ConversationSystem.EXTENSION:
        this.system = ConversationSystem.EXTENSION;
        break;
    }
  }

  isNew(): boolean {
    return this.summary == null && this.messages.length === 0;
  }

  conversationId(): ConversationId {
    return this.id.toString() as ConversationId;
  }

  async init() {
    this.state = await getConversationState(this.schema, this.id);

    const allMetadataFilters = await getMetadataFilterRecords(this.schema);
    this.metadataFilters = allMetadataFilters.filter((mdf) => mdf.is_active);
    if (this.state) {
      this.metadataFilters = this.metadataFilters.filter((mdf) =>
        this.state.metadata_filter.includes(mdf.id),
      );
    }
  }

  // Load active conversation history from the database.
  async loadHistory() {
    // Step 1: Get the latest summary for the conversation.
    this.summary = await getLatestSummaryByConversationId(
      this.schema,
      this.conversationId(),
    );

    let lastSummarySeq = 0;
    if (this.summary) {
      lastSummarySeq = this.summary.seq;
    }

    // Step 2: Get subsequent messages after the last summary
    this.messages = await getMessagesAfterSeq(
      this.schema,
      this.conversationId(),
      lastSummarySeq,
    );

    // Step 3: Record the current message sequence number.
    this.seq = lastSummarySeq + this.messages.length;
  }

  summaryMessage(): ConversationMessage {
    return {
      role: 'assistant',
      content: `Here is a summary of our conversation so far: ${this.summary.content}`,
    };
  }

  async summarizeHistory(
    correlationId: string,
    timeout: number,
    activityLogData: any,
    historyMaxLength: number,
  ) {
    // Reload hitory to ensure all messages are counted.
    await this.loadHistory();

    const historyLength = this.countHistoryTokens();

    console.log(
      `(${correlationId}): [conversation#summarizeHistory] convo #${this.id} current history:`,
      historyLength,
      'tokens,',
      historyMaxLength,
      'max tokens',
    );

    if (historyLength > historyMaxLength) {
      console.log(
        `(${correlationId}): [conversation#summarizeHistory] convo #${this.id} history too long, summarizing...`,
      );
      const summarized = await summarizeHistory({
        schema: this.schema,
        timeout: timeout,
        activityLogData,
        history: {
          seq: this.seq,
          messages: this.messages,
          summary: this.summary ? this.summaryMessage() : undefined,
        },
        correlationId,
      });

      // Nothing to save if there was no summary produced.
      if (!summarized) {
        console.log(
          `(${correlationId}): [conversation#summarizeHistory] convo #${this.id} no summary produced.`,
        );
        return;
      }

      console.log(
        `(${correlationId}): [conversation#summarizeHistory] convo #${this.id} summary produced, saving...:
          ${JSON.stringify(summarized.summary, null, 2)}`,
      );

      const summarizedHistorySeq = summarized.seq - summarized.messages.length;

      console.log(
        `(${correlationId}): [conversation#summarizeHistory] Saving summary:`,
        {
          last_seq: summarizedHistorySeq,
          summary: summarized.summary.content,
        },
      );
      await insertConversationSummary(this.schema, {
        conversation_id: this.conversationId(),
        content: summarized.summary.content as string,
        seq: summarizedHistorySeq,
      });

      // Reload history to reflect latest changes.
      await this.loadHistory();
    }
  }

  countHistoryTokens(): number {
    const fullHistory = [];
    if (this.summary) {
      fullHistory.push(this.summaryMessage());
    }
    fullHistory.push(...this.messages);

    let totalTokens = 0;
    const encoding = get_encoding('cl100k_base');

    fullHistory.forEach((message) => {
      if (textContent(message)) {
        const tokenCount = encoding.encode(textContent(message)).length;
        totalTokens += tokenCount;
      }
    });
    return totalTokens;
  }

  async setCartridge(cartridgeEnum: Cartridge, cartridgeId?: CartridgeId) {
    if (this.state) {
      await updateConversationStateCartridge(
        this.schema,
        this.id,
        cartridgeEnum,
        cartridgeId,
      );
      this.state.cartridge_enum_value = cartridgeEnum;
      this.state.cartridge_id = cartridgeId;
    } else {
      console.error(
        `[Conversation#setCartridge] No conversation state found for conversation ${this.id}`,
      );
    }
  }
}

export async function insertNewConversation(
  schema: string,
  conversation: NewConversation,
): Promise<{ id: ConversationId }> {
  const db = await getDB();

  return await db
    .withSchema(schema)
    .insertInto('conversation')
    .values({
      channel: conversation.channel,
      system: conversation.system,
      message_id: conversation.message_id,
      user_id: conversation.user_id,
      created_date: conversation.created_date || new Date(),
      last_activity_date: conversation.last_activity_date || new Date(),
      embedding_ids: conversation.embedding_ids,
      sources: JSON.stringify(conversation.sources),
    })
    .returning(['id'])
    .executeTakeFirst();
}

export async function getConversationById(
  schema: ClientSchema,
  conversation_id: ConversationId,
) {
  try {
    const db = await getDB();

    const result = await db
      .withSchema(schema)
      .selectFrom('conversation as c')
      .select([
        'c.id',
        'c.system',
        'c.channel',
        'c.message_id',
        'c.user_id',
        'c.embedding_ids',
        'c.created_date',
        'c.last_activity_date',
        'c.sources',
      ])
      .where('c.id', '=', conversation_id)
      .executeTakeFirst();

    if (!result) {
      return null;
    }

    const conversation = new Conversation(
      schema,
      result as ConversationOptions,
    );
    await conversation.init();
    return conversation;
  } catch (err) {
    console.error(`[getConversationById] ${err.message}`);
    throw err;
  }
}

export async function getConversationRecordByMessageIdAndChannel(
  schema: ClientSchema,
  channel: string,
  threadOrMessageId: string,
): Promise<Conversation> {
  try {
    const db = await getDB();

    const result = await db
      .withSchema(schema)
      .selectFrom('conversation as c')
      .select([
        'c.id',
        'c.system',
        'c.channel',
        'c.message_id',
        'c.user_id',
        'c.embedding_ids',
        'c.created_date',
        'c.last_activity_date',
        'c.sources',
      ])
      .where('c.channel', '=', channel)
      .where('c.message_id', '=', threadOrMessageId)
      .executeTakeFirst();

    if (!result) {
      return null;
    }

    const conversation = new Conversation(
      schema,
      result as ConversationOptions,
    );
    await conversation.init();
    return conversation;
  } catch (err) {
    console.error(`[getConversationByMessageIdAndChannel] ${err.message}`);
    throw err;
  }
}

export async function updateConversationSources(
  schema: ClientSchema,
  conversationId: ConversationId,
  sources: ConversationContext[],
) {
  try {
    const queryString = `
    UPDATE ${schema}.${tableNames.TABLE_CONVERSATION}
    SET sources = $1
    WHERE id = $2
    RETURNING id;
    `;
    const result = await query(queryString, [
      JSON.stringify(sources),
      conversationId,
    ]);

    if (result.rows && result.rows.length) {
      return result.rows[0].id;
    }
  } catch (err) {
    console.error(`[updateConversationSources] ${err.message}`);
  }
}
