import { getDB } from '@tribble/tribble-db/db_ops';

export async function getTagsForConversation(schema: string, channel: string) {
  try {
    const db = await getDB(schema);
    const result = await db
      .selectFrom('slack_channel_tags')
      .select(['slack_channel_tags.tag_id'])
      .where('slack_channel_tags.slack_channel', '=', channel)
      .where('slack_channel_tags.used_for_answer', '=', true)
      .executeTakeFirst();

    return result;
  } catch (err) {
    console.error(`[getTagsForConversation] ${err.message}`);
    return null;
  }
}
