import { chatCompletion } from '@tribble/chat-completion-service/client';
import { ClientSchema } from '@tribble/tribble-db';
import { getDB } from '@tribble/tribble-db/db_ops';
import {
  ConversationId,
  ConversationSummary,
  NewConversationSummary,
} from '@tribble/tribble-db/types';
import { get_encoding } from 'tiktoken';
import { ConversationMessage, ConversationMessageToolCall } from './message.ts';

export async function getLatestSummaryByConversationId(
  schema: ClientSchema,
  conversationId: ConversationId,
): Promise<ConversationSummary> {
  const db = await getDB();
  return db
    .withSchema(schema)
    .selectFrom('conversation_summary')
    .selectAll()
    .where('conversation_id', '=', conversationId)
    .orderBy('seq', 'desc')
    .executeTakeFirst();
}

export async function insertConversationSummary(
  schema: ClientSchema,
  summary: NewConversationSummary,
): Promise<{ id: number }> {
  const db = await getDB();
  return db
    .withSchema(schema)
    .insertInto('conversation_summary')
    .values(summary)
    .returning('id')
    .executeTakeFirstOrThrow();
}

const agentApiVersionLatest =
    process.env.AGENT_API_VERSION_LATEST ?? '2024-10-21',
  historyContextLength = parseInt(process.env.HISTORY_CONTEXT_LENGTH, 10),
  historySummaryModel = process.env.HISTORY_SUMMARY_MODEL ?? 'gpt-4.1',
  historySummaryLength = parseInt(
    process.env.HISTORY_SUMMARY_LENGTH || '10000',
    10,
  ),
  historySummaryTemp = parseFloat(process.env.HISTORY_SUMMARY_TEMP || '0.1');

export interface ConversationHistory {
  seq: number;
  messages: ConversationMessage[];
  summary: ConversationMessage;
}

interface summarizeHistoryParams {
  schema: string;
  timeout: number;
  activityLogData: any;
  history: ConversationHistory;
  correlationId: string;
}

export async function summarizeHistory(
  params: summarizeHistoryParams,
): Promise<ConversationHistory> {
  const history = params.history,
    encoding = get_encoding('cl100k_base');

  let tokenCount = 0,
    splitIndex = history.messages.length,
    lastKnownNonToolIndex = splitIndex; // In case we exceed the history length within a tool call, we'll use this to split.

  // We're going to set aside up to and including historyContextLength tokens
  // and leave them unsummarized for better context.
  //
  // However, we need to be careful not to split up assistant tool calls and
  // their corresponding responses as that causes an error.
  const openToolCalls = new Set();
  for (let i = history.messages.length - 1; i >= 0; i--) {
    const message: ConversationMessage = history.messages[i],
      tokenLength = message.content
        ? encoding.encode(message.content as string).length
        : 0;

    // Note any tool call "open". Working backwards, that happens when a tool
    // message (ie. tool response) is encountered.
    if (message.role == 'tool' && message.tool_call_id) {
      openToolCalls.add(message.tool_call_id);
    }

    // Note and tool call close. We're working backwards, so tool call "closes"
    // when the assistant actually issues the initial call.
    if (message.tool_calls) {
      message.tool_calls.forEach((tc: ConversationMessageToolCall) =>
        openToolCalls.delete(tc.id),
      );
    }

    const notToolMessage =
      !message.tool_calls && !(message.role == 'tool' && message.tool_call_id);

    if (notToolMessage) {
      lastKnownNonToolIndex = i;
    }

    // If there's still room in the context window, keep going.
    if (tokenCount + tokenLength <= historyContextLength) {
      tokenCount += tokenLength;
      splitIndex = i; // Update split index
      continue;
    }

    // If we're here, we've reached the context window limit.
    if (openToolCalls.size > 0) {
      // If we're in an open tool call return the last known non-tool call index.
      splitIndex = lastKnownNonToolIndex;
    }

    break; // Found the split point
  }

  const toKeep = history.messages.slice(splitIndex),
    toSummarize = history.messages.slice(0, splitIndex),
    messages = [
      ...(history.summary ? [history.summary] : []),
      ...toSummarize,
      {
        role: 'system',
        content:
          'Provide a detailed summary of the above conversation. Include any details from existing summaries. If there are any ongoing tasks, specific products, or competitors being discussed make sure to include them.',
      },
    ];

  if (toSummarize.length === 0) {
    // Cannot summarize yet, return what we were given.
    console.log(
      `(${params.correlationId}) Cannot summarize history, no qualifying messages.`,
    );
    return null;
  }

  console.log(
    `(${params.correlationId}) Summarizing oldest ${toSummarize.length} messages in conversation.`,
  );

  try {
    const { schema, timeout, activityLogData } = params;
    const response = await chatCompletion({
        schema,
        timeout,
        activityLogData,
        apiVersion: agentApiVersionLatest,
        resourceUrl: '/openai/deployments/tribble-gpt4/chat/completions',
        request: {
          messages: messages.filter((m) => m.role),
          model: historySummaryModel,
          max_tokens: historySummaryLength,
          temperature: historySummaryTemp,
        },
      }),
      openAIResponse = response.response,
      summary = openAIResponse.choices[0].message;

    return {
      seq: history.seq, // Seq doesn't change, just messages and summary.
      messages: toKeep,
      summary,
    };
  } catch (error) {
    console.error('Error summarizing history:', error);
    throw error;
  }
}
