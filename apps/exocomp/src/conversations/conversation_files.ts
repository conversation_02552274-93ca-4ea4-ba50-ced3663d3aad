import { FormRecognizedDoc } from '@tribble/formrecognizer';
import { getDB } from '@tribble/tribble-db/db_ops';
import { RfxBidPacketFile } from '@tribble/tribble-db/types';
import { Conversation } from './conversation.ts';

async function getBidPacketFiles(
  schema: string,
  ids: RfxBidPacketFile['id'][],
): Promise<RfxBidPacketFile[]> {
  const db = await getDB(schema);
  return db
    .selectFrom('rfx_bid_packet_file')
    .selectAll()
    .where('id', 'in', ids)
    .execute();
}

export async function getBidPacketFilesForConversation(
  conversation: Conversation,
): Promise<FormRecognizedDoc[]> {
  // Get the bid packet files associated with a conversation
  const bidPacketIds = conversation.state?.files.map(
    (f) => f.rfx_bid_packet_file_id as RfxBidPacketFile['id'],
  );

  if (!bidPacketIds.length) {
    return [];
  }

  const rawBidPacketFiles = await getBidPacketFiles(
    conversation.schema,
    bidPacketIds,
  );
  return (
    rawBidPacketFiles?.map(
      (file) => new FormRecognizedDoc(conversation.schema, file),
    ) || []
  );
}
