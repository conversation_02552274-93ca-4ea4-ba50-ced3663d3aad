import { ClientSchema } from '@tribble/tribble-db';
import { tableNames } from '@tribble/tribble-db/constants';
import { getClient, getDB } from '@tribble/tribble-db/db_ops';
import { ConversationId } from '@tribble/tribble-db/types';
import { sql } from 'kysely';

// Try to acquire a lock on processing this conversation.
export async function tryConversationLock(
  schema: ClientSchema,
  conversationId: ConversationId,
  lockDurationInSeconds = 120,
): Promise<boolean> {
  // Try to acquire the lock.

  const db = await getDB();
  const result = await db
    .withSchema(schema)
    .updateTable('conversation')
    .set(({ fn }) => ({
      lock_expires_at: sql<Date>`NOW() + ${lockDurationInSeconds} * INTERVAL '1 second'`,
    }))
    .where('id', '=', conversationId)
    .where((eb) =>
      eb('lock_expires_at', 'is', null).or(
        'lock_expires_at',
        '<',
        sql<Date>`NOW()`,
      ),
    )
    .returning('id')
    .execute();

  return result.length > 0; // True if the lock was acquired, false otherwise
}

// Extend a conversation lock that you have already acquired.
export async function extendConversationLock(
  schema: ClientSchema,
  conversationId: ConversationId,
  lockDurationInSeconds = 120,
): Promise<{ id: ConversationId }> {
  // Extend the lock using PostgreSQL's NOW() function.
  const db = await getDB();
  return await db
    .withSchema(schema)
    .updateTable('conversation')
    .set(({ fn }) => ({
      lock_expires_at: sql<Date>`NOW() + ${lockDurationInSeconds} * INTERVAL '1 second'`,
    }))
    .where('id', '=', conversationId)
    .returning('id')
    .executeTakeFirst();
}

// Release conversation lock if queue is empty. If release fails, there are
// more messages to process.
export async function tryReleaseConversationLock(
  schema: ClientSchema,
  conversationId: ConversationId,
): Promise<boolean> {
  const client = await getClient();

  try {
    // Start a transaction
    await client.query('BEGIN');

    // Check if there are any queued messages
    const checkQuery = `
      SELECT 1
      FROM ${schema}.${tableNames.TABLE_CONVERSATION_DETAIL}
      WHERE conversation_id = $1 AND type = 'queued'
      LIMIT 1;
    `;
    const checkRes = await client.query(checkQuery, [conversationId]);

    // If no queued messages, release the lock
    if (checkRes.rows.length === 0) {
      const releaseQuery = `
        UPDATE ${schema}.${tableNames.TABLE_CONVERSATION}
        SET lock_expires_at = NULL
        WHERE id = $1;
      `;
      await client.query(releaseQuery, [conversationId]);

      // Commit the transaction
      await client.query('COMMIT');
      return true; // Lock released
    } else {
      // There are still queued messages, do not release the lock
      // Rollback the transaction to ensure atomicity
      await client.query('ROLLBACK');
      return false; // Lock not released
    }
  } catch (error) {
    // In case of any error, rollback the transaction
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release(); // Ensure the client is always released back to the pool
  }
}
