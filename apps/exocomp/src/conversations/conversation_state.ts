import { Cartridge } from '@tribble/conversation-service';
import { getDB, query } from '@tribble/tribble-db/db_ops';
import { CartridgeId } from '@tribble/tribble-db/types';
import { TimeFilter } from '../tools/extract_temporal.ts';

interface ConversationStateResearch {
  ids: number[];
  note: string;
  from_exemplar?: boolean;
}
export interface ConversationState {
  id?: number;
  conversation_id: number;
  metadata_filter?: number[];
  time_filter?: TimeFilter;
  created_date?: Date;
  files?: {
    rfx_bid_packet_file_id: number;
    writer_packet_ids?: number[];
  }[];
  research?: ConversationStateResearch[];
  cartridge_enum_value?: Cartridge;
  cartridge_id?: CartridgeId;
}

export async function insertNewConversationState(
  schema: string,
  conversationState: ConversationState,
) {
  const queryString = `
    INSERT INTO ${schema}.conversation_state (conversation_id, metadata_filter)
    VALUES ($1, $2)
    RETURNING id;
  `;
  try {
    const result = await query(queryString, [
      conversationState.conversation_id,
      JSON.stringify(conversationState.metadata_filter ?? []),
    ]);
    if (result.rows && result.rows.length) {
      return result.rows[0].id;
    }
  } catch (err) {
    console.error(`[insertNewConversationState] ${err.message}`);
    return null;
  }
}

export async function getConversationState(
  schema: string,
  conversationId: number,
): Promise<ConversationState> {
  try {
    const db = await getDB(schema);
    const conversationState = await db
      .selectFrom('conversation_state')
      .selectAll()
      .where('conversation_id', '=', conversationId as any)
      .executeTakeFirst();
    if (conversationState) {
      // Parse the time filter from JSON string
      const parsedTimeFilter = conversationState.time_filter
        ? (() => {
            try {
              const timeFilter = conversationState.time_filter as TimeFilter;
              return {
                ...(timeFilter as TimeFilter),
                ...(timeFilter.startDate && {
                  startDate: new Date(timeFilter.startDate),
                }),
                ...(timeFilter.endDate && {
                  endDate: new Date(timeFilter.endDate),
                }),
              };
            } catch (error) {
              console.error(
                `[getConversationState] Error parsing time filter: ${error.message}`,
              );
              return null;
            }
          })()
        : null;

      return {
        ...conversationState,
        conversation_id: Number(conversationState.conversation_id),
        metadata_filter: conversationState.metadata_filter as number[],
        time_filter: parsedTimeFilter,
        files: conversationState.files as {
          rfx_bid_packet_file_id: number;
        }[],
        research: conversationState.research as ConversationStateResearch[],
      };
    }
  } catch (err) {
    console.error(`[getConversationState] ${err.message}`);
    return null;
  }
}

export async function updateConversationStateTimeFilter(
  schema: string,
  conversationStateId: number,
  timeFilter: TimeFilter,
) {
  const queryString = `
    UPDATE ${schema}.conversation_state
    SET time_filter = $2
    WHERE conversation_id = $1
    RETURNING id;
  `;
  try {
    const result = await query(queryString, [
      conversationStateId,
      JSON.stringify(timeFilter ?? null),
    ]);
    if (result.rows && result.rows.length) {
      return result.rows[0].id;
    }
  } catch (err) {
    console.error(`[updateConversationStateTimeFilter] ${err.message}`);
    return null;
  }
}

export async function updateConversationStateCartridge(
  schema: string,
  conversationId: number,
  cartridgeEnum: Cartridge,
  cartridgeId: number,
) {
  const db = await getDB(schema);
  try {
    await db
      .updateTable('conversation_state')
      .set({
        cartridge_enum_value: cartridgeEnum,
        cartridge_id: cartridgeId as CartridgeId,
      })
      .where('conversation_id', '=', conversationId as any)
      .execute();
  } catch (err) {
    console.error(`[updateConversationStateCartridge] ${err.message}`);
  }
}
