import { ClientSchema } from '@tribble/tribble-db';
import { getDB } from '@tribble/tribble-db/db_ops';
import { ConversationId } from '@tribble/tribble-db/types';
import { sql } from 'kysely';

export interface ConversationImageUrl {
  // url property may contain base64 encoded image data.
  url: string;
  detail?: string;
}

export interface ConversationImageUrlContentPart {
  type: 'image_url';
  image_url: ConversationImageUrl;
}

export interface ConversationTextContentPart {
  type: 'text';
  text: string;
}

export type ConversationContentPart =
  | ConversationImageUrlContentPart
  | ConversationTextContentPart;

export interface ConversationMessageToolCall {
  id: string;
  type: string;
  function: {
    name: string;
    arguments: string;
  };
  do_not_recurse?: boolean;
}

export interface ConversationMessage {
  role: string;
  content?: string | ConversationContentPart[];
  name?: string;
  tool_calls?: ConversationMessageToolCall[];
  tool_call_id?: string;
}

let INTROSPECT_AGENT_USER_LIST;

export function introspectMessages(
  messages: ConversationMessage[],
  userId: number,
): void {
  const list = getList();
  if (!list.length) return;

  if (list.includes(userId)) {
    console.log('****');
    for (const message of messages) {
      console.log(JSON.stringify(message, null, 2));
    }
    console.log('****');
  }
}

function getList() {
  if (!INTROSPECT_AGENT_USER_LIST) {
    const list = process.env.INTROSPECT_AGENT_USER_LIST ?? '';
    INTROSPECT_AGENT_USER_LIST = list
      .split(',')
      .map((id) => parseInt(id, 10))
      .filter((id) => !isNaN(id));
  }
  return INTROSPECT_AGENT_USER_LIST;
}

export async function consumeMessageQueue(
  schema: ClientSchema,
  conversation_id: ConversationId,
): Promise<ConversationMessage[]> {
  // Step 1: Load Queued Messages
  const db = await getDB();
  const messageRows = await db
    .withSchema(schema)
    .selectFrom('conversation_detail')
    .select(['id', 'message'])
    .where('conversation_id', '=', conversation_id)
    .where('type', '=', 'queued')
    .orderBy('seq', 'asc')
    .execute();

  if (messageRows.length === 0) {
    // Queue is empty.
    return [];
  }

  // After reading them, delete the messages from the queue
  const messageIds = messageRows.map((row) => row.id);
  const messages = messageRows.map((row) => row.message as ConversationMessage);

  if (messageIds.length > 0) {
    await db
      .withSchema(schema)
      .deleteFrom('conversation_detail')
      .where('id', 'in', messageIds)
      .execute();
  }

  return messages;
}

export async function queueConversationMessage(
  schema: ClientSchema,
  conversationId: ConversationId,
  message: ConversationMessage,
): Promise<{ id: number }> {
  const db = await getDB();
  return db
    .withSchema(schema)
    .insertInto('conversation_detail')
    .values((eb) => ({
      type: 'queued',
      conversation_id: conversationId,
      cartridge_enum_value: eb
        .selectFrom('conversation_state')
        .select('cartridge_enum_value')
        .where('conversation_id', '=', conversationId),
      message,
      seq: eb
        .selectFrom('conversation_detail')
        .select(sql<number>`coalesce(max(seq), 0) + 1`.as('next_seq'))
        .where('type', '=', 'queued')
        .where('conversation_id', '=', conversationId)
        .limit(1),
      created_date: new Date(),
    }))
    .returning(['id'])
    .executeTakeFirst();
}

export async function getMessagesAfterSeq(
  schema: ClientSchema,
  conversationId: ConversationId,
  seq: number,
): Promise<ConversationMessage[]> {
  const db = await getDB();
  const result = await db
    .withSchema(schema)
    .selectFrom('conversation_detail')
    .select(['conversation_detail.message'])
    .where('conversation_id', '=', conversationId)
    .where('seq', '>', seq)
    .where('type', '=', 'agent')
    .orderBy('seq', 'asc')
    .execute();
  return result.map((r) => r.message as ConversationMessage);
}

// textContent extracts the text content from a conversation message,
// regardless of whether the message is pure text or a text/image mix.
export function textContent(message: ConversationMessage): string {
  if (typeof message.content === 'string') {
    return message.content;
  } else if (Array.isArray(message.content)) {
    return message.content
      .filter((part) => part.type === 'text')
      .map((part) => (part as ConversationTextContentPart).text)
      .join('\n');
  }
  return ' ';
}

export function getEagerRagToolResults(messages: ConversationMessage[]) {
  const eagerRagToolResults = messages.filter(
    (message) =>
      message.role === 'tool' && message.tool_call_id == 'call_ncc1701',
  );
  return eagerRagToolResults;
}
