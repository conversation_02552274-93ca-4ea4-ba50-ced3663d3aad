import { Filter } from '@tribble/salesforce';
import { query } from '@tribble/tribble-db/db_ops';
import {
  ClientSetting,
  SlackPrivacySetting,
  UserSetting,
} from '@tribble/types-shared';
import { ClientSetting as ClientSettingRecord } from './clients.ts';
import { UserSetting as UserSettingRecord } from './users.ts';

const constants = {
  SCHEMA_TRIBBLE: 'tribble',
  TABLE_CLIENT_SETTING: 'client_setting',
  TABLE_SETTING: 'setting',

  SETTING_AGENTIC_PROMPT: 'client_agentic_prompt',
  SETTING_AGENT_WEB_SEARCH_ALLOWLIST: 'client_web_search_allowlist',
  SETTING_ALLOW_SLACK_RAG: 'allow_slack_rag_conversation',
  SETTING_ASK_FOR_TAGS: 'ask_for_tags',
  SETTING_CLIENT_OVERRIDE_ALWAYS_ASK_FOR_TAGS:
    'client_override_always_ask_for_tags',
  SETTING_STRICT_MODE: 'client_metadata_strict_mode',
  SETTING_USES_AGENTIC_SLACKBOT: 'client_uses_agentic_slackbot',
  SETTING_USES_REDACATION: 'client_uses_redaction',
  SETTING_RFP_WRITER_MODEL: 'client_rfp_writer_model',
  SETTING_ENABLE_GONG: 'enable_gong_in_brain_search',
  SETTING_SALESFORCE_FILTER_CONFIG: 'salesforce_filter_config',
  SETTING_ENABLE_EXPORT_PPTX: 'enable_export_pptx',
};

export interface ResolvedSettings {
  usesRedaction: boolean;
  strictMode: boolean;
  askForTags: 'always' | 'never' | 'sometimes';

  defaultTags: number[];
  clientOverrideAlwaysAskForTags: boolean;
  agenticConfig: {
    useAgent: boolean;
    promptTemplate: string;
  };
  allowSlackRag: boolean;
  agentWebSearchAllowlist: string;
  slackPrivacySetting: SlackPrivacySetting;
  agentModelOverride: string;
  answerRfpModelOverride: string;
  useVerbatimMode: boolean;
  longformWriterModel: string;
  enableGong: boolean;
  salesforceFilterConfig: Filter.FilterConfig;
  enableExportPptx: boolean;
  enableAccessSequence: boolean;
}

export async function getResolvedSettings(
  schema: string,
  userSettings: UserSettingRecord[],
  clientSettings: ClientSettingRecord[],
): Promise<ResolvedSettings> {
  const userUseVerbatimMode = userSettings.find(
    (setting: UserSettingRecord) =>
      setting.setting_name === UserSetting.USE_VERBATIM_MODE,
  );

  const clientUseVerbatimMode =
    clientSettings.find(
      (setting) => setting.name === ClientSetting.SETTING_USE_VERBATIM_MODE,
    )?.value === 'true';

  const useVerbatimMode =
    userUseVerbatimMode != null
      ? userUseVerbatimMode.value == 'true'
      : clientUseVerbatimMode;

  const userModelOverride =
    userSettings.find(
      (setting: UserSettingRecord) =>
        setting.setting_name === UserSetting.USER_LEVEL_MODEL,
    )?.value ?? '';

  const clientModelOverride =
    clientSettings.find((s) => s.name === ClientSetting.CLIENT_LEVEL_MODEL)
      ?.value ?? '';

  const modelOverride = userModelOverride || clientModelOverride;

  const userAnswerRfpModelOverride =
    userSettings.find(
      (setting: UserSettingRecord) =>
        setting.setting_name === UserSetting.USER_RFP_MODEL,
    )?.value ?? '';

  const clientAnswerRfpModelOverride =
    clientSettings.find((s) => s.name === ClientSetting.CLIENT_RFP_MODEL)
      ?.value ?? '';

  const answerRfpModelOverride =
    userAnswerRfpModelOverride || clientAnswerRfpModelOverride;

  const defaultTagString = userSettings.find((setting: UserSettingRecord) => {
    return setting.setting_name === UserSetting.DEFAULT_TAGS;
  });
  const defaultTags: number[] = (defaultTagString?.value ?? '')
    .split(',')
    .map(Number)
    .filter((tagId: number) => !isNaN(tagId));

  const usesRedaction = clientSettings.some(
    (setting) =>
      setting.name === constants.SETTING_USES_REDACATION &&
      setting.value === 'true',
  );

  const clientOverrideAlwaysAskForTags = clientSettings.some((setting) => {
    return (
      setting.name === constants.SETTING_CLIENT_OVERRIDE_ALWAYS_ASK_FOR_TAGS &&
      setting.value === 'true'
    );
  });

  const slackPrivacySetting: SlackPrivacySetting =
    await getSlackPrivacySetting(schema);

  const strictMode = clientSettings.some(
    (setting) =>
      setting.name === constants.SETTING_STRICT_MODE &&
      setting.value === 'true',
  );
  const clientLevelUseAgent = clientSettings.some(
    (setting) =>
      setting.name === constants.SETTING_USES_AGENTIC_SLACKBOT &&
      setting.value === 'true',
  );

  const userLevelUseAgent = userSettings.find(
    (setting: UserSettingRecord) =>
      setting.setting_name === UserSetting.USE_AGENTIC,
  );

  const useAgent = userLevelUseAgent
    ? userLevelUseAgent.value === 'true'
    : clientLevelUseAgent;

  const allowSlackRag = clientSettings.some(
    (setting: any) =>
      setting.name === constants.SETTING_ALLOW_SLACK_RAG &&
      setting.value === 'true',
  );

  const clientAgenticPrompt = clientSettings.find(
    (setting: any) => setting.name === constants.SETTING_AGENTIC_PROMPT,
  )?.value;

  const userAgenticPrompt = userSettings.find(
    (setting: UserSettingRecord) =>
      setting.setting_name === UserSetting.USER_LEVEL_PROMPT,
  )?.value;

  const agentWebSearchAllowlist =
    clientSettings.find(
      (setting: any) =>
        setting.name === constants.SETTING_AGENT_WEB_SEARCH_ALLOWLIST,
    )?.value || '';

  let askForTags: 'always' | 'never' | 'sometimes' = 'sometimes';
  const haveAskForTags = userSettings.find(
    (setting: UserSettingRecord) =>
      setting.setting_name === constants.SETTING_ASK_FOR_TAGS,
  )?.value;
  switch (haveAskForTags) {
    case 'always':
    case 'never':
      askForTags = haveAskForTags;
      break;
  }
  const longformWriterModel = clientSettings.find(
    (setting) => setting.name === constants.SETTING_RFP_WRITER_MODEL,
  )?.value;

  const enableGong =
    clientSettings.find(
      (setting) => setting.name === constants.SETTING_ENABLE_GONG,
    )?.value === 'true';

  const enableAccessSequence =
    clientSettings.find(
      (setting) => setting.name === ClientSetting.ENABLE_ACCESS_SEQUENCE,
    )?.value === 'true';

  const salesforceFilterConfigString = clientSettings.find(
    (setting) => setting.name === constants.SETTING_SALESFORCE_FILTER_CONFIG,
  )?.value;

  let salesforceFilterConfig = {};
  try {
    salesforceFilterConfig = salesforceFilterConfigString
      ? JSON.parse(salesforceFilterConfigString)
      : {};
  } catch (err) {
    console.error(
      `[getResolvedSettings] Error parsing salesforce filter config: ${err.message}`,
    );
  }

  const enableExportPptx =
    clientSettings.find((s) => s.name === constants.SETTING_ENABLE_EXPORT_PPTX)
      ?.value === 'true';

  return {
    usesRedaction,
    strictMode,
    askForTags,
    clientOverrideAlwaysAskForTags,
    defaultTags,
    agenticConfig: {
      useAgent,
      promptTemplate: userAgenticPrompt || clientAgenticPrompt,
    },
    allowSlackRag,
    slackPrivacySetting,
    agentWebSearchAllowlist,
    agentModelOverride: modelOverride,
    answerRfpModelOverride,
    useVerbatimMode,
    longformWriterModel,
    enableGong,
    enableAccessSequence,
    salesforceFilterConfig,
    enableExportPptx,
  };
}

export async function getSlackPrivacySetting(
  schema: string,
): Promise<SlackPrivacySetting> {
  const queryString = `
    SELECT 
      (SELECT cs.value_boolean
      FROM ${schema}.${constants.TABLE_CLIENT_SETTING} cs
      JOIN ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_SETTING} setting ON cs.setting_id = setting.id
      WHERE setting.name = '${ClientSetting.SETTING_PREVENT_PRIVATE_CHANNELS}') AS prevent_private_channels,
      (SELECT cs.value_string
      FROM ${schema}.${constants.TABLE_CLIENT_SETTING} cs
      JOIN ${constants.SCHEMA_TRIBBLE}.${constants.TABLE_SETTING} setting ON cs.setting_id = setting.id
      WHERE setting.name = '${ClientSetting.SETTING_PRIVATE_CHANNEL_ALLOW_LIST}') AS private_channel_allow_list
  `;

  try {
    const result_raw = await query(queryString);

    if (result_raw.rows && result_raw.rows.length) {
      const allow_list = result_raw.rows[0].private_channel_allow_list ?? '';
      const prevent_private_channels =
        result_raw.rows[0].prevent_private_channels ?? false;

      return {
        prevent_private_channels,
        private_channel_allow_list: allow_list
          .split(',')
          .map((s: string) => s.trim())
          .filter((s: string) => s.length > 0),
      };
    }

    return { prevent_private_channels: false, private_channel_allow_list: [] };
  } catch (err) {
    console.error(`[getSlackPrivacySetting], ${err.message}`);
    return null;
  }
}
