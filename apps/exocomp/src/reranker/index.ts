import { chatCompletion } from '@tribble/chat-completion-service/client';
import { ClientId, ConversationId, UserId } from '@tribble/tribble-db/types';
import fs from 'fs';
import Handlebars from 'handlebars';
import path, { dirname } from 'path';
import { fileURLToPath } from 'url';
import {
  ConversationContext,
  MetadataFilterValue,
} from '../conversations/conversation_context.ts';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const RERANK_MODEL = process.env.RERANK_MODEL || 'gpt-4.1';

interface ReRankParams {
  contexts: ConversationContext[];
  clientName: string;
  question: string;
  schema: string;
  clientId: ClientId;
  userId: UserId;
  metadataFilters?: any;
  conversationId: ConversationId;
}

const reRankTool = {
  type: 'function',
  function: {
    name: 'rerank_contexts',
    description: 'Re-ranks a list of documents based on their relevance.',
    parameters: {
      type: 'object',
      properties: {
        documents: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: {
                type: 'number',
                description: 'Unique identifier for the document.',
              },
              relevance: {
                type: 'number',
                description:
                  'Relevance score of the document, ranging from 0 (not relevant) to 10 (most relevant).',
                minimum: 0,
                maximum: 10,
              },
            },
            required: ['id', 'relevance'],
          },
        },
      },
      required: ['documents'],
      description:
        'An object containing a list of documents, each represented by an id and a relevance score.',
    },
  },
};

export const ReRank = async (params: ReRankParams) => {
  let reRanked = [];

  try {
    const {
      contexts,
      clientName,
      question,
      schema,
      clientId,
      userId,
      metadataFilters,
    } = params;
    let contextStr = ``;

    if (!contexts || contexts.length === 0) {
      return reRanked;
    }

    contexts.map((context, i) => {
      let docText = context.text;

      if (context.table_text) {
        docText += ` ${context.table_text}`;
      }

      const metadataPrefix = getPlainLanguageMetadataFilterString(context);

      contextStr += `Document ${i + 1}: ${metadataPrefix}${docText}

`;
    });

    const promptSource = fs.readFileSync(
        path.join(__dirname, 'prompt.hbs'),
        'utf8',
      ),
      promptTemplate = Handlebars.compile(promptSource);

    const mergeFields = {
      hasMDF: metadataFilters?.length,
      contextStr: contextStr,
      clientName: clientName,
    };
    if (mergeFields.hasMDF)
      mergeFields['formattedMetadataFilters'] =
        stringifiedMDFs(metadataFilters);

    const systemPrompt = promptTemplate(mergeFields);

    const llmResponse = await chatCompletion({
      schema,
      timeout: Number(process.env.LLM_Q_TIMEOUT) || 30000,
      activityLogData: {
        source_id: String(params.conversationId),
        source_table: 'conversation',
        type: 'rerank',
        client_id: clientId,
        user_id: userId,
      },
      resourceUrl: '/openai/deployments/tribble-gpt4/chat/completions',
      request: {
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: question },
        ],
        model: RERANK_MODEL,
        tools: [reRankTool],
        tool_choice: {
          type: 'function',
          function: { name: reRankTool.function.name },
        },
      },
      apiVersion: process.env.AGENT_API_VERSION_LATEST ?? '2024-10-21',
    });
    const toolCall = llmResponse.response.choices[0].message.tool_calls[0],
      reRankArgs = JSON.parse(toolCall.function.arguments);

    let numFiltered = 0;
    for (let i = 0; i < reRankArgs.documents.length; i++) {
      const doc = reRankArgs.documents[i];

      // Tim: changed from > 0 to > 1: be a little more aggressive
      if (doc.relevance > 1) {
        reRanked.push(contexts[doc.id - 1]);
      } else {
        numFiltered++;
      }
    }

    console.log(
      `[reranker] 
      - ${contexts.length} contexts presented to Reranker. ${reRankArgs.documents.length} documents returned by Reranker.
      - Further filtered ${numFiltered} (out of ${reRankArgs.documents.length}) documents with relevance <= 1.`,
    );
  } catch (err) {
    console.log(`[reranker] Error: ${err.message}\n${err.stack}`);
    reRanked = params.contexts;
  } finally {
    return reRanked;
  }
};

function getPlainLanguageMetadataFilterString(
  context: ConversationContext,
): string {
  /**
   * Extract the 'metadata_filter' values from the 'context' object and return a plain string
   * for use in a prompt.
   * This metadatafilter {'vertical': ['cpg', 'fintech']} yields this string "For cpg and fintech: "
   **/
  if (
    !context.metadata_filter ||
    Object.keys(context.metadata_filter).length === 0
  ) {
    return '';
  } else {
    const val = Object.values(context.metadata_filter)[0];
    //val is either a string or an array of strings;
    if (typeof val === 'string') {
      return `For ${val}: `;
    } else {
      const allValues = ([] as any[]).concat(
        ...Object.values(context.metadata_filter),
      );
      const numItems = allValues.length;

      if (numItems == 1) {
        return `For ${allValues[0]}: `;
      } else if (numItems == 2) {
        return `For ${allValues[0]} and ${allValues[1]}: `;
      } else {
        let output = 'For ';
        for (let val of allValues.slice(0, numItems - 1)) {
          output += `${val}, `;
        }
        return output + `and ${allValues[numItems - 1]}: `;
      }
    }
  }
}

export function stringifiedMDFs(mdf_values: MetadataFilterValue[]): string {
  if (!mdf_values || mdf_values.length == 0) return '';

  return JSON.stringify(
    mdf_values.map((mdf) => {
      const tag = {
        type: mdf.type!.name,
        tag: mdf.value,
      };
      if (mdf.description) {
        tag['description'] = mdf.description;
      }
      if (mdf.synonyms) {
        tag['synonyms'] = mdf.synonyms;
      }
      return tag;
    }),
    null,
    2,
  );
}
