import { ClientUtils } from '@tribble/common-utils';
import { query } from '@tribble/tribble-db/db_ops';
import { constants, Filter, generateFilterClauses } from './brain.ts';
import { TimeFilter } from './tools/extract_temporal.ts';
import { SearchParam } from './tools/find_documents.ts';

export interface DocumentSearchParams {
  schema: string;
  search_params: SearchParam[];
  timeFilter?: TimeFilter;
}

export interface DocumentSearchResult {
  id: number;
  file_name: string;
  label: string;
  type: string;
  url: string;
  privacy: string;
  created_date?: string;
}

export const DocumentSearch = async (
  params: DocumentSearchParams,
): Promise<DocumentSearchResult[]> => {
  const { schema, search_params, timeFilter } = params;
  const filters = mapSearchParamsToFilters(search_params);
  const combinedFilterClause = await generateFilterClauses(schema, filters);

  // Time filter conditions
  const timeFilterClause = (() => {
    if (!timeFilter || (!timeFilter.startDate && !timeFilter.endDate))
      return '';

    // NOTE: must have unambiguous `created_date` column in the query
    const startDateCondition = timeFilter.startDate
      ? ` AND created_date >= '${timeFilter.startDate.toISOString()}'`
      : '';
    const endDateCondition = timeFilter.endDate
      ? ` AND created_date <= '${timeFilter.endDate.toISOString()}'`
      : '';

    return startDateCondition + endDateCondition;
  })();

  // Sort order based on timeFilter
  const sortOrderClause = (() => {
    if (!timeFilter?.sortOrder || timeFilter.sortOrder === 'not_applicable')
      return '';

    // NOTE: must have unambiguous `created_date` column in the query
    return timeFilter.sortOrder === 'newest'
      ? ' ORDER BY created_date DESC'
      : ' ORDER BY created_date ASC';
  })();

  const queryString = `
  WITH context_data AS (
    SELECT DISTINCT
      d.id,
      d.file_name,
      d.label,
      d.description,
      COALESCE(dt.name, 'Document') as type,
      d.privacy,
      -- 'created_date' should ideally be the date the original document should represent. Therefore, this order of preference:
      --    1. e.source_modified_date tends to be the most true to the original date of the document
      --    2. e.source_created_date tends to be the date of the document as it was uploaded to the source system
      --    3. e.modified_date is the date of the last modification to the document in Tribble
      --    4. d.created_date is the date of the document as it was created in Tribble
      COALESCE(e.source_modified_date, e.source_created_date, e.modified_date, d.created_date) as created_date,
      CASE
        -- Hacky for now.
        -- Blank Highspot for now: need to be able to construct a real URL (will be in a later PR)
        WHEN d.id = highspot.document_id THEN ''
        WHEN d.id = zendesk.document_id THEN zendesk.url
        ELSE d.source_url
      END AS source_url,
      CASE
        -- Hacky for now. But, no way to tell a Zendesk URL vs. a generic URL
        WHEN d.id = highspot.document_id THEN 'highspot'
        WHEN d.id = zendesk.document_id THEN 'zendesk'
        ELSE COALESCE(metadata_json->>'externalDocSource', '')
      END AS external_doc_source,
      CASE
        -- Hacky for now. Mainly needed for Highspot Item Deeplink
        WHEN d.id = highspot.document_id THEN highspot.item_id::text
        WHEN d.id = zendesk.document_id THEN zendesk.zendesk_id::text
        ELSE COALESCE(metadata_json->>'externalDocSourceId', '')
      END AS external_doc_source_id
    FROM ${schema}.${constants.TABLE_DOC} d
    LEFT JOIN ${schema}.${constants.TABLE_DOCUMENT_TYPE} dt ON d.document_type_id = dt.id
    INNER JOIN ${schema}.${constants.TABLE_EMBEDDING} e ON e.document_id = d.id

    -- See: brain.ts::getDocumentChain()
    LEFT JOIN ${schema}.${constants.TABLE_HIGHSPOT_ASSET} highspot ON d.id = highspot.document_id
    LEFT JOIN ${schema}.${constants.TABLE_ZENDESK_ASSET} zendesk ON d.id = zendesk.document_id       
    
    WHERE d.deleted_date is NULL
    AND (d.use_for_generation = TRUE OR dt.name = 'Slack')
    ${combinedFilterClause}
  )
  SELECT * FROM context_data
  WHERE TRUE
    -- Warn: ensure clauses below has access to needed columns from the CTE above
    ${timeFilterClause}
    ${sortOrderClause}
  LIMIT 10
  `;

  const result = await query(queryString);

  if (result && result.rows) {
    // Need special processing to handle source_url for Highspot
    // (See the /knowledge/source/:id route)
    const containsHighspotResult = result.rows.some(
      (row: any) => row.external_doc_source === 'highspot',
    );

    const highspotBaseUrl = containsHighspotResult
      ? await ClientUtils.getClientSetting(
          schema,
          constants.SETTING_CLIENT_HIGHSPOT_BASE_URL,
        )
      : '';

    return result.rows.map((row: any) => {
      let url;

      if (row.external_doc_source === 'highspot') {
        url = `${highspotBaseUrl}/items/${row.external_doc_source_id}`;
      } else if (row.source_url) {
        url = row.source_url;
      } else if (row.file_name?.startsWith('https://')) {
        url = row.file_name;
      } else {
        url = `${process.env.APP_PATH}/sources/source/${row.id}`;
      }

      return {
        id: row.id,
        file_name: row.file_name,
        label: row.label,
        ...(row.description && { description: row.description }),
        type: row.type,
        url: url,
        privacy: row.privacy,
        created_date: row.created_date,
      };
    });
  }

  return [];
};

function mapSearchParamsToFilters(searchParams: SearchParam[]): Filter[] {
  return searchParams.map((param) => ({
    source_name: param.document_name,
    source_type: param.document_type,
    source_platform: param.document_platform,
  }));
}
