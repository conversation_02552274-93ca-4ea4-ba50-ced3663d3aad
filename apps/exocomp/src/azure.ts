import { DefaultAzureCredential } from '@azure/identity';
import { SecretClient as AzureSecretClient } from '@azure/keyvault-secrets';

const credential = new DefaultAzureCredential();
const vaultName = process.env.AZURE_KEY_VAULT_NAME;
const vaultUrl = `https://${vaultName}.vault.azure.net`;

export class SecretClient {
  #client: AzureSecretClient;
  constructor() {
    this.#client = new AzureSecretClient(vaultUrl, credential);
  }

  async getSecret(secretName: string): Promise<string | null> {
    try {
      const res = await this.#client.getSecret(secretName);
      return res.value;
    } catch (err) {
      console.error(`[SecretClient.getSecret] ${err.message}`);
      return null;
    }
  }
}
