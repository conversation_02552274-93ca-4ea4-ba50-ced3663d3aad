import { getDB } from '@tribble/tribble-db/db_ops';

export async function insertActivityLog(
  client_id,
  user_id,
  source_table,
  source_id,
  type,
  event,
  token_usage,
  details,
  exclude_from_interaction_count = false,
): Promise<{ id: string }> {
  const db = await getDB();

  return db
    .insertInto('tribble.activity_log')
    .values({
      client_id,
      user_id,
      date_timestamp: new Date(),
      source_table,
      source_id,
      type,
      event,
      token_usage,
      details,
      exclude_from_interaction_count,
    })
    .returning('id')
    .executeTakeFirst();
}
