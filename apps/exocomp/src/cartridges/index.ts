import { loadCustomCartridgeById } from '@tribble/cartridge-helper';
import { Cartridge as CartridgeEnum } from '@tribble/conversation-service';
import { HubSpotClient } from '@tribble/hubspot';
import { getDB } from '@tribble/tribble-db/db_ops';
import { CartridgeId } from '@tribble/tribble-db/types';
import { Connection as SalesforceConnection } from 'jsforce';
import { Client } from '../clients.ts';
import { Conversation } from '../conversations/conversation.ts';
import { ResolvedSettings } from '../resolved_settings.ts';
import { AnswerRFPCartridge } from './answer_rfp/index.ts';
import { Cartridge } from './cartridge.ts';
import { CustomCartridge } from './custom_cartridge/index.ts';
import { DefaultCartridge } from './default/index.ts';
import { MeetingPrepCartridge } from './meeting_prep/index.ts';
import { PaddChat } from './padd_chat/index.ts';
import { RfpRequirementsEditor } from './rfp_requirements_editor/index.ts';
import { RfpResearcherCartridge } from './rfp_researcher/index.ts';
import { RfpWriterCartridge } from './rfp_writer/index.ts';
import { RFPWriterPromptGenerator } from './rfp_writer_prompt_generator/index.ts';
import { TribblyticsCartridge } from './tribblytics/index.ts';

export {
  AnswerRFPCartridge,
  DefaultCartridge,
  MeetingPrepCartridge,
  PaddChat,
  RfpRequirementsEditor,
  RfpResearcherCartridge,
  RfpWriterCartridge,
  RFPWriterPromptGenerator,
  TribblyticsCartridge,
};

export type CommonCartridgeArgs = {
  schema: `c${string}`;
  userId: number;
  client: Client;
  settings: ResolvedSettings;
  documentTypes: string[];
  conversation: Conversation;
  salesforceConn: SalesforceConnection;
  hubspotClient?: HubSpotClient; // HubSpot client connection
  message: string;
  prompt: string;
  imageInfos: {
    dataUrl: string;
    mimetype: string;
  }[];
};

//When adding a new cartridge, add it here.
export const getCartridgeByEnum = (cartridgeEnum: CartridgeEnum): Cartridge => {
  switch (cartridgeEnum) {
    case CartridgeEnum.DEFAULT:
      return new DefaultCartridge();
    case CartridgeEnum.RFP_WRITER:
      return new RfpWriterCartridge();
    case CartridgeEnum.RFP_PROMPT_GENERATOR:
      return new RFPWriterPromptGenerator();
    case CartridgeEnum.PADD_CHAT:
      return new PaddChat();
    case CartridgeEnum.RFP_RESEARCHER:
      return new RfpResearcherCartridge();
    case CartridgeEnum.ANSWER_RFP:
      return new AnswerRFPCartridge();
    case CartridgeEnum.RFP_REQUIREMENTS_EDITOR:
      return new RfpRequirementsEditor();
    case CartridgeEnum.MEETING_PREP:
      return new MeetingPrepCartridge();
    case CartridgeEnum.TRIBBLYTICS:
      return new TribblyticsCartridge();
    default:
      throw new Error(`Unknown cartridge name: ${cartridgeEnum}`);
  }
};

interface GetCartridgeParams {
  cartridgeEnum: CartridgeEnum;
  cartridgeId?: number;
  schema: string;
}

export const getCartridge = async ({
  cartridgeEnum,
  cartridgeId,
  schema,
}: GetCartridgeParams) => {
  if (cartridgeEnum === CartridgeEnum.CUSTOM) {
    const db = await getDB(schema);
    const cartridgeRecord = await loadCustomCartridgeById(
      db,
      cartridgeId! as CartridgeId,
    );
    return new CustomCartridge(cartridgeRecord);
  }
  return getCartridgeByEnum(cartridgeEnum);
};
