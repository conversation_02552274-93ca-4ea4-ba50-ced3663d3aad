import { Tool } from '@tribble/chat-completion-service/client';
import { ToolChoice } from '@tribble/types-shared';
import { Client, getClientDocumentTypes } from '../../clients.ts';
import { Conversation } from '../../conversations/conversation.ts';
import { ResolvedSettings } from '../../resolved_settings.ts';
import { AddToResearch } from '../../tools/add_to_research.ts';
import { BrainSearch } from '../../tools/brain_search.ts';
import { CompleteTask } from '../../tools/complete_task.ts';
import { SearchInDocuments } from '../../tools/search_in_documents.ts';
import { Cartridge } from '../cartridge.ts';

interface RfpResearcherArgs {
  schema: string;
  userId: number;
  client: Client;
  settings: ResolvedSettings;
  conversation: Conversation;
  prompt: string;
}

export class RfpResearcherCartridge extends Cartridge {
  toolChoice: ToolChoice = 'required'; //The only way out of this cartridge is to complete the research
  availableTools: Tool[] = [];
  conversation: Conversation = null;
  model = (process.env.RFP_RESEARCH_MODEL || 'o3-mini') as Cartridge['model'];

  private schema: string;
  private userId: number;
  private client: Client;
  private settings: ResolvedSettings;
  private raw_prompt: string;
  private prompt: string;

  constructor() {
    super();
  }

  async init(args: RfpResearcherArgs): Promise<void> {
    this.schema = args.schema;
    this.userId = args.userId;
    this.client = args.client;
    this.settings = args.settings;
    this.conversation = args.conversation;
    this.raw_prompt = args.prompt;
    const documentTypes = await getClientDocumentTypes(this.schema);
    const enableExtraFilters = false;

    if (
      this.settings.longformWriterModel &&
      this.settings.longformWriterModel !== ''
    ) {
      this.model = this.settings.longformWriterModel as Cartridge['model'];
    }

    this.availableTools = [
      SearchInDocuments.getJsonSchema(),
      AddToResearch.getJsonSchema(),
      CompleteTask.getJsonSchema(),
      BrainSearch.getJsonSchema(documentTypes, enableExtraFilters),
    ];
    this.prompt = this.raw_prompt;
  }

  async newConversationInit(_: any): Promise<void> {
    // Initialize the RFP Writer cartridge for a new conversation
  }

  async getPrompt(_: any): Promise<string> {
    return this.prompt;
  }
}
