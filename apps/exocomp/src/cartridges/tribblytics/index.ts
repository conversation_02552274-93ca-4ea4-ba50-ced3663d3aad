import { Tool } from '@tribble/chat-completion-service/client';
import fs from 'fs';
import Handlebars from 'handlebars';
import path from 'path';
import { fileURLToPath } from 'url';
import { Client } from '../../clients.ts';
import { Conversation } from '../../conversations/conversation.ts';
import { ConversationMessage } from '../../conversations/message.ts';
import { ResolvedSettings } from '../../resolved_settings.ts';
import {
  CreateImageFromPromptTool,
  ExtractTemporal,
} from '../../tools/index.ts';
import { ConversationIntelligence } from '../../tools/tribblytics/conversation_intelligence.ts';
import { RfxIntelligence } from '../../tools/tribblytics/rfx_intelligence.ts';
import { Cartridge } from '../cartridge.ts';

interface TribblyticsCartridgeArgs {
  schema: string;
  userId: number;
  client: Client;
  settings: ResolvedSettings;
  conversation: Conversation;
  message: string;
  documentTypes: string[];
  prompt?: string;
}

export class TribblyticsCartridge extends Cartridge {
  availableTools: Tool[] = [];
  conversation: Conversation = null;

  private settings: ResolvedSettings;
  private userId: number;
  private schema: string;
  private client: Client;
  private prompt: string;
  private text: string;
  private documentTypes: string[];

  model = (process.env.TRIBBLYTICS_AGENT_MODEL ||
    'claude-sonnet-4') as Cartridge['model'];

  constructor() {
    super();
  }

  async init(args: TribblyticsCartridgeArgs): Promise<void> {
    this.settings = args.settings;
    this.userId = args.userId;
    this.schema = args.schema;
    this.client = args.client;
    this.text = args.message;
    this.conversation = args.conversation;
    this.documentTypes = args.documentTypes;

    this.availableTools = [
      CreateImageFromPromptTool.getJsonSchema(),
      ExtractTemporal.getJsonSchema(),
      ConversationIntelligence.getJsonSchema({ clientName: this.client.name }),
      RfxIntelligence.getJsonSchema({ clientName: this.client.name }),
    ];

    this.prompt = await this.compilePrompt();
  }

  async newConversationInit() {
    // @TODO: Perhaps some sort of high-level picture of what data is available for a deeper dive later?
  }

  async midConversationInit(args: {
    conversationId: number;
    messages: ConversationMessage[];
    conversation: Conversation;
    cartridgeArgs: Record<string, any>;
  }): Promise<{
    messages: ConversationMessage[];
    systemMessage: string;
    tools: Tool[];
    model?: string;
  }> {
    await this.init(args.cartridgeArgs as TribblyticsCartridgeArgs);

    const systemMessage = await this.getPrompt();

    return {
      messages: args.messages,
      systemMessage,
      tools: this.getAvailableTools(),
      model: this.model,
    };
  }

  async getPrompt(): Promise<string> {
    return this.prompt;
  }

  getAvailableTools(): Tool[] {
    return this.availableTools;
  }

  private async compilePrompt(): Promise<string> {
    const currentDate = new Date().toISOString().split('T')[0]; //YYYY-MM-DD
    const promptSource = await this.getDefaultPrompt();
    const promptTemplate = Handlebars.compile(promptSource);

    const retrievalToolDescriptions = [
      ConversationIntelligence.getPromptDescription({
        clientName: this.client.name,
      }),
      RfxIntelligence.getPromptDescription({
        clientName: this.client.name,
      }),
      ExtractTemporal.getPromptDescription(),
    ]
      .filter((description) => description && description.trim().length > 0)
      .map((description, index) => `${index + 1}. ${description}`)
      .join('\n');

    const generationToolDescriptions = [
      CreateImageFromPromptTool.getPromptDescription(),
    ]
      .filter((description) => description && description.trim().length > 0)
      .map((description, index) => `${index + 1}. ${description}`)
      .join('\n');

    const promptVars = {
      clientName: this.client.name,
      currentDate,
      extractTemporalToolName: ExtractTemporal.tool_name,
      temporalSupportedTools: ExtractTemporal.getSupportedTools().join(', '),
      retrievalToolDescriptions: retrievalToolDescriptions || '',
      generationToolDescriptions: generationToolDescriptions || '',
    };

    return promptTemplate(promptVars);
  }

  private async getDefaultPrompt(): Promise<string> {
    const thisFile = fileURLToPath(import.meta.url);
    const dirname = path.dirname(thisFile);
    return fs.readFileSync(path.join(dirname, 'prompt.hbs'), 'utf8');
  }
}
