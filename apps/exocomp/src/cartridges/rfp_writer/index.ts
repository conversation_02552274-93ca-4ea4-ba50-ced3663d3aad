import { Tool } from '@tribble/chat-completion-service/client';
import Handlebars from 'handlebars';
import { Client } from '../../clients.ts';
import { Conversation } from '../../conversations/conversation.ts';
import { ResolvedSettings } from '../../resolved_settings.ts';
import { BidPacketSearch } from '../../tools/bid_packet_search.ts';
import { BrainSearch } from '../../tools/brain_search.ts';
import { FetchUrl } from '../../tools/fetch_url.ts';
import { SearchInDocuments } from '../../tools/search_in_documents.ts';
import { WebSearch } from '../../tools/web_search.ts';
import { Cartridge } from '../cartridge.ts';

interface RfpWriterCartridgeArgs {
  schema: string;
  userId: number;
  client: Client;
  settings: ResolvedSettings;
  conversation: Conversation;
  prompt: string;
}

export class RfpWriterCartridge extends Cartridge {
  availableTools: Tool[] = [];
  conversation: Conversation = null;
  model = process.env.RFP_WRITER_MODEL as Cartridge['model'];
  maxTokens: number = 16000;

  private schema: string;
  private userId: number;
  private client: Client;
  private settings: ResolvedSettings;
  private raw_prompt: string;
  private prompt: string;

  constructor() {
    super();
  }

  async init(args: RfpWriterCartridgeArgs): Promise<void> {
    this.schema = args.schema;
    this.userId = args.userId;
    this.client = args.client;
    this.settings = args.settings;
    this.conversation = args.conversation;
    this.raw_prompt = args.prompt;

    if (
      this.settings.longformWriterModel &&
      this.settings.longformWriterModel !== ''
    ) {
      this.model = this.settings.longformWriterModel as Cartridge['model'];
    }

    this.availableTools = [
      // BidPacketSearch.getJsonSchema(),
      // BrainSearch.getJsonSchema(null),
      // WebSearch.getJsonSchema({ sites: null }),
      // FetchUrl.getJsonSchema(),
    ];
    this.prompt = await this.compilePrompt();
  }

  async newConversationInit(_: any): Promise<void> {
    // Initialize the RFP Writer cartridge for a new conversation
  }

  async getPrompt(_: any): Promise<string> {
    return this.prompt;
  }

  private async compilePrompt(): Promise<string> {
    // Compile the prompt for the user
    return this.raw_prompt;
    const toolDescriptions = [
      BidPacketSearch.getPromptDescription(),
      BrainSearch.getPromptDescription({ clientName: this.client.name }),
      WebSearch.getPromptDescription({ clientName: this.client.name }),
      `Use the ${SearchInDocuments.tool_name} tool to search for information in a specific document, using a document ID.`,
    ].join(' ');

    if (this.raw_prompt.includes('{{{toolDescriptions}}}') === false) {
      this.raw_prompt += `\nYou have access to the following tools to help accomplish your task:\n{{{toolDescriptions}}}`;
    }
    const promptTemplate = Handlebars.compile(this.raw_prompt);
    return promptTemplate({
      toolDescriptions,
      clientName: this.client.name,
    });
  }
}
