import { Tool } from '@tribble/chat-completion-service/client';
import { ToolChoice } from '@tribble/types-shared';
import { Client } from '../../clients.ts';
import { Conversation } from '../../conversations/conversation.ts';
import { ResolvedSettings } from '../../resolved_settings.ts';

import { CompleteTask } from '../../tools/complete_task.ts';
import { EditRequirements } from '../../tools/edit_requirements.ts';
import { Cartridge } from '../cartridge.ts';

interface RfpResearcherArgs {
  schema: string;
  userId: number;
  client: Client;
  settings: ResolvedSettings;
  conversation: Conversation;
  prompt: string;
}

export class RfpRequirementsEditor extends Cartridge {
  toolChoice: ToolChoice = 'required'; //The only way out of this cartridge is to call complete_task
  availableTools: Tool[] = [];
  conversation: Conversation = null;
  model = (process.env.RFP_RESEARCH_MODEL || 'o3-mini') as Cartridge['model'];

  private raw_prompt: string;
  private prompt: string;

  constructor() {
    super();
  }

  async init(args: RfpResearcherArgs): Promise<void> {
    this.conversation = args.conversation;
    this.raw_prompt = args.prompt;

    if (
      args.settings.longformWriterModel &&
      args.settings.longformWriterModel !== ''
    ) {
      this.model = args.settings.longformWriterModel as Cartridge['model'];
    }

    this.availableTools = [
      EditRequirements.getJsonSchema(),
      CompleteTask.getJsonSchema(),
    ];
    this.prompt = this.raw_prompt;
  }

  async newConversationInit(_: any): Promise<void> {
    // Initialize the RFP Writer cartridge for a new conversation
  }

  async getPrompt(_: any): Promise<string> {
    return this.prompt;
  }
}
