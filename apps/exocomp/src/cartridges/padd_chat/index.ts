import { Tool } from '@tribble/chat-completion-service/client';
import { ClientUtils } from '@tribble/common-utils';
import Handlebars from 'handlebars';
import { Client } from '../../clients.ts';
import { Conversation } from '../../conversations/conversation.ts';
import { ResolvedSettings } from '../../resolved_settings.ts';
// import { BidPacketSearch } from '../../tools/bid_packet_search.ts';
import { BrainSearch } from '../../tools/brain_search.ts';
import { WebSearch } from '../../tools/web_search.ts';
import { Cartridge } from '../cartridge.ts';

interface PaddChatArgs {
  schema: string;
  userId: number;
  client: Client;
  settings: ResolvedSettings;
  conversation: Conversation;
  prompt: string;
}

export class PaddChat extends Cartridge {
  availableTools: Tool[] = [];
  conversation: Conversation = null;
  model = process.env.PADD_CHAT_MODEL as Cartridge['model'];

  private schema: string;
  private userId: number;
  private client: Client;
  private settings: ResolvedSettings;
  private raw_prompt: string;
  private prompt: string;

  constructor() {
    super();
  }

  async init(args: PaddChatArgs): Promise<void> {
    this.schema = args.schema;
    this.userId = args.userId;
    this.client = args.client;
    this.settings = args.settings;
    this.conversation = args.conversation;
    this.raw_prompt = args.prompt;

    this.availableTools = [
      // BidPacketSearch.getJsonSchema(),
      BrainSearch.getJsonSchema(null),
      WebSearch.getJsonSchema({ sites: null }),
    ];
    this.prompt = await this.compilePrompt();
  }

  async newConversationInit(_: any): Promise<void> {
    // Initialize the cartridge for a new conversation
  }

  async getPrompt(_: any): Promise<string> {
    return this.prompt;
  }

  private async compilePrompt(): Promise<string> {
    const clientDescription = await ClientUtils.getClientDescription(
      this.schema,
    );

    const toolDescriptions = [
      // BidPacketSearch.getPromptDescription(),
      BrainSearch.getPromptDescription({ clientName: this.client.name }),
      WebSearch.getPromptDescription({ clientName: this.client.name }),
    ].join(' ');

    if (this.raw_prompt.includes('{{{toolDescriptions}}}') === false) {
      this.raw_prompt += `\nYou have access to the following tools to help accomplish your task:\n{{{toolDescriptions}}}`;
    }
    const promptTemplate = Handlebars.compile(this.raw_prompt);
    return promptTemplate({
      toolDescriptions,
      clientName: this.client.name,
      clientDescription,
      //@TODO: Add customer name to the prompt
      //  after multifile and making rfx records the main thing
      //  perhaps linking conversations to rfx records
    });
  }
}
