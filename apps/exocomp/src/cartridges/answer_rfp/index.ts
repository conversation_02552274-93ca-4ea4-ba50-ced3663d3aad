import { Tool } from '@tribble/chat-completion-service/client';
import {
  ClientUtils,
  CONSTANTS,
  ContentUtils,
  RFXUtils,
  UserUtils,
} from '@tribble/common-utils';
import { ClientSchema } from '@tribble/tribble-db';
import { ClientId, UserId } from '@tribble/tribble-db/types';
import fs from 'fs';
import Handlebars from 'handlebars';
import { Connection as SalesforceConnection } from 'jsforce';
import path from 'path';
import { fileURLToPath } from 'url';
import { CustomTableSearch } from '../../brain.ts';
import { Client } from '../../clients.ts';
import { extractDomainName } from '../../conversation_service.ts';
import {
  Conversation,
  updateConversationSources,
} from '../../conversations/conversation.ts';
import { insertConversationDetail } from '../../conversations/conversation_detail.ts';
import {
  formatTagsForPrompt,
  getTagTypeString,
} from '../../metadata_filters.ts';
import { ResolvedSettings } from '../../resolved_settings.ts';
import { hasStructuredData } from '../../structured_data.ts';
import {
  BrainSearch,
  CustomTableDescribe,
  ExtractTemporal,
  FetchUrl,
  FindDocuments,
  LearnFact,
  SalesforceDescribe,
  SalesforceQuery,
  SearchCustomTables,
  SearchInDocuments,
  StructuredQuery,
  SummarizeDocuments,
  TieredContextSearchTool,
  WebSearch,
} from '../../tools/index.ts';
import { Cartridge } from '../cartridge.ts';

const constants = {
  OUTPUT_NO_ANSWER: 'Could not provide an answer',
  DOCUMENT_INTELLIGENCE_PAGE_DELIMITER: '<!-- PageBreak -->',
};

interface AnswerRFPCartridgeArgs {
  schema: string;
  userId: number;
  client: Client;
  settings: ResolvedSettings;
  documentTypes: string[];
  conversation: Conversation;
  salesforceConn?: SalesforceConnection;
  message: string;
}

export class AnswerRFPCartridge extends Cartridge {
  availableTools: Tool[] = [];
  conversation: Conversation = null;
  model = (process.env.ANSWER_RFP_MODEL as Cartridge['model']) || 'gpt-4.1';
  agentType: string = 'rfp_agent';

  private webSearchSites: string[];
  private settings: ResolvedSettings;
  private userId: number;
  private schema: string;
  private client: Client;
  private prompt: string;
  private text: string;
  private documentTypes: string[];

  private hasSalesforce: boolean = false;
  private hasStructuredData: boolean = false;
  private enableAccessSequence: boolean = false;
  private internalSearchToolName: string = BrainSearch.tool_name;

  constructor() {
    super();
  }

  getAgentType(): string {
    return this.agentType;
  }

  async init(args: AnswerRFPCartridgeArgs): Promise<void> {
    this.webSearchSites = args.settings.agentWebSearchAllowlist
      .split('\n')
      .map(extractDomainName)
      .filter(Boolean);
    this.settings = args.settings;
    this.userId = args.userId;
    this.schema = args.schema;
    this.client = args.client;
    this.text = args.message;
    this.conversation = args.conversation;
    this.documentTypes = args.documentTypes;

    // In conversation service, the user or client agent model override
    // can be superceded by this cartridge's model. However, we want to
    // allow the user or client to override the cartridge's model.
    if (this.settings.answerRfpModelOverride) {
      console.log(
        `[AnswerRFPCartridge] Using answerRfpModelOverride: ${this.settings.answerRfpModelOverride}`,
      );
      this.model = this.settings.answerRfpModelOverride as Cartridge['model'];
    }

    // Check if enable_access_sequence setting is enabled
    this.enableAccessSequence = this.settings.enableAccessSequence;

    // Set the internal search tool name based on the setting
    this.internalSearchToolName = this.enableAccessSequence
      ? TieredContextSearchTool.tool_name
      : BrainSearch.tool_name;

    // Choose the appropriate search tool based on the setting
    const searchTool = this.enableAccessSequence
      ? TieredContextSearchTool.getJsonSchema(
          this.documentTypes.filter((dt) => dt !== 'Spreadsheet'),
          true, // enableExtraFilters
        )
      : BrainSearch.getJsonSchema(
          this.documentTypes.filter((dt) => dt !== 'Spreadsheet'),
        );

    this.availableTools = [
      ExtractTemporal.getJsonSchema(),
      searchTool,
      FindDocuments.getJsonSchema(this.documentTypes),
      SearchInDocuments.getJsonSchema(),
      // SummarizeDocuments.getJsonSchema(),
      // WebSearch.getJsonSchema(this.webSearchSites),
      // FetchUrl.getJsonSchema(),
      // LearnFact.getJsonSchema(),
    ];

    if (args.salesforceConn) {
      this.hasSalesforce = true;
      this.availableTools.push(SalesforceQuery.getJsonSchema());
      this.availableTools.push(SalesforceDescribe.getJsonSchema());
    }

    if (await hasStructuredData(args.schema)) {
      this.hasStructuredData = true;
      this.availableTools.push(StructuredQuery.getJsonSchema());
      this.availableTools.push(SearchCustomTables.getJsonSchema());
      this.availableTools.push(CustomTableDescribe.getJsonSchema());
    }

    this.prompt = await this.compilePrompt();
  }

  async newConversationInit() {
    //If this is the FIRST message in a conversation, append an eager RAG tool call to start the process.
    try {
      const query = this.stripUserIds(this.text);

      let searchResults: string;
      let searchToolCall: BrainSearch | TieredContextSearchTool;
      let toolName: string;
      let toolArguments: string;

      if (this.enableAccessSequence) {
        // Use TieredContextSearchTool with tier 1 for eager RAG
        searchToolCall = new TieredContextSearchTool(
          {
            schema: this.schema,
            clientId: this.client.id as ClientId,
            clientName: this.client.name,
            conversationId: this.conversation.conversationId(),
            metadataFilters: this.conversation.metadataFilters,
            tribbleUserId: this.userId as UserId,
            includeVerbatim: true,
            includeImages: false,
            settings: this.settings,
          },
          {
            query: query,
            tier: 1, // Use tier 1 for eager RAG
          },
        );
        toolName = TieredContextSearchTool.tool_name;
        toolArguments = `{\n"query": ${JSON.stringify(query)},\n"tier": 1\n}`;
      } else {
        // Use regular BrainSearch
        searchToolCall = new BrainSearch(
          {
            schema: this.schema,
            clientId: this.client.id as ClientId,
            clientName: this.client.name,
            conversationId: this.conversation.conversationId(),
            metadataFilters: this.conversation.metadataFilters,
            tribbleUserId: this.userId as UserId,
            includeVerbatim: true,
            includeImages: false,
            settings: this.settings,
          },
          { query: query },
        );
        toolName = BrainSearch.tool_name;
        toolArguments = `{\n"query": ${JSON.stringify(query)}\n}`;
      }

      searchResults = await searchToolCall.performCall();
      if (searchResults) {
        // Inject the contexts into the initial system prompt.
        this.prompt =
          this.prompt.trim() + `\n\n## Initial Contexts\n${searchResults}`;

        //Eager structured tables search
        if (this.hasStructuredData) {
          const EAGER_SIMILARITY_THRESHOLD = process.env
            .EAGER_TABLE_SIMILARITY_THRESHOLD
            ? parseInt(process.env.EAGER_TABLE_SIMILARITY_THRESHOLD)
            : 0.8;

          const eagerStructuredTables = (
            await CustomTableSearch({
              query,
              schema: this.schema,
            })
          )
            .filter((result) => result.similarity > EAGER_SIMILARITY_THRESHOLD)
            .map((result) => {
              return {
                table_name: result.table_name,
                description: result.description,
              };
            });

          if (eagerStructuredTables.length) {
            this.prompt += `\n\nThe following tables may contain useful information. Use ${CustomTableDescribe.tool_name} and ${StructuredQuery.tool_name} to query them:\n${JSON.stringify(eagerStructuredTables, null, 2)}`;
          }
        }
        // Update the conversation record sources to this initial context
        await updateConversationSources(
          this.schema as ClientSchema,
          this.conversation.conversationId(),
          searchToolCall.getContexts(),
        );

        //Add the results to the conversation history so it'll be pulled next time.
        const eagerSearch = {
            role: 'assistant',
            content: null,
            tool_calls: [
              {
                id: 'call_ncc1701',
                type: 'function',
                function: {
                  name: toolName,
                  arguments: toolArguments,
                },
              },
            ],
          },
          eagerSearchResults = {
            role: 'tool',
            content: searchResults,
            tool_call_id: 'call_ncc1701',
          };

        await insertConversationDetail(
          this.schema,
          this.conversation.conversationId(),
          eagerSearch,
        );
        await insertConversationDetail(
          this.schema,
          this.conversation.conversationId(),
          eagerSearchResults,
        );
      }
    } catch (err) {
      console.error(`[chatResponder] eager RAG error: ${err.message}`);
      console.error(err.stack);
    }
  }

  private stripUserIds(text: string) {
    // Strip out @ mentioned User IDs enclosed in <@> e.g., <@UIDIDih39>
    const pattern = /<@(.*?)>/g;
    let stripped = text.replace(pattern, '');
    return stripped;
  }

  async getPrompt() {
    return this.prompt;
  }

  private async compilePrompt(): Promise<string> {
    const currentDate = new Date().toISOString().split('T')[0]; //YYYY-MM-DD

    let promptSource = await this.getDefaultPrompt();
    const clientOverrideAnswerRfpPrompt = await ClientUtils.getClientSetting(
      this.schema,
      CONSTANTS.SETTING_CLIENT_RFP_PROMPT,
    );
    const userOverrideAnswerRfpPrompt = await UserUtils.getUserSetting(
      this.schema,
      this.userId,
      CONSTANTS.SETTING_USER_RFP_PROMPT,
    );

    if (userOverrideAnswerRfpPrompt) {
      promptSource = userOverrideAnswerRfpPrompt;
    } else if (clientOverrideAnswerRfpPrompt) {
      promptSource = clientOverrideAnswerRfpPrompt;
    }

    // Get the appropriate search tool description
    const searchToolDescription = this.enableAccessSequence
      ? TieredContextSearchTool.getPromptDescription({
          clientName: this.client.name,
        })
      : BrainSearch.getPromptDescription({ clientName: this.client.name });

    const mainToolDescription =
      'You have access to the following tools:\n\n' +
      [
        ExtractTemporal.getPromptDescription(),
        searchToolDescription,
        FindDocuments.getPromptDescription({
          documentTypes: this.documentTypes,
          clientName: this.client.name,
        }),
        SearchInDocuments.getPromptDescription({
          clientName: this.client.name,
        }),
        // SummarizeDocuments.getPromptDescription(),
        // WebSearch.getPromptDescription({
        //   sites: this.webSearchSites,
        //   clientName: this.client.name,
        // }),
      ]
        .map((description, index) => `${index + 1}. ${description}`)
        .join('\n');

    const toolDescriptionStrings = [mainToolDescription];

    if (this.hasSalesforce) {
      toolDescriptionStrings.push(SalesforceQuery.getPromptDescription());
    }

    if (this.hasStructuredData) {
      toolDescriptionStrings.push(StructuredQuery.getPromptDescription());
    }

    const toolDescriptions = toolDescriptionStrings.join('\n');

    const promptTemplate = Handlebars.compile(promptSource);

    const activeMetadataFilters = this.conversation.metadataFilters;
    const clientDescription = await ClientUtils.getClientDescription(
      this.schema,
    );

    // Find the customer name from the conversation --> content detail --> content record --> customer name
    const contentRecord =
      await ContentUtils.getContentRecordFromContentDetailConversationId(
        this.schema,
        Number(this.conversation.conversationId()),
      );

    const customerName = contentRecord?.details?.customer_name;

    // Get bid packet files from content record
    const bidPacketFiles = await RFXUtils.getBidPacketFilesFromContentId(
      this.schema,
      contentRecord.id,
    );

    // Helper function to format content with pagination
    const formatContentWithPagination = (content_markdown: string | null) => {
      if (!content_markdown) return '';

      return content_markdown
        .split(constants.DOCUMENT_INTELLIGENCE_PAGE_DELIMITER)
        .map((page, index) => `<!-- PageNumber="${index + 1}" -->\n${page}`)
        .join('\n\n')
        .trim();
    };

    const bidPacketFilesContent = bidPacketFiles.map((bidPacketFile) => ({
      fileName: bidPacketFile.file_name,
      content: formatContentWithPagination(bidPacketFile.content_markdown),
    }));

    const promptVars = {
      clientName: this.client.name,
      clientDescription,
      customerName,
      internalSearchToolName: this.internalSearchToolName,
      metadataFilters: activeMetadataFilters,
      multipleTypes: false,
      numTagTypes: 0,
      tagGroupNames: '',
      toolDescriptions,
      output_no_answer: constants.OUTPUT_NO_ANSWER,
      formattedMetadataFilters: '',
      summary: this.conversation.summary,
      currentDate,
      extractTemporalToolName: ExtractTemporal.tool_name,
      temporalSupportedTools: ExtractTemporal.getSupportedTools().join(', '),
      enableTieredSearch: this.enableAccessSequence,
      tieredSearchToolName: TieredContextSearchTool.tool_name,
      bidPacketFilesContent,
    };

    if (activeMetadataFilters && activeMetadataFilters.length) {
      (promptVars.numTagTypes = new Set(
        activeMetadataFilters.map((mdf) => mdf.type_id),
      ).size),
        (promptVars.tagGroupNames = getTagTypeString(activeMetadataFilters));
      promptVars.formattedMetadataFilters = JSON.stringify(
        formatTagsForPrompt(activeMetadataFilters),
        null,
        2,
      );
    }

    const prompt = promptTemplate(promptVars);
    return prompt;
  }

  private async getDefaultPrompt(): Promise<string> {
    const thisFile = fileURLToPath(import.meta.url);
    const dirname = path.dirname(thisFile);
    return fs.readFileSync(path.join(dirname, 'prompt.hbs'), 'utf8');
  }
}
