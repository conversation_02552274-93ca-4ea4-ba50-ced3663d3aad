import { Tool } from '@tribble/chat-completion-service/client';
import { ClientUtils } from '@tribble/common-utils';
import { initializeGongClient } from '@tribble/gong-service';
import fs from 'fs';
import Handlebars from 'handlebars';
import { Connection as SalesforceConnection } from 'jsforce';
import path from 'path';
import { fileURLToPath } from 'url';
import { Client } from '../../clients.ts';
import { extractDomainName } from '../../conversation_service.ts';
import { Conversation } from '../../conversations/conversation.ts';
import { ResolvedSettings } from '../../resolved_settings.ts';
import { FetchUrl } from '../../tools/fetch_url.ts';
import { GetMeetingSummaries } from '../../tools/get_meeting_summaries.ts';
import { CreateImageFromPromptTool } from '../../tools/index.ts';
import { SalesforceDescribe } from '../../tools/salesforce_describe.ts';
import { SalesforceQuery } from '../../tools/salesforce_query.ts';
import { WebSearch } from '../../tools/web_search.ts';
import { Cartridge } from '../cartridge.ts';

interface MeetingPrepArgs {
  schema: string;
  userId: number;
  client: Client;
  settings: ResolvedSettings;
  documentTypes: string[];
  conversation: Conversation;
  salesforceConn?: SalesforceConnection;
}

export class MeetingPrepCartridge extends Cartridge {
  private schema: string;
  private settings: ResolvedSettings;
  private salesforceConn: SalesforceConnection;
  private conversation: Conversation;
  private client: Client;

  private prompt: string;

  private hasSalesforce: boolean = false;
  private hasGong: boolean = false;

  availableTools: Tool[] = [];
  model = (process.env.MEETING_PREP_MODEL ||
    'claude-3-7-sonnet') as Cartridge['model'];

  constructor() {
    super();
  }

  async init(args: MeetingPrepArgs) {
    this.schema = args.schema;
    this.settings = args.settings;
    this.conversation = args.conversation;
    this.client = args.client;

    const toolDescripions = [];

    if (args.salesforceConn) {
      this.salesforceConn = args.salesforceConn;
      this.hasSalesforce = true;
      this.availableTools.push(SalesforceQuery.getJsonSchema());
      this.availableTools.push(SalesforceDescribe.getJsonSchema());

      toolDescripions.push(SalesforceQuery.getPromptDescription());
    }

    const gongClient = await initializeGongClient(this.schema);
    if (gongClient) {
      this.hasGong = true;
    }

    if (this.hasGong && this.hasSalesforce) {
      this.availableTools.push(GetMeetingSummaries.getJsonSchema());
      toolDescripions.push(GetMeetingSummaries.getPromptDescription());
    }

    //Web
    const webSearchSites =
      this.settings.agentWebSearchAllowlist ??
      ''.split('\n').map(extractDomainName).filter(Boolean);

    this.availableTools.push(WebSearch.getJsonSchema(webSearchSites));
    this.availableTools.push(FetchUrl.getJsonSchema());
    toolDescripions.push(WebSearch.getPromptDescription(webSearchSites));

    this.availableTools.push(CreateImageFromPromptTool.getJsonSchema());
    toolDescripions.push(CreateImageFromPromptTool.getPromptDescription());

    //Prompt!
    const currentDate = new Date().toISOString().split('T')[0]; //YYYY-MM-DD
    const clientName = this.client.name;
    const clientDescription = await ClientUtils.getClientDescription(
      this.schema,
    );

    const promptVars = {
      clientName,
      clientDescription,
      meeting_summary_tool: GetMeetingSummaries.tool_name,
      currentDate,
      slide_generation_tool: CreateImageFromPromptTool.tool_name,
      toolDescripions: toolDescripions.join('\n'),
      ...(this.hasSalesforce ? { has_salesforce: true } : {}),
    };

    const promptSource = await this.getDefaultPrompt();
    const promptTemplate = Handlebars.compile(promptSource);
    const prompt = promptTemplate(promptVars);
    this.prompt = prompt;
  }

  async getPrompt() {
    return this.prompt;
  }

  getAvailableTools(): Tool[] {
    return this.availableTools;
  }

  newConversationInit(_: any): Promise<void> {
    return;
  }

  private async getDefaultPrompt(): Promise<string> {
    const thisFile = fileURLToPath(import.meta.url);
    const dirname = path.dirname(thisFile);
    return fs.readFileSync(path.join(dirname, 'prompt.hbs'), 'utf8');
  }
}
