import { Tool } from '@tribble/chat-completion-service/client';
import { initializeGongClient } from '@tribble/gong-service';
import { HubSpotClient } from '@tribble/hubspot';
import { ClientSchema } from '@tribble/tribble-db';
import { ClientId, UserId } from '@tribble/tribble-db/types';
import fs from 'fs';
import Handlebars from 'handlebars';
import { Connection as SalesforceConnection } from 'jsforce';
import path from 'path';
import { fileURLToPath } from 'url';
import { CustomTableSearch } from '../../brain.ts';
import { Client } from '../../clients.ts';
import { extractDomainName } from '../../conversation_service.ts';
import {
  Conversation,
  updateConversationSources,
} from '../../conversations/conversation.ts';
import { insertConversationDetail } from '../../conversations/conversation_detail.ts';
import {
  formatTagsForPrompt,
  getTagTypeString,
} from '../../metadata_filters.ts';
import { ResolvedSettings } from '../../resolved_settings.ts';
import { hasStructuredData } from '../../structured_data.ts';
import { GetMeetingSummaries } from '../../tools/get_meeting_summaries.ts';
import {
  BrainSearch,
  CreateImageFromPromptTool,
  CreatePptxFromImageTool,
  CreateShareableDeckTool,
  CustomTableDescribe,
  ExtractTemporal,
  FetchUrl,
  FindDocuments,
  GenerateEphemeralUI,
  GenerateResultUI,
  HubSpotDescribe,
  HubSpotQuery,
  HubSpotWrite,
  LearnFact,
  ProcessUISubmission,
  SalesforceDescribe,
  SalesforceQuery,
  SearchCustomTables,
  SearchInDocuments,
  StructuredQuery,
  SummarizeDocuments,
  TieredContextSearchTool,
  WebSearch,
} from '../../tools/index.ts';
import { InvokeMeetingPrep } from '../../tools/invoke_meeting_prep.ts';
import { SalesforceWrite } from '../../tools/salesforce_write.ts';
import { Cartridge } from '../cartridge.ts';

const constants = {
  OUTPUT_NO_ANSWER: 'Could not provide an answer',
};

interface DefaultCartridgeArgs {
  schema: string;
  userId: number;
  client: Client;
  settings: ResolvedSettings;
  documentTypes: string[];
  conversation: Conversation;
  salesforceConn?: SalesforceConnection;
  hubspotClient?: HubSpotClient;
  message: string;
  prompt?: string;
  imageInfos?: Array<{ dataUrl: string; mimetype: string }>;
}

export class DefaultCartridge extends Cartridge {
  availableTools: Tool[] = [];
  conversation: Conversation = null;
  agentType: 'rfp_agent' | 'digital_se_agent' = 'digital_se_agent';

  private webSearchSites: string[];
  private settings: ResolvedSettings;
  private userId: number;
  private schema: string;
  private client: Client;
  private prompt: string;
  private text: string;
  private documentTypes: string[];
  private imageInfos?: Array<{ dataUrl: string; mimetype: string }>;

  private hasSalesforce: boolean = false;
  private hasHubspot: boolean = false;
  private hasGong: boolean = false;
  private hasStructuredData: boolean = false;
  private enableAccessSequence: boolean = false;
  private internalSearchToolName: string = BrainSearch.tool_name;

  constructor() {
    super();
  }

  getAgentType(): 'rfp_agent' | 'digital_se_agent' {
    return this.agentType;
  }

  async init(args: DefaultCartridgeArgs): Promise<void> {
    this.webSearchSites = args.settings.agentWebSearchAllowlist
      .split('\n')
      .map(extractDomainName)
      .filter(Boolean);
    this.settings = args.settings;
    this.userId = args.userId;
    this.schema = args.schema;
    this.client = args.client;
    this.text = args.message;
    this.conversation = args.conversation;
    this.documentTypes = args.documentTypes;
    this.imageInfos = args.imageInfos || [];

    // Check if enable_access_sequence setting is enabled
    this.enableAccessSequence = this.settings.enableAccessSequence;

    // Set the internal search tool name based on the setting
    this.internalSearchToolName = this.enableAccessSequence
      ? TieredContextSearchTool.tool_name
      : BrainSearch.tool_name;

    // Choose the appropriate search tool based on the setting
    const searchTool = this.enableAccessSequence
      ? TieredContextSearchTool.getJsonSchema(
          this.documentTypes.filter((dt) => dt !== 'Spreadsheet'),
          true, // enableExtraFilters
        )
      : BrainSearch.getJsonSchema(
          this.documentTypes.filter((dt) => dt !== 'Spreadsheet'),
        );

    this.availableTools = [
      searchTool,
      FindDocuments.getJsonSchema(this.documentTypes),
      SearchInDocuments.getJsonSchema(),
      SummarizeDocuments.getJsonSchema(),
      WebSearch.getJsonSchema(this.webSearchSites),
      FetchUrl.getJsonSchema(),
      LearnFact.getJsonSchema(),
      ExtractTemporal.getJsonSchema(),
      CreateImageFromPromptTool.getJsonSchema(),
      CreatePptxFromImageTool.getJsonSchema(),
      CreateShareableDeckTool.getJsonSchema(),
      GenerateEphemeralUI.getJsonSchema({}),
      ProcessUISubmission.getJsonSchema({}),
      GenerateResultUI.getJsonSchema({}),
    ];

    if (args.salesforceConn) {
      this.hasSalesforce = true;
      this.availableTools.push(SalesforceQuery.getJsonSchema());
      this.availableTools.push(SalesforceDescribe.getJsonSchema());
      this.availableTools.push(SalesforceWrite.getJsonSchema());
    }

    if (args.hubspotClient) {
      this.hasHubspot = true;
      this.availableTools.push(HubSpotQuery.getJsonSchema());
      this.availableTools.push(HubSpotDescribe.getJsonSchema());
      this.availableTools.push(HubSpotWrite.getJsonSchema());
    }

    if (await hasStructuredData(args.schema)) {
      this.hasStructuredData = true;
      this.availableTools.push(StructuredQuery.getJsonSchema());
      this.availableTools.push(SearchCustomTables.getJsonSchema());
      this.availableTools.push(CustomTableDescribe.getJsonSchema());
    }

    const gongClient = await initializeGongClient(this.schema);
    if (gongClient && gongClient.isExtensiveApiEnabled && this.hasSalesforce) {
      this.hasGong = true;

      this.availableTools.push(
        InvokeMeetingPrep.getJsonSchema(['meeting_prep']),
      );

      this.availableTools.push(GetMeetingSummaries.getJsonSchema());
    }

    this.prompt = await this.compilePrompt();
  }

  async newConversationInit() {
    // Initialize eager RAG for first message
    try {
      const text = this.text;
      const imageInfos = this.imageInfos;

      const query = this.stripUserIds(text);

      let searchResultsStringified: string;
      let searchToolCallContexts: any[];
      let toolName: string;
      let toolArguments: string;

      if (this.enableAccessSequence) {
        // Use TieredContextSearchTool with tier 1 for eager RAG
        const tieredSearchToolCall = new TieredContextSearchTool(
          {
            schema: this.schema,
            clientId: this.client.id as ClientId,
            clientName: this.client.name,
            conversationId: this.conversation.conversationId(),
            metadataFilters: this.conversation.metadataFilters,
            tribbleUserId: this.userId as UserId,
            settings: this.settings,
            agentType: this.agentType,
          },
          {
            query: query,
            imageInfos: imageInfos,
            tier: 1, // Use tier 1 for eager RAG
          },
        );
        searchResultsStringified = await tieredSearchToolCall.performCall();
        searchToolCallContexts = tieredSearchToolCall.getContexts();
        toolName = TieredContextSearchTool.tool_name;
        toolArguments = `{\n"query": ${JSON.stringify(query)},\n"tier": 1${
          imageInfos?.length
            ? ',\n"imageInfos": ' +
              JSON.stringify(
                imageInfos.map((i) => ({
                  dataUrl: i.dataUrl.substring(0, 30) + '...',
                  mimetype: i.mimetype,
                })),
              )
            : ''
        }\n}`;
      } else {
        // Use regular BrainSearch
        const brainSearchToolCall = new BrainSearch(
          {
            schema: this.schema,
            clientId: this.client.id as ClientId,
            clientName: this.client.name,
            conversationId: this.conversation.conversationId(),
            metadataFilters: this.conversation.metadataFilters,
            tribbleUserId: this.userId as UserId,
            settings: this.settings,
          },
          {
            query: query,
            imageInfos: imageInfos,
          },
        );
        searchResultsStringified = await brainSearchToolCall.performCall();
        searchToolCallContexts = brainSearchToolCall.getContexts();
        toolName = BrainSearch.tool_name;
        toolArguments = `{\n"query": ${JSON.stringify(query)}${
          imageInfos?.length
            ? ',\n"imageInfos": ' +
              JSON.stringify(
                imageInfos.map((i) => ({
                  dataUrl: i.dataUrl.substring(0, 30) + '...',
                  mimetype: i.mimetype,
                })),
              )
            : ''
        }\n}`;
      }

      if (searchResultsStringified) {
        this.prompt += `\nInitial Contexts:\n${searchResultsStringified}`;

        // Search structured tables
        if (this.hasStructuredData) {
          const EAGER_SIMILARITY_THRESHOLD = process.env
            .EAGER_TABLE_SIMILARITY_THRESHOLD
            ? parseInt(process.env.EAGER_TABLE_SIMILARITY_THRESHOLD)
            : 0.8;

          const eagerStructuredTables = (
            await CustomTableSearch({
              query,
              schema: this.schema,
            })
          )
            .filter((result) => result.similarity > EAGER_SIMILARITY_THRESHOLD)
            .map((result) => {
              return {
                table_name: result.table_name,
                description: result.description,
              };
            });

          if (eagerStructuredTables.length) {
            this.prompt += `\n\nThe following tables may contain useful information. Use ${CustomTableDescribe.tool_name} and ${StructuredQuery.tool_name} to query them:\n${JSON.stringify(eagerStructuredTables, null, 2)}`;
          }
        }
        await updateConversationSources(
          this.schema as ClientSchema,
          this.conversation.conversationId(),
          searchToolCallContexts,
        );

        // Add results to conversation history
        const eagerSearch = {
            role: 'assistant',
            content: null,
            tool_calls: [
              {
                id: 'call_ncc1701',
                type: 'function',
                function: {
                  name: toolName,
                  arguments: toolArguments,
                },
              },
            ],
          },
          eagerSearchResults = {
            role: 'tool',
            content: searchResultsStringified,
            tool_call_id: 'call_ncc1701',
          };

        await insertConversationDetail(
          this.schema,
          this.conversation.conversationId(),
          eagerSearch,
        );
        await insertConversationDetail(
          this.schema,
          this.conversation.conversationId(),
          eagerSearchResults,
        );
      }
    } catch (err) {
      console.error(`[chatResponder] eager RAG error: ${err.message}`);
      console.error(err.stack);
    }
  }

  private stripUserIds(text: string) {
    // Strip out @ mentioned User IDs enclosed in <@> e.g., <@UIDIDih39>
    const pattern = /<@(.*?)>/g;
    let stripped = text.replace(pattern, '');
    return stripped;
  }

  async getPrompt() {
    return this.prompt;
  }

  private async compilePrompt(): Promise<string> {
    const currentDate = new Date().toISOString().split('T')[0]; //YYYY-MM-DD
    let promptSource = this.settings.agenticConfig.promptTemplate;

    if (!promptSource) {
      promptSource = await this.getDefaultPrompt();
    }

    // Get the appropriate search tool description
    const searchToolDescription = this.enableAccessSequence
      ? TieredContextSearchTool.getPromptDescription({
          clientName: this.client.name,
        })
      : BrainSearch.getPromptDescription({ clientName: this.client.name });

    const mainToolDescription =
      'You have access to the following tools:\n\n' +
      [
        ExtractTemporal.getPromptDescription(),
        searchToolDescription,
        FindDocuments.getPromptDescription({
          documentTypes: this.documentTypes,
          clientName: this.client.name,
        }),
        SearchInDocuments.getPromptDescription({
          clientName: this.client.name,
        }),
        SummarizeDocuments.getPromptDescription(),
        WebSearch.getPromptDescription({
          sites: this.webSearchSites,
          clientName: this.client.name,
        }),
        CreateImageFromPromptTool.getPromptDescription(),
        CreatePptxFromImageTool.getPromptDescription(),
        CreateShareableDeckTool.getPromptDescription(),
        GenerateEphemeralUI.getPromptDescription({}),
        ProcessUISubmission.getPromptDescription({}),
        GenerateResultUI.getPromptDescription({}),
      ]
        .map((description, index) => `${index + 1}. ${description}`)
        .join('\n');

    const toolDescriptionStrings = [mainToolDescription];

    if (this.hasSalesforce) {
      toolDescriptionStrings.push(SalesforceQuery.getPromptDescription());
      toolDescriptionStrings.push(SalesforceWrite.getPromptDescription());
    }

    if (this.hasHubspot) {
      toolDescriptionStrings.push(HubSpotQuery.getPromptDescription());
      toolDescriptionStrings.push(HubSpotWrite.getPromptDescription());
    }

    if (this.hasStructuredData) {
      toolDescriptionStrings.push(StructuredQuery.getPromptDescription());
    }

    if (this.hasGong) {
      //Meeting prep is allowed!
      toolDescriptionStrings.push(InvokeMeetingPrep.getPromptDescription());
      toolDescriptionStrings.push(GetMeetingSummaries.getPromptDescription());
    }

    const toolDescriptions = toolDescriptionStrings.join('\n');

    const promptTemplate = Handlebars.compile(promptSource);

    const activeMetadataFilters = this.conversation.metadataFilters;

    const promptVars = {
      clientName: this.client.name,
      internalSearchToolName: this.internalSearchToolName,
      extractTemporalToolName: ExtractTemporal.tool_name,
      temporalSupportedTools: ExtractTemporal.getSupportedTools().join(', '),
      metadataFilters: activeMetadataFilters,
      multipleTypes: false,
      numTagTypes: 0,
      tagGroupNames: '',
      toolDescriptions,
      output_no_answer: constants.OUTPUT_NO_ANSWER,
      formattedMetadataFilters: '',
      summary: this.conversation.summary,
      currentDate,
      enableTieredSearch: this.enableAccessSequence,
      tieredSearchToolName: TieredContextSearchTool.tool_name,
    };

    if (activeMetadataFilters && activeMetadataFilters.length) {
      (promptVars.numTagTypes = new Set(
        activeMetadataFilters.map((mdf) => mdf.type_id),
      ).size),
        (promptVars.tagGroupNames = getTagTypeString(activeMetadataFilters));
      promptVars.formattedMetadataFilters = JSON.stringify(
        formatTagsForPrompt(activeMetadataFilters),
        null,
        2,
      );
    }
    return promptTemplate(promptVars);
  }

  private async getDefaultPrompt(): Promise<string> {
    const thisFile = fileURLToPath(import.meta.url);
    const dirname = path.dirname(thisFile);
    return fs.readFileSync(path.join(dirname, 'prompt.hbs'), 'utf8');
  }
}
