import { Tool } from '@tribble/chat-completion-service/client';
import Handlebars from 'handlebars';
import { Client } from '../../clients.ts';
import { Conversation } from '../../conversations/conversation.ts';
import { ResolvedSettings } from '../../resolved_settings.ts';
import { BidPacketSearch } from '../../tools/bid_packet_search.ts';
import { Cartridge } from '../cartridge.ts';

interface RfpPromptGenArgs {
  schema: string;
  userId: number;
  client: Client;
  settings: ResolvedSettings;
  conversation: Conversation;
  prompt: string;
}

export class RFPWriterPromptGenerator extends Cartridge {
  availableTools: Tool[] = [];
  conversation: Conversation = null;
  model =
    (process.env.RFP_WRITER_PROMPT_GENERATION_MODEL as any) ||
    ('o3-mini' as 'o3-mini');

  private client: Client;
  private raw_prompt: string;
  private prompt: string;

  constructor() {
    super();
  }

  async init(args: RfpPromptGenArgs): Promise<void> {
    this.client = args.client;
    this.conversation = args.conversation;
    this.raw_prompt = args.prompt;

    this.availableTools = [BidPacketSearch.getJsonSchema()];
    this.prompt = await this.compilePrompt();
  }

  async newConversationInit(_: any): Promise<void> {}

  async getPrompt(_: any): Promise<string> {
    return this.prompt;
  }

  private async compilePrompt(): Promise<string> {
    // Compile the prompt for the user

    const toolDescriptions = [BidPacketSearch.getPromptDescription()].join(' ');

    if (this.raw_prompt.includes('{{{toolDescriptions}}}') === false) {
      this.raw_prompt += `\nYou have access to the following tools to help accomplish your task:\n{{{toolDescriptions}}}`;
    }
    const promptTemplate = Handlebars.compile(this.raw_prompt);
    return promptTemplate({
      toolDescriptions,
      clientName: this.client.name,
    });
  }
}
