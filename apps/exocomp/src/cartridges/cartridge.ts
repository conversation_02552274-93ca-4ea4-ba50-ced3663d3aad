import { ModelType } from '@tribble/cartridge-helper';
import { Tool } from '@tribble/chat-completion-service/client';
import { isReasoningModel } from '@tribble/common-utils';
import { ToolChoice } from '@tribble/types-shared';
import { Conversation } from '../conversations/conversation.ts';
import { ConversationMessage } from '../conversations/message.ts';

export abstract class Cartridge {
  availableTools: Tool[];
  disableParallelToolCalls: boolean = false;
  toolChoice?: ToolChoice;
  maxTokens?: number;
  model: ModelType;

  constructor() {}

  abstract init(_: any): Promise<void>;

  abstract newConversationInit(_: any): Promise<void>; // Let the cartridge do special things on new convo (like eager rag!)

  abstract getPrompt(_: any): Promise<string>;

  /**
   * Returns the agent type identifier for this cartridge.
   * Used for filtering access sequences in tiered context search.
   * Override in subclasses to provide specific agent types.
   */
  getAgentType(): string | undefined {
    return undefined; // Default: no specific agent type
  }

  async midConversationInit(args: {
    /**
     * This is called during cartridge handoff. I.e. we are midloop and
     * need to hotswap the system message and tools.
     */
    conversationId: number;
    messages: ConversationMessage[];
    conversation: Conversation;
    cartridgeArgs: Record<string, any>;
  }): Promise<{
    messages: ConversationMessage[];
    systemMessage: string;
    tools: Tool[];
    model?: string;
  }> {
    await this.init(args.cartridgeArgs);

    const superRole = isReasoningModel(this.model) ? 'developer' : 'system';
    let processedMessages = args.messages.map((m) => {
      return {
        ...m,
        role: m.role === 'system' ? superRole : m.role,
      };
    });

    const newSystemMessage = await this.getPrompt(null);

    //Add on the new system message:

    if (newSystemMessage) {
      processedMessages = processedMessages.filter((m) => m.role !== superRole);
      processedMessages.unshift({
        role: superRole,
        content: newSystemMessage,
      });
    }

    return {
      messages: processedMessages,
      systemMessage: newSystemMessage,
      tools: this.getAvailableTools(),
      model: this.model,
    };
  }

  getAvailableTools(): Tool[] {
    return this.availableTools;
  }
}
