import {
  CustomCartridge as CustomCartridgeRecord,
  CustomTool,
} from '@tribble/cartridge-helper';
import { Tool } from '@tribble/chat-completion-service/client';
import { initializeGongClient } from '@tribble/gong-service';
import { ClientSchema } from '@tribble/tribble-db';
import { ClientId, UserId } from '@tribble/tribble-db/types';
import { Connection as SalesforceConnection } from 'jsforce';
import { CustomTableSearch } from '../../brain.ts';
import { Client } from '../../clients.ts';
import { extractDomainName } from '../../conversation_service.ts';
import {
  Conversation,
  updateConversationSources,
} from '../../conversations/conversation.ts';
import { insertConversationDetail } from '../../conversations/conversation_detail.ts';
import { ResolvedSettings } from '../../resolved_settings.ts';
import { hasStructuredData } from '../../structured_data.ts';
import {
  BrainSearch,
  CustomTableDescribe,
  SearchCustomTables,
  StructuredQuery,
  TieredContextSearchTool,
} from '../../tools/index.ts';
import { toolBox } from '../../tools/toolbox.ts';
import { Cartridge } from '../cartridge.ts';
import { CommonCartridgeArgs } from '../index.ts';

//This cartridge is fed by db records. So it's user defined. Different from its siblings.
export class CustomCartridge extends Cartridge {
  private eagerRagEnabled: boolean = false;
  private cartridgeRecord: CustomCartridgeRecord;
  private prompt: string;
  private schema: string;
  private settings: ResolvedSettings;
  private conversation: Conversation;
  private client: Client;
  private salesforceConn?: SalesforceConnection;
  private commonCartridgeArgs: CommonCartridgeArgs;

  private hasSalesforce: boolean = false;
  private hasGong: boolean = false;
  private hasStructuredData: boolean = false;
  private enableAccessSequence: boolean = false;
  availableTools: Tool[] = [];

  constructor(record: CustomCartridgeRecord) {
    super();
    this.prompt = record.prompt;
    this.model = record.model;
    this.toolChoice = record.toolChoice;
    this.cartridgeRecord = record;
    this.eagerRagEnabled = record.eagerRag;
  }

  async init(args: CommonCartridgeArgs) {
    this.schema = args.schema;
    this.settings = args.settings;
    this.conversation = args.conversation;
    this.client = args.client;
    this.salesforceConn = args.salesforceConn;
    this.commonCartridgeArgs = args;

    // Check if enable_access_sequence setting is enabled
    this.enableAccessSequence = this.settings.enableAccessSequence;

    if (args.salesforceConn) {
      this.hasSalesforce = true;
    }

    try {
      const gongClient = await initializeGongClient(this.schema);
      if (gongClient) {
        await gongClient.ensureValidToken(args.userId as UserId);
        if (gongClient.isExtensiveApiEnabled) {
          this.hasGong = true;
        }
      }
    } catch (gongError) {
      console.error('Error initializing Gong client:', gongError);
      this.hasGong = false;
    }

    //Tools!!
    this.availableTools = this.cartridgeRecord.tools
      .map((tool: CustomTool) => {
        const foundTool = toolBox[tool.name];
        if (!foundTool) {
          console.error(
            `Tool ${tool.name} not found in toolbox | `,
            this.conversation.id,
          );
          return;
        }

        if (tool.name === 'web_search') {
          const webSearchSites = (this.settings.agentWebSearchAllowlist ?? '')
            .split('\n')
            .map(extractDomainName)
            .filter(Boolean);
          return foundTool.toolClass.getJsonSchema(webSearchSites);
        }

        if (Array.isArray(tool.jsonSchemaArgs)) {
          return foundTool.toolClass.getJsonSchema(...tool.jsonSchemaArgs);
        }
        return foundTool.toolClass.getJsonSchema(tool.jsonSchemaArgs);
      })
      .filter(Boolean);

    // Always add ephemeral UI tools for custom cartridges
    const ephemeralUITools = [
      toolBox['generate_ephemeral_ui']?.toolClass.getJsonSchema({}),
      toolBox['process_ui_submission']?.toolClass.getJsonSchema({}),
      toolBox['generate_result_ui']?.toolClass.getJsonSchema({}),
    ].filter(Boolean);

    this.availableTools = [...this.availableTools, ...ephemeralUITools];

    //Filter out unusable tools:
    if (!this.hasSalesforce) {
      this.availableTools = this.availableTools.filter(
        (t) => t.function.name.toLowerCase().includes('salesforce') === false,
      );
    }

    if (!this.hasGong) {
      this.availableTools = this.availableTools.filter(
        (t) =>
          t.function.name.toLowerCase().includes('get_meeting_summary') ===
          false,
      );
    }

    const toolsIncludesStructuredQuery = this.availableTools.some(
      (t) => t.function.name === StructuredQuery.tool_name,
    );
    const clientHasStructuredData = await hasStructuredData(this.schema);

    if (toolsIncludesStructuredQuery && clientHasStructuredData) {
      this.hasStructuredData = true;
    }

    if (!clientHasStructuredData) {
      this.hasStructuredData = false;
      // Remove structured query and related tools if no structured data is available
      this.availableTools = this.availableTools.filter(
        (t) =>
          [
            StructuredQuery.tool_name,
            SearchCustomTables.tool_name,
            CustomTableDescribe.tool_name,
          ].includes(t.function.name) === false,
      );
    }

    //TODO: Tool descriptions?
  }
  async newConversationInit(_: any) {
    if (this.eagerRagEnabled) {
      try {
        const text = this.commonCartridgeArgs.message;
        const imageInfos = this.commonCartridgeArgs.imageInfos;

        const query = this.stripUserIds(text);

        let searchResultsStringified: string;
        let searchToolCallContexts: any[];
        let toolName: string;
        let toolArguments: string;

        if (this.enableAccessSequence) {
          // Use TieredContextSearchTool with tier 1 for eager RAG
          const tieredSearchToolCall = new TieredContextSearchTool(
            {
              schema: this.schema,
              clientId: this.client.id as ClientId,
              clientName: this.client.name,
              conversationId: this.conversation.conversationId(),
              metadataFilters: this.conversation.metadataFilters,
              tribbleUserId: this.commonCartridgeArgs.userId as UserId,
              settings: this.settings,
            },
            {
              query: query,
              imageInfos: imageInfos,
              tier: 1, // Use tier 1 for eager RAG
            },
          );
          searchResultsStringified = await tieredSearchToolCall.performCall();
          searchToolCallContexts = tieredSearchToolCall.getContexts();
          toolName = TieredContextSearchTool.tool_name;
          toolArguments = `{\n"query": ${JSON.stringify(query)},\n"tier": 1${
            imageInfos?.length
              ? ',\n"imageInfos": ' +
                JSON.stringify(
                  imageInfos.map((i) => ({
                    dataUrl: i.dataUrl.substring(0, 30) + '...',
                    mimetype: i.mimetype,
                  })),
                )
              : ''
          }\n}`;
        } else {
          // Use regular BrainSearch
          const brainSearchToolCall = new BrainSearch(
            {
              schema: this.schema,
              clientId: this.client.id as ClientId,
              clientName: this.client.name,
              conversationId: this.conversation.conversationId(),
              metadataFilters: this.conversation.metadataFilters,
              tribbleUserId: this.commonCartridgeArgs.userId as UserId,
              settings: this.settings,
            },
            {
              query: query,
              imageInfos: imageInfos,
            },
          );
          searchResultsStringified = await brainSearchToolCall.performCall();
          searchToolCallContexts = brainSearchToolCall.getContexts();
          toolName = BrainSearch.tool_name;
          toolArguments = `{\n"query": ${JSON.stringify(query)}${
            imageInfos?.length
              ? ',\n"imageInfos": ' +
                JSON.stringify(
                  imageInfos.map((i) => ({
                    dataUrl: i.dataUrl.substring(0, 30) + '...',
                    mimetype: i.mimetype,
                  })),
                )
              : ''
          }\n}`;
        }

        if (searchResultsStringified) {
          this.prompt += `\nInitial Contexts:\n${searchResultsStringified}`;

          // Search structured tables
          if (this.hasStructuredData) {
            const EAGER_SIMILARITY_THRESHOLD = process.env
              .EAGER_TABLE_SIMILARITY_THRESHOLD
              ? parseInt(process.env.EAGER_TABLE_SIMILARITY_THRESHOLD)
              : 0.8;

            const eagerStructuredTables = (
              await CustomTableSearch({
                query,
                schema: this.schema,
              })
            )
              .filter(
                (result) => result.similarity > EAGER_SIMILARITY_THRESHOLD,
              )
              .map((result) => {
                return {
                  table_name: result.table_name,
                  description: result.description,
                };
              });

            if (eagerStructuredTables.length) {
              this.prompt += `\n\nThe following tables may contain useful information. Use ${CustomTableDescribe.tool_name} and ${StructuredQuery.tool_name} to query them:\n${JSON.stringify(eagerStructuredTables, null, 2)}`;
            }
          }
          await updateConversationSources(
            this.schema as ClientSchema,
            this.conversation.conversationId(),
            searchToolCallContexts,
          );

          // Add results to conversation history
          const eagerSearch = {
              role: 'assistant',
              content: null,
              tool_calls: [
                {
                  id: 'call_ncc1701',
                  type: 'function',
                  function: {
                    name: toolName,
                    arguments: toolArguments,
                  },
                },
              ],
            },
            eagerSearchResults = {
              role: 'tool',
              content: searchResultsStringified,
              tool_call_id: 'call_ncc1701',
            };

          await insertConversationDetail(
            this.schema,
            this.conversation.conversationId(),
            eagerSearch,
          );
          await insertConversationDetail(
            this.schema,
            this.conversation.conversationId(),
            eagerSearchResults,
          );
        }
      } catch (err) {
        console.error('Error during eager RAG initialization:', err);
      }
    }
  }
  async getPrompt(_: any) {
    return this.prompt;
  }

  private stripUserIds(text: string) {
    // Strip out @ mentioned User IDs enclosed in <@> e.g., <@UIDIDih39>
    const pattern = /<@(.*?)>/g;
    let stripped = text.replace(pattern, '');
    return stripped;
  }
}
