import { ClientSchema } from '@tribble/tribble-db';
import { getDB } from '@tribble/tribble-db/db_ops';
import { MetadataFilterId } from '@tribble/tribble-db/types';

export type MetadataFilterType = {
  id: number;
  name: string;
  distinct_answer: boolean;
  is_content_access: boolean;
  is_verbatim: boolean;
};

export type MetadataFilterValue = {
  id: number;
  type_id: number;
  value: string;
  is_active: boolean;
  type: MetadataFilterType;
  synonyms: string;
  description: string;
};

export async function getMetadataFilterRecords(
  schema: ClientSchema,
  metadata_filter_ids: MetadataFilterId[] = [],
): Promise<MetadataFilterValue[]> {
  const db = await getDB();
  const query = db
    .withSchema(schema)
    .selectFrom('metadata_filter as mdf')
    .innerJoin('metadata_filter_type as mdf_type', 'mdf.type_id', 'mdf_type.id')
    .$if(metadata_filter_ids.length > 0, (qb) =>
      qb.where('mdf.id', 'in', metadata_filter_ids),
    )
    .select([
      'mdf.id',
      'mdf.type_id',
      'mdf.value',
      'mdf.is_active',
      'mdf_type.name as type_name',
      'mdf_type.is_content_access as is_content_access',
      'mdf_type.distinct_answer as distinct_answer',
      'mdf_type.is_verbatim as is_verbatim',
      'mdf.synonyms',
      'mdf.description',
    ]);

  const result = await query.execute();

  return result.map((mdf) => {
    return {
      id: mdf.id,
      type_id: mdf.type_id,
      value: mdf.value,
      is_active: mdf.is_active,
      type: {
        id: mdf.type_id,
        name: mdf.type_name,
        distinct_answer: mdf.distinct_answer,
        is_content_access: mdf.is_content_access,
        is_verbatim: mdf.is_verbatim,
      },
      synonyms: mdf.synonyms,
      description: mdf.description,
    };
  });
}

interface PlainTag {
  //Just another tag model
  id: number;
  name: string;
  type: string; //metadata_filter_type name
  description?: string;
  synonyms?: string;
}

//Strip out extraneous stuff
export function formatTagsForPrompt(
  metadataFilters: MetadataFilterValue[],
): PlainTag[] {
  const tags = metadataFilters.map((mdf) => {
    let tag: PlainTag = {
      id: mdf.id,
      type: mdf.type.name,
      name: mdf.value,
    };

    if (mdf.description) {
      tag.description = mdf.description;
    }

    if (mdf.synonyms) {
      tag.synonyms = mdf.synonyms;
    }

    return tag;
  });
  //sort by type:
  tags.sort((a, b) => {
    if (a.type > b.type) return 1;
    if (a.type < b.type) return -1;
    return 0;
  });
  return tags;
}

//Get a plain language list of types we can use in a prompt.
//e.g. 'Vertical'
//e.g. 'Vertical or Product'
//e.g. 'Vertical, Type, or Product'
export function getTagTypeString(tags: MetadataFilterValue[]): string {
  let types = tags.map((t) => t.type.name);
  let unique = Array.from(new Set(types));

  if (unique.length > 1) {
    unique = prependToLastValueStringInArray(unique, 'or ');
  }
  let seperator = ' ';
  if (unique.length > 2) seperator = ', ';

  return unique.join(seperator);
}

export function prependToLastValueStringInArray(
  arr: string[],
  valueToPrepend: string,
): string[] {
  if (arr.length === 0) {
    return arr;
  }
  const lastIndex = arr.length - 1;
  arr[lastIndex] = valueToPrepend + arr[lastIndex];
  return arr;
}
