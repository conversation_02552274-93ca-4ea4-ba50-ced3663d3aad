import { generateEmbedding as generateEmbeddingFromChatCompletionService } from '@tribble/chat-completion-service';
import { ClientUtils, CONSTANTS } from '@tribble/common-utils';
import { DB, query, TRX } from '@tribble/tribble-db/db_ops';
import { ConversationContext } from './conversations/conversation_context.ts';
export type ContentType = 'DOC' | 'RFP' | 'URL' | 'SLACK' | 'LEARNED';

type EmbeddingId = string & { __brand: 'EmbeddingId' };

export interface Embedding {
  id: string;
  document_id: string;
  index: number;
  content: string;
  content_meta: any;
  content_type: string;
  use_for_generation: boolean;
  metadata_filter: any;
  uuid?: string;
  job_id?: number;
  file_name?: string;
  created_date?: Date;
  status?: string;
  privacy?: string;
  label?: string;
  document_type_id?: number;
  expiry_date?: Date;
}

export async function getEmbeddingsByIds(
  dbOrTrx: DB | TRX,
  embeddingIds: EmbeddingId[],
  includeDocumentDetails: boolean = false,
): Promise<Embedding[]> {
  let dbQuery = dbOrTrx
    .selectFrom('embedding as e')
    .$if(includeDocumentDetails, (qb) =>
      qb
        .innerJoin('document as d', 'e.document_id', 'd.id')
        .select([
          'd.uuid',
          'd.job_id',
          'd.file_name',
          'd.created_date',
          'd.status',
          'd.privacy',
          'd.label',
          'd.document_type_id',
          'd.expiry_date',
        ]),
    )
    .select([
      'e.id',
      'e.document_id',
      'e.index',
      'e.content',
      'e.content_meta',
      'e.content_type',
      'e.use_for_generation',
      'e.metadata_filter',
    ])
    .where((eb) => eb('e.id', '=', eb.fn.any(eb.val(embeddingIds))));

  return dbQuery.execute().catch((err) => {
    console.error(`[getEmbeddingsByIds] ${err}`);
    throw err;
  });
}

export async function generateEmbeddings(
  text: string,
): Promise<{ embedding: number[]; origin: string } | null> {
  try {
    const embedding = await generateEmbeddingFromChatCompletionService(text);
    return {
      embedding: embedding.embedding,
      origin: embedding.provider,
    };
  } catch (err) {
    console.error(`[generateEmbeddings] failed ${err.message}`);
    return null;
  }
}

type WithoutNullableKeys<Type> = {
  [Key in keyof Type]-?: NonNullable<Type[Key]> extends object
    ? WithoutNullableKeys<NonNullable<Type[Key]>>
    : NonNullable<Type[Key]>;
};

export async function selectValidEmbeddings(
  schema: string,
  candidateIds: number[],
): Promise<
  { id: number; content_type: ContentType; privacy: 'public' | 'private' }[]
> {
  const queryString = `
    SELECT 
      e.id,
      e.content_type,
      d.privacy
    FROM ${schema}.embedding e
    JOIN ${schema}.document d ON e.document_id = d.id
    WHERE e.id = ANY($1::int[])
  `;
  try {
    const result = await query(queryString, [candidateIds]);
    if (result.rows && result.rows.length) {
      return result.rows;
    }
    return [];
  } catch (err) {
    console.error(`[selectValidEmbeddings] ${err.message}`);
    return [];
  }
}

async function getDetailedEmbeddingsByIds(
  schema: string,
  embeddingIds: number[],
): Promise<any[]> {
  const queryString = `
    SELECT 
        content as text,
        content_meta as metadata,
        d.file_name,
        d.label,
        d.document_type_id,
        d.privacy,
        CASE
          -- Hacky for now.
          -- Blank Highspot for now: need to be able to construct a real URL (will be in a later PR)
          WHEN d.id = highspot.document_id THEN ''
          WHEN d.id = zendesk.document_id THEN zendesk.url
          ELSE d.source_url
        END AS source_url,
        e.id as embedding_id,
        e.content_type,
        d.use_for_generation,
        e.document_id,
        e.use_for_generation,
        e.origin,
        e.metadata_filter,
        COALESCE(e.modified_date, d.created_date) as created_date,
        LOWER(COALESCE(metadata_json->>'webCategory', 'internal')) as applicability,
        COALESCE(metadata_json->>'webCategoryLabel', '') as applicability_label,
        CASE
          -- Hacky for now. But, no way to tell a Zendesk URL vs. a generic URL
          WHEN d.id = highspot.document_id THEN 'highspot'
          WHEN d.id = zendesk.document_id THEN 'zendesk'
          ELSE COALESCE(metadata_json->>'externalDocSource', '')
        END AS external_doc_source,
        CASE
          -- Hacky for now. Mainly needed for Highspot Item Deeplink
          WHEN d.id = highspot.document_id THEN highspot.item_id::text
          WHEN d.id = zendesk.document_id THEN zendesk.zendesk_id::text
          ELSE COALESCE(metadata_json->>'externalDocSourceId', '')
        END AS external_doc_source_id,
        d.deleted_date,
        e.index as snippet_index,
        e.content_type,
        COALESCE(dt.name, 'Document') as document_type_name,
        COALESCE(dt_priority.multiplier, 1) as multiplier

      FROM    ${schema}.embedding e
      JOIN    ${schema}.document d ON e.document_id = d.id

      LEFT JOIN ${schema}.document_type dt ON d.document_type_id = dt.id
      LEFT JOIN ${schema}.document_type_priority dt_priority ON dt.priority_id = dt_priority.id

      -- See: brain.ts::getDocumentChain()
      LEFT JOIN ${schema}.highspot_asset highspot ON d.id = highspot.document_id
      LEFT JOIN ${schema}.zendesk_asset zendesk ON d.id = zendesk.document_id        

      WHERE   (d.use_for_generation IS NULL OR d.use_for_generation = true)
      AND     e.use_for_generation = true
      AND     e.origin = '${process.env.LLM_SYSTEM ?? 'AZURE'}'
      AND     d.deleted_date is NULL
      AND     LENGTH(TRIM(content)) > 0
      AND     e.id = ANY($1::int[])
  `;

  const result = await query(queryString, [embeddingIds]);
  return result?.rows || [];
}

export async function getEmbeddingsAsConversationContext(
  schema: string,
  embeddingIds: number[],
): Promise<ConversationContext[]> {
  const rows = await getDetailedEmbeddingsByIds(schema, embeddingIds);

  if (rows.length) {
    // Need special processing to handle source_url for Highspot
    // (See the /knowledge/source/:id route)
    const containsHighspotResult = rows.some(
      (row: any) => row.external_doc_source === 'highspot',
    );

    const highspotBaseUrl = containsHighspotResult
      ? await ClientUtils.getClientSetting(
          schema,
          CONSTANTS.SETTING_CLIENT_HIGHSPOT_BASE_URL,
        )
      : '';

    return rows.map((row: any) => {
      if (row.external_doc_source === 'highspot') {
        row.source_url = `${highspotBaseUrl}/items/${row.external_doc_source_id}`;
      }
      return new ConversationContext(row);
    });
  }
  return [];
}

export async function getEmbeddingsAsSourceFormat(
  schema: string,
  embeddingIds: number[],
): Promise<Record<string, any>[]> {
  if (!embeddingIds?.length) {
    return [];
  }
  const rows = await getDetailedEmbeddingsByIds(schema, embeddingIds);
  const sourceFormat = rows.map((row) => {
    const url_file_name = row.file_name.startsWith('https://');
    const out = {
      id: row.embedding_id,
      text: row.text,
      file_name:
        url_file_name || row.content_type === 'LEARNED'
          ? row.label
          : row.file_name,
    } as any;
    if (row.external_doc_source?.length > 0 && row.source_url) {
      out.url = row.source_url;
    } else if (url_file_name) {
      out.url = row.file_name;
    } else {
      out.url = `${process.env.APP_PATH}/sources/source/${row.document_id}`;
      if ((row.metadata.page_numbers?.length ?? 0) === 0) {
        out.url += `/fact/${row.embedding_id}`;
      } else {
        out.url += `?page=${row.metadata.page_numbers[0]}`;
      }
    }

    if (row.source_url) {
      out.document_url = row.source_url;
    }

    if (row.metadata.page_numbers) {
      out.reference = `p. ${row.metadata.page_numbers.join(', ')}`;
    } else if (row.metadata.row_index) {
      out.reference = `row ${row.metadata.row_index}`;
    }
    if (row.content_type === 'SLACK') {
      out.file_name = (row.label.startsWith('#') ? '' : '#') + row.label;
      if (row.metadata.slack_link) out.url = row.metadata.slack_link;
      out.date = row.metadata.date;
      out.reference = `Slack message${
        row.metadata.date ? ' from ' + row.metadata.date : ''
      }`;
    } else if (row.created_date) {
      try {
        const date = new Date(row.created_date);
        out.date = date.toLocaleString();
        if (!out.reference) {
          out.reference = date.toLocaleString();
        }
      } catch (e) {
        console.warn('Error parsing date', e.message);
      }
    }

    if (row.table_json) {
      out.table_json = row.table_json;
    }

    return out;
  });

  return sourceFormat;
}
