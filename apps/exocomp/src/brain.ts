import { ClientUtils, CONSTANTS, ContentUtils } from '@tribble/common-utils';
import { DB, getDB, query, TRX } from '@tribble/tribble-db/db_ops';
import { IntegrationSystemId } from '@tribble/tribble-db/tribble/IntegrationSystem';
import { ConversationId } from '@tribble/tribble-db/types';
import { sql } from 'kysely';
import {
  ContentType,
  ConversationContext,
} from './conversations/conversation_context.ts';
import { generateEmbeddings } from './embeddings.ts';
import { MetadataFilterValue } from './metadata_filters.ts';
import { queryTableCatalog, TableCatalogResult } from './structured_data.ts';
import { TimeFilter } from './tools/extract_temporal.ts';

export const constants = CONSTANTS;

const SlackChannelIdRegex = /[CGD][A-Z0-9]{8,}/; // Regex to match Slack Channel IDs
const UrlRegex = /^(https?:\/\/)?([\w\-]+(\.[\w\-]+)+)(:\d+)?(\/[^\s]*)?$/i;

export interface SearchParams {
  query: string;
  schema: string;
  strictMode: boolean;
  metadataFilters?: MetadataFilterValue[];
  allowSlackRag?: boolean;
  queryEmbedding?: number[];
  includeVerbatim?: boolean;
  timeFilter?: TimeFilter;
  allowGongRag?: boolean;
  conversationId?: ConversationId;
}

enum SourcePlatformEnum {
  DOC_LEARNED = 'doc_learned',
  URL = 'url',
  GONG = 'gong',
  RFP = 'rfp',
  SLACK = 'slack',
}

export const ContextSearch = async (
  params: SearchParams,
): Promise<ConversationContext[]> => {
  let { queryEmbedding } = params;
  if (!queryEmbedding) {
    const embeddingsResult = await generateEmbeddings(params.query);
    if (!embeddingsResult || embeddingsResult.embedding.length === 0) {
      return [];
    }
    queryEmbedding = embeddingsResult.embedding;
  }

  // No need for this verbatimClause check if the client doesn't have any verbatim MDF types at all
  const verbatimMdfTypes = await query(`
    SELECT *
    FROM ${params.schema}.${constants.TABLE_METADATA_FILTER_TYPE}
    WHERE is_verbatim = true
  `);

  const hasVerbatimMdfTypes =
    verbatimMdfTypes?.rows && verbatimMdfTypes.rows.length > 0;

  const contextGetters: Record<
    SourcePlatformEnum,
    Promise<ConversationContext[]>
  > = {
    [SourcePlatformEnum.DOC_LEARNED]: getRelatedContexts(
      params.schema,
      queryEmbedding,
      params.strictMode,
      params.metadataFilters || [],
      ['DOC', 'LEARNED'],
      [],
      params.includeVerbatim || !hasVerbatimMdfTypes,
    ),
    [SourcePlatformEnum.URL]: getRelatedContexts(
      params.schema,
      queryEmbedding,
      params.strictMode,
      params.metadataFilters || [],
      ['URL'],
      [],
      params.includeVerbatim || !hasVerbatimMdfTypes,
    ),
    [SourcePlatformEnum.RFP]: getRelatedContexts(
      params.schema,
      queryEmbedding,
      params.strictMode,
      params.metadataFilters || [],
      ['RFP'],
      [],
      params.includeVerbatim || !hasVerbatimMdfTypes,
      params.timeFilter,
    ),
    [SourcePlatformEnum.GONG]: params.allowGongRag
      ? getRelatedContexts(
          params.schema,
          queryEmbedding,
          params.strictMode,
          params.metadataFilters || [],
          ['GONG'],
          [],
          params.includeVerbatim || !hasVerbatimMdfTypes,
          params.timeFilter,
        )
      : Promise.resolve([]),
    [SourcePlatformEnum.SLACK]: params.allowSlackRag
      ? getRelatedContexts(
          params.schema,
          queryEmbedding,
          params.strictMode,
          params.metadataFilters || [],
          ['SLACK'],
          ['SLACK'], //Overrides useForGeneration

          // Note: why does this check original not have params.includeVerbatim?
          // As such, just adding hasVerbatimMdfTypes check only.
          !hasVerbatimMdfTypes,
          params.timeFilter,
        )
      : Promise.resolve([]),
  };

  const excludedPlatforms = await getExcludedPlatforms(
    params.schema,
    params.conversationId,
  );

  const filteredContextGetters = Object.entries(contextGetters).map(
    ([source_platform, contextGetter]) =>
      excludedPlatforms?.some(
        (excludedPlatform) =>
          excludedPlatform.toLowerCase() === source_platform.toLowerCase(),
      )
        ? Promise.resolve([])
        : contextGetter,
  );

  const contexts = await Promise.all(filteredContextGetters);

  return contexts.flat().sort((a, b) => b.similarity - a.similarity);
};

interface CustomTableSearchArgs {
  query: string;
  schema: string;
  queryEmbedding?: number[];
}
export const CustomTableSearch = async (
  params: CustomTableSearchArgs,
): Promise<TableCatalogResult[]> => {
  let { queryEmbedding } = params;
  if (!queryEmbedding) {
    const embeddingsResult = await generateEmbeddings(params.query);
    if (!embeddingsResult || embeddingsResult.embedding.length === 0) {
      return [];
    }
    queryEmbedding = embeddingsResult.embedding;
  }

  return await queryTableCatalog(params.schema, queryEmbedding);
};

/**
 * For a given embedding fetch the top related docs from the embedding table.
 */
export async function getRelatedContexts(
  schema: string,
  embedding: number[],
  strictMode: boolean,
  metadata_filters: MetadataFilterValue[] = [],
  contentType?: ContentType[],
  contentTypeOverrides: ContentType[] = [], //This is a list of content types that should be included in the search even if they are not set to use_for_generation
  includeVerbatim: boolean = false,
  timeFilter?: TimeFilter,
): Promise<ConversationContext[]> {
  //Metadata handling
  let metadataFilterClause = '';
  const metadataIsBlank = metadata_filters.length == 0;

  if (!metadataIsBlank) {
    metadataFilterClause = _metadataFilterClause(metadata_filters, strictMode);
  }

  let contentTypeClause = '';

  if (contentType && contentType.length > 0) {
    contentTypeClause = `AND e.content_type = ANY(ARRAY['${contentType.join("','")}'])`;
  }

  const verbatimClause = includeVerbatim
    ? ''
    : `AND NOT EXISTS (
        SELECT 1
        FROM metadata_filter_keys mft
        WHERE e.metadata_filter ? mft.key
      )`;

  const timeFilterClause = generateTimeFilterClause(timeFilter);

  const embeddingString = JSON.stringify(embedding);
  const maxDistanceThreshold = 0.5; //Todo make this configurable

  const verbatimMdfCTE = includeVerbatim
    ? ''
    : `WITH metadata_filter_keys AS (
      -- Pre-materialize the verbatim metadata filter types (executed once)
      SELECT id::text AS key
      FROM ${schema}.${constants.TABLE_METADATA_FILTER_TYPE}
      WHERE is_verbatim = true
    ) 
  `;

  const EmbeddingCTE =
    (verbatimMdfCTE ? ', ' : 'WITH ') +
    `EmbeddingsFiltered AS NOT MATERIALIZED (
      SELECT e.id
      FROM ${schema}.${constants.TABLE_EMBEDDING} e
      WHERE e.origin = '${process.env.LLM_SYSTEM ?? 'AZURE'}'
      ${contentTypeClause}
      AND (e.use_for_generation = true ${contentTypeOverrides.length > 0 ? `OR e.content_type = ANY(ARRAY['${contentTypeOverrides.join("','")}'])` : ''})
      AND LENGTH(TRIM(e.content)) > 0 
    ),
  `;

  // Need to wrap in a faux transaction in order to set the probes parameter.
  const queryString = `
    BEGIN;
    SET LOCAL ivfflat.probes = 10;

    ${verbatimMdfCTE}
    ${EmbeddingCTE}
    context_data AS (
      SELECT
        -- New Agent Test
        content as text,
        content_meta as metadata,
        d.file_name,
        d.label,
        d.document_type_id,
        d.privacy,
        CASE
          -- Hacky for now.
          -- Blank Highspot for now: need to be able to construct a real URL (will be in a later PR)
          WHEN d.id = highspot.document_id THEN ''
          WHEN d.id = zendesk.document_id THEN zendesk.url
          ELSE d.source_url
        END AS source_url,
        e.id as embedding_id,
        e.content_type,
        d.use_for_generation,
        e.document_id,
        e.use_for_generation,
        e.origin,
        e.metadata_filter,
        -- 'created_date' should ideally be the date the original document should represent. Therefore, this order of preference:
        --    1. e.source_modified_date tends to be the most true to the original date of the document
        --    2. e.source_created_date tends to be the date of the document as it was uploaded to the source system
        --    3. e.modified_date is the date of the last modification to the document in Tribble
        --    4. d.created_date is the date of the document as it was created in Tribble
        COALESCE(e.source_modified_date, e.source_created_date, e.modified_date, d.created_date) as created_date,
        LOWER(COALESCE(metadata_json->>'webCategory', 'internal')) as applicability,
        COALESCE(metadata_json->>'webCategoryLabel', '') as applicability_label,
        CASE
          -- Hacky for now. But, no way to tell a Zendesk URL vs. a generic URL
          WHEN d.id = highspot.document_id THEN 'highspot'
          WHEN d.id = zendesk.document_id THEN 'zendesk'
          ELSE COALESCE(metadata_json->>'externalDocSource', '')
        END AS external_doc_source,
        CASE
          -- Hacky for now. Mainly needed for Highspot Item Deeplink
          WHEN d.id = highspot.document_id THEN highspot.item_id::text
          WHEN d.id = zendesk.document_id THEN zendesk.zendesk_id::text
          ELSE COALESCE(metadata_json->>'externalDocSourceId', '')
        END AS external_doc_source_id,
        d.deleted_date,
        e.index as snippet_index,
        e.content_type,
        COALESCE(dt.name, 'Document') as document_type_name,
        -- Document type priority not used
        -- COALESCE(dt_priority.multiplier, 1) as multiplier,
        1 as multiplier,
        (e.embedding <=> '${embeddingString}'::vector) as distance

      FROM    ${schema}.${constants.TABLE_EMBEDDING} e
      JOIN    ${schema}.${constants.TABLE_DOC} d ON e.document_id = d.id
      JOIN    EmbeddingsFiltered ef ON e.id = ef.id

      LEFT JOIN ${schema}.${constants.TABLE_DOCUMENT_TYPE} dt ON d.document_type_id = dt.id
      -- Document type priority not used
      -- LEFT JOIN ${schema}.${constants.TABLE_DOCUMENT_TYPE_PRIORITY} dt_priority ON dt.priority_id = dt_priority.id

      -- See: brain.ts::getDocumentChain()
      LEFT JOIN ${schema}.${constants.TABLE_HIGHSPOT_ASSET} highspot ON d.id = highspot.document_id
      LEFT JOIN ${schema}.${constants.TABLE_ZENDESK_ASSET} zendesk ON d.id = zendesk.document_id        

      WHERE   (
        d.use_for_generation IS NULL 
        OR d.use_for_generation = true 
        ${
          contentTypeOverrides.length > 0
            ? `OR e.content_type = ANY(ARRAY['${contentTypeOverrides.join("','")}'])`
            : ''
        }
      )
      AND d.deleted_date is NULL
      AND e.embedding <=> '${embeddingString}'::vector < ${maxDistanceThreshold}
      ${verbatimClause}
      ${metadataFilterClause}
      ${timeFilterClause}
    )
    SELECT * FROM context_data
      WHERE TRUE
      -- Warn: ensure clauses below has access to needed columns from the CTE above
      ${generateSortOrderClause(timeFilter?.sortOrder, ` ORDER BY distance ASC`, embeddingString)}
      LIMIT 7;
    COMMIT;
  `;

  // Because we wrap the above in a transaction, each statement is executed
  // and returned in the txResults array.
  // The results we want are in txResults[2].
  const txResults = await query(queryString);
  if (txResults && txResults.length === 4) {
    const result = txResults[2];

    // Need special processing to handle source_url for Highspot
    // (See the /knowledge/source/:id route)
    const containsHighspotResult = result.rows.some(
      (row: any) => row.external_doc_source === 'highspot',
    );

    const highspotBaseUrl = containsHighspotResult
      ? await ClientUtils.getClientSetting(
          schema,
          constants.SETTING_CLIENT_HIGHSPOT_BASE_URL,
        )
      : '';

    return result.rows.map((row: any) => {
      // We calculate distance in the query so that the pgvector index can be used.
      // Calculate 1 - distance to get similarity.
      row.similarity = 1 - row.distance;

      if (row.external_doc_source === 'highspot') {
        row.source_url = `${highspotBaseUrl}/items/${row.external_doc_source_id}`;
      }
      return new ConversationContext(row);
    });
  }
  return [];
}

/**
 * Generates a SQL clause for filtering by time based on the provided TimeFilter
 * NOTE: must have unambiguous `created_date` column in the query
 * @returns SQL WHERE clause string for filtering by date
 */
export function generateTimeFilterClause(
  timeFilter: TimeFilter | null,
): string {
  if (!timeFilter || (!timeFilter.startDate && !timeFilter.endDate)) {
    return '';
  }

  // Where this is used, the created_date alias has to exist.
  return [
    timeFilter.startDate &&
      `created_date >= '${timeFilter.startDate.toISOString()}'`,
    timeFilter.endDate &&
      `created_date <= '${timeFilter.endDate.toISOString()}'`,
  ]
    .filter(Boolean)
    .reduce(
      (clause, condition) =>
        clause ? `${clause} AND ${condition}` : ` AND (${condition}`,
      '',
    )
    .concat(')');
}

/**
 * Generates a SQL clause for sorting by time based on the provided sortOrder
 * NOTE: must have `created_date` and `distance` columns in the query
 * @returns SQL ORDER BY clause string for sorting by date
 */
function generateSortOrderClause(
  sortOrder: 'newest' | 'oldest' | 'not_applicable',
  defaultClause = '',
  embeddingString: string,
  skipVectorSearchAndSort = false,
  similarityThreshold = 0.85,
): string {
  if (!sortOrder || sortOrder === 'not_applicable') return defaultClause;

  if (skipVectorSearchAndSort) {
    return `
      ORDER BY
        CASE
          WHEN '${sortOrder}' = 'newest' THEN EXTRACT(EPOCH FROM created_date)
          WHEN '${sortOrder}' = 'oldest' THEN -EXTRACT(EPOCH FROM created_date)
        END DESC NULLS LAST`;
  }

  // first identify the top similarity score,
  // then only applies time-based sorting to results within the threshold % of that top score
  return `
    ORDER BY
      CASE
        WHEN (1 - distance) >= ${similarityThreshold} * MAX(1 - distance) OVER ()
        THEN
          CASE
            WHEN '${sortOrder}' = 'newest' THEN EXTRACT(EPOCH FROM created_date)
            WHEN '${sortOrder}' = 'oldest' THEN -EXTRACT(EPOCH FROM created_date)
          END
      END DESC NULLS LAST,
      distance ASC
  `;
}

function _metadataFilterClause(
  metadataFilters: MetadataFilterValue[],
  strictMode: boolean,
) {
  const grouped = groupByType(metadataFilters);
  const type_ids = Object.keys(grouped);

  const hasSingleType = type_ids.length == 1;
  const hasMultipleTypes = type_ids.length > 1;

  let metadata_filter_clause_array = '';
  let metadata_filter_clause = '';

  if (hasMultipleTypes) {
    if (strictMode) {
      metadata_filter_clause = 'AND ((';
    } else {
      metadata_filter_clause = "AND (metadata_filter = '{}'::jsonb OR (";
    }

    for (let i = 0; i < type_ids.length; i++) {
      const type_id = type_ids[i];
      const metadata_filter_multi_one = grouped[type_id];
      const values = metadata_filter_multi_one.map((mdf) => mdf.value);
      const metadata_filter_one_clause_array = "'" + values.join("','") + "'";

      if (strictMode) {
        metadata_filter_clause += `(metadata_filter -> '${type_id}' ?| ARRAY[${metadata_filter_one_clause_array}]`;
      } else {
        // either the key doesn't exist in the text chunk's metadata_filter value OR the value exists
        metadata_filter_clause += `(metadata_filter ->> '${type_id}' is null OR metadata_filter -> '${type_id}' ?| ARRAY[${metadata_filter_one_clause_array}]`;
      }

      if (i < type_ids.length - 1) {
        metadata_filter_clause += ') AND ';
      } else {
        metadata_filter_clause += ')';
      }
    }

    metadata_filter_clause += ')) ';
  } else if (hasSingleType) {
    const type_id = type_ids[0];
    const metadata_filters = grouped[type_id];
    const values = metadata_filters.map((mdf) => mdf.value);
    metadata_filter_clause_array = "'" + values.join("','") + "'";

    if (strictMode) {
      metadata_filter_clause = `AND (metadata_filter -> '${type_id}' ?| ARRAY[${metadata_filter_clause_array}])`;
    } else {
      // Either the embedding doesn't have any metadata filters OR
      // There are MDFs, but the type_id we care about isn't present OR
      // There are MDFs, and the type_id is present, and contains one of the values we care about
      metadata_filter_clause = `AND (metadata_filter = '{}'::jsonb OR metadata_filter ->> '${type_id}' is null OR metadata_filter -> '${type_id}' ?| ARRAY[${metadata_filter_clause_array}])`;
    }
  }

  return metadata_filter_clause;
}

//Get an object of metadata filter values, keyed by type_id or type.name
export function groupByType(
  mdf_values: MetadataFilterValue[],
  useStrings: boolean = false,
): Record<number, MetadataFilterValue[]> {
  let grouped = {};
  mdf_values.forEach((mdfv) => {
    const key = useStrings ? mdfv.type!.name : mdfv.type_id;

    if (!grouped[key]) grouped[key] = [];
    grouped[key].push(mdfv);
  });
  return grouped;
}

export const PlatformMapping: Record<
  string,
  { type: 'asset_table' | 'metadata_filter' | 'document_type'; filter: string }
> = {
  // Slack and Website are document types in the database but LLM often refers to them as platforms.
  // We convert them to document types in generatePlatformFilters method.
  Slack: {
    type: 'document_type',
    filter: 'Slack',
  },
  Notion: {
    type: 'metadata_filter',
    filter: 'notion',
  },
  GoogleDrive: {
    type: 'asset_table',
    filter: 'gdrive_asset',
  },
  Confluence: {
    type: 'metadata_filter',
    filter: 'confluence',
  },
  Box: {
    type: 'asset_table',
    filter: 'box_asset',
  },
  Highspot: {
    type: 'asset_table',
    filter: 'highspot_asset',
  },
  Salesforce: {
    type: 'metadata_filter',
    filter: 'salesforce',
  },
  SharePoint: {
    type: 'asset_table',
    filter: 'm365drive_asset',
  },
  Zendesk: {
    type: 'asset_table',
    filter: 'zendesk_asset',
  },
  Gong: {
    type: 'metadata_filter',
    filter: 'gong',
  },
};

/**
 * AccessSequenceIntegrationMapping is used for access sequence filtering.
 * It uses the most reliable identifier for each integration based on actual data.
 * - For most: use externalDocSource
 * - For Zendesk, GoogleDrive, Highspot: use asset table
 * - For Website, Slack: use document_type
 */
export const AccessSequenceIntegrationMapping: Record<
  string,
  { type: 'asset_table' | 'metadata_filter' | 'document_type'; filter: string }
> = {
  Box: {
    type: 'metadata_filter',
    filter: 'box',
  },
  SharePoint: {
    type: 'metadata_filter',
    filter: 'sharepoint',
  },
  Notion: {
    type: 'metadata_filter',
    filter: 'notion',
  },
  Confluence: {
    type: 'metadata_filter',
    filter: 'confluence',
  },
  Salesforce: {
    type: 'metadata_filter',
    filter: 'salesforce',
  },
  Gong: {
    type: 'metadata_filter',
    filter: 'gong',
  },
  Zendesk: {
    type: 'asset_table',
    filter: 'zendesk_asset',
  },
  Highspot: {
    type: 'asset_table',
    filter: 'highspot_asset',
  },
  GoogleDrive: {
    type: 'asset_table',
    filter: 'gdrive_asset',
  },
  Slack: {
    type: 'document_type',
    filter: 'Slack',
  },
};

export default PlatformMapping;

export type Filter = {
  source_type?: string;
  source_name?: string;
  source_platform?: string;
};

export interface FilteredSearchParams extends SearchParams {
  filters?: Filter[];
  timeFilter?: TimeFilter;
}

export const FilteredContextSearch = async (
  params: FilteredSearchParams,
): Promise<ConversationContext[]> => {
  const {
    schema,
    strictMode,
    metadataFilters,
    filters,
    includeVerbatim,
    timeFilter,
  } = params;

  const filterClauses = await generateFilterClauses(schema, filters);

  if (filterClauses.trim().length === 0) {
    // This means we only have source_name filter and no documents found for the filter.
    return [];
  }

  let { queryEmbedding } = params;
  if (!queryEmbedding) {
    const embeddingsResult = await generateEmbeddings(params.query);
    if (!embeddingsResult || embeddingsResult.embedding.length === 0) {
      return [];
    }
    queryEmbedding = embeddingsResult.embedding;
  }

  // No need for this verbatimClause check if the client doesn't have any verbatim MDF types at all
  const verbatimMdfTypes = await query(`
    SELECT *
    FROM ${params.schema}.${constants.TABLE_METADATA_FILTER_TYPE}
    WHERE is_verbatim = true
  `);

  const hasVerbatimMdfTypes =
    verbatimMdfTypes?.rows && verbatimMdfTypes.rows.length > 0;

  let contexts = await getFilteredRelatedContexts(
    schema,
    queryEmbedding,
    strictMode,
    metadataFilters || [],
    filterClauses,
    timeFilter,
    includeVerbatim || !hasVerbatimMdfTypes,
  );

  return contexts;
};

export interface FilteredContextSearchByDocumentParams extends SearchParams {
  document_ids: string[];
}

export const FilteredContextSearchByDocumentId = async (
  params: FilteredContextSearchByDocumentParams,
): Promise<ConversationContext[]> => {
  const {
    query,
    schema,
    strictMode,
    metadataFilters,
    document_ids,
    timeFilter,
  } = params;

  const filterClause = `AND d.id IN (${document_ids.map((id) => `'${id}'`).join(', ')})`;

  let { queryEmbedding } = params;
  if (!queryEmbedding) {
    const embeddingsResult = await generateEmbeddings(query);
    queryEmbedding = embeddingsResult.embedding;
  }

  let contexts = await getFilteredRelatedContexts(
    schema,
    queryEmbedding,
    strictMode,
    metadataFilters || [],
    filterClause,
    timeFilter || null,
  );

  return contexts;
};

export async function getFilteredRelatedContexts(
  schema: string,
  embedding: number[],
  strictMode: boolean,
  metadata_filters: MetadataFilterValue[] = [],
  filterClauses: string,
  timeFilter: TimeFilter | null,
  includeVerbatim: boolean = false,
  includeImages: boolean = true,
): Promise<ConversationContext[]> {
  let metadataFilterClause = '';
  const metadataIsBlank = metadata_filters.length == 0;

  if (!metadataIsBlank) {
    metadataFilterClause = _metadataFilterClause(metadata_filters, strictMode);
  }

  const verbatimClause = includeVerbatim
    ? ''
    : `AND NOT EXISTS (
        SELECT 1
        FROM metadata_filter_keys mft
        WHERE e.metadata_filter ? mft.key
      )`;

  const embeddingString = JSON.stringify(embedding);
  const skipVectorSearchAndSort = embeddingString === '[]';

  const timeFilterClause = generateTimeFilterClause(timeFilter);

  const maxDistanceThreshold = 0.5; //Todo make this configurable

  const verbatimMdfCTE = includeVerbatim
    ? ''
    : `WITH metadata_filter_keys AS (
      -- Pre-materialize the verbatim metadata filter types (executed once)
      SELECT id::text AS key
      FROM ${schema}.${constants.TABLE_METADATA_FILTER_TYPE}
      WHERE is_verbatim = true
    ),
  `;

  const queryString = `
    ${verbatimMdfCTE ? verbatimMdfCTE : 'WITH'}
    context_data AS (
      SELECT 
          -- Filtered Search
          content as text,
          content_meta as metadata,
          d.file_name,
          d.label,
          d.document_type_id,
          d.privacy,
          d.source_url,
          e.id as embedding_id,
          e.content_type,
          d.use_for_generation,
          e.document_id,
          e.use_for_generation,
          e.origin,
          e.metadata_filter,
          -- 'created_date' should ideally be the date the original document should represent. Therefore, this order of preference:
          --    1. e.source_modified_date tends to be the most true to the original date of the document
          --    2. e.source_created_date tends to be the date of the document as it was uploaded to the source system
          --    3. e.modified_date is the date of the last modification to the document in Tribble
          --    4. d.created_date is the date of the document as it was created in Tribble
          COALESCE(e.source_modified_date, e.source_created_date, e.modified_date, d.created_date) as created_date,
          LOWER(COALESCE(metadata_json->>'webCategory', 'internal')) as applicability,
          COALESCE(metadata_json->>'webCategoryLabel', '') as applicability_label,
          metadata_json->>'externalDocSource' as external_doc_source,
          d.deleted_date,
          e.index as snippet_index,
          e.content_type,
          COALESCE(dt.name, 'Document') as document_type_name,
          -- Not used anymore
          -- COALESCE(dt_priority.multiplier, 1) as multiplier,
          ${skipVectorSearchAndSort ? '1 as distance,' : `(e.embedding <=> '${embeddingString}'::vector) as distance,`}
          1 as multiplier

        FROM ${schema}.${constants.TABLE_EMBEDDING} e
        JOIN ${schema}.${constants.TABLE_DOC} d ON e.document_id = d.id
        
        LEFT JOIN ${schema}.${constants.TABLE_DOCUMENT_TYPE} dt ON d.document_type_id = dt.id
        -- Not used anymore
        -- LEFT JOIN ${schema}.${constants.TABLE_DOCUMENT_TYPE_PRIORITY} dt_priority ON dt.priority_id = dt_priority.id
        
        WHERE   (d.use_for_generation IS NULL OR d.use_for_generation = true OR e.content_type = ANY(ARRAY['SLACK']))
        AND     (e.use_for_generation = true OR e.content_type = ANY(ARRAY['SLACK']))
        AND     e.origin = '${process.env.LLM_SYSTEM ?? 'AZURE'}'
        AND     d.deleted_date is NULL
        AND     LENGTH(TRIM(content)) > 0
        ${skipVectorSearchAndSort ? '' : `AND    e.embedding <=> '${embeddingString}'::vector < ${maxDistanceThreshold}`}
        ${verbatimClause}
        ${metadataFilterClause}
        ${filterClauses}
    )
    SELECT * FROM context_data
    WHERE TRUE
      -- Warn: ensure clauses below has access to needed columns from the CTE above
      ${timeFilterClause}
      ${generateSortOrderClause(timeFilter?.sortOrder, ` ORDER BY distance ASC`, embeddingString, skipVectorSearchAndSort)}
    LIMIT 11
  `;

  const result = await query(queryString);
  if (result && result.rows) {
    return result.rows.map((row: any) => {
      // We calculate distance in the query so that the pgvector index can be used.
      // Calculate 1 - distance to get similarity.
      row.similarity = 1 - row.distance;
      return new ConversationContext(row);
    });
  }

  console.log('No results found for filtered search');
  return [];
}

export async function generateFilterClauses(schema: string, filters: Filter[]) {
  const clauses = await Promise.all(
    filters.map(async (filter) => {
      return generateFilterClause(schema, filter);
    }),
  );

  const nonEmptyClauses = clauses.filter((clause) => clause.trim().length > 0);

  if (nonEmptyClauses.length === 0) {
    return '';
  }

  return `AND (${nonEmptyClauses.join(' OR\n')})`;
}

async function generateFilterClause(schema: string, filter: Filter) {
  const clauses: string[] = [];

  if (filter.source_platform) {
    clauses.push(generatePlatformFilterClause(schema, filter.source_platform));
  }

  if (filter.source_type) {
    // If source_type is Presentation or Document, we also include the other as sometimes we
    // import from a source (e.g. Highspot) and we won't know ahead of time whether
    // it's a doc or presentation.
    if (
      filter.source_type === 'Presentation' ||
      filter.source_type === 'Document'
    ) {
      // Note: this won't handle clients that create their own custom document type,
      // but, that's a super corner case
      clauses.push(`dt.name IN ('Document', 'Presentation')`);
    } else {
      clauses.push(`dt.name = '${filter.source_type}'`);
    }
  }

  if (filter.source_name) {
    // First check if the source_name is a Slack channel ID
    const slack_channel_id = filter.source_name.match(SlackChannelIdRegex)
      ? filter.source_name.match(SlackChannelIdRegex)[0]
      : null;

    if (
      // If the source_name is a Slack channel ID we don't force source_platform or source_type to be Slack
      slack_channel_id != null ||
      filter.source_platform === 'Slack' ||
      filter.source_type === 'Slack'
    ) {
      if (slack_channel_id) {
        clauses.push(
          `e.content_meta -> 'channel' ->> 'id' = '${slack_channel_id}'`,
        );
      } else {
        // In case the LLM generates source_name as '#dev', 'dev chanel', 'slack dev channel' etc.
        const channel_name = filter.source_name
          .replace(/slack/gi, '')
          .replace(/channel/gi, '')
          .replace(/#/g, '')
          .trim();
        clauses.push(
          `e.content_meta -> 'channel' ->> 'name' ILIKE '%' || '${channel_name}' || '%'`,
        );
      }
    } else if (
      filter.source_platform === 'Website' ||
      filter.source_type === 'Website'
    ) {
      if (UrlRegex.test(filter.source_name)) {
        clauses.push(`d.file_name ILIKE '%' || '${filter.source_name}' || '%'`);
      } else {
        const nameClause = await getMatchingDocumentIdsClause(
          schema,
          filter.source_name,
        );
        if (nameClause.trim().length !== 0) {
          clauses.push(nameClause);
        }
      }
    } else {
      const nameClause = await getMatchingDocumentIdsClause(
        schema,
        filter.source_name,
      );
      if (nameClause.trim().length !== 0) {
        clauses.push(nameClause);
      }
    }
  }

  if (clauses.length === 0) {
    return '';
  }

  return `(${clauses.join(' AND\n')})`;
}

function generatePlatformFilterClause(
  schema: string,
  platform: string,
): string {
  // Handle case-insensitive lookup for platform mapping
  const platformKey = Object.keys(PlatformMapping).find(
    (key) => key.toLowerCase() === platform.toLowerCase(),
  );
  const platformData = platformKey ? PlatformMapping[platformKey] : null;
  if (!platformData) {
    throw new Error(`Platform '${platform}' is not supported.`);
  }

  switch (platformData.type) {
    case 'asset_table':
      return `EXISTS ( SELECT 1 FROM ${schema}.${platformData.filter} ${platformData.filter.slice(0, 2)}a
        WHERE ${platformData.filter.slice(0, 2)}a.document_id = d.id
        )`;
    case 'metadata_filter':
      return `COALESCE(d.metadata_json->>'externalDocSource', '') = '${platformData.filter}'`;
    case 'document_type':
      return `dt.name = '${platformData.filter}'`;
    default:
      throw new Error(`Unsupported platform type for '${platform}'`);
  }
}

interface SimilarityResult {
  id: string;
  final_similarity_score: number;
}

async function getMatchingDocumentIdsClause(
  schema: string,
  name: string,
): Promise<string> {
  const sanitizedName = name.replace(/'/g, "''");
  const embeddingsResult = await generateEmbeddings(sanitizedName);
  const queryEmbedding = JSON.stringify(embeddingsResult.embedding);

  const similarityQuery = `
    WITH embedding_data AS (
        SELECT 
            d.id,
            AVG(1 - (dme.label_embedding <=> '${queryEmbedding}')) AS embedding_similarity
        FROM ${schema}.document d
        JOIN ${schema}.document_metadata_embedding dme 
            ON d.id = dme.document_id
        WHERE d.deleted_date IS NULL 
        AND d.use_for_generation = TRUE
        GROUP BY d.id
    ),
    token_match_data AS (
        -- Calculate token match count
        SELECT
            d.id,
            (
                SELECT COUNT(*)
                FROM unnest(string_to_array(regexp_replace(lower('${sanitizedName}'), '[-_.]', ' ', 'g'), ' ')) AS query_token
                WHERE query_token = ANY(
                    string_to_array(
                        regexp_replace(lower(d.label || ' ' || COALESCE(d.description, '')), '[-_.]', ' ', 'g'),
                        ' '
                    )
                )
            ) AS token_match_count
        FROM ${schema}.document d
        WHERE d.deleted_date IS NULL AND d.use_for_generation = TRUE
    ),
    ilike_data AS (
        SELECT 
            id,
            CASE 
                WHEN regexp_replace(lower(label || ' ' || COALESCE(description, '')), '[\\.\\-\\s]', '', 'g') 
                    ILIKE '%' || regexp_replace(lower('${sanitizedName}'), '[\\.\\-\\s]', '', 'g') || '%'
                THEN 1 ELSE 0 
            END AS ilike_match
        FROM ${schema}.document
        WHERE deleted_date IS NULL AND use_for_generation = TRUE
    )

    SELECT *
    FROM (
        SELECT 
            d.id,
            (0.5 * COALESCE(e.embedding_similarity, 0)) + 
            (0.3 * (COALESCE(t.token_match_count, 0) / NULLIF(array_length(string_to_array(regexp_replace(lower('${sanitizedName}'), '[-_.]', ' ', 'g'), ' '), 1)::FLOAT, 0))) + 
            (0.2 * COALESCE(i.ilike_match, 0)) AS final_similarity_score
        FROM ${schema}.document d
        LEFT JOIN embedding_data e ON d.id = e.id
        LEFT JOIN token_match_data t ON d.id = t.id
        LEFT JOIN ilike_data i ON d.id = i.id
    ) AS scores
    WHERE final_similarity_score >= 0.5
    ORDER BY final_similarity_score DESC
    LIMIT 5;
  `;

  //Run the query
  const similarityResults = await query(similarityQuery);
  const topResults = similarityResults.rows as SimilarityResult[];

  //Extract unique IDs
  const uniqueIds = Array.from(
    new Set(topResults.map((result) => result.id).filter(Boolean)),
  );

  //Generate the IN clause
  if (uniqueIds.length > 0) {
    return `d.id IN (${uniqueIds.map((id) => `'${id}'`).join(', ')})`;
  }

  return '';
}

async function getExcludedPlatforms(
  schema: string,
  conversationId: ConversationId,
): Promise<SourcePlatformEnum[]> {
  const conversationIdNumber = Number(conversationId);
  if (!conversationIdNumber) return [];

  const contentRecord =
    await ContentUtils.getContentRecordFromContentDetailConversationId(
      schema,
      conversationIdNumber,
    );
  const excludedPlatformsConfig = contentRecord?.source_excluded_platforms;
  if (!excludedPlatformsConfig) return [];

  const excludedPlatforms = parsePlatformsList(excludedPlatformsConfig)
    .map(checkEnumAndNormalizePlatform)
    .filter(Boolean) as SourcePlatformEnum[];

  if (excludedPlatforms.length > 0) {
    console.debug(
      `[brain] Excluding platforms for project (content.id:${contentRecord.id}): ${excludedPlatforms.join(', ')}`,
    );
  }

  return excludedPlatforms;
}

function parsePlatformsList(platformsConfig: string | string[]): string[] {
  return typeof platformsConfig === 'string'
    ? platformsConfig.split(',').map((p) => p.trim())
    : platformsConfig;
}

function checkEnumAndNormalizePlatform(
  platform: string,
): SourcePlatformEnum | null {
  const isValidPlatform = (platform: string): platform is SourcePlatformEnum =>
    Object.values(SourcePlatformEnum).includes(platform as SourcePlatformEnum);

  const normalizedPlatform = platform.toLowerCase() as SourcePlatformEnum;
  if (!isValidPlatform(normalizedPlatform)) {
    console.warn(`[brain] Unknown platform to exclude: ${platform}`);
    return null;
  }
  return normalizedPlatform;
}

type AccessSequenceOption = {
  value: string;
  label: string;
  type: string;
  is_single_source?: boolean;
  document_id?: number;
  tier?: number;
  agent_type?: string;
  document_label?: string;
  document_file_name?: string;
};

export interface IntegrationWithGenerationDocuments {
  integration_system_id: number;
  system_name: string;
  label: string;
  document_count: number;
  last_document_date: Date | null;
}

/**
 * Get all website roots (hostnames) with document counts and metadata
 * @param db - Database to query
 * @returns Array of website roots with counts and timestamps
 */
export async function getWebsiteRoots(db: DB | TRX) {
  const query = db
    .selectFrom('document as d')
    .innerJoin('document_type as dt', 'd.document_type_id', 'dt.id')
    .select([
      sql<string>`d.metadata_json->>'hostname'`.as('hostname'),
      sql<number>`COUNT(*)`.as('doc_count'),
      sql<Date>`MAX(d.created_date)`.as('last_updated'),
    ])
    .where('d.deleted_date', 'is', null)
    .where('dt.is_website', '=', true)
    .where('d.metadata_json', 'is not', null)
    .where(sql`d.metadata_json::text`, '!=', '{}')
    .where(sql`d.metadata_json->>'hostname'`, 'is not', null)
    .groupBy(sql`d.metadata_json->>'hostname'`)
    .orderBy(sql`d.metadata_json->>'hostname'`);

  return query.execute();
}

/**
 * Check which integrations have active user or client integrations in a given schema
 * @param db - Database to query
 * @param schema - Schema name to check for integrations
 * @returns Array of integrations with their availability status
 */
async function getIntegrationAvailability(db: DB | TRX, schema: string) {
  // Create subqueries for user and client integration counts
  const userIntegrationCounts = db
    .selectFrom('user_integration')
    .select([
      'integration_system_id',
      (eb) => eb.fn.count<number>('integration_system_id').as('count'),
    ])
    .groupBy('integration_system_id');

  const clientIntegrationCounts = db
    .selectFrom('client_integration')
    .select([
      'integration_system_id',
      (eb) => eb.fn.count<number>('integration_system_id').as('count'),
    ])
    .groupBy('integration_system_id');

  // Main query with joins
  const baseQuery = db
    .selectFrom('tribble.integration_system as is_sys')
    .leftJoin(
      userIntegrationCounts.as('ui_count'),
      'is_sys.id',
      'ui_count.integration_system_id',
    )
    .leftJoin(
      clientIntegrationCounts.as('ci_count'),
      'is_sys.id',
      'ci_count.integration_system_id',
    )
    .select([
      'is_sys.id',
      'is_sys.system_name',
      'is_sys.label',
      (eb) =>
        eb
          .case()
          .when('ui_count.count', '>', 0)
          .then(true)
          .else(false)
          .end()
          .as('has_user_integration'),
      (eb) =>
        eb
          .case()
          .when('ci_count.count', '>', 0)
          .then(true)
          .else(false)
          .end()
          .as('has_client_integration'),
      (eb) =>
        eb.fn
          .coalesce('ui_count.count', eb.lit(0))
          .as('user_integration_count'),
      (eb) =>
        eb.fn
          .coalesce('ci_count.count', eb.lit(0))
          .as('client_integration_count'),
    ])
    .where('is_sys.hide_from_integration_modal', '=', false)
    .where((eb) =>
      eb.or([eb('ui_count.count', '>', 0), eb('ci_count.count', '>', 0)]),
    );

  // Add Slack installation query to include Slack when there's a slack_installation record
  const slackQuery = db
    .selectFrom('tribble.slack_installation as slack_install')
    .innerJoin('tribble.client as c', 'c.id', 'slack_install.client_id')
    .select([
      sql<IntegrationSystemId>`-1`.as('id'),
      sql<string>`'slack'`.as('system_name'),
      sql<string>`'Slack'`.as('label'),
      sql<boolean>`false`.as('has_user_integration'),
      sql<boolean>`true`.as('has_client_integration'),
      sql<number>`0`.as('user_integration_count'),
      sql<number>`1`.as('client_integration_count'),
    ])
    .where('c.database_schema_id', '=', schema)
    .where(
      sql<boolean>`(slack_install.installation->>'system' IS NULL OR slack_install.installation->>'system' != ${'TEAMS'})`,
    );

  // Combine both queries with UNION and order by system_name
  const combinedQuery = baseQuery.union(slackQuery).orderBy('system_name');

  return combinedQuery.execute();
}

/**
 * Get integrations that have documents marked for generation
 * @param db - Database to query (already connected to correct client schema)
 * @returns Array of integrations with document counts that have use_for_generation = true
 */
async function getIntegrationsWithGenerationDocuments(
  db: DB | TRX,
): Promise<IntegrationWithGenerationDocuments[]> {
  // Step 1: Get all integration systems (not filtered by availability)
  const allIntegrations = await db
    .selectFrom('tribble.integration_system as is_sys')
    .select(['is_sys.id', 'is_sys.system_name', 'is_sys.label'])
    .where('is_sys.hide_from_integration_modal', '=', false)
    .execute();

  // Step 2: Count documents per integration type with complex mapping
  const documentCounts = await db
    .with('doc_with_source', (db) =>
      db
        .selectFrom('document as d')
        .leftJoin('document_type as dt', 'd.document_type_id', 'dt.id')
        .leftJoin('highspot_asset as ha', 'd.id', 'ha.document_id')
        .leftJoin('zendesk_asset as za', 'd.id', 'za.document_id')
        .select([
          'd.id',
          'd.created_date',
          // Complex integration source mapping (matches SourceTable.tsx logic)
          (eb) =>
            eb
              .case()
              .when(
                eb.and([
                  eb(
                    sql`d.metadata_json->>'externalDocSource'`,
                    'is not',
                    null,
                  ),
                  eb(sql`d.metadata_json->>'externalDocSource'`, '!=', ''),
                ]),
              )
              .then(sql`d.metadata_json->>'externalDocSource'`)
              .when('ha.document_id', 'is not', null)
              .then('highspot')
              .when('za.document_id', 'is not', null)
              .then('zendesk')
              .when('dt.name', '=', 'Slack')
              .then('slack')
              .when('dt.name', '=', 'Website')
              .then('website')
              .else('manual_upload')
              .end()
              .as('integration_source'),
        ])
        .where('d.use_for_generation', '=', true)
        .where('d.status', '!=', 'deleted'),
    )
    .selectFrom('doc_with_source')
    .select([
      'integration_source',
      (eb) => eb.fn.count<number>('id').as('document_count'),
      (eb) => eb.fn.max('created_date').as('last_document_date'),
    ])
    .groupBy('integration_source')
    .execute();

  // Step 3: Join integration systems with document counts, filter only those with docs
  return allIntegrations
    .map((integration) => {
      const docData = documentCounts.find(
        (dc) => dc.integration_source === integration.system_name,
      );
      return {
        integration_system_id: integration.id,
        system_name: integration.system_name,
        label: integration.label,
        document_count: Number(docData?.document_count || 0),
        last_document_date: docData?.last_document_date || null,
      };
    })
    .filter((integration) => integration.document_count > 0)
    .sort((a, b) => b.document_count - a.document_count); // Sort by doc count desc
}

/**
 * Get all access sequences ordered by tier
 * @param db - Database to query
 * @returns Array of access sequences ordered by tier with document info
 */
async function getAccessSequences(db: DB | TRX) {
  return db
    .selectFrom('access_sequence')
    .leftJoin('document as doc', 'access_sequence.document_id', 'doc.id')
    .selectAll('access_sequence')
    .select([
      'doc.label as document_label',
      'doc.file_name as document_file_name',
    ])
    .orderBy('tier', 'asc')
    .execute();
}

/**
 * Get the Tribble Knowledge Base document if it exists
 * @param db - Database to query
 * @returns The document info or null if not found
 */
async function getTribbleKnowledgeBaseDocument(db: DB | TRX) {
  const result = await db
    .selectFrom('document')
    .select(['id', 'label', 'file_name'])
    .where('file_name', '=', 'tribble_knowledge_base')
    .where('deleted_date', 'is', null)
    .executeTakeFirst();

  return result || null;
}

/**
 * Get all available data sources that can be configured in access sequences
 * This function replicates the logic from the /access_sequences route
 */
export const getAccessSequenceOptions = async (
  schema: string,
): Promise<AccessSequenceOption[]> => {
  const db = await getDB(schema);

  // Add static options
  const options: AccessSequenceOption[] = [
    {
      value: 'uploaded_documents',
      label: 'Uploaded Documents',
      type: 'grouped_documents',
      tier: 1,
    },
  ];

  // Get the tribble_knowledge_base document and add it if it exists
  const tribbleKbDoc = await getTribbleKnowledgeBaseDocument(db);
  if (tribbleKbDoc) {
    options.push({
      value: String(tribbleKbDoc.id),
      label: tribbleKbDoc.label,
      type: 'document',
      is_single_source: true,
      document_id: Number(tribbleKbDoc.id),
      tier: 1,
      document_label: tribbleKbDoc.label,
      document_file_name: tribbleKbDoc.file_name,
    });
  }

  // Call all three functions in parallel
  const [websiteRoots, integrationAvailability, integrationsWithDocs] =
    await Promise.all([
      getWebsiteRoots(db),
      getIntegrationAvailability(db, schema),
      getIntegrationsWithGenerationDocuments(db),
    ]);

  // Transform website roots to unified schema
  const websiteOptions = websiteRoots.map((root: any) => ({
    value: root.hostname,
    label: root.hostname,
    type: 'website',
    tier: 1,
  }));

  options.push(...websiteOptions);

  // Transform integrations to unified schema and deduplicate using reduce
  const integrationOptions = [
    ...integrationAvailability,
    ...integrationsWithDocs,
  ]
    .map((integration: any) => ({
      value: integration.system_name,
      label: integration.label,
      type: 'integration',
      tier: 1,
    }))
    .reduce((acc: AccessSequenceOption[], current: AccessSequenceOption) => {
      // Only add if not already present (deduplicate by value)
      if (!acc.find((item) => item.value === current.value)) {
        acc.push(current);
      }
      return acc;
    }, []);

  options.push(...integrationOptions);

  const agentTypes = ['rfp_agent', 'digital_se_agent'];
  const dupeOptions = agentTypes.flatMap((agentType) =>
    options.map((option) => ({
      ...option,
      agent_type: agentType,
    })),
  );

  const accessSequences = await getAccessSequences(db);

  // Create an object of dupeOptions using reduce
  const optionMap = dupeOptions.reduce(
    (obj, option) => {
      const key = `${option.value}-${option.agent_type}`;
      obj[key] = option;
      return obj;
    },
    {} as Record<string, AccessSequenceOption>,
  );

  // Merge access sequences with existing options using reduce
  const mergedMap = accessSequences.reduce((obj, seqItem: any) => {
    const key = `${seqItem.value}-${seqItem.agent_type}`;
    const existing = obj[key] ?? ({} as AccessSequenceOption);

    obj[key] = {
      ...existing,
      ...seqItem,
      document_id: seqItem.document_id
        ? Number(seqItem.document_id)
        : undefined,
      label: existing.label || seqItem.document_label || seqItem.value,
    };

    return obj;
  }, optionMap);

  // Convert object values to array and sort by label
  const sortedOptions = Object.values(mergedMap).sort((a, b) =>
    a.label.localeCompare(b.label),
  );

  return sortedOptions;
};

/**
 * Given an AccessSequenceOption, return the SQL WHERE clause for that option.
 * Handles document, integration, website, and grouped_documents types.
 */
function getAccessSequenceWhereClause(
  option: AccessSequenceOption,
  schema: string,
): string {
  switch (option.type) {
    case 'document':
      return `d.id = ${option.document_id}`;
    case 'integration': {
      // Handle case-insensitive lookup for integration mapping
      const integrationKey = Object.keys(AccessSequenceIntegrationMapping).find(
        (key) => key.toLowerCase() === option.value.toLowerCase(),
      );
      const platformData = integrationKey
        ? AccessSequenceIntegrationMapping[integrationKey]
        : null;
      if (!platformData)
        throw new Error(`Unknown integration: ${option.value}`);
      switch (platformData.type) {
        case 'asset_table':
          return `EXISTS (SELECT 1 FROM ${schema}.${platformData.filter} ${platformData.filter.slice(0, 2)}a WHERE ${platformData.filter.slice(0, 2)}a.document_id = d.id)`;
        case 'metadata_filter':
          return `COALESCE(d.metadata_json->>'externalDocSource', '') = '${platformData.filter}'`;
        case 'document_type':
          return `dt.name = '${platformData.filter}'`;
        default:
          throw new Error(`Unknown platform type: ${platformData.type}`);
      }
    }
    case 'website':
      return `COALESCE(d.metadata_json->>'hostname', '') = '${option.value}'`;
    case 'grouped_documents':
      return `(
        dt.is_website = false AND
        dt.name != 'Slack' AND
        COALESCE(d.metadata_json->>'externalDocSource', '') = '' AND
        NOT EXISTS (SELECT 1 FROM ${schema}.gdrive_asset ga WHERE ga.document_id = d.id) AND
        NOT EXISTS (SELECT 1 FROM ${schema}.zendesk_asset za WHERE za.document_id = d.id) AND
        NOT EXISTS (SELECT 1 FROM ${schema}.highspot_asset ha WHERE ha.document_id = d.id)
      )`;
    default:
      throw new Error(`Unknown access sequence type: ${option.type}`);
  }
}

export interface TieredSearchParams extends SearchParams {
  tier: number;
  agentType?: 'rfp_agent' | 'digital_se_agent';
  filters?: Filter[];
}

/**
 * Performs a context search filtered by access sequence tier
 */
export const TieredContextSearch = async (
  params: TieredSearchParams,
): Promise<ConversationContext[]> => {
  let { queryEmbedding } = params;
  if (!queryEmbedding) {
    const embeddingsResult = await generateEmbeddings(params.query);
    if (!embeddingsResult || embeddingsResult.embedding.length === 0) {
      return [];
    }
    queryEmbedding = embeddingsResult.embedding;
  }

  const access_sequences = await getAccessSequenceOptions(params.schema);
  const current_agent_access_sequences = access_sequences.filter(
    (x) => x.agent_type === params.agentType,
  );

  // Example usage: get WHERE clause for each access sequence for this agent
  const whereClauses = new Map(
    current_agent_access_sequences.map((opt) => [
      opt.value,
      getAccessSequenceWhereClause(opt, params.schema),
    ]),
  );
  const current_tier_document_clause = current_agent_access_sequences
    .filter((x) => x.tier === params.tier)
    .filter((x) => x.type === 'document')
    .map((x) => whereClauses.get(x.value))
    .join(' OR ');

  const current_tier_uploaded_documents = current_agent_access_sequences
    .filter((x) => x.tier === params.tier)
    .filter((x) => x.type === 'grouped_documents')
    .map((x) => whereClauses.get(x.value));
  let current_tier_uploaded_documents_clause = '';
  if (current_tier_uploaded_documents.length > 0) {
    if (current_tier_document_clause) {
      current_tier_uploaded_documents_clause = `(${current_tier_uploaded_documents[0]} AND NOT (${current_tier_document_clause}))`;
    } else {
      current_tier_uploaded_documents_clause = `(${current_tier_uploaded_documents[0]})`;
    }
  }

  const current_tier_integration_clause = current_agent_access_sequences
    .filter((x) => x.tier === params.tier)
    .filter((x) => x.type === 'integration')
    .map((x) => whereClauses.get(x.value))
    .join(' OR ');

  const current_tier_website_clause = current_agent_access_sequences
    .filter((x) => x.tier === params.tier)
    .filter((x) => x.type === 'website')
    .map((x) => whereClauses.get(x.value))
    .join(' OR ');

  let current_tier_clauses = [];
  if (current_tier_document_clause) {
    current_tier_clauses.push(current_tier_document_clause);
  }
  if (current_tier_uploaded_documents_clause) {
    current_tier_clauses.push(current_tier_uploaded_documents_clause);
  }
  if (current_tier_integration_clause) {
    current_tier_clauses.push(current_tier_integration_clause);
  }
  if (current_tier_website_clause) {
    current_tier_clauses.push(current_tier_website_clause);
  }

  const current_tier_clause =
    current_tier_clauses.length > 0
      ? `(${current_tier_clauses.join(' OR ')})`
      : '';
  const doNotUse = current_agent_access_sequences
    .filter((x) => x.tier === -1)
    .map((x) => {
      if (x.type === 'grouped_documents') {
        if (current_tier_document_clause) {
          return `(${whereClauses.get(x.value)} AND NOT (${current_tier_document_clause}))`;
        } else {
          return `(${whereClauses.get(x.value)})`;
        }
      }
      return whereClauses.get(x.value);
    })
    .join(' OR ');

  let final_tier_clause = current_tier_clause;
  if (doNotUse && doNotUse.trim().length > 0) {
    final_tier_clause = current_tier_clause
      ? `(${current_tier_clause} AND NOT (${doNotUse}))`
      : `NOT (${doNotUse})`;
  }

  // Generate regular filter clauses if filters are provided
  let regularFilterClauses = '';
  if (params.filters && params.filters.length > 0) {
    regularFilterClauses = await generateFilterClauses(
      params.schema,
      params.filters,
    );
  }

  // Call getRelatedContextsWithAccessFilter with the tier filters and regular filters
  return getRelatedContextsWithAccessFilter(
    params.schema,
    queryEmbedding,
    params.strictMode,
    params.metadataFilters || [],
    params.includeVerbatim || false,
    params.timeFilter,
    final_tier_clause,
    regularFilterClauses,
  );
};

/**
 * Enhanced version of getRelatedContexts that supports access sequence filtering
 */
async function getRelatedContextsWithAccessFilter(
  schema: string,
  embedding: number[],
  strictMode: boolean,
  metadata_filters: MetadataFilterValue[] = [],
  includeVerbatim: boolean = false,
  timeFilter?: TimeFilter,
  accessFilterClause: string = '',
  regularFilterClauses: string = '',
): Promise<ConversationContext[]> {
  //Metadata handling
  let metadataFilterClause = '';
  const metadataIsBlank = metadata_filters.length == 0;

  if (!metadataIsBlank) {
    metadataFilterClause = _metadataFilterClause(metadata_filters, strictMode);
  }

  const verbatimClause = includeVerbatim
    ? ''
    : `AND NOT EXISTS (
        SELECT 1
        FROM metadata_filter_keys mft
        WHERE e.metadata_filter ? mft.key
      )`;

  const timeFilterClause = generateTimeFilterClause(timeFilter);

  const embeddingString = JSON.stringify(embedding);
  const maxDistanceThreshold = 0.5;

  const verbatimMdfCTE = includeVerbatim
    ? ''
    : `WITH metadata_filter_keys AS (
      SELECT id::text AS key
      FROM ${schema}.${constants.TABLE_METADATA_FILTER_TYPE}
      WHERE is_verbatim = true
    ) 
  `;

  const EmbeddingCTE =
    (verbatimMdfCTE ? ', ' : 'WITH ') +
    `EmbeddingsFiltered AS NOT MATERIALIZED (
      SELECT e.id
      FROM ${schema}.${constants.TABLE_EMBEDDING} e
      WHERE e.origin = '${process.env.LLM_SYSTEM ?? 'AZURE'}'
      AND e.use_for_generation = true
      AND LENGTH(TRIM(e.content)) > 0 
    ),
  `;

  const queryString = `
    BEGIN;
    SET LOCAL ivfflat.probes = 10;

    ${verbatimMdfCTE}
    ${EmbeddingCTE}
    context_data AS (
      SELECT
        content as text,
        content_meta as metadata,
        d.file_name,
        d.label,
        d.document_type_id,
        d.privacy,
        CASE
          WHEN d.id = highspot.document_id THEN ''
          WHEN d.id = zendesk.document_id THEN zendesk.url
          ELSE d.source_url
        END AS source_url,
        e.id as embedding_id,
        e.content_type,
        d.use_for_generation,
        e.document_id,
        e.use_for_generation,
        e.origin,
        e.metadata_filter,
        COALESCE(e.source_modified_date, e.source_created_date, e.modified_date, d.created_date) as created_date,
        LOWER(COALESCE(metadata_json->>'webCategory', 'internal')) as applicability,
        COALESCE(metadata_json->>'webCategoryLabel', '') as applicability_label,
        CASE
          WHEN d.id = highspot.document_id THEN 'highspot'
          WHEN d.id = zendesk.document_id THEN 'zendesk'
          ELSE COALESCE(metadata_json->>'externalDocSource', '')
        END AS external_doc_source,
        CASE
          WHEN d.id = highspot.document_id THEN highspot.item_id::text
          WHEN d.id = zendesk.document_id THEN zendesk.zendesk_id::text
          ELSE COALESCE(metadata_json->>'externalDocSourceId', '')
        END AS external_doc_source_id,
        d.deleted_date,
        e.index as snippet_index,
        e.content_type,
        COALESCE(dt.name, 'Document') as document_type_name,
        1 as multiplier,
        (e.embedding <=> '${embeddingString}'::vector) as distance

      FROM    ${schema}.${constants.TABLE_EMBEDDING} e
      JOIN    ${schema}.${constants.TABLE_DOC} d ON e.document_id = d.id
      JOIN    EmbeddingsFiltered ef ON e.id = ef.id

      LEFT JOIN ${schema}.${constants.TABLE_DOCUMENT_TYPE} dt ON d.document_type_id = dt.id
      LEFT JOIN ${schema}.${constants.TABLE_HIGHSPOT_ASSET} highspot ON d.id = highspot.document_id
      LEFT JOIN ${schema}.${constants.TABLE_ZENDESK_ASSET} zendesk ON d.id = zendesk.document_id        

      WHERE   (
        d.use_for_generation IS NULL 
        OR d.use_for_generation = true 
      )
      AND d.deleted_date is NULL
      AND e.embedding <=> '${embeddingString}'::vector < ${maxDistanceThreshold}
      ${verbatimClause}
      ${metadataFilterClause}
      ${timeFilterClause}
      ${accessFilterClause && accessFilterClause.trim() && accessFilterClause.trim() !== '()' ? `AND (${accessFilterClause})` : ''}
      ${regularFilterClauses}
    )
    SELECT * FROM context_data
      WHERE TRUE
      ${generateSortOrderClause(timeFilter?.sortOrder, ` ORDER BY distance ASC`, embeddingString)}
      LIMIT 24;
    COMMIT;
  `;

  const txResults = await query(queryString);
  if (txResults && txResults.length === 4) {
    const result = txResults[2];

    const containsHighspotResult = result.rows.some(
      (row: any) => row.external_doc_source === 'highspot',
    );

    const highspotBaseUrl = containsHighspotResult
      ? await ClientUtils.getClientSetting(
          schema,
          constants.SETTING_CLIENT_HIGHSPOT_BASE_URL,
        )
      : '';

    return result.rows.map((row: any) => {
      // We calculate distance in the query so that the pgvector index can be used.
      // Calculate 1 - distance to get similarity.
      row.similarity = 1 - row.distance;

      if (row.external_doc_source === 'highspot') {
        row.source_url = `${highspotBaseUrl}/items/${row.external_doc_source_id}`;
      }
      return new ConversationContext(row);
    });
  }

  console.error(
    '[getRelatedContextsWithAccessFilter] Unexpected txResults format',
  );
  return [];
}
