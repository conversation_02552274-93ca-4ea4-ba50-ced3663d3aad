import { Tool } from '@tribble/chat-completion-service/client';
import { Logger } from '@tribble/common-utils';
import { FormRecognizedDoc } from '@tribble/formrecognizer';
import { ToolCall } from './tool_call.ts';

export class BidPacketSearch extends ToolCall {
  static tool_name = 'bid_packet_search';
  private pageQuery: number[];
  private fileName: string | undefined;
  // private searchQuery: string;
  private bidPacket: FormRecognizedDoc[];

  constructor(
    config: { bidPacketFiles: FormRecognizedDoc[]; logger?: Logger },
    args: { page_query: number[]; file_name?: string }, //; search_query?: string
  ) {
    super(config.logger);
    this.pageQuery = args.page_query;
    this.fileName = args.file_name;
    this.bidPacket = config.bidPacketFiles;
    // this.searchQuery = args.search_query || '';
  }

  static getPromptDescription(): string {
    return `Use ${BidPacketSearch.tool_name} to look at the bid packet file by returning certain pages.`;
  }

  static getJsonSchema(): Tool {
    return {
      type: 'function',
      function: {
        name: BidPacketSearch.tool_name,
        description: 'Search the bid packet file for specific pages',
        strict: true,
        parameters: {
          type: 'object',
          properties: {
            // Note: toolbox mapper maps this to "page_query"
            page_numbers: {
              type: 'array',
              description:
                '0 or more page numbers to return from the bid packet file',
              items: {
                type: 'number',
              },
            },
            file_name: {
              type: 'string',
              description: 'The name of the file to search',
            },
          },
          required: ['page_numbers'],
          additionalProperties: false,
        },
      },
    };
  }

  getPendingMessage(): string {
    return `Searching the bid packet for ${this.pageQuery?.length || 0} page(s)`;
  }

  getCompletionMessage(): string {
    return `Searched the bid packet for ${this.pageQuery?.length || 0} page(s)`;
  }

  getAvailablePages(): { file_name: string; total_pages_count: number }[] {
    return this.bidPacket.map((bidPacketFile) => {
      return {
        file_name: bidPacketFile.file_name,
        total_pages_count: bidPacketFile.pages.length,
      };
    });
  }

  async performCall(): Promise<string> {
    if (!this.bidPacket.length) {
      return 'No bid packet files found';
    }

    if (this.pageQuery === null || this.pageQuery === undefined) {
      return `Invalid page number. The pages available for each file are from page 1 till the total_pages_count: \n${JSON.stringify(this.getAvailablePages(), null, 2)}`;
    }

    const pages = this.bidPacket
      .filter(({ file_name }) => {
        // if this.fileName was not provided by the Tool Caller, do not filter by file name
        //   in a one-file bid packet, fileName is not required nor relevant
        const fileNameNotProvided = !this.fileName;
        return fileNameNotProvided || file_name === this.fileName;
      })
      .map((bidPacketFile) => {
        return {
          fileName: bidPacketFile.file_name,
          pages: bidPacketFile.getPages(this.pageQuery).map((page) => {
            return { pageNumber: page.pageNumber, content: page.content };
          }),
        };
      });

    const hasPages = pages.some((p) => p.pages.some((p) => p.pageNumber));
    if (!hasPages) {
      return `Pages not found. The pages available for each file are from page 1 till the total_pages_count: \n${JSON.stringify(this.getAvailablePages(), null, 2)}`;
    }

    return JSON.stringify(pages, null, 2);
  }
}
