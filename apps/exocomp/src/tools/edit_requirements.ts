import { Tool } from '@tribble/chat-completion-service/client';
import { Logger } from '@tribble/common-utils';
import { DB, getDB, TRX } from '@tribble/tribble-db/db_ops';
import { ConversationId } from '@tribble/tribble-db/types';
import { getConversationState } from '../conversations/conversation_state.ts';
import { ToolCall } from './tool_call.ts';

export class EditRequirements extends ToolCall {
  static tool_name = 'edit_requirements';
  conversationId: ConversationId;
  schema: string;

  requirement_index: string;
  new_requirement_text: string;
  operation: 'delete' | 'add' | 'edit';

  constructor(
    config: { schema: string; conversationId: ConversationId; logger?: Logger },
    args: {
      requirement_index: string;
      requirement_text: string;
      operation: 'delete' | 'add' | 'edit';
    },
  ) {
    super(config.logger);
    this.requirement_index = args.requirement_index;
    this.new_requirement_text = args.requirement_text;
    this.operation = args.operation;
    this.conversationId = config.conversationId;
    this.schema = config.schema;
  }

  static getPromptDescription(_: any): string {
    return `Use ${EditRequirements.tool_name} to edit requirements in the list.`;
  }

  static getPendingMessage() {
    return 'Doing stuff to requirements';
  }

  static getCompletionMessage() {
    return 'Done doing stuff to requirements';
  }

  static getJsonSchema(): Tool {
    return {
      type: 'function',
      function: {
        name: EditRequirements.tool_name,
        description: 'Make changes to the requirements list',
        parameters: {
          type: 'object',
          properties: {
            requirement_index: {
              type: 'string',
              description:
                'The requirement index to edit or delete, or where to insert a new requirement.',
            },
            requirement_text: {
              type: 'string',
              description: 'New text for the requirement',
            },
            operation: {
              type: 'string',
              description: 'Operation to perform on the requirement',
              enum: ['delete', 'add', 'edit'],
            },
          },
          required: ['requirement_index', 'operation'],
          additionalProperties: false,
        },
      },
    };
  }

  async performCall(): Promise<string> {
    if (!this.operation) {
      return JSON.stringify('Operation is required');
    }

    if (!this.requirement_index) {
      return JSON.stringify('Requirement index is required');
    }

    const db = await getDB(this.schema);
    try {
      return await db.transaction().execute(async (trx) => {
        const state = await getConversationState(
          this.schema,
          parseInt(this.conversationId),
        );
        const writerPacketIds = state.files?.[0]?.writer_packet_ids ?? [];
        if (!writerPacketIds.length) {
          return JSON.stringify(['No writer packets found']);
        }

        const helper = new WriterPacketHelper({
          schema: this.schema,
          writerPacketIds: writerPacketIds.map((id) => id.toString()),
        });
        await helper.init(trx);

        if (this.operation == 'edit') {
          const sectionToEdit = helper.writerPackets.find((packets) =>
            packets.requirements.find(
              (req) => req.requirement_id == parseInt(this.requirement_index),
            ),
          );
          if (!sectionToEdit) {
            return JSON.stringify([
              'Requirement not found',
              this.requirement_index,
            ]);
          }

          const requirements = sectionToEdit.requirements;
          const index = requirements.findIndex(
            (req) => req.requirement_id == parseInt(this.requirement_index),
          );
          requirements[index].requirement_text = this.new_requirement_text;

          await helper.updateWriterPackets();
          return helper.prettyPrintAsJsonString();
        }

        if (this.operation == 'delete') {
          const sectionToDelete = helper.writerPackets.find((packet) =>
            packet.requirements.find(
              (req) => req.requirement_id == parseInt(this.requirement_index),
            ),
          );
          if (!sectionToDelete) {
            return JSON.stringify([
              'Requirement not found',
              this.requirement_index,
            ]);
          }

          const requirements = sectionToDelete.requirements;
          const index = requirements.findIndex(
            (req) => req.requirement_id == parseInt(this.requirement_index),
          );
          requirements.splice(index, 1);

          await helper.updateWriterPackets();
          return helper.prettyPrintAsJsonString();
        }

        if (this.operation == 'add') {
          const sectionToAdd = helper.writerPackets.find((packet) =>
            packet.requirements.find(
              (req) => req.requirement_id == parseInt(this.requirement_index),
            ),
          );
          if (!sectionToAdd) {
            return JSON.stringify([
              'Requirement not found',
              this.requirement_index,
            ]);
          }

          const requirements = sectionToAdd.requirements;
          requirements.push({
            requirement_text: this.new_requirement_text,
            requirement_id: requirements.length + 1 + 1, // 1-indexed
          });

          await helper.updateWriterPackets();
          return helper.prettyPrintAsJsonString();
        }
      });
    } catch (err) {
      return JSON.stringify(['Something went wrong', err]);
    }
  }
}

interface WriterPacket {
  name: string;
  requirements: { requirement_text: string; requirement_id?: number }[];
  highlevelSummary?: string;
  writerPromptId?: number;
}

export class WriterPacketHelper {
  schema: string;
  writerPacketIds: string[];
  db: DB | TRX;

  writerPackets: WriterPacket[];

  constructor(args: { schema: string; writerPacketIds?: string[] }) {
    this.schema = args.schema;
    this.writerPacketIds = args.writerPacketIds;

    if (!this.schema) {
      throw new Error('Schema is required');
    }
  }

  async init(db?: DB | TRX) {
    if (db) {
      this.db = db;
    } else {
      this.db = await getDB(this.schema);
    }

    await this.refreshWriterPackets();
  }

  async refreshWriterPackets() {
    this.writerPackets = await this.fetchWriterPackets();
  }

  generateRequirementIndexes() {
    let i = 1;
    for (const section of this.writerPackets) {
      for (const req of section.requirements ?? []) {
        req.requirement_id = i++;
      }
    }
  }

  async updateWriterPackets() {
    for (const writerPacket of this.writerPackets) {
      const res = await this.db

        .updateTable('rfx_writer_prompt')
        .set({
          requirements: JSON.stringify(writerPacket.requirements),
        })
        .where('id', '=', writerPacket.writerPromptId as any)
        .returningAll()
        .execute();
    }

    await this.refreshWriterPackets();
  }

  async fetchWriterPackets(): Promise<WriterPacket[]> {
    const writerPackets = await this.db
      .selectFrom('rfx_writer_prompt')
      .innerJoin(
        'rfx_section_outline',
        'rfx_section_outline.id',
        'rfx_writer_prompt.rfx_section_outline_id',
      )
      .select([
        'rfx_writer_prompt.id',
        'rfx_writer_prompt.prompt',
        'rfx_writer_prompt.rfx_section_outline_id',
        'rfx_writer_prompt.requirements',
        'rfx_writer_prompt.research',
        'rfx_section_outline.seq as section_seq',
        'rfx_section_outline.name as section_name',
        'rfx_section_outline.content as section_description',
      ])
      .where('rfx_writer_prompt.id', 'in', this.writerPacketIds as any)
      .orderBy('section_seq', 'asc')
      .execute();

    return writerPackets.map((packet) => ({
      outlineSectionId: packet.rfx_section_outline_id,
      name: packet.section_name,
      description: packet.section_description,
      requirements: packet.requirements as { requirement_text: string }[],
      highlevelSummary: packet.prompt,
      writerPromptId: Number(packet.id),
    }));
  }

  prettyPrintAsJsonString() {
    return JSON.stringify(
      this.writerPackets.map((packet) => {
        return {
          name: packet.name,
          highlevelSummary: packet.highlevelSummary,
          requirements: packet.requirements.map((req) => {
            return { id: req.requirement_id, text: req.requirement_text };
          }),
        };
      }),
    );
  }
}
