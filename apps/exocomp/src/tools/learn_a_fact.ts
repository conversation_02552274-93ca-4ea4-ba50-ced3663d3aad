import { KnownBlock } from '@slack/bolt';
import { chatCompletion, Tool } from '@tribble/chat-completion-service/client';
import { Logger } from '@tribble/common-utils';
import { ResponseFormat } from '@tribble/types-shared';
import { ConversationMessage } from '../conversations/message.ts';
import { learnFactResponseBlocks } from '../views/learnAFactResponse.ts';
import { ToolCall } from './tool_call.ts';

export class LearnFact extends ToolCall {
  static tool_name = 'learn_a_fact';
  do_not_recurse = true;
  static has_completion_blocks = true;
  private data: string;
  private reWrittenData: string;
  private is_content_mod: boolean;

  private conversationMessages: ConversationMessage[];
  private conversationDetailId: number;
  private schema: string;
  private client_id: number;
  private user_id: number;
  private agentApiVersion: string;

  constructor(
    config: {
      is_content_mod: boolean;
      messages: ConversationMessage[];
      schema: string;
      client_id: number;
      user_id: number;
      agentApiVersion: string;
      conversationDetailId: number;
      logger?: Logger;
    },
    args: { data: string },
  ) {
    super(config.logger);
    this.schema = config.schema;
    this.conversationDetailId = config.conversationDetailId;
    this.client_id = config.client_id;
    this.user_id = config.user_id;
    this.conversationMessages = config.messages;
    this.agentApiVersion = config.agentApiVersion;
    this.is_content_mod = config.is_content_mod;
    this.data = args.data;
    this.content = `I will add this to the brain: """${this.data}"""`;
  }

  static getJsonSchema(): Tool {
    return {
      type: 'function',
      function: {
        name: 'learn_a_fact',
        description:
          'Add a new piece of information to the brain. Useful when given a correction or told to store something for later.',
        parameters: {
          type: 'object',
          properties: {
            data: {
              type: 'string',
              description:
                'A well crafted piece of information to add to the brain. Includes all the necessary context and details. For example if the new fact is an answer to a question, the question is included here as well.',
            },
          },
          required: ['data'],
        },
      },
    };
  }

  getPendingMessage(): string {
    return `_Formatting "${this.data}"_ :brain:`;
  }

  getCompletionMessage(): string {
    return `All set :white_check_mark:`;
  }

  getCompletionBlocks(args): KnownBlock[] {
    const {
      schema,
      client_id,
      conversation_detail_id,
      tool_call_id,
      metadata_filter_ids,
    } = args;

    // Use reWrittenData if available, otherwise fall back to data
    const factContent = this.reWrittenData || this.data;

    return learnFactResponseBlocks(
      factContent,
      schema,
      client_id,
      conversation_detail_id,
      tool_call_id,
      metadata_filter_ids,
      this.is_content_mod,
    );
  }

  private formatMessages(): string {
    return this.conversationMessages
      .filter((m) => m.role)
      .map(
        (m) =>
          `${m.role == 'user' ? 'User: ' : 'Digital Assistant: '}${m.content}`,
      )
      .join('\n');
  }

  async performCall(): Promise<string> {
    try {
      const systemMessage =
        `You are a helpful digital assistant. In a conversation, a user has asked you to add some information to ` +
        `our internal knowledge base. Respond with the new information in the provided JSON schema. Make sure it is clear and cogent. ` +
        `If this new fact is being added in response to a question, include the question in the provided schema, otherwise leave the question blank.\n` +
        `New Fact\n\n"""\n${this.data}\n"""\n\n` +
        `Here is the rest of the conversation:\n\n` +
        `"""\n` +
        `${this.formatMessages()}\n` +
        `"""`;
      const retries = 3;
      const completion = await chatCompletion(
        {
          apiVersion: this.agentApiVersion,
          resourceUrl: '/openai/deployments/tribble-gpt4/chat/completions',
          timeout: 120000,
          schema: this.schema,
          activityLogData: {
            source_id: this.conversationDetailId.toString(),
            source_table: 'conversation_detail',
            type: 'learn_a_fact',
            client_id: this.client_id,
            user_id: this.user_id,
          },
          request: {
            messages: [{ role: 'system', content: systemMessage }],
            model: 'gpt-4.1',
            response_format: responseFormat,
          },
        },
        retries,
      );

      const response = completion.response.choices[0].message;
      try {
        const parsed: { question?: string; new_fact: string } = JSON.parse(
          response.content as string,
        );
        if (parsed.question) {
          this.reWrittenData = `Question: ${parsed.question}\n\nAnswer: ${parsed.new_fact}`;
        } else {
          this.reWrittenData = parsed.new_fact || this.data;
        }
      } catch (e) {
        console.warn(
          `[LearnFact.performCall] Error parsing JSON: ${e.message}`,
        );
        this.reWrittenData = this.data;
      }
      return `I will add this to the brain: """${this.reWrittenData}"""`;
    } catch (err) {
      console.error(`[LearnFact.performCall] ${err.message}`);
      return `An error occured trying to learn that fact. You can try again or ask for help.`;
    }
  }
}

const responseFormat: ResponseFormat = {
  type: 'json_schema',
  json_schema: {
    name: 'new_fact',
    strict: true,
    schema: {
      type: 'object',
      properties: {
        question: {
          type: ['string', 'null'],
          description: 'The question that the new fact answers.',
        },
        new_fact: {
          type: 'string',
          description:
            'The new fact to be added to the brain. Includes all the necessary context and details.',
        },
      },
      required: ['question', 'new_fact'],
      additionalProperties: false,
    },
  },
};
