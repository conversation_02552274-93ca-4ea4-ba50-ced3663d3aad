import { chatCompletion, Tool } from '@tribble/chat-completion-service/client';
import { Logger } from '@tribble/common-utils';
import { ClientId, ConversationId, UserId } from '@tribble/tribble-db/types';
import { ResponseFormat } from '@tribble/types-shared';
import { BrainSearch } from './brain_search.js';
import { FindDocuments } from './find_documents.js';
import { ToolCall } from './tool_call.ts';
import { ConversationIntelligence } from './tribblytics/conversation_intelligence.ts';
import { RfxIntelligence } from './tribblytics/rfx_intelligence.ts';

export interface TimeFilter {
  startDate?: Date; // For date range filtering
  endDate?: Date; // For date range filtering
  timeRangeText?: string; // Original text description of time range
  sortOrder?: 'newest' | 'oldest' | 'not_applicable'; // For sorting by recency
}

/**
 * ExtractTemporal Tool
 *
 * How It Works
 *
 * 1. The `ExtractTemporal` tool extracts time constraints from user queries
 * 2. The time filter is saved to conversation state
 * 3. Subsequent tool calls, such as `BrainSearch`, automatically receive the timeFilter
 *
 * The tool parses natural language time expressions into structured time filters that can be used
 * by other tools. It handles:
 * - Date ranges ("last 3 months", "between January and March")
 * - Relative dates ("last week", "past year")
 * - Recency requirements ("most recent", "latest", "oldest")
 *
 * The extracted TimeFilter is used to scope document searches and other time-based operations
 * across the conversation.
 */
export class ExtractTemporal extends ToolCall {
  static tool_name = 'extract_temporal';

  private clientId: ClientId;
  private clientName: string;
  private conversationId: ConversationId;
  private tribbleUserId: UserId;
  private userQuery: string;
  private extractedTimeFilter: TimeFilter | null = null;
  private schema: string;
  private agentApiVersion: string;

  constructor(
    config: {
      clientId: ClientId;
      clientName: string;
      conversationId: ConversationId;
      tribbleUserId: UserId;
      schema: string;
      agentApiVersion: string;
      logger?: Logger;
    },
    args: {
      query: string;
    },
  ) {
    super(config.logger);

    this.clientId = config.clientId;
    this.clientName = config.clientName;
    this.conversationId = config.conversationId;
    this.tribbleUserId = config.tribbleUserId;
    this.userQuery = args.query;
    this.schema = config.schema;
    this.agentApiVersion = config.agentApiVersion;
  }

  static getSupportedTools(): string[] {
    return [
      BrainSearch.tool_name,
      FindDocuments.tool_name,
      ConversationIntelligence.tool_name,
      RfxIntelligence.tool_name,
    ];
  }

  static getPromptDescription(): string {
    return (
      `${this.tool_name}: Extracts time-related constraints from user queries, ` +
      'such as date ranges ("last 3 months", "between January and March", "created in March 2024") or ' +
      'recency requirements ("most recent", "latest", "oldest"). Returns the time constraints in a structured JSON format.' +
      'If a user query has time-related constraints, you MUST use this tool before running any other ' +
      `supported tools in the following list: ${this.getSupportedTools().join(', ')}.`
    );
  }

  static getJsonSchema(): Tool {
    return {
      type: 'function',
      function: {
        name: ExtractTemporal.tool_name,
        description:
          'Extract time-related constraints from a user query to filter results by time range or sort by recency.',
        strict: true,
        parameters: {
          type: 'object',
          properties: {
            query: {
              type: 'string',
              description:
                'The verbatim user query to be used to extract time constraints like "show me documents from the last 3 months" or "find the most recent marketing plan".',
            },
          },
          required: ['query'],
          additionalProperties: false,
        },
      },
    };
  }

  getTimeFilter(): TimeFilter | null {
    return this.extractedTimeFilter;
  }

  getPendingMessage(): string {
    return `_Analyzing time constraints in your query..._`;
  }

  getCompletionMessage(): string {
    if (!this.extractedTimeFilter) {
      return `No time constraints identified in query.`;
    }

    let message = 'Time constraints identified: ';

    if (this.extractedTimeFilter.timeRangeText) {
      message += `${this.extractedTimeFilter.timeRangeText}`;
    }

    const isFullDateMentioned = this.extractedTimeFilter.timeRangeText
      ? checkIfFullDateMentioned(this.extractedTimeFilter.timeRangeText)
      : false;

    if (
      this.extractedTimeFilter.startDate &&
      this.extractedTimeFilter.endDate &&
      !isFullDateMentioned
    ) {
      message += ` (from ${this.extractedTimeFilter.startDate.toLocaleDateString()} to ${this.extractedTimeFilter.endDate.toLocaleDateString()})`;
    } else if (this.extractedTimeFilter.startDate && !isFullDateMentioned) {
      message += ` (after ${this.extractedTimeFilter.startDate.toLocaleDateString()})`;
    } else if (this.extractedTimeFilter.endDate && !isFullDateMentioned) {
      message += ` (before ${this.extractedTimeFilter.endDate.toLocaleDateString()})`;
    } else if (
      this.extractedTimeFilter.sortOrder &&
      this.extractedTimeFilter.sortOrder !== 'not_applicable'
    ) {
      message += ` (sort by ${this.extractedTimeFilter.sortOrder})`;
    }

    return message;
  }

  async performCall(): Promise<string> {
    const extractionPrompt = `
      You are an AI assistant helping to extract time-related information from user queries.
      Based on the user's query, extract any time constraints such as:
      
      1. Date ranges (e.g., "last 3 months", "between January and March 2023")
      2. Recency requirements (e.g., "most recent", "latest", "oldest")
      3. Specific dates (e.g., "after June 2023", "before 2022")
      
      If no time constraints are present, indicate that none were found.

      IMPORTANT: You MUST use the current date as the reference point for  "today" or "now". Do not use your training data's cutoff date or any internal date references.
      The current date is ${new Date().toISOString().split('T')[0]}.
      
      User Query: "${this.userQuery}"
    `;

    try {
      const retries = 3;
      const completion = await chatCompletion(
        {
          apiVersion: this.agentApiVersion,
          resourceUrl: '/openai/deployments/tribble-gpt4/chat/completions',
          timeout: 120000,
          schema: this.schema,
          activityLogData: {
            source_id: this.conversationId.toString(),
            source_table: 'conversation_detail',
            type: 'extract_temporal',
            client_id: this.clientId,
            user_id: this.tribbleUserId,
          },
          request: {
            messages: [{ role: 'system', content: extractionPrompt }],
            model: process.env.TIME_EXTRACTION_MODEL || 'gpt-4.1-mini',
            temperature: 0,
            response_format: responseFormat,
          },
        },
        retries,
      );

      const response = completion.response.choices[0].message;
      try {
        const parsedResponse = JSON.parse(response.content as string);
        if (parsedResponse.hasTimeConstraint) {
          this.extractedTimeFilter = {
            ...(parsedResponse.timeRangeText && {
              timeRangeText: parsedResponse.timeRangeText,
            }),
            ...(parsedResponse.sortOrder && {
              sortOrder: parsedResponse.sortOrder,
            }),
            ...(parsedResponse.startDate && {
              startDate: new Date(parsedResponse.startDate),
            }),
            ...(parsedResponse.endDate && {
              endDate: new Date(parsedResponse.endDate),
            }),
          };
        }

        // The format of this response doesn't really matter; at the time of this commit
        //   Except for brainCitation extraction which needs it to be an array
        //   Although citation extraction is irrelevant here, the piping is processing all tool calls
        return JSON.stringify([this.extractedTimeFilter]);
      } catch (error) {
        console.error('Error parsing JSON:', error);
        return JSON.stringify([
          'Failed to extract time constraints from query.',
        ]);
      }
    } catch (error) {
      console.error('Error extracting time filter:', error);
      return JSON.stringify(['Failed to extract time constraints from query.']);
    }
  }
}

const responseFormat: ResponseFormat = {
  type: 'json_schema',
  json_schema: {
    name: 'time_filter',
    strict: true,
    schema: {
      type: 'object',
      properties: {
        hasTimeConstraint: {
          type: 'boolean',
          description: 'Whether the user query has a time constraint.',
        },
        timeRangeText: {
          type: 'string',
          description: 'The original phrase describing the time range.',
        },
        startDate: {
          type: ['string', 'null'],
          description:
            'The start date of the time range, if the user query has a time range constraint.' +
            'If time is not specified, set the very beginning of the day.',
        },
        endDate: {
          type: ['string', 'null'],
          description:
            'The end date of the time range, if the user query has a time range constraint. ' +
            'If time is not specified, set the very end of the day.',
        },
        sortOrder: {
          type: 'string',
          enum: ['newest', 'oldest', 'not_applicable'],
          description:
            'The sort order of the time constraint, if the user query request is to be sorted by time, such as to get the latest or oldest documents. ' +
            'This can be either "newest" or "oldest".' +
            'For example, if the user query is "show me the most recent document for X", the sort order should be "newest".' +
            'If the user query by nature does not require a sort order, this should be "not_applicable".',
        },
      },
      required: [
        'hasTimeConstraint',
        'timeRangeText',
        'startDate',
        'endDate',
        'sortOrder',
      ],
      additionalProperties: false,
    },
  },
};

function checkIfFullDateMentioned(timeRangeText: string): boolean {
  const lowerCaseText = timeRangeText.toLowerCase();

  // Common date formats with all three components (day, month, year)
  const dateFormatPatterns = [
    // Numeric formats (various separators)
    /\b\d{1,2}[-/\.]\d{1,2}[-/\.]\d{2,4}\b/, // DD/MM/YYYY or MM/DD/YYYY
    /\b\d{4}[-/\.]\d{1,2}[-/\.]\d{1,2}\b/, // YYYY/MM/DD (ISO format)

    // Text formats with month names
    /\b(january|february|march|april|may|june|july|august|september|october|november|december|jan|feb|mar|apr|jun|jul|aug|sep|oct|nov|dec)(\s+\d{1,2}(st|nd|rd|th)?)?([\s,]+)?\d{4}\b/i, // Month DD YYYY
    /\b\d{1,2}(st|nd|rd|th)?(\s+(of)?)?\s*(january|february|march|april|may|june|july|august|september|october|november|december|jan|feb|mar|apr|jun|jul|aug|sep|oct|nov|dec)([\s,]+)?\d{4}\b/i, // DD of Month YYYY

    // Common time expressions with all date components
    /\bon\s+\d{1,2}(st|nd|rd|th)?(\s+(of)?)?\s*(january|february|march|april|may|june|july|august|september|october|november|december|jan|feb|mar|apr|jun|jul|aug|sep|oct|nov|dec)([\s,]+)?\d{4}\b/i, // on DD of Month YYYY
    /\bfrom\s+\d{1,2}(st|nd|rd|th)?(\s+(of)?)?\s*(january|february|march|april|may|june|july|august|september|october|november|december|jan|feb|mar|apr|jun|jul|aug|sep|oct|nov|dec)([\s,]+)?\d{4}\b/i, // from DD of Month YYYY

    // Additional formats
    /\b\d{1,2}(st|nd|rd|th)?\s+(january|february|march|april|may|june|july|august|september|october|november|december|jan|feb|mar|apr|jun|jul|aug|sep|oct|nov|dec)\s+\d{4}\b/i, // DD Month YYYY
    /\b\d{4}\s+(january|february|march|april|may|june|july|august|september|october|november|december|jan|feb|mar|apr|jun|jul|aug|sep|oct|nov|dec)\s+\d{1,2}(st|nd|rd|th)?\b/i, // YYYY Month DD
  ];

  // Test all patterns against the input text
  return dateFormatPatterns.some((pattern) => pattern.test(lowerCaseText));
}
