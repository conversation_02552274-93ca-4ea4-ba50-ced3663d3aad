/**
 * Example handoff tool
 */

import { Tool } from '@tribble/chat-completion-service/client';
import { Cartridge as CartridgeEnum } from '@tribble/conversation-service';
import { ToolCall } from './tool_call.ts';

export class InvokeMeetingPrep extends Tool<PERSON>all {
  static tool_name = 'invoke_meeting_prep';
  is_handoff = true;
  agent: string;

  constructor(config: any, args: { agent: string }) {
    super();
    this.agent = args.agent;
  }

  static getPromptDescription(): string {
    return `${this.tool_name}: Hands off the conversation to a one specially designed for meeting prep. Do this when explicitly asked.`;
  }

  static getJsonSchema(allowedAgentsEnum: string[]): Tool {
    const schema = {
      type: 'function',
      function: {
        name: `${this.tool_name}`,
        description: `Hands off the conversation to meeting prep agent.`,
        parameters: {
          type: 'object',
          properties: {
            agent: {
              type: 'string',
              enum: allowedAgentsEnum,
              description: 'The agent to handoff to.',
            },
          },
          required: ['agent'],
        },
      },
    };

    return schema;
  }

  getPendingMessage(): string {
    return `Switching to ${this.agent}...`;
  }

  getCompletionMessage(): string {
    return `Engaging meeting prep mode... [BETA]`;
  }

  async performCall(): Promise<string> {
    return `Engaging meeting prep mode...`;
  }

  getTargetCartrigeForHandoff() {
    switch (this.agent) {
      case 'meeting_prep':
        return CartridgeEnum.MEETING_PREP;
      default:
        return null;
    }
  }
}
