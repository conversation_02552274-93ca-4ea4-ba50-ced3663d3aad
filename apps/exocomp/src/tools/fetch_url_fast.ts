import { Tool } from '@tribble/chat-completion-service/client';
import { Logger } from '@tribble/common-utils';
import fetch from 'node-fetch';
import { ActivityLogData } from '../conversation_service.ts';
import { FetchUrl } from './fetch_url.ts';
import { ToolCall } from './tool_call.ts';

export interface FetchUrlFastConfig {
  pureMDToken: string;
  logger?: Logger;
  //For now, let's have a fallback to the old fetch_url
  fallBackTool: {
    toolClass: FetchUrl;
    config: {
      schema: string;
      activityLogData: ActivityLogData;
      helperFuncKey: string;
      scrapeWebPageEndpoint: string;
      pageSummaryLength: number;
      pageSummaryModel: string;
      pageSummaryTemp: number;
      logger?: Logger;
    };
  };
}

export class FetchUrlFast extends ToolCall {
  static tool_name = 'fetch_url';
  private url: string;
  private pureMDToken: string;
  private fallBackTool: FetchUrl;

  constructor(config: FetchUrlFastConfig, args: { url: string }) {
    super(config.logger);
    this.url = args.url;
    this.pureMDToken = config.pureMDToken;
    this.fallBackTool = new FetchUrl(config.fallBackTool.config, args);
  }

  static getPromptDescription(): string {
    return '';
  }

  static getJsonSchema(): Tool {
    return {
      type: 'function',
      function: {
        name: 'fetch_url',
        description: 'Fetch text content from the provided URL.',
        parameters: {
          type: 'object',
          properties: {
            url: {
              type: 'string',
              description:
                'A complete URL from which to fetch text content for analysis. Must be a valid URL.',
            },
          },
          required: ['url'],
        },
      },
    };
  }

  getPendingMessage(): string {
    return `_Retrieving content from <${this.url}|${this.url}>..._ :articulated_lorry:`;
  }

  getCompletionMessage(): string {
    return `Retrieved content from <${this.url}|website> :newspaper:`;
  }

  async performCall(): Promise<string> {
    const pureUrl = `https://pure.md/${this.url}`;

    try {
      const resp = await fetch(pureUrl, {
        method: 'GET',
        headers: {
          'x-puremd-api-token': this.pureMDToken,
          'Content-Type': 'application/json',
          Accept: 'text/plain',
        },
      });

      if (!resp.ok) {
        throw new Error(`Error: ${resp.status} ${resp.statusText}`);
      }
      return await resp.text();
    } catch (error) {
      if (error instanceof Error) {
        console.warn('(First) Error fetching URL:', error);
      }
      console.warn('Falling back to old fetch_url tool');
      try {
        //Fallback!
        return await this.fallBackTool.performCall();
      } catch (secondError) {
        if (secondError instanceof Error) {
          console.error('(Second) Error fetching URL:', error);
          console.error('Failing permanently');
          return error.toString();
        }
      }
      return 'Unknown error occurred while fetching URL.';
    }
  }
}
