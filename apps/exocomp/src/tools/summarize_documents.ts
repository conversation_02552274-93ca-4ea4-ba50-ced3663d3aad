import { Tool } from '@tribble/chat-completion-service/client';
import { Logger } from '@tribble/common-utils';
import { DocumentSummary, DocumentSummaryResult } from '../summary.ts';
import { FindDocuments } from './find_documents.ts';
import { ToolCall } from './tool_call.ts';

export class SummarizeDocuments extends ToolCall {
  static tool_name = 'summarize_documents';
  private schema: string;
  private document_ids: string[];

  constructor(
    config: {
      schema: string;
      logger?: Logger;
    },
    args: { document_ids: string[] },
  ) {
    super(config.logger);
    this.schema = config.schema;
    this.document_ids = args.document_ids;
  }

  static getPromptDescription(): string {
    return (
      `${this.tool_name}: Summarizes specific documents. ` +
      `This tool uses document IDs returned from the ${FindDocuments.tool_name} tool.`
    );
  }

  static getJsonSchema(): Tool {
    const schema = {
      type: 'function',
      function: {
        name: `${this.tool_name}`,
        description: `Search for information in specific documents using the document IDs returned from the ${FindDocuments.tool_name} tool call.`,
        parameters: {
          type: 'object',
          properties: {
            document_ids: {
              type: 'array',
              description: 'IDs of the documents to summarize.',
              items: {
                type: 'string',
                description: 'The ID of the document to summarize.',
              },
            },
          },
          required: ['document_ids'],
        },
      },
    };

    return schema;
  }

  getPendingMessage(): string {
    return `_Summarizing documents..._ :memo:`;
  }

  getCompletionMessage(): string {
    return `Summarized documents :memo:`;
  }

  async performCall(): Promise<string> {
    if (!this.document_ids || this.document_ids.length === 0) {
      return 'Invalid document IDs. Please provide at least one document ID.';
    }

    const summaries = await DocumentSummary({
      schema: this.schema,
      document_ids: this.document_ids,
    });

    if (summaries && summaries.length > 0) {
      return JSON.stringify(summaries);
    }

    return 'No summaries found.';
  }
}
