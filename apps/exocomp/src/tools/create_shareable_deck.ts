import { Tool } from '@tribble/chat-completion-service/client';
import { v4 as uuidv4 } from 'uuid';
import { ToolCall } from './tool_call.ts';

import { ConversationId } from '@tribble/tribble-db/types';
import { CreateImageFromPromptTool } from './create_image_from_prompt.ts';
import { CreatePptxFromImageTool } from './create_pptx_from_image.ts';

interface CreateShareableDeckResponse {
  success: boolean;
  id: string;
  id_hash: string;
  error?: string;
}

export class CreateShareableDeckTool extends ToolCall {
  static tool_name = 'create_shareable_deck';

  private helpersUrl: string;
  private conversationId: ConversationId;
  private clientId: string;
  private userId: string;
  private databaseId: string;
  private helperFuncKey?: string;

  private imageIds: string[];
  private title: string;
  private description: string;

  constructor(
    config: {
      helpersUrl?: string;
      helperFuncKey?: string;
      conversationId: ConversationId;
      clientId: string;
      userId: string;
      databaseId: string;
    },
    args: {
      image_ids: string[];
      title: string;
      description: string;
    },
  ) {
    super();
    this.imageIds = args.image_ids;
    this.title = args.title;
    this.description = args.description;

    this.helpersUrl = config.helpersUrl;
    this.conversationId = config.conversationId;
    this.clientId = config.clientId;
    this.userId = config.userId;
    this.databaseId = config.databaseId;
    this.helperFuncKey = config.helperFuncKey;
  }

  static getPromptDescription(): string {
    return (
      `${this.tool_name}: This tool creates a public, shareable HTML presentation from slide images. ` +
      `This tool can only be called after the user has created one or more slide images, via the ${CreateImageFromPromptTool.tool_name} tool. ` +
      `After generating the slide images, ask the user if they would like to create a public, shareable HTML presentation from the slide images. Make it very clear to the user that this is a PUBLICLY ACCESSIBLE URL. If they do want to proceed, call this tool.`
    );
  }

  static getJsonSchema(): Tool {
    return {
      type: 'function',
      function: {
        name: 'create_shareable_deck',
        description: `Call this tool whenever the user asks for a public, shareable HTML presentation from slide images.
        
        This tool creates a public, shareable HTML presentation from generated slide images (latter created via the ${CreateImageFromPromptTool.tool_name} tool.) 

        Whenever you finish one turn generating a slide deck image(s) with the user, ask the user if they would like to create a public, shareable HTML presentation from the slide images. If they do, call this tool, passing in the image Ids of the slide images generated.`,
        parameters: {
          type: 'object',
          properties: {
            image_ids: {
              type: 'array',
              description:
                'The IDs of the images to use as inputs for generating the PPTX file.',
              items: {
                type: 'string',
              },
            },
            title: {
              type: 'string',
              description:
                "A short title (8-10 words max) for the presentation, based on the user's request and the content of the slide images.",
            },
            description: {
              type: 'string',
              description:
                "A succinct 1-sentence description of the presentation, based on the user's request and the content of the slide images.",
            },
          },
          required: ['image_ids', 'description'],
        },
      },
    };
  }

  static getAdditionalInstructions(): string {
    return '';
  }

  static disableCache(): boolean {
    return true;
  }

  getPendingMessage(): string {
    return `_Creating public, shareable HTML presentation from slide images_ :memo:`;
  }

  getCompletionMessage(): string {
    return `Generated shareable HTML presentation :art:`;
  }

  async performCall(): Promise<string> {
    const correlationId = uuidv4();

    let pptxRequestBody = {
      client_id: this.clientId,
      user_id: this.userId,
      schema: this.databaseId,
      image_ids: this.imageIds,
      title: this.title,
      description: this.description,
      conversation_id: this.conversationId,
    };

    try {
      console.log(
        `(${correlationId}) [create_shareable_deck] Sending request to slide-images-to-public-html`,
      );

      const pptxResponse = await fetch(
        `${this.helpersUrl}/api/slide-images-to-public-html`,
        {
          method: 'POST',
          body: JSON.stringify(pptxRequestBody),
          headers: {
            ...(this.helperFuncKey && {
              'x-functions-key': this.helperFuncKey,
            }),
          },
        },
      );

      const result = (await pptxResponse.json()) as CreateShareableDeckResponse;

      if (result.success && result.id) {
        const urlPrefix =
          process.env.ENVIRONMENT === 'development'
            ? 'http://localhost:3000'
            : process.env.APP_PATH;

        const suffixHash = result.id_hash;
        const publicUrl = `${urlPrefix}/share/${this.clientId}/${result.id}-${suffixHash}`;

        console.log(
          `(${correlationId}) [create_shareable_deck] Created the following public, shareable HTML presentation with the URL: ${publicUrl}`,
        );

        return `I created a public, shareable HTML presentation from the slide images:
        
        [View Presentation](${publicUrl}). YOU MUST include this link PRECISELY in your message back to the user.
        
        Remind the user that this is a PUBLICLY accessible URL, and that they can manage their public presentations in the Tribble Admin Console.`;
      } else if (!result.success) {
        console.error(
          '(${correlationId}) [create_shareable_deck] Public HTML generation failed:',
          result.error,
        );
        return 'There was an error creating the public, shareable HTML presentation. Please try again.';
      }
    } catch (err) {
      console.error(
        `(${correlationId}) [create_shareable_deck] Error calling slide-images-to-public-html:`,
        err,
      );
      return 'There was an error creating the public, shareable HTML presentation. Please try again.';
    }
  }
}
