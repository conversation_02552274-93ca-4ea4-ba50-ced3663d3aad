import { Tool } from '@tribble/chat-completion-service/client';
import {
  GongCallExtensiveApiResponse,
  initializeGongClient,
} from '@tribble/gong-service';
import { getDB } from '@tribble/tribble-db/db_ops';
import { UserId } from '@tribble/tribble-db/tribble/User';
import { SalesforceQuery } from './salesforce_query.ts';
import { ToolCall } from './tool_call.ts';

interface MeetingSummary {
  meeting_title?: string;
  date: string;
  duration_seconds: number;
  internal_participants: {
    name: string;
    title?: string;
    email?: string;
  }[];
  external_participants: {
    name: string;
    title?: string;
    email?: string;
    salesforceContactId?: string;
  }[];
  summary: string;
  keypoints?: string[];
  highlights?: {
    title: string;
    items: string[];
  }[];
}

export class GetMeetingSummaries extends ToolCall {
  static tool_name = 'get_meeting_summaries';
  private schema: string;
  private salesforce_opportunity_id: string;
  private salesforce_account_id: string;
  private last_n: number = 3;

  private summaries: MeetingSummary[] = [];
  private tribbleUserId: string;

  constructor(
    config: { schema: string; tribbleUserId: string },
    args: {
      salesforce_opportunity_id: string;
      salesforce_account_id: string;
      last_n: number;
    },
  ) {
    super();
    this.schema = config.schema;
    this.salesforce_opportunity_id = args.salesforce_opportunity_id?.replaceAll(
      'salesforce_',
      '',
    );
    this.salesforce_account_id = args.salesforce_account_id?.replaceAll(
      'salesforce_',
      '',
    );
    this.last_n = args.last_n;
    this.tribbleUserId = config.tribbleUserId;
  }

  static getJsonSchema(): Tool {
    return {
      type: 'function',
      function: {
        name: GetMeetingSummaries.tool_name,
        description:
          'Get the last N meeting summaries for a given opportunity OR account. You must supply at least one of the two.',
        parameters: {
          type: 'object',
          properties: {
            salesforce_opportunity_id: {
              type: 'string',
              description: `The 18 character Salesforce opportunity ID to get meeting summaries for. Can be fetched using the ${SalesforceQuery.tool_name} tool.`,
            },
            salesforce_account_id: {
              type: 'string',
              description: `The 18 character Salesforce account ID to get meeting summaries for. Can be fetched using the ${SalesforceQuery.tool_name} tool.`,
            },
            last_n: {
              type: 'number',
              description:
                'Optional - The number of meeting summaries to retrieve. Defaults to 3.',
            },
          },
        },
      },
    };
  }

  getPendingMessage(): string {
    return `Fetching meeting summaries...`;
  }

  getCompletionMessage(): string {
    return `Fetched meeting summaries... :memo:`;
  }

  static getPromptDescription(): string {
    return `You have access to the ${this.tool_name} tool, which returns the last N Gong meeting summaries for a given Salesforce opportunity or account. You must provide at least one of these. You can use the ${SalesforceQuery.tool_name} tool to help find the right records, but always confirm your choice with the user. Use this tool when the user asks about customer status, meeting summaries, or Gong call recaps.`;
  }

  async performCall(): Promise<string> {
    if (!this.salesforce_opportunity_id && !this.salesforce_account_id) {
      return JSON.stringify([
        `You must supply at least one of: salesforce_opportunity_id or salesforce_account_id.`,
      ]);
    }

    const gongClient = await initializeGongClient(this.schema);
    await gongClient.ensureValidToken(Number(this.tribbleUserId) as UserId);

    if (!gongClient) {
      return JSON.stringify([
        `Failed to connect to Gong. Double check that your Gong credentials are correct.`,
      ]);
    }

    if (!(await gongClient.hasValidConnection())) {
      return JSON.stringify([
        `Failed to connect to Gong. Your credentials may have expired.`,
      ]);
    }

    if (!gongClient.isExtensiveApiEnabled) {
      return JSON.stringify([
        `The user must re-authorize Gong in the Tribble admin console to use this tool. Please contact your admin.`,
      ]);
    }

    const db = await getDB(this.schema);

    let gongCalls: GongCallExtensiveApiResponse['calls'];
    if (this.salesforce_opportunity_id) {
      gongCalls = await gongClient.getLastNCallsForOppty(
        db,
        this.salesforce_opportunity_id,
        this.last_n,
      );
    }

    if (!this.salesforce_opportunity_id || !gongCalls?.length) {
      gongCalls = await gongClient.getLastNCallsForAccount(
        db,
        this.salesforce_account_id,
        this.last_n,
      );
    }

    if (!gongCalls?.length) {
      return JSON.stringify([
        `No meeting summaries found for the given opportunity or account id. Double check the IDs`,
      ]);
    }

    this.summaries = gongCalls.map((call) => {
      const internalParticipants: MeetingSummary['internal_participants'] =
        call.parties
          ?.filter((p) => p.affiliation === 'Internal')
          .map((p) => {
            return {
              name: p.name,
              title: p.title,
              email: p.emailAddress,
            };
          }) || [];

      const externalParticipants: MeetingSummary['external_participants'] =
        call.parties
          ?.filter((p) => p.affiliation === 'External')
          .map((p) => {
            const contactId = p.context
              ?.find((c) => c.system === 'Salesforce')
              ?.objects?.find((o) => o.objectType === 'Contact')?.objectId;

            return {
              name: p.name,
              title: p.title,
              email: p.emailAddress,
              ...(contactId ? { salesforceContactId: contactId } : {}),
            };
          }) || [];

      const meetingTitle = call.metaData.title;
      const brief = call.content?.brief;
      const highlights: MeetingSummary['highlights'] =
        call.content?.highlights?.map((h) => {
          return {
            title: h.title,
            items: h.items?.map((i) => i.text) || [],
          };
        });
      const keypoints: MeetingSummary['keypoints'] =
        call.content?.keyPoints?.map((k) => k.text);

      return {
        meeting_title: meetingTitle,
        date: call.metaData.started,
        duration_seconds: call.metaData.duration,
        internal_participants: internalParticipants,
        external_participants: externalParticipants,
        summary: brief,
        ...(highlights ? { highlights } : {}),
        ...(keypoints ? { keypoints } : {}),
      };
    });

    return JSON.stringify(this.summaries.map(formatSummary));
  }
}

function formatSummary(summary: MeetingSummary): string {
  // Format date to be more readable
  const meetingDate = new Date(summary.date);
  const formattedDate = meetingDate.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  // Format duration from seconds to minutes and seconds
  const minutes = Math.floor(summary.duration_seconds / 60);
  const seconds = summary.duration_seconds % 60;
  const formattedDuration = `${minutes} minutes, ${seconds} seconds`;

  // Format participants
  const formatParticipant = (participant: {
    name: string;
    title?: string;
    email?: string;
  }) => {
    let result = participant.name;
    if (participant.title) result += ` (${participant.title})`;
    if (participant.email) result += ` - ${participant.email}`;
    return result;
  };

  const internalParticipantsList = summary.internal_participants
    .map((p) => `    • ${formatParticipant(p)}`)
    .join('\n');

  const externalParticipantsList = summary.external_participants
    .map((p) => {
      let text = `    • ${formatParticipant(p)}`;
      if (p.salesforceContactId) {
        text += ` [Contact ID: ${p.salesforceContactId}]`;
      }
      return text;
    })
    .join('\n');

  // Format highlights if available
  let highlightsText = '';
  if (summary.highlights && summary.highlights.length > 0) {
    highlightsText = '\n\n## Highlights\n';
    summary.highlights.forEach((highlight) => {
      highlightsText += `\n### ${highlight.title}\n`;
      highlight.items.forEach((item) => {
        highlightsText += `  • ${item}\n`;
      });
    });
  }

  // Format key points if available
  let keypointsText = '';
  if (summary.keypoints && summary.keypoints.length > 0) {
    keypointsText = '\n\n## Key Points\n';
    summary.keypoints.forEach((point) => {
      keypointsText += `  • ${point}\n`;
    });
  }

  // Build the complete formatted summary
  const formattedSummary = `# ${summary.meeting_title || 'Meeting Summary'}
  
  ## Meeting Details
  • Date: ${formattedDate}
  • Duration: ${formattedDuration}
  
  ## Internal Participants
  ${internalParticipantsList || '    • None'}
  
  ## External Participants
  ${externalParticipantsList || '    • None'}
  
  ## Summary
  ${summary.summary || 'No summary available.'}${keypointsText}${highlightsText}
  
  ----------------------------------------
  `;

  return formattedSummary;
}
