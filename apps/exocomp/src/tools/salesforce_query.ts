import { Tool } from '@tribble/chat-completion-service/client';
import { LOG_LEVEL, LOG_TOGGLE_NAME, Logger } from '@tribble/common-utils';
import { Filter } from '@tribble/salesforce';
import { UserId } from '@tribble/tribble-db/types';
import { Connection, QueryResult, Record } from 'jsforce';
import { ResolvedSettings } from '../resolved_settings.ts';
import { ExtractCitationFormat, ToolCall } from './tool_call.ts';

const filterersCache = new Map<string, Filter.GenericIntelligentFilter>();

export class SalesforceQuery extends ToolCall {
  static tool_name = 'salesforce_query';
  private query: string;
  private conn: Connection;
  private schema: string;
  private settings: ResolvedSettings;
  private tribbleUserId?: UserId;
  static citationIdPrefix = 'salesforce_';

  constructor(
    config: {
      conn: Connection;
      schema: string;
      settings: ResolvedSettings;
      tribbleUserId?: UserId;
      logger?: Logger;
    },
    args: { soql_query: string },
  ) {
    super(config.logger);
    this.query = args.soql_query;
    this.conn = config.conn;
    this.schema = config.schema;
    this.settings = config.settings;
    this.tribbleUserId = config.tribbleUserId;
  }

  static getJsonSchema(): Tool {
    return {
      type: 'function',
      function: {
        name: 'salesforce_query',
        description: 'Search known Salesforce objects using SOQL.',
        parameters: {
          type: 'object',
          properties: {
            soql_query: {
              type: 'string',
              description:
                'A perfectly formatted SOQL (salesforce object query language) string that can be run in Salesforce.',
            },
          },
          required: ['soql_query'],
        },
      },
    };
  }

  getPendingMessage(): string {
    return 'Searching Salesforce... :cloud:';
  }

  getCompletionMessage(): string {
    return `Searched Salesforce... :partly_sunny:`;
  }

  static getPromptDescription(): string {
    return `You also have access to salesforce query tools and describe tools. In cases where the query fails, use the describe tool to figure out why, and possibly retry.`;
  }

  async performCall(): Promise<string> {
    const startTime = Date.now();

    this.logger?.log(
      'Starting Salesforce query execution',
      LOG_TOGGLE_NAME.SF_QUERY,
      LOG_LEVEL.INFO,
      {
        originalQuery: this.query,
        schema: this.schema,
        userId: this.tribbleUserId,
      },
      this.schema,
    );

    try {
      let finalQuery = this.query;
      if (this.schema) {
        if (!filterersCache.has(this.schema)) {
          filterersCache.set(
            this.schema,
            new Filter.GenericIntelligentFilter(),
          );
          this.logger?.log(
            'Created new intelligent filter for schema',
            LOG_TOGGLE_NAME.SF_QUERY,
            LOG_LEVEL.DEBUG,
            { schema: this.schema, userId: this.tribbleUserId },
            this.schema,
          );
        }
        const filterer = filterersCache.get(this.schema)!;
        filterer.setFilterConfig(this.settings.salesforceFilterConfig);

        this.logger?.log(
          'Applying intelligent filtering to query',
          LOG_TOGGLE_NAME.SF_QUERY,
          LOG_LEVEL.DEBUG,
          {
            originalQuery: this.query,
            filterConfig: this.settings.salesforceFilterConfig,
            userId: this.tribbleUserId,
          },
          this.schema,
        );

        finalQuery = await filterer.filterQueryIntelligently(
          this.conn,
          this.query,
        );

        if (finalQuery !== this.query) {
          this.logger?.log(
            'Query was modified by intelligent filtering',
            LOG_TOGGLE_NAME.SF_QUERY,
            LOG_LEVEL.INFO,
            {
              originalQuery: this.query,
              filteredQuery: finalQuery,
              userId: this.tribbleUserId,
            },
            this.schema,
          );
        }
      }

      this.logger?.log(
        'Executing Salesforce query',
        LOG_TOGGLE_NAME.SF_QUERY,
        LOG_LEVEL.INFO,
        { finalQuery, userId: this.tribbleUserId },
        this.schema,
      );

      const res = await this.conn.query(finalQuery);
      const executionTime = Date.now() - startTime;

      this.logger?.log(
        'Salesforce query completed successfully',
        LOG_TOGGLE_NAME.SF_QUERY,
        LOG_LEVEL.INFO,
        {
          recordCount: res.totalSize,
          executionTimeMs: executionTime,
          done: res.done,
          nextRecordsUrl: res.nextRecordsUrl ? 'present' : 'none',
          userId: this.tribbleUserId,
        },
        this.schema,
      );

      // Log API usage if available
      const apiUsage = this.conn.limitInfo?.apiUsage;
      if (apiUsage) {
        const percent = ((apiUsage.used ?? 0) / (apiUsage.limit ?? 1)) * 100;
        this.logger?.log(
          'Salesforce API usage updated',
          LOG_TOGGLE_NAME.SF_QUERY,
          LOG_LEVEL.DEBUG,
          {
            apiUsage: {
              used: apiUsage.used,
              limit: apiUsage.limit,
              percentUsed: Math.round(percent * 100) / 100,
            },
            userId: this.tribbleUserId,
          },
          this.schema,
        );
      }

      return this.recordsToSourceContext(res);
    } catch (err) {
      const executionTime = Date.now() - startTime;

      this.logger?.log(
        'Salesforce query failed',
        LOG_TOGGLE_NAME.SF_QUERY,
        LOG_LEVEL.ERROR,
        {
          error: err.toString(),
          errorName: err.name,
          errorCode: err.errorCode,
          executionTimeMs: executionTime,
          query: this.query,
          userId: this.tribbleUserId,
        },
        this.schema,
      );

      return err.toString();
    }
  }

  recordsToSourceContext(queryResult: QueryResult<Record>): string {
    if (!queryResult) {
      this.logger?.log(
        'No query result to process',
        LOG_TOGGLE_NAME.SF_QUERY,
        LOG_LEVEL.WARN,
        { userId: this.tribbleUserId },
        this.schema,
      );
      return JSON.stringify('No results found.');
    }

    if (!queryResult.records || !queryResult.records.length) {
      this.logger?.log(
        'Query returned no records',
        LOG_TOGGLE_NAME.SF_QUERY,
        LOG_LEVEL.INFO,
        { totalSize: queryResult.totalSize, userId: this.tribbleUserId },
        this.schema,
      );
      return JSON.stringify(queryResult || 'No results found.');
    }

    this.logger?.log(
      'Processing query results',
      LOG_TOGGLE_NAME.SF_QUERY,
      LOG_LEVEL.DEBUG,
      {
        recordCount: queryResult.records.length,
        totalSize: queryResult.totalSize,
        done: queryResult.done,
        userId: this.tribbleUserId,
      },
      this.schema,
    );

    let records = queryResult.records.map((record, i) => {
      //record id is provided if it was in the SOQL query,
      //otherwise it's the last value in the url after the last slash
      let recordId =
        record.Id || record.attributes?.url?.split('/').pop() || `${i}`;

      record.Id = SalesforceQuery.citationIdPrefix + recordId;
      if (recordId.length > 10) {
        //It's a salesforce id. Affix a nice url for the llm to use
        record.url = this.conn.instanceUrl + '/' + recordId;
      }

      // Remove attributes to make the record more readable to the LLM.
      if (record.attributes) {
        delete record.attributes;
      }

      return record;
    });

    const result = JSON.stringify({
      totalSize: queryResult.totalSize,
      records,
    });

    this.logger?.log(
      'Query results processed successfully',
      LOG_TOGGLE_NAME.SF_QUERY,
      LOG_LEVEL.DEBUG,
      {
        processedRecords: records.length,
        resultSizeBytes: result.length,
        userId: this.tribbleUserId,
      },
      this.schema,
    );

    return result;
  }

  // //Copying the logic found in web_search
  static extractCitations(answer: string): ExtractCitationFormat {
    const usedSalesforceCitations: ExtractCitationFormat['citations'] = [];
    const sf_search_RE = new RegExp(
      `\\^${SalesforceQuery.citationIdPrefix}[a-zA-Z0-9]+`,
      'g',
    );
    const sf_search_match = answer.match(sf_search_RE);
    if (sf_search_match != null) {
      sf_search_match.forEach((citation, i) => {
        const replaced = citation.replace(
          `^${SalesforceQuery.citationIdPrefix}`,
          '',
        );
        usedSalesforceCitations.push({
          id: i,
          text: replaced,
        });

        answer = answer.replace(citation, '');
      });
    }

    return {
      citations: usedSalesforceCitations,
      updatedAnswer: answer,
    };
  }
}
