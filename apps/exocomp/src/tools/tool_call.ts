import { KnownBlock } from '@slack/bolt';
import { Tool } from '@tribble/chat-completion-service/client';
import { Logger } from '@tribble/common-utils';
import { Cartridge as CartridgeEnum } from '@tribble/conversation-service';
import { Filter } from '@tribble/salesforce';
import { ConversationSourceFormat } from '../conversations/conversation_context.ts';
import { ConversationMessage } from '../conversations/message.ts';

export interface ExtractCitationFormat {
  citations: Partial<ConversationSourceFormat>[];
  updatedAnswer: string;
}

export abstract class ToolCall {
  do_not_recurse: boolean;
  is_handoff: boolean = false;
  protected content: string | null;
  protected args: any;
  protected logger: Logger;

  constructor(logger?: Logger) {
    this.content = null;
    this.logger = logger;
  }

  abstract performCall(args?: {
    schema?: string;
    salesforceFilterConfig?: Filter.FilterConfig;
  }): Promise<string>;

  getPendingMessage(): string {
    return 'Your request is being processed ⏳';
  }

  getCompletionMessage(): string {
    return 'Your request has been processed  ✅';
  }

  getCompletionBlocks?(args: any): KnownBlock[];

  static getJsonSchema(_: any): Tool {
    return {
      type: 'function',
      function: {
        name: '',
      },
    };
  }

  // Each tool should implement this
  static extractCitations(
    answer: string,
    messages: ConversationMessage[],
    initialContexts?: ConversationSourceFormat[],
  ): ExtractCitationFormat {
    return null;
  }

  static getPromptDescription(_: any): string {
    return '';
  }

  getTargetCartrigeForHandoff(): CartridgeEnum {
    // Only gets implemented in a handoff tool.
    return null;
  }
}
