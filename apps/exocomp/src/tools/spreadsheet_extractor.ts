import { Tool } from '@tribble/chat-completion-service/client';
import { JsonSheet } from '@tribble/file-helper';
import { ToolCall } from './tool_call.ts';

export class SpreadsheetExtractor extends ToolCall {
  static tool_name = 'spreadsheet_extractor';
  private spreadSheet: JsonSheet[];
  private cells: string[];
  private sheetName: string;

  constructor(
    config: { jsonData: JsonSheet[] },
    args: { sheetName: string; cells: string[] },
  ) {
    super();
    this.spreadSheet = config.jsonData;
    this.cells = args.cells;
    this.sheetName = args.sheetName;
  }

  static getJsonSchema(): Tool {
    return {
      type: 'function',
      function: {
        name: 'spreadsheet_extractor',
        description: 'Extract rows and cells from a spreadsheet.',
        parameters: {
          type: 'object',
          properties: {
            sheetName: {
              type: 'string',
              description: 'The name of the sheet to extract data from.',
            },
            cells: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  address: {
                    type: 'string',
                    description:
                      'The address of the cell to extract data from.',
                  },
                  row: {
                    type: 'integer',
                    description: 'The row number to extract data from.',
                  },
                  column: {
                    type: 'integer',
                    description: 'The column number to extract data from.',
                  },
                },
                required: ['address'],
              },
              description: 'The rows and columns to extract data from.',
            },
          },
          required: ['sheetName', 'cells'],
        },
      },
    };
  }

  performCall = () => {
    const sheet = this.spreadSheet.find(
      (sheet) => sheet.sheetName === this.sheetName,
    );
    if (!sheet) {
      return Promise.resolve(
        'The sheet you requested does not exist in the spreadsheet.',
      );
    }

    const results = sheet.cells.filter(
      (c) =>
        this.cells.includes(c.address) ||
        (this.cells.includes(c.row.toString()) &&
          this.cells.includes(c.column.toString())),
    );

    return Promise.resolve(JSON.stringify(results));
  };
}
