import { Tool } from '@tribble/chat-completion-service/client';
import { ClientSchema } from '@tribble/tribble-db';
import { ClientId, ConversationId, UserId } from '@tribble/tribble-db/types';
import {
  ContextSearch,
  Filter,
  FilteredContextSearch,
  PlatformMapping,
} from '../brain.ts';
import {
  ConversationContext,
  ConversationSourceFormat,
} from '../conversations/conversation_context.ts';
import { ConversationMessage } from '../conversations/message.ts';
import {
  getMetadataFilterRecords,
  MetadataFilterValue,
} from '../metadata_filters.ts';
import { ResolvedSettings } from '../resolved_settings.ts';
import { telemetryClient } from '../telemetry.ts';
import {
  processAnswerCitations,
  reorderCitations,
} from '../views/answer_citations.ts';
import { BaseSearchTool } from './base_search_tool.ts';
import { TimeFilter } from './extract_temporal.ts';
import { FindDocuments, SearchCustomTables } from './index.ts';
import { ExtractCitationFormat } from './tool_call.ts';

const SpreadsheetRegex = /spreadsheets?/gi;
export class BrainSearch extends BaseSearchTool {
  static tool_name = 'brain_search';

  constructor(
    config: {
      clientId: ClientId;
      clientName: string;
      conversationId: ConversationId;
      metadataFilters: MetadataFilterValue[];
      schema: string;
      tribbleUserId: UserId;
      includeVerbatim?: boolean;
      includeImages?: boolean;
      timeFilter?: TimeFilter;
      settings: ResolvedSettings;
    },
    args: {
      query: string;
      filters?: Filter[];
      imageInfos?: Array<{ dataUrl: string; mimetype: string }>;
    },
  ) {
    super(config, args);
  }

  static getPromptDescription(params: any): string {
    const clientName = params.clientName || 'our company';
    return (
      `${this.tool_name}: Searches ` +
      `${clientName}'s knowledge base to retrieve relevant context based on a query. ` +
      `Do not use this tool to locate an entire document by name or type. ` +
      `Instead, use the ${FindDocuments.tool_name} tool for that.`
    );
  }

  static getJsonSchema(
    suppliedSourceTypes: any,
    enableExtraFilters = true, // Flag to disable filters for unavailable platforms
  ): Tool {
    const sourceTypes = suppliedSourceTypes || [];

    return {
      type: 'function',
      function: {
        name: 'brain_search',
        description: "Search your company's internal knowledge base.",
        parameters: {
          type: 'object',
          properties: {
            query: {
              type: 'string',
              description:
                'Find internal search results aligned with this string. ' +
                'Conducts a vector search on the Tribble Brain. A long query phrase is preferable.',
            },
            ...(enableExtraFilters && {
              filters: {
                type: 'array',
                description:
                  'Optional filters to narrow down the search. Use any combination of the supported fields:\n' +
                  '- `source_name`: Filter by a search term for source names, e.g., "project plan", "my file pdf", "<slack channel id>".\n' +
                  '- `source_type`: Filter by source type, e.g., "Presentation", "Document", "Slack", "Website".\n' +
                  '- `source_platform`: Filter by platform, e.g., "GoogleDrive", "Box", "Notion", "Confluence".',
                items: {
                  type: 'object',
                  properties: {
                    source_name: {
                      type: 'string',
                      description:
                        'A search term to match partial or complete source names, e.g., "audit report", "pitch deck".',
                    },
                    source_type: {
                      type: 'string',
                      enum: sourceTypes,
                      description:
                        'The source type to filter, e.g. "Presentation" (slide, pptx etc.), "Document", or "Website".',
                    },
                    source_platform: {
                      type: 'string',
                      enum: Object.keys(PlatformMapping).filter(
                        (p) => p !== 'Slack' && p !== 'Website',
                      ),
                      description:
                        'The platform where the source is located, e.g., "GoogleDrive", "Box", or "Notion".',
                    },
                  },
                  additionalProperties: false,
                  description:
                    'Each filter object can include any combination of `source_type`, `source_name`, and `source_platform` fields.',
                },
              },
            }),
          },
          required: ['query'],
        },
      },
    };
  }

  getPendingMessage(): string {
    return `_Searching Tribble Brain for "${this.query}"..._ :brain:`;
  }

  getCompletionMessage(): string {
    let matchString = '';
    switch (this.contexts.length) {
      case 0:
        matchString = ' found _no_ matches';
      case 1:
        matchString = ''; // ' found 1 match';
      default:
        matchString = ''; // ` found ${this.contexts.length} matches`;
    }
    // Only show "no matches" status to avoid misleading match counts
    return `Searched Tribble Brain for "${this.query}"${matchString} :brain:`;
  }

  protected getTelemetryPrefix(): string {
    return 'brain_search';
  }

  /**
   * Performs text search
   * @returns Array of search contexts
   */
  protected async performTextSearch(
    query: string,
  ): Promise<ConversationContext[]> {
    const startTime = Date.now();
    let results;

    if (this.filters.length > 0) {
      console.log(
        `[conversation id=${this.conversationId}] Found filters ${this.filters}. Using FilteredContextSearch`,
      );

      results = await FilteredContextSearch({
        query,
        schema: this.schema,
        strictMode: this.strictMode,
        metadataFilters: this.metadataFilters,
        filters: this.filters,
        allowSlackRag: this.allowSlackRag,
        includeVerbatim: this.includeVerbatim,
        timeFilter: this.timeFilter,
      });
    } else {
      results = await ContextSearch({
        query,
        schema: this.schema,
        strictMode: this.strictMode,
        metadataFilters: this.metadataFilters,
        allowSlackRag: this.allowSlackRag,
        includeVerbatim: this.includeVerbatim,
        timeFilter: this.timeFilter,
        allowGongRag: this.settings.enableGong,
        conversationId: this.conversationId, // To fetch excludedPlatforms
      });
    }

    const queryTime = Date.now() - startTime;

    console.log(
      `[BrainSearch] Search finished in ${queryTime}ms [schema=${this.schema} conversation=${this.conversationId}]`,
    );

    telemetryClient.trackMetric({
      name:
        this.filters.length > 0
          ? 'brain_search_filtered_context_search'
          : 'brain_search_context_search',
      value: queryTime,
      properties: {
        conversation_id: this.conversationId.toString(),
      },
    });

    return results;
  }

  async performCall(): Promise<string> {
    if (
      this.filters.some((filter) => filter.source_type?.match(SpreadsheetRegex))
    ) {
      throw new Error(
        `Spreadsheet search must be done through the ${SearchCustomTables.tool_name} tool`,
      );
    }

    // Use the shared base implementation
    await this.performBaseCall();

    if (!this.contexts.length) {
      return JSON.stringify(['No results found for query']);
    }

    // Special handling for verbatim contexts in Agentic Answer RFP
    if (this.includeVerbatim) {
      const metadataFilterRecords = await getMetadataFilterRecords(
        this.schema as ClientSchema,
      );

      this.contexts.forEach((ctx) => {
        const verbatimMetadataFilter = metadataFilterRecords.find((mdf) => {
          const strMdfTypeId = String(mdf.type_id);
          return (
            ctx.metadata_filter &&
            ctx.metadata_filter[strMdfTypeId] &&
            ctx.metadata_filter[strMdfTypeId].includes(mdf.value) &&
            mdf.type.is_verbatim
          );
        });
        if (verbatimMetadataFilter) {
          ctx.is_verbatim_metadata_filter = true;
        }
      });
    }

    return JSON.stringify(
      this.contexts
        .filter(Boolean)
        .map((ctx, idx) =>
          ctx.toSourceFormat(idx, this.metadataFilters, this.includeImages),
        ),
    );
  }

  static extractCitations(
    answer: string,
    messages: ConversationMessage[],
    initialContexts?: ConversationSourceFormat[],
  ): ExtractCitationFormat {
    const usedBrainCitations = [];

    answer = processAnswerCitations(answer, false);
    const { usedCitationIndexes: usedBrainCitationIndexes } = reorderCitations(
      answer,
      answer,
    );

    // Find corresponding citation for each brain citation index
    usedBrainCitationIndexes.forEach((index) => {
      let brainCitation;

      if (initialContexts && initialContexts.length > 0) {
        brainCitation = initialContexts.find((context) => {
          return Number(context.id) === index;
        });
      }

      if (!brainCitation) {
        messages.forEach((msg) => {
          if (msg.role === 'tool' && !brainCitation) {
            try {
              const toolMessageContent = JSON.parse(msg.content as string);

              // Handle different tool message formats
              let searchArray = toolMessageContent;
              if (
                toolMessageContent &&
                typeof toolMessageContent === 'object' &&
                !Array.isArray(toolMessageContent)
              ) {
                // Handle Salesforce query results format: { totalSize: N, records: [...] }
                if (
                  toolMessageContent.records &&
                  Array.isArray(toolMessageContent.records)
                ) {
                  searchArray = toolMessageContent.records;
                } else {
                  // Skip non-array objects that don't have a records property
                  return false;
                }
              }

              if (Array.isArray(searchArray)) {
                brainCitation = searchArray.find(
                  (citation: any) => Number(citation.id) === index,
                );
              }
            } catch (e) {
              console.log(
                '[BrainCitationExtract parse error]',
                'toolMessageContent',
                msg?.content,
                e,
              );
              telemetryClient.trackMetric({
                name: 'brain_citation_extract_error',
                value: 1,
                properties: {
                  error: e,
                  content: msg?.content,
                  tool_call_id: msg?.tool_call_id,
                },
              });
              return false;
            }
          }
          return false;
        });
      }

      if (
        brainCitation &&
        brainCitation.id?.length > 0 &&
        brainCitation.url?.length > 0
      ) {
        usedBrainCitations.push({
          id: index,
          file_name: brainCitation.file_name,
          url: brainCitation.url,
          reference: brainCitation.reference,
        });
      }
    });

    return {
      citations: usedBrainCitations,
      updatedAnswer: answer,
    };
  }
}
