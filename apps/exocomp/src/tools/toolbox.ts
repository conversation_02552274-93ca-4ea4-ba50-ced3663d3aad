import { Logger } from '@tribble/common-utils';
import { FormRecognizedDoc } from '@tribble/formrecognizer';
import { HubSpotClient } from '@tribble/hubspot';
import { ClientSchema } from '@tribble/tribble-db';
import {
  ClientId,
  ConversationDetailId,
  ConversationId,
  UserId,
} from '@tribble/tribble-db/types';
import { Connection } from 'jsforce';
import { ConversationMessage } from '../conversations/message.ts';
import { MetadataFilterValue } from '../metadata_filters.ts';
import { ResolvedSettings } from '../resolved_settings.ts';
import { AddToResearch } from './add_to_research.ts';
import { BidPacketSearch } from './bid_packet_search.ts';
import { BrainSearch } from './brain_search.ts';
import { CompleteTask } from './complete_task.ts';
import { CreateImageFromPromptTool } from './create_image_from_prompt.ts';
import { CreatePptxFromImageTool } from './create_pptx_from_image.ts';
import { CreateShareableDeckTool } from './create_shareable_deck.ts';
import { CustomTableDescribe } from './custom_table_describe.ts';
import { StructuredQuery } from './custom_table_query.ts';
import { SearchCustomTables } from './custom_table_search.ts';
import { EditRequirements } from './edit_requirements.ts';
import { ExemplarSearch } from './exemplar_doc_search.ts';
import { ExtractTemporal, TimeFilter } from './extract_temporal.ts';
import { FetchUrl } from './fetch_url.ts';
import { FetchUrlFast } from './fetch_url_fast.ts';
import { FindDocuments } from './find_documents.ts';
import { GenerateEphemeralUI } from './generate_ephemeral_ui.ts';
import { GenerateResultUI } from './generate_result_ui.ts';
import { GetMeetingSummaries } from './get_meeting_summaries.ts';
import { Handoff } from './handoff.ts';
import { HubSpotDescribe } from './hubspot_describe.ts';
import { HubSpotQuery } from './hubspot_query.ts';
import { HubSpotWrite } from './hubspot_write.ts';
import { InvokeMeetingPrep } from './invoke_meeting_prep.ts';
import { LearnFact } from './learn_a_fact.ts';
import { ProcessUISubmission } from './process_ui_submission.ts';
import { SalesforceDescribe } from './salesforce_describe.ts';
import { SalesforceQuery } from './salesforce_query.ts';
import { SalesforceWrite } from './salesforce_write.ts';
import { SearchInDocuments } from './search_in_documents.ts';
import { SummarizeDocuments } from './summarize_documents.ts';
import { TieredContextSearchTool } from './tiered_context_search.ts';
import { ToolCall } from './tool_call.ts';
import { ConversationIntelligence } from './tribblytics/conversation_intelligence.ts';
import { RfxIntelligence } from './tribblytics/rfx_intelligence.ts';
import { WebSearch } from './web_search.ts';

// Common config type shared across all tools
export interface ToolCommonConfig {
  schema: ClientSchema;
  tribbleUserId?: UserId;
  clientId?: ClientId;
  clientName?: string;
  conversationId?: ConversationId;
  conversationDetailId?: ConversationDetailId;
  metadataFilters?: MetadataFilterValue[];
  messages?: ConversationMessage[];
  activityLogData?: any;
  helperFuncKey?: string;
  salesforceConn?: Connection;
  hubspotClient?: HubSpotClient;
  isContentMod?: boolean;
  bidPacketFiles?: FormRecognizedDoc[];
  exemplarFiles?: FormRecognizedDoc[];
  webSearchSites?: string[];
  timeFilter?: TimeFilter;
  pureMDToken?: string;
  settings: ResolvedSettings;
  posiHelpersUrl: string;
  agentType?: string;
  logger: Logger;
  // Add more common config properties as needed
}

// Tool registry interface with constructor and config definitions
export interface ToolPropsMapper {
  toolClass: any;
  configMapper?: (commonConfig: ToolCommonConfig) => any;
  argsMapper?: (args: any) => any;
}

// The main tool registry with all available tools
export const toolBox: Record<string, ToolPropsMapper> = {
  [BrainSearch.tool_name]: {
    toolClass: BrainSearch,
    configMapper: (config) => ({
      schema: config.schema,
      clientId: config.clientId,
      clientName: config.clientName,
      conversationId: config.conversationId,
      metadataFilters: config.metadataFilters,
      tribbleUserId: config.tribbleUserId,
      timeFilter: config.timeFilter,
      settings: config.settings,
      logger: config.logger,
    }),
  },

  [WebSearch.tool_name]: {
    toolClass: WebSearch,
    configMapper: (config) => ({
      sites: config.webSearchSites || [],
      serperKey: process.env.SERP_API_KEY,
      currentMessages: config.messages,
      logger: config.logger,
    }),
  },

  [FetchUrlFast.tool_name]: {
    toolClass: FetchUrlFast,
    configMapper: (config) => ({
      pureMDToken: process.env.PURE_MD_TOKEN ?? config.pureMDToken,
      logger: config.logger,
      fallBackTool: {
        //This can be remove eventually
        toolClass: FetchUrl,
        config: {
          schema: config.schema,
          activityLogData: config.activityLogData,
          helperFuncKey: config.helperFuncKey,
          scrapeWebPageEndpoint: process.env.SCRAPE_WEB_PAGE_ENDPOINT,
          pageSummaryLength: parseInt(process.env.PAGE_SUMMARY_LENGTH, 10),
          pageSummaryModel: process.env.PAGE_SUMMARY_MODEL || 'gpt-4.1',
          pageSummaryTemp: parseFloat(process.env.PAGE_SUMMARY_TEMP),
          logger: config.logger,
        },
      },
    }),
  },

  [LearnFact.tool_name]: {
    toolClass: LearnFact,
    configMapper: (config) => ({
      is_content_mod: config.isContentMod,
      agentApiVersion:
        process.env.AGENT_API_VERSION_LATEST || '2024-12-01-preview',
      client_id: config.clientId,
      user_id: config.tribbleUserId,
      conversationDetailId: config.conversationDetailId,
      messages: config.messages,
      schema: config.schema,
      logger: config.logger,
    }),
  },

  [SalesforceQuery.tool_name]: {
    toolClass: SalesforceQuery,
    configMapper: (config) => ({
      conn: config.salesforceConn,
      schema: config.schema,
      settings: config.settings,
      tribbleUserId: config.tribbleUserId,
      logger: config.logger,
    }),
  },

  [SalesforceDescribe.tool_name]: {
    toolClass: SalesforceDescribe,
    configMapper: (config) => ({
      conn: config.salesforceConn,
      logger: config.logger,
    }),
    argsMapper: (args) => ({ sObject: args['s_object'], field: args['field'] }),
  },

  [SalesforceWrite.tool_name]: {
    toolClass: SalesforceWrite,
    configMapper: (config) => {
      if (!config.messages || !config.messages.length) {
        return { conn: config.salesforceConn, logger: config.logger };
      }

      const lastMessage = config.messages[config.messages.length - 1];
      const isConfirmed =
        lastMessage?.role === 'user' &&
        typeof lastMessage.content === 'string' &&
        lastMessage.content.toLowerCase() === 'yes';

      return {
        conn: config.salesforceConn,
        isConfirmed,
        messages: config.messages,
        schema: config.schema,
        settings: config.settings,
        logger: config.logger,
      };
    },
    argsMapper: (args) => {
      // Clean up the Salesforce ID by removing the 'salesforce_' prefix if present
      if (args.records) {
        const records = Array.isArray(args.records)
          ? args.records
          : [args.records];
        records.forEach((record) => {
          if (record.Id && typeof record.Id === 'string') {
            record.Id = record.Id.replace('salesforce_', '');
          }
        });
        args.records = records;
      }
      return args;
    },
  },

  [HubSpotQuery.tool_name]: {
    toolClass: HubSpotQuery,
    configMapper: (config) => ({
      hubspotClient: config.hubspotClient,
      schema: config.schema,
      clientId: config.clientId,
      tribbleUserId: config.tribbleUserId,
      user: config.tribbleUserId ? { id: config.tribbleUserId } : undefined,
      client: config.clientId
        ? { id: config.clientId, database_schema_id: config.schema }
        : undefined,
      logger: config.logger,
    }),
  },

  [HubSpotDescribe.tool_name]: {
    toolClass: HubSpotDescribe,
    configMapper: (config) => ({
      hubspotClient: config.hubspotClient,
      logger: config.logger,
    }),
  },

  [HubSpotWrite.tool_name]: {
    toolClass: HubSpotWrite,
    configMapper: (config) => {
      if (!config.messages || !config.messages.length) {
        return { hubspotClient: config.hubspotClient, logger: config.logger };
      }

      const lastMessage = config.messages[config.messages.length - 1];
      const isConfirmed =
        lastMessage?.role === 'user' &&
        typeof lastMessage.content === 'string' &&
        lastMessage.content.toLowerCase() === 'yes';

      return {
        hubspotClient: config.hubspotClient,
        isConfirmed,
        messages: config.messages,
        schema: config.schema,
        settings: config.settings,
        logger: config.logger,
      };
    },
  },

  [SearchCustomTables.tool_name]: {
    toolClass: SearchCustomTables,
  },

  [StructuredQuery.tool_name]: {
    toolClass: StructuredQuery,
  },

  [CustomTableDescribe.tool_name]: {
    toolClass: CustomTableDescribe,
  },

  [FindDocuments.tool_name]: {
    toolClass: FindDocuments,
    configMapper: (config) => ({
      clientName: config.clientName,
      schema: config.schema,
      timeFilter: config.timeFilter,
      logger: config.logger,
    }),
  },

  [SearchInDocuments.tool_name]: {
    toolClass: SearchInDocuments,
    configMapper: (config) => ({
      clientName: config.clientName,
      schema: config.schema,
      metadataFilters: config.metadataFilters,
      strictMode: config.settings.strictMode,
      allowSlackRag: config.settings.allowSlackRag,
      timeFilter: config.timeFilter,
      logger: config.logger,
    }),
  },

  [SummarizeDocuments.tool_name]: {
    toolClass: SummarizeDocuments,
  },

  [ExemplarSearch.tool_name]: {
    toolClass: ExemplarSearch,
    configMapper: (config) => ({
      exemplarFiles: config.exemplarFiles,
      logger: config.logger,
    }),
    argsMapper: (args) => ({ page_query: args['page_numbers'] }),
  },

  [BidPacketSearch.tool_name]: {
    toolClass: BidPacketSearch,
    configMapper: (config) => ({
      bidPacketFiles: config.bidPacketFiles,
      logger: config.logger,
    }),
    argsMapper: (args) => ({
      page_query: args['page_numbers'],
      file_name: args['file_name'],
    }),
  },

  [AddToResearch.tool_name]: {
    toolClass: AddToResearch,
    configMapper: (config) => ({
      schema: config.schema,
      conversationId: config.conversationId,
      logger: config.logger,
    }),
  },

  [EditRequirements.tool_name]: {
    toolClass: EditRequirements,
    configMapper: (config) => ({
      schema: config.schema,
      conversationId: config.conversationId,
      logger: config.logger,
    }),
  },

  [CompleteTask.tool_name]: {
    toolClass: CompleteTask,
  },

  [ExtractTemporal.tool_name]: {
    toolClass: ExtractTemporal,
    configMapper: (config) => ({
      clientId: config.clientId,
      clientName: config.clientName,
      conversationId: config.conversationId,
      tribbleUserId: config.tribbleUserId,
      schema: config.schema,
      agentApiVersion:
        process.env.AGENT_API_VERSION_LATEST || '2024-12-01-preview',
      logger: config.logger,
    }),
  },

  [Handoff.tool_name]: {
    toolClass: Handoff,
  },

  [GetMeetingSummaries.tool_name]: {
    toolClass: GetMeetingSummaries,
  },

  [InvokeMeetingPrep.tool_name]: {
    toolClass: InvokeMeetingPrep,
  },

  [CreateImageFromPromptTool.tool_name]: {
    toolClass: CreateImageFromPromptTool,
    configMapper: (config) => ({
      helpersUrl: config.posiHelpersUrl,
      helperFuncKey: config.helperFuncKey,
      clientId: config.clientId?.toString() || '',
      userId: config.tribbleUserId?.toString() || '',
      databaseId: config.schema || '',
      logger: config.logger,
    }),
  },

  [CreatePptxFromImageTool.tool_name]: {
    toolClass: CreatePptxFromImageTool,
    configMapper: (config) => ({
      helpersUrl: config.posiHelpersUrl,
      helperFuncKey: config.helperFuncKey,
      conversationId: config.conversationId,
      clientId: config.clientId?.toString() || '',
      userId: config.tribbleUserId?.toString() || '',
      databaseId: config.schema || '',
    }),
  },

  [CreateShareableDeckTool.tool_name]: {
    toolClass: CreateShareableDeckTool,
    configMapper: (config) => ({
      helpersUrl: config.posiHelpersUrl,
      helperFuncKey: config.helperFuncKey,
      conversationId: config.conversationId,
      clientId: config.clientId?.toString() || '',
      userId: config.tribbleUserId?.toString() || '',
      databaseId: config.schema || '',
    }),
  },

  [ConversationIntelligence.tool_name]: {
    toolClass: ConversationIntelligence,
    configMapper: (config) => ({
      clientId: config.clientId,
      clientName: config.clientName,
      conversationId: config.conversationId,
      schema: config.schema,
      tribbleUserId: config.tribbleUserId,
      timeFilter: config.timeFilter,
      logger: config.logger,
    }),
  },

  [RfxIntelligence.tool_name]: {
    toolClass: RfxIntelligence,
    configMapper: (config) => ({
      clientId: config.clientId,
      clientName: config.clientName,
      schema: config.schema,
      tribbleUserId: config.tribbleUserId,
      timeFilter: config.timeFilter,
      logger: config.logger,
    }),
  },

  [TieredContextSearchTool.tool_name]: {
    toolClass: TieredContextSearchTool,
    configMapper: (config) => ({
      clientId: config.clientId,
      clientName: config.clientName,
      conversationId: config.conversationId,
      metadataFilters: config.metadataFilters,
      schema: config.schema,
      tribbleUserId: config.tribbleUserId,
      includeVerbatim: false, // Default to false, can be overridden
      includeImages: true, // Default to true, can be overridden
      timeFilter: config.timeFilter,
      settings: config.settings,
      agentType: config.agentType,
      logger: config.logger,
    }),
  },

  [GenerateEphemeralUI.tool_name]: {
    toolClass: GenerateEphemeralUI,
    configMapper: (config) => ({
      schema: config.schema,
      conversationId: config.conversationId,
      conversationDetailId: config.conversationDetailId,
      messages: config.messages,
      hubspotClient: config.hubspotClient,
      salesforceConn: config.salesforceConn,
      logger: config.logger,
    }),
  },

  [ProcessUISubmission.tool_name]: {
    toolClass: ProcessUISubmission,
    configMapper: (config) => ({
      schema: config.schema,
      conversationId: config.conversationId,
      conversationDetailId: config.conversationDetailId,
      logger: config.logger,
    }),
  },

  [GenerateResultUI.tool_name]: {
    toolClass: GenerateResultUI,
    configMapper: (config) => ({
      logger: config.logger,
    }),
  },
};

// Helper function to determine if a tool name matches the tool
export function useTool(
  llmSelectedName: string,
  candidateTool: string,
): boolean {
  llmSelectedName = llmSelectedName.replace('functions.', '');
  if (llmSelectedName === candidateTool) {
    return true;
  }

  if (llmSelectedName.startsWith(candidateTool)) {
    console.warn(
      'Bad tool name (but still using it):',
      llmSelectedName,
      'vs',
      candidateTool,
    );
    return true;
  }

  return false;
}

// Function to create a tool instance based on registry
export function createToolInstance(
  toolName: string,
  commonConfig: ToolCommonConfig,
  toolCallArgs: any,
): ToolCall | null {
  // Find the best matching tool
  const matchingToolName = Object.keys(toolBox).find((name) =>
    useTool(toolName, name),
  );

  if (!matchingToolName) {
    return null;
  }

  const toolDef = toolBox[matchingToolName];
  const normalizedToolName = matchingToolName; // Return the correct tool name

  // Apply config and args mappers if they exist
  const config = toolDef.configMapper
    ? toolDef.configMapper(commonConfig)
    : { schema: commonConfig.schema, logger: commonConfig.logger };
  const args = toolDef.argsMapper
    ? toolDef.argsMapper(toolCallArgs)
    : toolCallArgs;
  console.log('Tool', normalizedToolName, 'args:', args);
  // Note: just because timeFilter is in commonConfig doesn't mean it's used by the tool
  console.debug(
    'Tool',
    normalizedToolName,
    'timeFilter:',
    commonConfig.timeFilter,
  );

  // Create and return the tool instance
  return new toolDef.toolClass(config, args);
}
