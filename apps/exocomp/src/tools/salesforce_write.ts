import { Tool } from '@tribble/chat-completion-service/client';
import { Logger } from '@tribble/common-utils';
import { Filter } from '@tribble/salesforce';
import { Connection, SaveResult } from 'jsforce';
import { ResolvedSettings } from '../resolved_settings.ts';
import { ToolCall } from './tool_call.ts';

const filterersCache = new Map<string, Filter.GenericIntelligentFilter>();

// Type for records that can be created/updated/upserted
type SalesforceRecord = {
  Id?: string;
  [key: string]: any;
};

/**
 * A robust tool for writing (create/update/upsert) Salesforce records.
 *
 * Usage:
 *   const toolCall = new SalesforceWrite(
 *     { conn: yourSalesforceConnection },
 *     {
 *       operation: 'update',
 *       object: 'Contact',
 *       records: [{ Id: '0031Q00000ABCDX', Email: '<EMAIL>' }],
 *     }
 *   );
 *   const results = await toolCall.performCall();
 */
export class SalesforceWrite extends ToolCall {
  static tool_name = 'salesforce_write';

  private conn: Connection;
  private operation: 'create' | 'update' | 'upsert';
  private sObjectName: string;
  private records: SalesforceRecord[];
  private externalIdFieldName?: string;
  private messages: any[];
  private isConfirmed: boolean;
  private schema: string;
  private settings: ResolvedSettings;

  constructor(
    props: {
      conn: Connection;
      schema: string;
      settings: ResolvedSettings;
      messages?: any[];
      isConfirmed?: boolean;
      logger?: Logger;
    },
    params: {
      operation: 'create' | 'update' | 'upsert';
      object: string;
      records: SalesforceRecord | SalesforceRecord[];
      externalIdFieldName?: string;
    },
  ) {
    super(props.logger);
    if (!props.conn) {
      throw new Error('Salesforce connection is required');
    }
    this.conn = props.conn;
    this.schema = props.schema;
    this.settings = props.settings;
    this.messages = props.messages || [];
    this.isConfirmed = props.isConfirmed || false;
    this.operation = params.operation;
    this.sObjectName = params.object;

    // Normalize records to array format
    this.records = (
      Array.isArray(params.records) ? params.records : [params.records]
    ).map((record) => {
      const cleanRecord: SalesforceRecord = { ...record };
      delete cleanRecord.object;
      delete cleanRecord.operation;
      delete cleanRecord.externalIdFieldName;
      return cleanRecord;
    });

    this.externalIdFieldName = params.externalIdFieldName;
  }

  /**
   * getJsonSchema() tells the agent how to call this tool:
   *
   * "operation" is required. Possible values: "create", "update", "upsert".
   * "object"   is required. The Salesforce object name to write to (e.g. "Contact").
   * "records"  is required. Each item is a JSON object with the fields to create/update.
   * "externalIdFieldName" is only used for upsert. If omitted, we cannot do upsert.
   */
  static getJsonSchema(): Tool {
    return {
      type: 'function',
      function: {
        name: SalesforceWrite.tool_name,
        description: 'Creates, updates, or upserts records in Salesforce',
        parameters: {
          type: 'object',
          properties: {
            operation: {
              type: 'string',
              enum: ['create', 'update', 'upsert'],
              description: 'The type of write operation to perform',
            },
            object: {
              type: 'string',
              description: 'The Salesforce object type (e.g. Account, Contact)',
            },
            records: {
              oneOf: [
                {
                  type: 'array',
                  items: {
                    type: 'object',
                    additionalProperties: true,
                  },
                  description: 'Array of records to write',
                },
                {
                  type: 'object',
                  additionalProperties: true,
                  description: 'Single record to write',
                },
              ],
              description:
                'Records to write - can be a single record object or array of records',
            },
            externalIdFieldName: {
              type: 'string',
              description: 'External ID field name for upsert operations',
            },
          },
          required: ['operation', 'object', 'records'],
          additionalProperties: false,
        },
      },
    };
  }

  /**
   * Provide a short description for the LLM.
   * This will appear in the agent's system prompt if needed.
   */
  static getPromptDescription(): string {
    return (
      'salesforce_write: Creates, updates, or upserts records in Salesforce. ' +
      'Use this to modify existing or create new records when the user specifically wants changes in Salesforce. ' +
      'IMPORTANT: Before making any Salesforce writes, you MUST: ' +
      '1. Gather and validate the data (using salesforce_describe or other tools) ' +
      '2. Present the data to the user and ask for confirmation ' +
      '3. Only proceed with the write operation after explicit user approval. The word "yes" is required. ' +
      'Required parameters: operation (create/update/upsert), object (e.g. Account), ' +
      'records (array of field-value pairs). Optional: externalIdFieldName for upsert.'
    );
  }

  /**
   * Shown to user if we want to post updates or ephemeral messages about the tool call.
   * This is optional but helps keep consistent with other tools.
   */
  getPendingMessage(): string {
    return `Writing records to Salesforce object "${this.sObjectName}" via ${this.operation}... :cloud:`;
  }

  /**
   * Shown to user once the tool call is completed.
   */
  getCompletionMessage(): string {
    return this.isConfirmed
      ? `Completed ${this.operation} to Salesforce object "${this.sObjectName}" :white_check_mark:`
      : 'User confirmation required for Salesforce write operation :double_vertical_bar:';
  }

  /**
   * The main function that performs the Salesforce write operation.
   * We handle partial success/failure, returning a detailed JSON object.
   */
  async performCall(): Promise<string> {
    try {
      if (!this.conn) {
        throw new Error('No Salesforce connection available.');
      }

      // Validate basic input
      if (!this.sObjectName || !this.sObjectName.trim()) {
        throw new Error('No Salesforce object name provided.');
      }

      if (this.records.length === 0) {
        throw new Error('No records provided for Salesforce write.');
      }

      // Check if this is a confirmation call (user responded with "yes")
      if (
        this.isConfirmed ||
        (this.messages.length > 0 &&
          this.messages[this.messages.length - 1]?.role === 'user' &&
          this.messages[this.messages.length - 1]?.content?.toLowerCase() ===
            'yes')
      ) {
        return this.executeWrite(true);
      }

      // First return a preview that requires user confirmation
      const recordPreview = this.records.map((record, index) => ({
        index,
        operation: this.operation,
        object: this.sObjectName,
        data: record,
      }));

      return JSON.stringify(
        {
          status: 'preview',
          message:
            'Please confirm the following Salesforce write operation with the word "yes"',
          operation: {
            type: this.operation,
            object: this.sObjectName,
            records: recordPreview,
            externalIdFieldName: this.externalIdFieldName,
          },
          confirmation_required: true,
        },
        null,
        2,
      );
    } catch (err) {
      return JSON.stringify({
        error: true,
        message: err instanceof Error ? err.message : String(err),
      });
    }
  }

  async executeWrite(confirmed: boolean = false): Promise<string> {
    if (!confirmed) {
      return JSON.stringify({
        error: true,
        message: 'Operation cancelled - user confirmation required',
      });
    }

    try {
      const resultPayload: {
        successes: any[];
        failures: any[];
        message: string;
      } = {
        successes: [],
        failures: [],
        message: '',
      };

      // Validate operation type
      if (!['create', 'update', 'upsert'].includes(this.operation)) {
        throw new Error(`Invalid operation type: ${this.operation}`);
      }

      // Special validation for upsert
      if (this.operation === 'upsert' && !this.externalIdFieldName) {
        throw new Error(
          'External ID field name is required for upsert operations',
        );
      }

      // Clean up Salesforce IDs by removing 'salesforce_' prefix if present
      const cleanedRecords = this.records.map((record) => {
        const cleanedRecord = { ...record };
        if (
          cleanedRecord.Id &&
          typeof cleanedRecord.Id === 'string' &&
          cleanedRecord.Id.startsWith('salesforce_')
        ) {
          cleanedRecord.Id = cleanedRecord.Id.replace('salesforce_', '');
        }
        return cleanedRecord;
      });

      // Apply filtering for update and upsert operations
      let filteredRecords = cleanedRecords;
      if (this.operation === 'update' || this.operation === 'upsert') {
        filteredRecords = await this.applyFiltering(cleanedRecords);
      }

      let results: SaveResult[];

      // Perform the appropriate operation
      switch (this.operation) {
        case 'create':
          results = await this.conn
            .sobject(this.sObjectName)
            .create(filteredRecords);
          break;
        case 'update':
          // For update, we need to ensure each record has an Id
          if (!filteredRecords.every((record) => 'Id' in record && record.Id)) {
            throw new Error(
              'All records must have an Id field for update operation',
            );
          }
          results = await this.conn
            .sobject(this.sObjectName)
            .update(
              filteredRecords as Array<SalesforceRecord & { Id: string }>,
            );
          break;
        case 'upsert':
          results = await this.conn
            .sobject(this.sObjectName)
            .upsert(filteredRecords, this.externalIdFieldName);
          break;
        default:
          throw new Error(`Unhandled operation type: ${this.operation}`);
      }

      // Process results
      results.forEach((result, index) => {
        if (result.success) {
          resultPayload.successes.push({
            id: result.id,
            success: true,
            recordIndex: index,
          });
        } else {
          resultPayload.failures.push({
            recordIndex: index,
            errors: result.errors,
            record: this.records[index],
          });
        }
      });

      // Generate summary message
      const successCount = resultPayload.successes.length;
      const failureCount = resultPayload.failures.length;
      resultPayload.message = `${this.operation} operation completed: ${successCount} successful, ${failureCount} failed`;

      return JSON.stringify(resultPayload, null, 2);
    } catch (err) {
      return JSON.stringify({
        error: true,
        message: err instanceof Error ? err.message : String(err),
        details: err,
      });
    }
  }

  /**
   * Apply filtering to records for update/upsert operations
   * This ensures that only records that pass the configured filters are modified
   */
  private async applyFiltering(
    records: SalesforceRecord[],
  ): Promise<SalesforceRecord[]> {
    if (!this.schema || !this.settings.salesforceFilterConfig) {
      return records; // No filtering configured
    }

    try {
      // Use cached filterer for performance
      if (!filterersCache.has(this.schema)) {
        filterersCache.set(this.schema, new Filter.GenericIntelligentFilter());
      }
      const filterer = filterersCache.get(this.schema)!;
      filterer.setFilterConfig(this.settings.salesforceFilterConfig);

      // Get the filter WHERE clause for this object
      const whereClause = await filterer.getFilterWhereClause(
        this.conn,
        this.sObjectName,
      );

      if (!whereClause) {
        return records; // No filters apply to this object
      }

      // Build record ID to record mapping for efficient batch processing
      const recordIdToRecord = new Map<string, SalesforceRecord>();
      const recordIdsToCheck: string[] = [];
      const newRecords: SalesforceRecord[] = []; // Records without existing IDs (new upserts)

      // First pass: collect all record IDs that need checking
      for (const record of records) {
        if (this.operation === 'update' && record.Id) {
          recordIdToRecord.set(record.Id, record);
          recordIdsToCheck.push(record.Id);
        } else if (
          this.operation === 'upsert' &&
          this.externalIdFieldName &&
          record[this.externalIdFieldName]
        ) {
          // For upsert, we need to find existing records by external ID first
          // Store with a temporary key for now
          recordIdToRecord.set(
            `ext_${record[this.externalIdFieldName]}`,
            record,
          );
        }
      }

      // For upsert operations, batch query to find existing records by external ID
      if (this.operation === 'upsert' && this.externalIdFieldName) {
        const externalIds = records
          .filter((r) => r[this.externalIdFieldName!])
          .map((r) => `'${r[this.externalIdFieldName!]}'`);

        if (externalIds.length > 0) {
          try {
            const existingQuery = `SELECT Id, ${this.externalIdFieldName} FROM ${this.sObjectName} WHERE ${this.externalIdFieldName} IN (${externalIds.join(',')})`;
            const existingResult = await this.conn.query(existingQuery);

            if (existingResult.records) {
              for (const existingRecord of existingResult.records) {
                const extId = existingRecord[this.externalIdFieldName!];
                const originalRecord = recordIdToRecord.get(`ext_${extId}`);
                if (originalRecord && existingRecord.Id) {
                  // Move from external ID mapping to actual ID mapping
                  recordIdToRecord.delete(`ext_${extId}`);
                  recordIdToRecord.set(existingRecord.Id, originalRecord);
                  recordIdsToCheck.push(existingRecord.Id);
                }
              }
            }

            // Any remaining external ID mappings are new records
            for (const [key, record] of recordIdToRecord.entries()) {
              if (key.startsWith('ext_')) {
                recordIdToRecord.delete(key);
                newRecords.push(record);
              }
            }
          } catch (err) {
            console.error('Error finding existing records for upsert:', err);
            // On error, treat all as new records
            for (const [key, record] of recordIdToRecord.entries()) {
              if (key.startsWith('ext_')) {
                recordIdToRecord.delete(key);
                newRecords.push(record);
              }
            }
          }
        }
      }

      // Batch query to check which existing records pass the filters
      const filteredRecords: SalesforceRecord[] = [...newRecords]; // Start with new records

      if (recordIdsToCheck.length > 0) {
        try {
          const idsForQuery = recordIdsToCheck.map((id) => `'${id}'`).join(',');
          const checkQuery = `SELECT Id FROM ${this.sObjectName} WHERE Id IN (${idsForQuery}) AND (${whereClause})`;
          const checkResult = await this.conn.query(checkQuery);

          // Create set of IDs that passed the filter
          const passedIds = new Set(
            checkResult.records?.map((r) => r.Id) || [],
          );

          // Add records that passed the filter
          for (const recordId of recordIdsToCheck) {
            const record = recordIdToRecord.get(recordId);
            if (record) {
              if (passedIds.has(recordId)) {
                filteredRecords.push(record);
              } else {
                console.warn(
                  `Record ${recordId} filtered out by data governance rules`,
                );
              }
            }
          }
        } catch (err) {
          console.error('Error checking filters for records:', err);
          // On error, exclude all existing records to be safe
        }
      }

      const filteredCount = records.length - filteredRecords.length;
      if (filteredCount > 0) {
        console.log(
          `Filtered out ${filteredCount} records due to data governance rules`,
        );
      }

      return filteredRecords;
    } catch (err) {
      console.error('Error applying filtering:', err);
      // On error, return original records to avoid blocking legitimate operations
      return records;
    }
  }
}
