import { Tool } from '@tribble/chat-completion-service/client';
import { Logger } from '@tribble/common-utils';
import { HubSpotClient } from '@tribble/hubspot';
import { ToolCall } from './tool_call.ts';

// Some type safety
type CreateOptions = Parameters<HubSpotClient['createRecord']>[1];
type BatchCreateOptions = Parameters<HubSpotClient['batchCreateRecords']>[1];
type BatchUpdateOptions = Parameters<HubSpotClient['batchUpdateRecords']>[1];

type CreateResults = Awaited<ReturnType<HubSpotClient['createRecord']>>;
type UpdateResults = Awaited<ReturnType<HubSpotClient['updateRecord']>>;
type DeleteResults = Awaited<ReturnType<HubSpotClient['deleteRecord']>>;

type BatchCreateResults = Awaited<
  ReturnType<HubSpotClient['batchCreateRecords']>
>;
type BatchUpdateResults = Awaited<
  ReturnType<HubSpotClient['batchUpdateRecords']>
>;

type Result =
  | CreateResults
  | UpdateResults
  | DeleteResults
  | BatchCreateResults
  | BatchUpdateResults;

export class HubSpotWrite extends ToolCall {
  static tool_name = 'hubspot_write';
  private hubspotClient: HubSpotClient;
  private operation: string;
  private objectType: string;
  private recordId?: string;
  private properties?: Record<string, any>;
  private records?: BatchCreateOptions['inputs'] | BatchUpdateOptions['inputs'];
  private associations?: CreateOptions['associations'];

  constructor(
    config: {
      hubspotClient: HubSpotClient;
      logger?: Logger;
    },
    args: {
      operation: string;
      objectType: string;
      recordId?: string;
      properties?: Record<string, any>;
      records?: BatchCreateOptions['inputs'] | BatchUpdateOptions['inputs'];
      associations?: CreateOptions['associations'];
    },
  ) {
    super(config.logger);
    this.hubspotClient = config.hubspotClient;
    this.operation = args.operation;
    this.objectType = args.objectType;
    this.recordId = args.recordId;
    this.properties = args.properties;
    this.records = args.records;
    this.associations = args.associations;
  }

  static getJsonSchema(): Tool {
    return {
      type: 'function',
      function: {
        name: 'hubspot_write',
        description: `Create, update, or delete records in HubSpot CRM. This tool requires user confirmation before executing any write operations.`,
        parameters: {
          type: 'object',
          properties: {
            operation: {
              type: 'string',
              enum: [
                'create',
                'update',
                'delete',
                'batch_create',
                'batch_update',
              ],
              description: 'The type of write operation to perform',
            },
            objectType: {
              type: 'string',
              description:
                'The type of HubSpot object (e.g., contacts, companies, deals, tickets, products, line_items, quotes)',
            },
            recordId: {
              type: 'string',
              description:
                'The ID of the record to update or delete (required for update/delete operations)',
            },
            properties: {
              type: 'object',
              description:
                'The properties to set on the record (for create/update operations)',
              additionalProperties: true,
            },
            records: {
              type: 'array',
              description: 'Array of records for batch operations',
              items: {
                type: 'object',
                properties: {
                  id: {
                    type: 'string',
                    description: 'Record ID (required for batch_update)',
                  },
                  properties: {
                    type: 'object',
                    description: 'Properties to set on the record',
                    additionalProperties: true,
                  },
                },
              },
            },
            associations: {
              type: 'array',
              description: 'Associations to create with other records',
              items: {
                type: 'object',
                properties: {
                  to: {
                    type: 'object',
                    properties: {
                      id: {
                        type: 'string',
                        description: 'ID of the record to associate with',
                      },
                    },
                    required: ['id'],
                  },
                  types: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        associationCategory: {
                          type: 'string',
                          enum: ['HUBSPOT_DEFINED', 'USER_DEFINED'],
                        },
                        associationTypeId: {
                          type: 'integer',
                          description: 'The ID of the association type',
                        },
                      },
                      required: ['associationCategory', 'associationTypeId'],
                    },
                  },
                },
                required: ['to', 'types'],
              },
            },
          },
          required: ['operation', 'objectType'],
        },
      },
    };
  }

  getPendingMessage(): string {
    return `Writing records to Hubspot object "${this.objectType}" via ${this.operation}... ✏️`;
  }

  /**
   * Shown to user once the tool call is completed.
   */
  getCompletionMessage(): string {
    return `Completed "${this.operation}" on Hubspot object "${this.objectType}" ✅`;
  }

  static getPromptDescription(): string {
    return `HubSpot Write Operations: You can create, update, or delete records in HubSpot CRM. Always ask for user confirmation before performing write operations. Use the hubspot_write tool for all modifications to HubSpot data.`;
  }

  async performCall(): Promise<string> {
    // Validate required parameters based on operation
    if (
      (this.operation === 'update' || this.operation === 'delete') &&
      !this.recordId
    ) {
      return `Record ID is required for ${this.operation} operation`;
    }

    if (
      (this.operation === 'create' || this.operation === 'update') &&
      !this.properties &&
      !this.records
    ) {
      return `Properties are required for ${this.operation} operation`;
    }

    if (
      (this.operation === 'batch_create' ||
        this.operation === 'batch_update') &&
      !this.records
    ) {
      return `Records array is required for ${this.operation} operation`;
    }

    try {
      await this.hubspotClient.ensureValidToken();
      let result: Result;

      switch (this.operation) {
        case 'create':
          result = await this.createRecord();
          break;
        case 'update':
          result = await this.updateRecord();
          break;
        case 'delete':
          result = await this.deleteRecord();
          break;
        case 'batch_create':
          result = await this.batchCreateRecords();
          break;
        case 'batch_update':
          result = await this.batchUpdateRecords();
          break;
        default:
          return `Unknown operation: ${this.operation}`;
      }

      return this.formatResult(result);
    } catch (error: any) {
      return `Error performing ${this.operation} on ${this.objectType}:  ${error}, ${error.message}`;
    }
  }

  private async createRecord(): Promise<CreateResults> {
    const createOptions: CreateOptions = {
      properties: this.properties,
    };

    if (this.associations && this.associations.length > 0) {
      createOptions.associations = this.associations;
    }

    return await this.hubspotClient.createRecord(
      this.objectType,
      createOptions,
    );
  }

  private async updateRecord(): Promise<UpdateResults> {
    return await this.hubspotClient.updateRecord(
      this.objectType,
      this.recordId!,
      { properties: this.properties },
    );
  }

  private async deleteRecord(): Promise<DeleteResults> {
    return await this.hubspotClient.deleteRecord(
      this.objectType,
      this.recordId!,
    );
  }

  private async batchCreateRecords(): Promise<BatchCreateResults> {
    const inputs = this.records!.map(
      (record: BatchCreateOptions['inputs'][0]) => ({
        record,
        properties: record.properties,
        associations: record.associations,
      }),
    );

    return await this.hubspotClient.batchCreateRecords(this.objectType, {
      inputs,
    });
  }

  private async batchUpdateRecords(): Promise<BatchUpdateResults> {
    const inputs = this.records!.map(
      (record: BatchUpdateOptions['inputs'][0]) => ({
        id: record.id,
        properties: record.properties,
      }),
    );

    return await this.hubspotClient.batchUpdateRecords(this.objectType, {
      inputs,
    });
  }

  private formatResult(result: any): string {
    switch (this.operation) {
      case 'create':
        return `Successfully created ${this.objectType} record with ID: ${result.id}\n\nProperties:\n${JSON.stringify(result.properties, null, 2)}`;

      case 'update':
        return `Successfully updated ${this.objectType} record ${result.id}\n\nUpdated properties:\n${JSON.stringify(result.properties, null, 2)}`;

      case 'delete':
        return `Successfully deleted ${this.objectType} record ${result.recordId}`;

      case 'batch_create':
        const created = result.results?.length || 0;
        return `Successfully created ${created} ${this.objectType} records.\n\nRecord IDs: ${result.results?.map((r: any) => r.id).join(', ')}`;

      case 'batch_update':
        const updated = result.results?.length || 0;
        return `Successfully updated ${updated} ${this.objectType} records.`;

      default:
        return `Operation ${this.operation} completed successfully.`;
    }
  }
}

export default HubSpotWrite;
