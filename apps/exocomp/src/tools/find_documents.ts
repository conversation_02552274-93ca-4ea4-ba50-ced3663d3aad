import { Tool } from '@tribble/chat-completion-service/client';
import { Logger } from '@tribble/common-utils';
import PlatformMapping from '../brain.ts';
import { DocumentSearch } from '../document.ts';
import { TimeFilter } from './extract_temporal.ts';
import { ToolCall } from './tool_call.ts';

export type SearchParam = {
  document_name?: string;
  document_type?: string;
  document_platform?: string;
};

export class FindDocuments extends ToolCall {
  static tool_name = 'find_documents';
  private clientName: string;
  private schema: string;
  private timeFilter: TimeFilter;

  private search_params: SearchParam[];

  constructor(
    config: {
      clientName: string;
      schema: string;
      timeFilter?: TimeFilter;
      logger?: Logger;
    },
    args: { search_params: SearchParam[] },
  ) {
    super(config.logger);
    this.search_params = args.search_params || [];

    this.clientName = config.clientName;
    this.schema = config.schema;
    this.timeFilter = config.timeFilter || {};
  }

  static getPromptDescription(params: any): string {
    const clientName = params.clientName || 'our company';
    return (
      `${this.tool_name}: Locates documents ` +
      `stored in ${clientName}'s platforms. Use this tool to find and list documents by name, type, or platform. ` +
      `Do not use this tool for searching within the contents of a document.`
    );
  }

  static getJsonSchema(params: any): Tool {
    const documentTypes = params || [];

    const schema = {
      type: 'function',
      function: {
        name: `${this.tool_name}`,
        description:
          "Locate and list documents in your company's knowledge base without retrieving their content.",
        parameters: {
          type: 'object',
          properties: {
            search_params: {
              type: 'array',
              description:
                'Search parameters to narrow down the search. Use any combination of the supported fields:\n' +
                '- `document_name`: Match partial or full document names, e.g., "project plan", "my file pdf", "<slack channel id>".\n' +
                '- `document_type`: Filter by document type, e.g., "Presentation", "Document", "Slack", "Website", etc.\n' +
                '- `document_platform`: Filter by platform, e.g., "GoogleDrive", "Box", "Notion", "Confluence", etc.',
              items: {
                type: 'object',
                properties: {
                  document_name: {
                    type: 'string',
                    description:
                      'Search term to match partial or complete document names, e.g., "audit report", "pitch deck", etc.',
                  },
                  document_type: {
                    type: 'string',
                    enum: documentTypes,
                    description:
                      'Document type to filter, e.g. "Presentation" (slide, pptx etc.), "Document", or "Website", etc.',
                  },
                  document_platform: {
                    type: 'string',
                    enum: Object.keys(PlatformMapping).filter(
                      (p) => p !== 'Slack' && p !== 'Website',
                    ),
                    description:
                      'Platform where the document is stored, e.g., "GoogleDrive", "Box", or "Notion", etc.',
                  },
                },
                additionalProperties: false,
                description:
                  'Each parameter is optional. However each `search_param` object must include AT LEAST ONE OF the fields: `document_type`, `document_name`, or `document_platform`.',
              },
            },
          },
          required: ['search_params'],
        },
      },
    };

    return schema;
  }

  getPendingMessage(): string {
    return `_Looking for documents..._ :mag_right:`;
  }

  getCompletionMessage(): string {
    return `Looked for documents :mag_right:`;
  }

  async performCall(): Promise<string> {
    if (this.search_params.length === 0) {
      return 'Invalid search parameters. Please provide at least one search parameter.';
    }

    const emptyObjects = this.search_params.filter(
      (param) => Object.keys(param).length === 0,
    );

    if (emptyObjects.length > 0) {
      return (
        'Empty search parameters are not allowed. Each `search_param` object must include ' +
        'at least one of the fields: `document_type`, `document_name`, or `document_platform`.'
      );
    }

    const result = await DocumentSearch({
      schema: this.schema,
      search_params: this.search_params,
      timeFilter: this.timeFilter,
    });

    if (result && result.length > 0) {
      return JSON.stringify(result);
    }

    return JSON.stringify(['no documents found']);
  }
}
