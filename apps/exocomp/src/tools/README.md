# Tribble Tools System

## How to Create a New Tool

1. Write the new tool. Each tool must extend the `ToolCall` abstract class. 
2. Add your tool to the `toolbox.ts`. 
- Add an entry to the `toolBox` object. 
- Args are mapped automatically, but you can override the mapping if needed (for example SalesforceDescribe's toolbox entry).
- Add config params using the config mapper.

```typescript
export const toolBox: Record<string, ToolPropsMapper> = {
  // ... existing tools
  
  [MyNewTool.tool_name]: {
    toolClass: MyNewTool,
    configMapper: (config) => ({
      schema: config.schema

    }),
    argsMapper: (args) => {
      // Optional: Transform or validate arguments
      return args;
    },
  },
};
```

3. Use Your Tool in a Cartridge
- To make your tool available in a specific cartridge, add it to the cartridge's available tools:
```typescript
// In your cartridge file
import { MyNewTool } from '../../tools/my_new_tool.ts';

export class MyCartridge extends Cartridge {
  // ...
  constructor() {
    // ...
    this.availableTools = [
      // ... other tools
      MyNewTool.getJsonSchema(),
    ];
  }
}
```