import { Tool } from '@tribble/chat-completion-service/client';
import { Logger } from '@tribble/common-utils';
import { describeCustomTable } from '../structured_data.ts';
import { clampText } from '../views/clamp_text.ts';
import { SearchCustomTables } from './custom_table_search.ts';
import { ToolCall } from './tool_call.ts';

export class CustomTableDescribe extends ToolCall {
  static tool_name = 'custom_table_describe';
  private schema: string;
  private table_name: string;
  private field: string;
  private label: string;

  constructor(
    config: { schema: string; logger?: Logger },
    args: { table_name: string; field?: string },
  ) {
    super(config.logger);
    this.schema = config.schema;
    this.table_name = args.table_name;
    this.field = args.field;
  }

  static getJsonSchema(): Tool {
    return {
      type: 'function',
      function: {
        name: CustomTableDescribe.tool_name,
        description:
          'Get a list of fields for a custom table. Or, get details about a specific field.',
        parameters: {
          type: 'object',
          properties: {
            table_name: {
              type: 'string',
              description: `The custom table to describe. Must be fetched from ${SearchCustomTables.tool_name}`,
            },
            field: {
              type: 'string',
              description:
                'Optional. The field to describe. If not provided, all fields for the table are returned.',
            },
          },
          required: ['table_name'],
        },
      },
    };
  }
  getPendingMessage(): string {
    return `Looking up table in imported spreadsheets... :mag:`;
  }

  getCompletionMessage(): string {
    const table = this.label
      ? `${clampText(this.label || this.table_name, 40)}${this.field ? `.${this.field}` : ''}`
      : this.table_name;
    return `Looked up "${table}" in imported spreadsheets... :mag_right:`;
  }

  async performCall(): Promise<string> {
    try {
      const table = await describeCustomTable(this.schema, this.table_name);
      if (!table) {
        console.log('[CustomTableDescribe] Table not found', this.table_name);
        return `Table ${this.table_name} not found. Check the table name and try again.`;
      }
      this.label = table.label;

      if (!this.field) {
        return JSON.stringify({
          name: this.table_name,
          description: table.description,
          fields: table.structure,
        });
      }

      const allFields = (table.structure as string[]) || [];
      const fieldsOfInterest = allFields.filter((field) =>
        field.includes(this.field.toLowerCase()),
      );

      if (fieldsOfInterest.length === 0) {
        //no fields found
        console.log('[CustomTableDescribe] No fields found');
        return JSON.stringify({
          name: this.table_name,
          message: `No fields with name ${this.field} were found. Here are all available fields`,
          fields: table.structure,
        });
      }

      //return fields of interest
      console.log(
        `[CustomTableDescribe] found ${fieldsOfInterest.length} field(s)`,
      );
      return JSON.stringify({
        name: this.table_name,
        fields: fieldsOfInterest,
      });
    } catch (err) {
      console.log(`[CustomTableDescribe] Error: ${err}`);
      return err.toString();
    }
  }
}
