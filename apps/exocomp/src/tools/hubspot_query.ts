import { Tool } from '@tribble/chat-completion-service/client';
import { Logger } from '@tribble/common-utils';
import { HubSpotClient, SearchOptions } from '@tribble/hubspot';
import { ToolCall } from './tool_call.ts';

type SearchResults = Awaited<ReturnType<HubSpotClient['searchRecords']>>;

export class HubSpotQuery extends ToolCall {
  static tool_name = 'hubspot_query';
  static citationIdPrefix = 'hubspot_';
  private hubspotClient: HubSpotClient;
  private objectType: string;
  private filters?: SearchOptions['filterGroups'];
  private properties?: string[];
  private limit?: number;
  private after?: string;
  private sorts?: SearchOptions['sorts'];

  constructor(
    config: {
      hubspotClient: HubSpotClient;
      logger?: Logger;
    },
    args: {
      objectType: string;
      filters?: SearchOptions['filterGroups'];
      properties?: string[];
      limit?: number;
      after?: string;
      sorts?: SearchOptions['sorts'];
    },
  ) {
    super(config.logger);
    this.hubspotClient = config.hubspotClient;
    this.objectType = args.objectType;
    this.filters = args.filters;
    this.properties = args.properties;
    this.limit = args.limit;
    this.after = args.after;
    this.sorts = args.sorts;
  }

  static getJsonSchema(): Tool {
    return {
      type: 'function',
      function: {
        name: 'hubspot_query',
        description: `Query HubSpot CRM data. This tool allows you to search and retrieve records from HubSpot including contacts, companies, deals, tickets, and other objects.`,
        parameters: {
          type: 'object',
          properties: {
            objectType: {
              type: 'string',
              description:
                'The type of HubSpot object to query (e.g., contacts, companies, deals, tickets, products, line_items, quotes)',
            },
            filters: {
              type: 'array',
              description: 'Array of filter groups for searching records',
              items: {
                type: 'object',
                properties: {
                  filters: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        propertyName: {
                          type: 'string',
                          description: 'The property to filter on',
                        },
                        operator: {
                          type: 'string',
                          description:
                            'The filter operator (EQ, NEQ, LT, LTE, GT, GTE, BETWEEN, IN, NOT_IN, HAS_PROPERTY, NOT_HAS_PROPERTY, CONTAINS_TOKEN, NOT_CONTAINS_TOKEN)',
                        },
                        value: {
                          type: 'string',
                          description: 'The value to filter by',
                        },
                      },
                      required: ['propertyName', 'operator'],
                    },
                  },
                },
              },
            },
            properties: {
              type: 'array',
              description: 'List of properties to include in the response',
              items: {
                type: 'string',
              },
            },
            limit: {
              type: 'integer',
              description:
                'Maximum number of records to return (default: 100, max: 100)',
              default: 100,
            },
            after: {
              type: 'string',
              description: 'Cursor for pagination',
            },
            sorts: {
              type: 'array',
              description: 'Array of sort criteria',
              items: {
                type: 'object',
                properties: {
                  propertyName: {
                    type: 'string',
                    description: 'Property to sort by',
                  },
                  direction: {
                    type: 'string',
                    enum: ['ASCENDING', 'DESCENDING'],
                    description: 'Sort direction',
                  },
                },
                required: ['propertyName', 'direction'],
              },
            },
          },
          required: ['objectType'],
        },
      },
    };
  }

  getPendingMessage(): string {
    return 'Searching Hubspot... :cloud:';
  }

  getCompletionMessage(): string {
    return `Searched Hubspot... ✅`;
  }

  static getPromptDescription(): string {
    return `HubSpot CRM Integration: You have access to query HubSpot CRM data including contacts, companies, deals, tickets, and other objects. Use the hubspot_query tool to search and retrieve records with filters and pagination support.`;
  }

  async performCall(): Promise<string> {
    try {
      await this.hubspotClient.ensureValidToken();
      const searchOptions: SearchOptions = {
        filterGroups: this.filters || [],
        properties:
          this.properties || this.getDefaultProperties(this.objectType),
        limit: Math.min(this.limit || 100, 100),
        sorts: this.sorts || [],
      };

      if (this.after) {
        searchOptions.after = this.after;
      }

      const results = await this.hubspotClient.searchRecords(
        this.objectType,
        searchOptions,
      );

      return this.formatResults(results);
    } catch (error: any) {
      return `Error querying ${this.objectType}: ${error}, ${error.message}`;
      // throw new Error(`HubSpot query failed: ${error.message}`);
    }
  }

  private getDefaultProperties(objectType: string): string[] {
    const defaultProps: Record<string, string[]> = {
      contacts: [
        'firstname',
        'lastname',
        'email',
        'phone',
        'company',
        'jobtitle',
        'lifecyclestage',
      ],
      companies: [
        'name',
        'domain',
        'industry',
        'city',
        'state',
        'country',
        'numberofemployees',
        'annualrevenue',
      ],
      deals: [
        'dealname',
        'dealstage',
        'amount',
        'closedate',
        'pipeline',
        'hs_priority',
      ],
      tickets: [
        'subject',
        'content',
        'hs_ticket_priority',
        'hs_ticket_category',
        'hs_pipeline_stage',
      ],
      products: [
        'name',
        'description',
        'price',
        'hs_sku',
        'hs_cost_of_goods_sold',
      ],
      line_items: ['name', 'quantity', 'price', 'amount', 'discount'],
      quotes: [
        'hs_title',
        'hs_expiration_date',
        'hs_status',
        'hs_public_url_key',
      ],
    };

    return defaultProps[objectType] || ['hs_object_id'];
  }

  private formatResults(results: SearchResults): string {
    if (!results.results || results.results.length === 0) {
      return JSON.stringify({
        message: `No ${this.objectType} found matching the criteria.`,
      });
    }

    // Process records to add citation IDs and URLs
    const records = results.results.map((record: any, index: number) => {
      const recordId = record.id;

      // Add citation ID
      record.Id = `${HubSpotQuery.citationIdPrefix}${this.objectType}_${recordId}`;

      // Add HubSpot URL
      const portalId = this.hubspotClient.portalId || '';
      record.url = this.getRecordUrl(recordId, portalId);

      // Flatten properties into the main record object
      const flatRecord: any = {
        Id: record.Id,
        url: record.url,
        ...record.properties,
      };

      return flatRecord;
    });

    const result = {
      totalSize: results.total,
      records,
      paging: results.paging,
    };

    return JSON.stringify(result);
  }

  private getRecordUrl(recordId: string, portalId: string): string {
    const baseUrl = 'https://app.hubspot.com/contacts';

    const urlMap: Record<string, string> = {
      contacts: `${baseUrl}/${portalId}/contact/${recordId}`,
      companies: `${baseUrl}/${portalId}/company/${recordId}`,
      deals: `${baseUrl}/${portalId}/deal/${recordId}`,
      tickets: `${baseUrl}/${portalId}/ticket/${recordId}`,
      products: `${baseUrl}/${portalId}/products/${recordId}`,
      quotes: `${baseUrl}/${portalId}/quotes/${recordId}`,
    };

    return (
      urlMap[this.objectType] || `${baseUrl}/${portalId}/record/0-1/${recordId}`
    );
  }
}

export default HubSpotQuery;
