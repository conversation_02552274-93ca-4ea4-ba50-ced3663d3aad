/*
 * Generate Result UI Tool
 *
 * This tool generates ephemeral UI for presenting results of complex operations
 * in a structured, easy-to-understand format. The UI is ephemeral and exists
 * only for the duration needed to convey the results.
 *
 * Changes made: Created new tool for result UI generation
 */

import { KnownBlock, SectionBlock } from '@slack/bolt';
import { Tool } from '@tribble/chat-completion-service/client';
import { Logger } from '@tribble/common-utils';
import { clampText } from '../views/clamp_text.ts';
import { ToolCall } from './tool_call.ts';

export interface ResultSection {
  type: 'summary' | 'table' | 'list' | 'details';
  title?: string;
  data: any;
}

export interface GenerateResultUIArgs {
  result_type: string;
  title: string;
  description?: string;
  sections: ResultSection[];
  status: 'success' | 'partial' | 'error';
  actions_taken?: string[];
  next_steps?: string[];
}

export class GenerateResultUI extends ToolCall {
  static tool_name = 'generate_result_ui';

  private resultArgs: GenerateResultUIArgs;

  constructor(
    config: {
      logger?: Logger;
    },
    args: GenerateResultUIArgs,
  ) {
    super(config.logger);
    this.resultArgs = args;
  }

  static getPromptDescription(_: any): string {
    return (
      `You have access to a tool that generates ephemeral UI for presenting results. ` +
      `Use this when you need to display complex results in a structured format.`
    );
  }

  static getJsonSchema(_: any): Tool {
    return {
      type: 'function',
      function: {
        name: GenerateResultUI.tool_name,
        description:
          'Generate an ephemeral UI for presenting operation results',
        parameters: {
          type: 'object',
          properties: {
            result_type: {
              type: 'string',
              description:
                'The type of result being presented (e.g., "migration_results", "query_results")',
            },
            title: {
              type: 'string',
              description: 'The title of the results',
            },
            description: {
              type: 'string',
              description: 'Optional description of the results',
            },
            sections: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  type: {
                    type: 'string',
                    enum: ['summary', 'table', 'list', 'details'],
                    description: 'The type of section',
                  },
                  title: {
                    type: 'string',
                    description: 'Section title',
                  },
                  data: {
                    type: ['object', 'array', 'string', 'number'],
                    description: 'The data to display in this section',
                  },
                },
                required: ['type', 'data'],
              },
            },
            status: {
              type: 'string',
              enum: ['success', 'partial', 'error'],
              description: 'The overall status of the operation',
            },
            actions_taken: {
              type: 'array',
              items: { type: 'string' },
              description: 'List of actions that were performed',
            },
            next_steps: {
              type: 'array',
              items: { type: 'string' },
              description: 'Suggested next steps for the user',
            },
          },
          required: ['result_type', 'title', 'sections', 'status'],
        },
      },
    };
  }

  getPendingMessage(): string {
    return '📊 Generating results display...';
  }

  getCompletionMessage(): string {
    return '✅ Results ready';
  }

  async performCall(): Promise<string> {
    return JSON.stringify({
      result_type: this.resultArgs.result_type,
      status: this.resultArgs.status,
      message: `Generated result UI for ${this.resultArgs.result_type}`,
    });
  }

  getCompletionBlocks(): KnownBlock[] {
    const blocks: KnownBlock[] = [];

    // Status emoji based on status
    const statusEmoji = {
      success: '✅',
      partial: '⚠️',
      error: '❌',
    }[this.resultArgs.status];

    // Header with status
    blocks.push({
      type: 'header',
      text: {
        type: 'plain_text',
        text: clampText(`${statusEmoji} ${this.resultArgs.title}`, 150),
      },
    });

    // Description if provided
    if (this.resultArgs.description) {
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: clampText(this.resultArgs.description || ' ', 3000),
        },
      });
    }

    // Process each section
    this.resultArgs.sections.forEach((section) => {
      if (section.title) {
        blocks.push({
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: clampText(`*${section.title || ' '}*`, 3000),
          },
        });
      }

      switch (section.type) {
        case 'summary':
          blocks.push(this.createSummaryBlock(section.data));
          break;
        case 'table':
          blocks.push(...this.createTableBlocks(section.data));
          break;
        case 'list':
          blocks.push(this.createListBlock(section.data));
          break;
        case 'details':
          blocks.push(this.createDetailsBlock(section.data));
          break;
      }

      blocks.push({ type: 'divider' });
    });

    // Actions taken
    if (
      this.resultArgs.actions_taken &&
      this.resultArgs.actions_taken.length > 0
    ) {
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: '*Actions Performed:*',
        },
      });
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: clampText(
            this.resultArgs.actions_taken
              .map((action) => `• ${action}`)
              .join('\n'),
            3000,
          ),
        },
      });
    }

    // Next steps
    if (this.resultArgs.next_steps && this.resultArgs.next_steps.length > 0) {
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: '*Suggested Next Steps:*',
        },
      });
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: clampText(
            this.resultArgs.next_steps
              .map((step, idx) => `${idx + 1}. ${step}`)
              .join('\n'),
            3000,
          ),
        },
      });
    }

    return blocks;
  }

  private createSummaryBlock(data: any): SectionBlock {
    if (typeof data === 'object') {
      const lines: string[] = [];
      for (const [key, value] of Object.entries(data)) {
        lines.push(`*${this.formatKey(key)}:* ${value}`);
      }
      return {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: clampText(lines.join('\n'), 3000),
        },
      };
    }

    return {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: clampText(String(data), 3000),
      },
    };
  }

  private createTableBlocks(data: any): KnownBlock[] {
    const blocks: KnownBlock[] = [];

    if (Array.isArray(data) && data.length > 0) {
      // Create header row
      const headers = Object.keys(data[0]);
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: clampText(
            headers.map((h) => `*${this.formatKey(h)}*`).join(' | '),
            3000,
          ),
        },
      });

      // Create data rows (limit to first 10 for readability)
      const rowsToShow = Math.min(data.length, 10);
      for (let i = 0; i < rowsToShow; i++) {
        const row = data[i];
        const values = headers.map((h) => String(row[h] || '-'));
        blocks.push({
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: clampText(values.join(' | '), 3000),
            },
          ],
        });
      }

      if (data.length > rowsToShow) {
        blocks.push({
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: `_... and ${data.length - rowsToShow} more rows_`,
            },
          ],
        });
      }
    }

    return blocks;
  }

  private createListBlock(data: any): SectionBlock {
    let items: string[] = [];

    if (Array.isArray(data)) {
      items = data.map((item) => {
        if (typeof item === 'object') {
          return `• ${JSON.stringify(item)}`;
        }
        return `• ${item}`;
      });
    } else if (typeof data === 'object') {
      items = Object.entries(data).map(
        ([key, value]) => `• *${this.formatKey(key)}:* ${value}`,
      );
    }

    return {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: clampText(items.join('\n'), 3000),
      },
    };
  }

  private createDetailsBlock(data: any): SectionBlock {
    let text: string;

    if (typeof data === 'string') {
      text = data;
    } else {
      text = clampText('```' + JSON.stringify(data, null, 2) + '```', 3000);
    }

    return {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: clampText(text || ' ', 3000),
      },
    };
  }

  private formatKey(key: string): string {
    // Convert snake_case or camelCase to Title Case
    return key
      .replace(/_/g, ' ')
      .replace(/([A-Z])/g, ' $1')
      .trim()
      .split(' ')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }
}
