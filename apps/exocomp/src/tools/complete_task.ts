import { Tool } from '@tribble/chat-completion-service/client';
import { ToolCall } from './tool_call.ts';

export class CompleteTask extends ToolCall {
  static tool_name = 'complete_task';
  do_not_recurse = true;

  constructor() {
    super();
  }

  static getPromptDescription(): string {
    return `Call this tool when task is complete`;
  }

  static getJsonSchema(): Tool {
    return {
      type: 'function',
      function: {
        name: CompleteTask.tool_name,
        description: 'Call this when task is complete',

        parameters: {
          type: 'object',
          properties: {
            complete: {
              type: 'boolean',
              required: ['complete'],
              additionalProperties: false,
            },
          },
        },
      },
    };
  }

  getPendingMessage(): string {
    return `Completing task...`;
  }

  getCompletionMessage(): string {
    return `Done`;
  }

  async performCall(): Promise<string> {
    console.log('Task complete');
    return JSON.stringify(['Task complete'], null, 2);
  }
}
