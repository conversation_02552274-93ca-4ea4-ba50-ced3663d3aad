import { Tool } from '@tribble/chat-completion-service/client';
import { Logger } from '@tribble/common-utils';
import { ClientSchema } from '@tribble/tribble-db';
import { queryAnalytics } from '@tribble/tribble-db/db_ops';
import { ClientId, UserId } from '@tribble/tribble-db/types';
import { TimeFilter } from '../extract_temporal.ts';
import { ToolCall } from '../tool_call.ts';

export class RfxIntelligence extends ToolCall {
  static tool_name = 'rfx_intelligence';
  static defaultTimeFilter = {
    timeRangeText: 'last 90 days',
    startDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
  };

  private clientId: ClientId;
  private clientName: string;
  private schema: ClientSchema;
  private tribbleUserId: UserId;

  private timeFilter?: TimeFilter;
  private metric: string;
  private userFilter?: string;
  private customInstructions?: string;

  constructor(
    config: {
      clientId: ClientId;
      clientName: string;
      schema: ClientSchema;
      tribbleUserId: UserId;
      timeFilter?: TimeFilter;
      logger?: Logger;
    },
    args: {
      metric: string;
      userFilter?: string;
      customInstructions?: string;
    },
  ) {
    super(config.logger);

    this.clientId = config.clientId;
    this.clientName = config.clientName;
    this.schema = config.schema;
    this.tribbleUserId = config.tribbleUserId;
    this.timeFilter = config.timeFilter || {};

    this.metric = args.metric;
    this.userFilter = args.userFilter;
    this.customInstructions = args.customInstructions;
  }

  static getPromptDescription(params: any): string {
    const clientName = params.clientName || 'our company';
    return (
      `${this.tool_name}: Retrieves RFX (Request for Proposal/Quote/Information) analytics and insights for ${clientName}. ` +
      `Can show metrics like review times, win rates, response distribution, human overrides, unanswered questions, ` +
      `topic categories for deals lost, confidence levels, and product/compliance gaps. ` +
      `Use this tool AFTER using extract_temporal to parse any time constraints from the user's query.`
    );
  }

  static getJsonSchema(params?: any): Tool {
    const clientName = params.clientName || 'our company';

    return {
      type: 'function',
      function: {
        name: 'rfx_intelligence',
        description:
          'Get analytics and insights about RFX (Request for Proposal/Quote/Information) processing and performance.',
        parameters: {
          type: 'object',
          properties: {
            metric: {
              type: 'string',
              enum: [
                // 'average_review_time',
                'rfps_under_review',
                'human_overrides_percentage',
                'distribution_by_use_case',
                'distribution_by_buyer',
                'win_rates',
                'win_rates_by_use_case',
                'distribution_by_product_industry',
                'response_scores_by_feature',
                'product_offering_gaps',
                'product_offering_to_bolster',
                'confidence_levels_over_time',
                'unanswered_questions',
                'deals_lost_top_topics',
                'rfx_details_and_contents',
                'rfx_custom_metric',
              ],
              description:
                'The type of RFX metric to retrieve. Options include review times, percentage of human overrides, deal win rates, ' +
                'deal win rates broken down by use case, response quality, analysis of deals lost, product offering gaps, and distribution metrics.' +
                'Use "rfx_details_and_contents" to run analyses using the details of RFX projects.' +
                'Use "rfx_custom_metric" for custom analysis based on user query. "rfx_custom_metric" returns raw RFX data including the ' +
                'customer names, opportunity details, outline of the RFX response document, and the confidence score counts of the Q&A responses.',
            },
            customInstructions: {
              type: 'string',
              description:
                'For "rfx_custom_metric": Pass through custom instructions based on the user query to help analyze RFX data.',
            },
            userFilter: {
              type: 'string',
              description:
                'Optional: Filter by specific user email or name pattern',
            },
          },
          required: ['metric'],
        },
      },
    };
  }

  getPendingMessage(): string {
    const timeDescription = this.timeFilter?.timeRangeText
      ? `for ${this.timeFilter?.timeRangeText} `
      : '';
    return `_Analyzing ${this.metric.replace(/_/g, ' ')} ${timeDescription}..._ :chart_with_upwards_trend:`;
  }

  getCompletionMessage(): string {
    const timeDescription = this.timeFilter?.timeRangeText
      ? `for ${this.timeFilter?.timeRangeText} `
      : '';
    return `Analyzed ${this.metric.replace(/_/g, ' ')} analytics data ${timeDescription}:chart_with_upwards_trend:`;
  }

  async performCall(): Promise<string> {
    try {
      let result: any;

      switch (this.metric) {
        // @TODO: To be implemented. data and query needs to be updated to capture data
        // case 'average_review_time':
        //   result = await this.getAverageReviewTime();
        //   break;
        case 'rfps_under_review':
          result = await this.getRfpsUnderReview();
          break;
        case 'human_overrides_percentage':
          result = await this.getHumanOverridesPercentage();
          break;
        case 'distribution_by_use_case':
          result = await this.getDistributionByUseCase();
          break;
        case 'distribution_by_buyer':
          result = await this.getDistributionByBuyer();
          break;
        case 'win_rates':
          result = await this.getWinRates();
          break;
        case 'win_rates_by_use_case':
          result = await this.getWinRatesByUseCase();
          break;
        case 'response_scores_by_feature':
          result = await this.getResponseScoresByFeature();
          break;
        case 'product_offering_gaps':
          result = await this.getProductOfferingGaps();
          break;
        case 'product_offering_to_bolster':
          result = await this.getProductOfferingBolster();
          break;
        case 'confidence_levels_over_time':
          result = await this.getConfidenceLevelsOverTime();
          break;
        case 'unanswered_questions':
          result = await this.getUnansweredQuestions();
          break;
        case 'deals_lost_top_topics':
          result = await this.getDealsLostTopTopics();
          break;
        case 'rfx_details_and_contents':
          result = await this.getRawRfxContentDetailsData();
          break;
        case 'rfx_custom_metric':
          result = await this.getRawRfxContentDetailsData();
          break;
        default:
          throw new Error(`Unknown metric: ${this.metric}`);
      }

      return JSON.stringify(result);
    } catch (error) {
      console.error(`[RfxIntelligence] Error: ${error.message}`);
      throw error;
    }
  }

  private getDateFilter(dateColumn: string = 'rfc.created_date'): string {
    if (
      !this.timeFilter ||
      (!this.timeFilter.startDate && !this.timeFilter.endDate)
    ) {
      // Default time filter if no time filter specified by user
      this.timeFilter = RfxIntelligence.defaultTimeFilter; // set timeFilter to default
      const defaultStartDate = RfxIntelligence.defaultTimeFilter.startDate;
      return ` ${dateColumn} >= '${defaultStartDate.toISOString()}'::timestamp`;
    }

    const conditions: string[] = [];

    if (this.timeFilter.startDate) {
      conditions.push(
        `${dateColumn} >= '${this.timeFilter.startDate.toISOString()}'::timestamp`,
      );
    }

    if (this.timeFilter.endDate) {
      conditions.push(
        `${dateColumn} <= '${this.timeFilter.endDate.toISOString()}'::timestamp`,
      );
    }

    return conditions.length > 0 ? ` ${conditions.join(' AND ')}` : '';
  }

  private getUserFilter(): string {
    return this.userFilter ? `AND u.id = c.created_by_id` : '';
  }

  private async getAverageReviewTime() {
    const dateFilter = this.getDateFilter('rfc.created_date');
    const userFilter = this.getUserFilter();

    // @TODO: Migrate away from job table
    // const queryString = `
    //   WITH completed_jobs AS (
    //     SELECT
    //       DATE_TRUNC('month', j.created_date) as period,
    //       EXTRACT(EPOCH FROM (j.updated_at - j.created_date))/3600 as hours_to_complete,
    //       (
    //           SELECT
    //             jsonb_build_array(
    //               jsonb_build_object(
    //                 'label', 'File',
    //                 'data', c.details -> 'fileName'
    //               ),
    //               jsonb_build_object(
    //                 'label', 'Project',
    //                 'data', c.details -> 'project_name'
    //               ),
    //               jsonb_build_object(
    //                 'label', 'Customer',
    //                 'data', c.details -> 'customer_name'
    //               ),
    //               jsonb_build_object(
    //                 'label', 'Hours to Complete',
    //                 'data',
    //                 CASE
    //                   WHEN j.updated_at IS NOT NULL
    //                   THEN ROUND(EXTRACT(EPOCH FROM (j.updated_at - j.created_date))/3600)::integer
    //                   ELSE NULL
    //                 END
    //               )
    //             )
    //           FROM ${this.schema}.content c
    //           WHERE c.job_id = j.id
    //         ) as drilldown_primary
    //     FROM ${this.schema}.content c
    //     JOIN ${this.schema}.job j ON j.id = c.job_id
    //     JOIN ${this.schema}.rfx_content rfc ON rfc.content_id = c.id
    //     JOIN ${this.schema}.rfx rfx ON rfx.id = rfc.rfx_id
    //     JOIN tribble.user u ON u.id = rfx.created_by ${userFilter}
    //     WHERE rfc.created_date IS NOT NULL
    //       AND ${dateFilter}
    //   )
    //   SELECT
    //     period,
    //     COUNT(*) as jobs_count,
    //     ROUND(AVG(hours_to_complete))::integer as avg_hours_to_complete
    //   FROM completed_jobs
    //   GROUP BY period
    //   ORDER BY period;
    // `;

    //  const result = await query(queryString, []);
    // if (result instanceof Error) {
    //     return {
    //       metric: this.metric,
    //       error:
    //         'There was an error while fetching data for this metric. Please try again.',
    //     };
    //   }

    // return {
    //   metric: 'average_review_time',
    //   timeFrame: this.timeFilter?.timeRangeText,
    //   data: result.rows,
    //   summary: {
    //     overall_avg_hours:
    //       result.rows?.length > 0
    //         ? Math.round(
    //             result.rows.reduce(
    //               (sum, row) => sum + row.avg_hours_to_complete,
    //               0,
    //             ) / result.rows.length,
    //           )
    //         : 0,
    //     total_jobs:
    //       result.rows?.reduce(
    //         (sum, row) => sum + parseInt(row.jobs_count),
    //         0,
    //       ) || 0,
    //   },
    // };

    return {
      metric: 'average_review_time',
      timeFrame: this.timeFilter?.timeRangeText,
      data: [],
      summary: 'Coming soon! This metric is not yet available.',
    };
  }

  private async getRfpsUnderReview() {
    const dateFilter = this.getDateFilter('rfc.created_date');

    const queryString = `
      WITH weeks AS (
        SELECT
          generate_series(
            date_trunc('week', min(created_date)),
            date_trunc('week', CURRENT_DATE - interval '1 week'),
            '1 week'::interval
          ) as week
        FROM ${this.schema}.rfx_content rfc
        WHERE rfc.created_date IS NOT NULL
          AND ${dateFilter}
      ),
      rfc_in_progress AS (
        SELECT
          rfc.id,
          rfc.created_date,
          rfc.last_updated,
          rfc.status,
          CASE
            WHEN rfc.status = 'finished' THEN rfc.last_updated
            ELSE NULL
          END as completed_at
        FROM ${this.schema}.rfx_content rfc
        JOIN ${this.schema}.rfx rfx ON rfx.id = rfc.rfx_id
        JOIN tribble.user u ON u.id = rfx.created_by
        WHERE rfc.created_date IS NOT NULL
          AND ${dateFilter}
          AND (rfc.status = 'finished' OR rfc.status LIKE 'in_review%')
          AND COALESCE(rfc.is_deleted, false) != true
      ),
      weekly_rfcs AS (
        SELECT
          w.week,
          rfcip.id as rfc_id
        FROM weeks w
        JOIN rfc_in_progress rfcip ON
          -- Include rfc if it was created before or during this week
          rfcip.created_date < w.week + '1 week'::interval
          -- AND it was either never completed or completed after week start
          AND (rfcip.completed_at IS NULL OR rfcip.completed_at > w.week)
      )
      SELECT
        w.week,
        to_char(w.week, 'Mon-DD') as week_string,
        COUNT(DISTINCT w.rfc_id) as uncomplete_rfps_count
      FROM weekly_rfcs w
      GROUP BY w.week
      ORDER BY w.week::date;
    `;

    const result = await queryAnalytics(queryString, []);
    if (result instanceof Error) {
      return {
        metric: this.metric,
        error:
          'There was an error while fetching data for this metric. Please try again.',
      };
    }

    return {
      metric: 'rfps_under_review',
      timeFrame: this.timeFilter?.timeRangeText,
      data: result.rows,
      summary: {
        current_under_review:
          result.rows?.[result.rows.length - 1]?.uncomplete_rfps_count || 0,
        avg_under_review_per_week:
          result.rows?.length > 0
            ? Math.round(
                result.rows.reduce(
                  (sum, row) => sum + parseInt(row.uncomplete_rfps_count || 0),
                  0,
                ) / result.rows.length,
              )
            : 0,
      },
    };
  }

  private async getHumanOverridesPercentage() {
    const dateFilter = this.getDateFilter('rfc.created_date');

    const queryString = `
      WITH stats_by_period AS (
        SELECT 
          DATE_TRUNC('month', to_timestamp((cd.statistics->'duration'->>'start')::numeric)) AS period,
          COUNT(*) as total_records,
          COUNT(CASE WHEN state = 'modified' THEN 1 END) as modified_records
        FROM ${this.schema}.content_detail cd
        JOIN ${this.schema}.content c ON c.id = cd.content_id
        JOIN ${this.schema}.rfx_content rfc ON rfc.content_id = c.id
        WHERE ${dateFilter}
          AND COALESCE(c.is_deleted, false) != true
        GROUP BY period
      )
      SELECT 
        period,
        total_records,
        modified_records,
        ROUND((modified_records::decimal / total_records * 100), 2) as modified_percentage
      FROM stats_by_period
      WHERE period IS NOT NULL
      ORDER BY period;
    `;

    const result = await queryAnalytics(queryString, []);
    if (result instanceof Error) {
      return {
        metric: this.metric,
        error:
          'There was an error while fetching data for this metric. Please try again.',
      };
    }

    return {
      metric: 'human_overrides_percentage',
      timeFrame: this.timeFilter?.timeRangeText,
      data: result.rows,
      summary: {
        overall_override_rate:
          result.rows?.length > 0
            ? Math.round(
                result.rows.reduce(
                  (sum, row) => sum + parseFloat(row.modified_percentage),
                  0,
                ) / result.rows.length,
              )
            : 0,
        total_processed:
          result.rows?.reduce(
            (sum, row) => sum + parseInt(row.total_records),
            0,
          ) || 0,
      },
    };
  }

  private async getRawRfxContentDetailsData() {
    // This would provide raw RFX data for custom analysis
    const dateFilter = this.getDateFilter('rfc.created_date');

    const rfxLongformQueryString = `
      SELECT 
        rfc.id as rfx_content_id,
        MAX(rfc.created_date) as created_date,
        MAX(rfx.due_date) as due_date,
        STRING_AGG(DISTINCT rcu.name, ' | ') as project_created_by,
        -- STRING_AGG(DISTINCT ou.name, ' | ') as project_owner_name, -- redundant until multi-file implemented
        -- STRING_AGG(DISTINCT cu.name, ' | ') as subproject_owner_name, -- redundant until multi-file implemented
        STRING_AGG(DISTINCT rfx.name, ' | ') as rfx_name,
        STRING_AGG(DISTINCT rfx.customer_name, ' | ') as rfx_customer_name,
        MAX(rfc.status) as rfx_content_status,
        MAX(sft.opportunity_amount) as opportunity_amount,
        MAX(sft.opportunity_stage) as opportunity_stage,
                STRING_AGG(DISTINCT rfxrd.name, ' | ') as rfx_response_document_title,
                MAX(rfxrd.highlevel_summary) as rfx_response_document_highlevel_summary,
        ARRAY_AGG(DISTINCT rfxso.content) FILTER (WHERE rfxso.content IS NOT NULL) as rfx_response_section_content_outlines
      FROM ${this.schema}.rfx_content rfc
      JOIN ${this.schema}.content c ON c.id = rfc.content_id
      JOIN ${this.schema}.rfx rfx ON rfx.id = rfc.rfx_id
      JOIN ${this.schema}.rfx_response_document rfxrd ON rfxrd.rfx_content_id = rfc.id
      LEFT JOIN ${this.schema}.rfx_section_outline rfxso ON rfxso.rfx_response_document_id = rfxrd.id
      LEFT JOIN ${this.schema}.salesforce_tribblytics sft ON sft.opportunity_id = c.details ->> 'opportunity_id'
      -- LEFT JOIN ${this.schema}.rfx_content_user_join rcuj ON rfc.id = rcuj.rfx_content_id AND rcuj.is_owner = TRUE
      -- LEFT JOIN tribble.user cu ON rcuj.user_id = cu.id
      LEFT JOIN tribble.user rcu ON rfx.created_by = rcu.id
      -- LEFT JOIN tribble.user ou ON rfx.owner_id = ou.id
      WHERE 
        ${dateFilter}
        AND COALESCE(c.is_deleted, false) != true
        AND COALESCE(rfx.is_deleted, false) != true
        AND (c.details->'agentParams'->>'longForm')::boolean IS TRUE 
      GROUP BY rfc.id
      ORDER BY rfc.created_date DESC
      LIMIT 300
    `;

    const rfxLongformResult = await queryAnalytics(rfxLongformQueryString, []);
    if (rfxLongformResult instanceof Error) {
      return {
        metric: this.metric,
        error:
          'There was an error while fetching data for this metric. Please try again.',
      };
    }

    const qnaQueryString = `
      SELECT 
        rfc.id as rfx_content_id,
        MAX(rfc.created_date) as created_date,
        MAX(rfx.due_date) as due_date,
        STRING_AGG(DISTINCT rcu.name, ' | ') as project_created_by,
        -- STRING_AGG(DISTINCT ou.name, ' | ') as project_owner_name, -- redundant until multi-file implemented
        -- STRING_AGG(DISTINCT cu.name, ' | ') as subproject_owner_name, -- redundant until multi-file implemented
        STRING_AGG(DISTINCT rfx.name, ' | ') as rfx_name,
        STRING_AGG(DISTINCT rfx.customer_name, ' | ') as rfx_customer_name,
        MAX(rfc.status) as rfx_content_status,
        MAX(sft.opportunity_amount) as opportunity_amount,
        MAX(sft.opportunity_stage) as opportunity_stage,
        -- Commenting out Q&A data for now as they are too much data to handle
        -- (Instructions: Analyze question types (questions), answer quality (original_answers), and modification patterns (original_answers vs modified_answers) for Q&A type responses)
        -- ARRAY_AGG(DISTINCT cd.input->>'query_string') FILTER (WHERE cd.input->>'query_string' IS NOT NULL) as questions,
        -- ARRAY_AGG(DISTINCT cd.output_original->>'answer') FILTER (WHERE cd.output_original->>'answer' IS NOT NULL) as original_answers,
        -- ARRAY_AGG(DISTINCT cd.output_modified->>'text') FILTER (WHERE cd.output_modified->>'text' IS NOT NULL) as modified_answers,
        JSON_BUILD_OBJECT(
          'none', COUNT(*) FILTER (WHERE cd.confidence_score = 0),
          'low', COUNT(*) FILTER (WHERE cd.confidence_score = 1),
          'medium', COUNT(*) FILTER (WHERE cd.confidence_score = 2),
          'high', COUNT(*) FILTER (WHERE cd.confidence_score >= 3)
        ) as confidence_score_counts
      FROM ${this.schema}.rfx_content rfc
      JOIN ${this.schema}.content c ON c.id = rfc.content_id
      JOIN ${this.schema}.rfx rfx ON rfx.id = rfc.rfx_id
      LEFT JOIN ${this.schema}.content_detail cd ON cd.content_id = c.id
      LEFT JOIN ${this.schema}.salesforce_tribblytics sft ON sft.opportunity_id = c.details ->> 'opportunity_id'
      -- LEFT JOIN ${this.schema}.rfx_content_user_join rcuj ON rfc.id = rcuj.rfx_content_id AND rcuj.is_owner = TRUE
      -- LEFT JOIN tribble.user cu ON rcuj.user_id = cu.id
      LEFT JOIN tribble.user rcu ON rfx.created_by = rcu.id
      -- LEFT JOIN tribble.user ou ON rfx.owner_id = ou.id
      WHERE
        ${dateFilter}
        AND COALESCE(c.is_deleted, false) != true
        AND COALESCE(rfx.is_deleted, false) != true
        AND (c.details->'agentParams'->>'longForm')::boolean IS NOT TRUE 
      GROUP BY rfc.id
      ORDER BY rfc.created_date DESC
      LIMIT 300
    `;

    const qnaResult = await queryAnalytics(qnaQueryString, []);
    if (qnaResult instanceof Error) {
      return {
        metric: this.metric,
        error:
          'There was an error while fetching data for this metric. Please try again.',
      };
    }

    const resultsGrouped = {
      longformDocument: rfxLongformResult.rows,
      qna: qnaResult.rows,
    };

    return {
      metric: this.metric,
      timeFrame: this.timeFilter?.timeRangeText,
      data: resultsGrouped,
      summary: {
        instructions: `
          Use the following instructions to analyze the RFX Content data:
          - The data is grouped by RFX Project. So each data object represents a unique RFX Project containing its details.
          - Use the rfx_name to understand the RFX project name
          - Use the rfx_customer_name to understand the customer name who is the recipient of the RFX response
          - Use the rfx_content_status to understand the status of the RFX response sub-project
          - Quantify business impact with deal size (opportunity_amount) and opportunity stage (opportunity_stage)
          - For RFX document-type responses:
            - Use the response document title (rfx_response_document_title) and high-level summary (rfx_response_document_highlevel_summary) to understand the overall response
            - Use the response document outline (rfx_response_section_content_outlines) to understand the structure and content of the response
          - For Question-Answer Q&A-type (qna) responses:
            - Look for trends in confidence scores (confidence_score_counts).  
          ${this.customInstructions || ''}
        `,
        total_rfx_records:
          (rfxLongformResult.rows?.length || 0) + (qnaResult.rows?.length || 0),
        rfx_records_count_by_type: {
          longformDocument: rfxLongformResult.rows?.length || 0,
          qna: qnaResult.rows?.length || 0,
        },
      },
    };
  }

  private async getDistributionByUseCase() {
    const dateFilter = this.getDateFilter('rfc.created_date');

    const queryString = `
      WITH version_info AS (
        SELECT 
          cs.*,
          t.enabled,
          t.name AS task_name,
          COUNT(*) OVER () as total_versions,
          MAX(CASE WHEN t.enabled = TRUE THEN cs.version END) OVER () as max_enabled_version,
          MAX(cs.version) OVER () as max_version
        FROM ${this.schema}.nlp_category_set cs
        LEFT JOIN ${this.schema}.nlp_task t 
          ON t.name = CONCAT('TEXT_CAT_', cs.name) 
          AND t.version = cs.version 
        WHERE cs.name = 'RFP_PRIMARY_USE_CASE'
      ),
      selected_category_set_version AS (
        SELECT *
        FROM version_info
        WHERE (
          (enabled = TRUE AND version = max_enabled_version)
          OR
          (max_enabled_version IS NULL 
            AND total_versions > 1 
            AND version = (
              SELECT MAX(version) 
              FROM version_info 
              WHERE version < max_version
          ))
          OR
          (total_versions = 1 AND enabled = TRUE)
        )
      ),
      category_counts AS (
        SELECT
          cd.content_id,
          nc.name as category_name,
          COUNT(*) as category_count
        FROM ${this.schema}.nlp_category_source ncs
        JOIN ${this.schema}.content_detail cd ON cd.id = ncs.content_detail_id
        JOIN ${this.schema}.nlp_category nc ON nc.id = ncs.category_id
        WHERE nc.category_set_id = (SELECT id FROM selected_category_set_version)
        GROUP BY cd.content_id, nc.name
      ),
      primary_category AS (
        SELECT
          content_id,
          category_name as rfp_target_category
        FROM (
          SELECT
            content_id,
            category_name,
            category_count,
            ROW_NUMBER() OVER (PARTITION BY content_id ORDER BY category_count DESC) as rn
          FROM category_counts
          WHERE LOWER(category_name) != LOWER('not applicable')
        ) ranked
        WHERE rn = 1
      )
      SELECT
        COALESCE(pc.rfp_target_category, 'Uncategorized') as rfp_target_category,
        COUNT(*)::int as content_count
      FROM ${this.schema}.content c
      JOIN ${this.schema}.rfx_content rfc ON rfc.content_id = c.id
      LEFT JOIN primary_category pc ON c.id = pc.content_id
      WHERE ${dateFilter}
        AND COALESCE(c.is_deleted, false) != true
      GROUP BY pc.rfp_target_category
      ORDER BY content_count DESC;
    `;

    const result = await queryAnalytics(queryString, []);
    if (result instanceof Error) {
      return {
        metric: this.metric,
        error:
          'There was an error while fetching data for this metric. Please try again.',
      };
    }

    return {
      metric: 'distribution_by_use_case',
      timeFrame: this.timeFilter?.timeRangeText,
      data: result.rows,
      summary: {
        total_rfps:
          result.rows?.reduce(
            (sum, row) => sum + parseInt(row.content_count),
            0,
          ) || 0,
        top_use_case: result.rows[0]?.rfp_target_category || 'N/A',
        unique_categories: result.rows?.length || 0,
      },
    };
  }

  private async getWinRates() {
    const dateFilter = this.getDateFilter('sft.opportunity_system_modstamp');

    const queryString = `
      SELECT
        c.type,
        sft.opportunity_stage,
        COUNT(DISTINCT sft.opportunity_id)::int AS count,
        ROUND(
          COUNT(DISTINCT sft.opportunity_id) * 100.0 / SUM(COUNT(DISTINCT sft.opportunity_id)) OVER (PARTITION BY c.type),
          0
        )::int AS percentage,
        (
            SELECT jsonb_agg(
              DISTINCT jsonb_build_array(
                jsonb_build_object(
                  'label', 'Customer', 
                  'data', COALESCE(sft.account_name, c.details ->> 'customer_name')
                ),
                jsonb_build_object(
                  'label', 'Opportunity', 
                  'data', sft.opportunity_name
                ),
                jsonb_build_object(
                  'label', 'Opportunity Stage', 
                  'data', sft.opportunity_stage
                ),
                jsonb_build_object(
                  'label', 'Opportunity Amount', 
                  'data', sft.opportunity_amount::money
                ),
                jsonb_build_object(
                  'label', 'File', 
                  'data', c.details -> 'fileName'
                ),
                jsonb_build_object(
                  'label', 'Created Date', 
                  'data', sft.opportunity_system_modstamp::date
                )
              )
            )
            WHERE LOWER(sft.opportunity_stage) = 'closed won'
          ) AS drilldown_primary,
          (
            SELECT jsonb_agg(
              DISTINCT jsonb_build_array(
                jsonb_build_object(
                  'label', 'Customer', 
                  'data', COALESCE(sft.account_name, c.details ->> 'customer_name')
                ),
                jsonb_build_object(
                  'label', 'Opportunity', 
                  'data', sft.opportunity_name
                ),
                jsonb_build_object(
                  'label', 'Opportunity Stage', 
                  'data', sft.opportunity_stage
                ),
                jsonb_build_object(
                  'label', 'Opportunity Amount', 
                  'data', sft.opportunity_amount::money
                ),
                jsonb_build_object(
                  'label', 'File', 
                  'data', c.details -> 'fileName'
                ),
                jsonb_build_object(
                  'label', 'Created Date', 
                  'data', sft.opportunity_system_modstamp::date
                )
              )
            )
            WHERE LOWER(sft.opportunity_stage) IN ('closed lost', 'closed dead')
          ) AS drilldown_secondary
      FROM ${this.schema}.content c
      JOIN ${this.schema}.salesforce_tribblytics sft ON sft.opportunity_id = c.details ->> 'opportunity_id'
      WHERE ${dateFilter}
        AND LOWER(sft.opportunity_stage) IN ('closed won', 'closed lost', 'closed dead') 
        AND COALESCE(c.is_deleted, false) != true
      GROUP BY type, opportunity_stage
      ORDER BY opportunity_stage DESC
      LIMIT 100
    `;

    const result = await queryAnalytics(queryString, []);
    if (result instanceof Error) {
      return {
        metric: this.metric,
        error:
          'There was an error while fetching data for this metric. Please try again.',
      };
    }

    const wonDeals =
      result.rows?.filter(
        (row) => row.opportunity_stage?.toLowerCase() === 'closed won',
      ) || [];
    const lostDeals =
      result.rows?.filter((row) =>
        ['closed lost', 'closed dead'].includes(
          row.opportunity_stage?.toLowerCase(),
        ),
      ) || [];

    return {
      metric: 'win_rates',
      timeFrame: this.timeFilter?.timeRangeText,
      data: result.rows,
      summary: {
        win_rate: wonDeals.length > 0 ? wonDeals[0].percentage : 0,
        total_closed_deals:
          result.rows?.reduce((sum, row) => sum + parseInt(row.count), 0) || 0,
        won_deals: wonDeals.reduce((sum, row) => sum + parseInt(row.count), 0),
        lost_deals: lostDeals.reduce(
          (sum, row) => sum + parseInt(row.count),
          0,
        ),
      },
    };
  }

  private async getUnansweredQuestions() {
    const dateFilter = this.getDateFilter('rfc.created_date');

    const queryString = `
      SELECT 
        cat_match.name as match_category_name,
        MAX(cat_match.id) as match_category_id,
        cat_qa.name as qa_category_name,
        MAX(cat_qa.id) as qa_category_id,
        COUNT(cats_match.*)::int as count
      FROM ${this.schema}.nlp_category_source cats_match
      JOIN ${this.schema}.nlp_category cat_match
        ON cat_match.id = cats_match.category_id
        AND cat_match.category_set_id = (
          SELECT id
          FROM ${this.schema}.nlp_category_set
          WHERE name = 'RFP_ANSWER_STATUS'
        )        
      JOIN ${this.schema}.content_detail cd
        ON cd.id = cats_match.content_detail_id
      JOIN ${this.schema}.content c
        ON c.id = cd.content_id
        AND COALESCE(c.is_deleted, false) != true
      JOIN ${this.schema}.rfx_content rfc ON rfc.content_id = c.id
      LEFT JOIN ${this.schema}.nlp_category_source cats_qa
        ON cats_qa.content_detail_id = cd.id
      LEFT JOIN ${this.schema}.nlp_category cat_qa
        ON cat_qa.id = cats_qa.category_id        
        AND cat_qa.category_set_id = (
          SELECT id
          FROM ${this.schema}.nlp_category_set
          WHERE name = 'RFP_QA'          
        )
      WHERE ${dateFilter}
        AND cat_qa.name IS NOT NULL
        AND LOWER(cat_match.name) = 'cannot answer'
      GROUP BY 
        cat_match.name, cat_qa.name
      ORDER BY 
        count DESC
    `;

    const result = await queryAnalytics(queryString, []);
    if (result instanceof Error) {
      return {
        metric: this.metric,
        error:
          'There was an error while fetching data for this metric. Please try again.',
      };
    }

    return {
      metric: 'unanswered_questions',
      timeFrame: this.timeFilter?.timeRangeText,
      data: result.rows,
      summary: {
        total_unanswered:
          result.rows?.reduce((sum, row) => sum + parseInt(row.count), 0) || 0,
        top_unanswered_category: result.rows[0]?.qa_category_name || 'N/A',
        categories_with_gaps: result.rows?.length || 0,
      },
    };
  }

  private async getProductOfferingGaps() {
    const dateFilter = this.getDateFilter('rfc.created_date');

    const queryString = `
      WITH target_matching_category AS (
        SELECT id
        FROM ${this.schema}.nlp_category
        WHERE name = 'Product Features'
      ),
      target_answer_category AS (
        SELECT id, name
        FROM ${this.schema}.nlp_category
        WHERE name = 'No Match'
      ),
      content_with_target_answer AS (
        SELECT DISTINCT ncs.content_detail_id
        FROM ${this.schema}.nlp_category_source ncs
        WHERE ncs.category_id = (SELECT id FROM target_matching_category)
      ),
      base_content AS (
        SELECT 
          cd.id as content_detail_id,
          answer_cat.name as match_category_name,
          answer_cat.id as match_category_id,
          c.id as content_id
        FROM ${this.schema}.nlp_category_source ncs
        JOIN target_answer_category answer_cat
          ON answer_cat.id = ncs.category_id
        JOIN ${this.schema}.content_detail cd
          ON cd.id = ncs.content_detail_id
        JOIN ${this.schema}.content c
          ON c.id = cd.content_id
        JOIN ${this.schema}.rfx_content rfc ON rfc.content_id = c.id
        WHERE ${dateFilter}
          AND COALESCE(c.is_deleted, false) != true
      ),
      client_name_setting AS (
        SELECT value_string as client_name
        FROM ${this.schema}.client_setting
        WHERE setting_id = (
          SELECT id
          FROM tribble.setting
          WHERE name = 'client_name'
        )
      ),
      entity_data AS (
        SELECT 
          ents.content_detail_id,
          COALESCE(ent_canonical.name, ent.name) as entity_name,
          COALESCE(ent_canonical.id, ent.id) as entity_id
        FROM ${this.schema}.nlp_entity_source ents
        LEFT JOIN ${this.schema}.nlp_entity ent
          ON ent.id = ents.entity_id
        LEFT JOIN ${this.schema}.nlp_entity ent_canonical
          ON ent_canonical.id = ent.canonical_id
        JOIN client_name_setting cns ON 1=1
        WHERE COALESCE(ent_canonical.name, ent.name) IS NOT NULL
          AND LOWER(ent.name) != LOWER(cns.client_name)
          AND LOWER(ent.name) != (LOWER(cns.client_name) || ' bot')     
          AND LOWER(COALESCE(ent_canonical.name, ent.name)) != LOWER(cns.client_name)
          AND LOWER(COALESCE(ent_canonical.name, ent.name)) != (LOWER(cns.client_name) || ' bot')
      )
      SELECT 
        base_content.match_category_name,
        MAX(base_content.match_category_id) as match_category_id,
        entity_data.entity_name,
        MAX(entity_data.entity_id) as entity_id,
        COUNT(DISTINCT entity_data.content_detail_id)::int as count
      FROM base_content
      JOIN entity_data
        ON entity_data.content_detail_id = base_content.content_detail_id
      WHERE EXISTS (
        SELECT 1
        FROM content_with_target_answer cta
        WHERE cta.content_detail_id = base_content.content_detail_id
      )
      GROUP BY 
        base_content.match_category_name, 
        entity_data.entity_name
      ORDER BY 
        count DESC, 
        LOWER(entity_data.entity_name)
      LIMIT 25
    `;

    const result = await queryAnalytics(queryString, []);
    if (result instanceof Error) {
      return {
        metric: this.metric,
        error:
          'There was an error while fetching data for this metric. Please try again.',
      };
    }

    return {
      metric: 'product_offering_gaps',
      timeFrame: this.timeFilter?.timeRangeText,
      data: result.rows,
      summary: {
        total_gap_mentions:
          result.rows?.reduce((sum, row) => sum + parseInt(row.count), 0) || 0,
        top_gap_keyword: result.rows[0]?.entity_name || 'N/A',
        unique_gap_areas: result.rows?.length || 0,
      },
    };
  }

  private async getConfidenceLevelsOverTime() {
    const dateFilter = this.getDateFilter('rfc.created_date');

    const queryString = `
      WITH single_answers AS (
        SELECT
          cd_single_answer.id as content_detail_id,
          CASE
            WHEN lower(cd_single_answer.answer_text) = 'could not provide an answer' THEN 'none'
            WHEN cd_single_answer.confidence_score::int >= 3 THEN 'high'
            WHEN cd_single_answer.confidence_score::int = 2 THEN 'medium'
            ELSE 'low'
          END AS confidence,
          DATE_TRUNC('month', rfc.created_date) AS month_start,
          1 AS row_count
        FROM ${this.schema}.rfx_content rfc
        JOIN ${this.schema}.content c
          ON c.id = rfc.content_id
          AND LOWER(c.type) ='rfp'
          AND COALESCE(c.is_deleted, false) != true
        JOIN ${this.schema}.content_detail cd_single_answer
          ON cd_single_answer.content_id = c.id
          AND (
            cd_single_answer.answer_versions_count = 0
            OR cd_single_answer.answer_versions_count IS NULL
          )
        WHERE ${dateFilter}
      ),
      multi_answers AS (
        SELECT
          cd_multi_answer.id as content_detail_id,
          CASE
            WHEN cd_multi_answer.max_answer_version_confidence <= 0 THEN 'none'
            WHEN cd_multi_answer.max_answer_version_confidence >= 3 THEN 'high'
            WHEN cd_multi_answer.max_answer_version_confidence = 2 THEN 'medium'
            WHEN cd_multi_answer.max_answer_version_confidence = 1 THEN 'low'
            ELSE 'ignore'
          END AS confidence,
          DATE_TRUNC('month', rfc.created_date) AS month_start,
          1 AS row_count
        FROM ${this.schema}.rfx_content rfc
        JOIN ${this.schema}.content c 
          ON c.id = rfc.content_id
          AND LOWER(c.type) = 'rfp'
          AND COALESCE(c.is_deleted, false) != true
        JOIN ${this.schema}.content_detail cd_multi_answer
          ON cd_multi_answer.content_id = c.id
          AND answer_versions_count > 0
        WHERE ${dateFilter}
      ),
      all_answers AS (
        SELECT * FROM single_answers
        UNION ALL
        SELECT * FROM multi_answers
      ),
      monthly_data AS (
        SELECT
          month_start,
          SUM(CASE WHEN confidence = 'low' 
              OR confidence = 'medium' 
              OR confidence = 'high' 
            THEN row_count ELSE 0 END) AS total_count,
          SUM(CASE WHEN confidence = 'low' THEN row_count ELSE 0 END) AS low_count,
          SUM(CASE WHEN confidence = 'medium' THEN row_count ELSE 0 END) AS medium_count,
          SUM(CASE WHEN confidence = 'high' THEN row_count ELSE 0 END) AS high_count
        FROM all_answers
        WHERE confidence <> 'ignore'
        GROUP BY month_start
        ORDER BY month_start
      )
      SELECT 
        TO_CHAR(month_start, 'YYYY-MM') AS date_label,
        ROUND(low_count * 100.0 / NULLIF(total_count, 0), 0) AS low_percentage,
        ROUND(medium_count * 100.0 / NULLIF(total_count, 0), 0) AS medium_percentage,
        ROUND(high_count * 100.0 / NULLIF(total_count, 0), 0) AS high_percentage
      FROM monthly_data;
    `;

    const result = await queryAnalytics(queryString, []);
    if (result instanceof Error) {
      return {
        metric: this.metric,
        error:
          'There was an error while fetching data for this metric. Please try again.',
      };
    }

    return {
      metric: 'confidence_levels_over_time',
      timeFrame: this.timeFilter?.timeRangeText,
      data: result.rows,
      summary: {
        latest_high_confidence_rate:
          result.rows?.[result.rows.length - 1]?.high_percentage || 0,
        avg_high_confidence_rate:
          result.rows?.length > 0
            ? Math.round(
                result.rows.reduce(
                  (sum, row) => sum + parseInt(row.high_percentage || 0),
                  0,
                ) / result.rows.length,
              )
            : 0,
        periods_analyzed: result.rows?.length || 0,
      },
    };
  }

  private async getDistributionByBuyer() {
    const dateFilter = this.getDateFilter('rfc.created_date');

    const queryString = `
      WITH version_info AS (
          SELECT 
            cs.*,
            t.enabled,
            t.name AS task_name,
            COUNT(*) OVER () as total_versions,
            MAX(CASE WHEN t.enabled = TRUE THEN cs.version END) OVER () as max_enabled_version,
            MAX(cs.version) OVER () as max_version
          FROM ${this.schema}.nlp_category_set cs
          LEFT JOIN ${this.schema}.nlp_task t 
            ON t.name = CONCAT('TEXT_CAT_', cs.name) 
            AND t.version = cs.version 
          WHERE cs.name = 'RFP_PRIMARY_BUYER'
        ),
        
        selected_category_set_version AS (
          -- Apply the version selection logic
          SELECT *
          FROM version_info
          WHERE (
            -- Case 1: If there's an enabled version, use the highest enabled version
            (enabled = TRUE AND version = max_enabled_version)
            OR
            -- Case 2: If no enabled versions exist and multiple versions available, 
            -- use the second highest version
            (max_enabled_version IS NULL 
              AND total_versions > 1 
              AND version = (
                SELECT MAX(version) 
                FROM version_info 
                WHERE version < max_version
            ))
            OR
            -- Case 3: If only one version exists, return it only if enabled
            (total_versions = 1 AND enabled = TRUE)
          )
        ),

        category_counts AS (
            -- Count occurrences of each category per content by joining through content_detail
            SELECT
                cd.content_id,
                nc.name as category_name,
                COUNT(*) as category_count
            FROM ${this.schema}.nlp_category_source ncs
            JOIN ${this.schema}.content_detail cd ON cd.id = ncs.content_detail_id
            JOIN ${this.schema}.nlp_category nc ON nc.id = ncs.category_id
            WHERE nc.category_set_id = (SELECT id FROM selected_category_set_version)
            GROUP BY cd.content_id, nc.name
        ),

        primary_category AS (
            -- Get the category with highest count for each content
            SELECT
                content_id,
                category_name as rfp_target_category
            FROM (
                SELECT
                    content_id,
                    category_name,
                    category_count,
                    ROW_NUMBER() OVER (PARTITION BY content_id ORDER BY category_count DESC) as rn
                FROM category_counts
                WHERE LOWER(category_name) != LOWER('not applicable')
            ) ranked
            WHERE rn = 1
        ),

        category_breakdown AS (
            -- Create the count breakdown as a JSON object
            SELECT
                content_id,
                jsonb_object_agg(category_name, category_count) as rfp_target_category_counts
            FROM category_counts
            GROUP BY content_id
        ),

        content_details AS (
            -- Get all content details with their category information
            SELECT
                c.id as content_id,
                c.source,
                COALESCE(pc.rfp_target_category, 'Uncategorized') as rfp_target_category,
                COALESCE(cb.rfp_target_category_counts, '{}'::jsonb) as rfp_target_category_counts
            FROM ${this.schema}.content c
            JOIN ${this.schema}.rfx_content rfc ON rfc.content_id = c.id
            LEFT JOIN primary_category pc ON c.id = pc.content_id
            LEFT JOIN category_breakdown cb ON c.id = cb.content_id
            WHERE ${dateFilter}
              AND COALESCE(c.is_deleted, false) != true
        )

        -- Final aggregation grouped by the target category
        SELECT
            cd.rfp_target_category,
            COUNT(*)::int as content_count,
            -- array_agg(cd.content_id::int) as content_ids,
            jsonb_agg(
                jsonb_build_object(
                    'content_id', cd.content_id,
                    'source', cd.source,
                    'rfp_target_category_counts', cd.rfp_target_category_counts
                )
            ) as rfp_target_category_breakdown
        FROM content_details cd
        GROUP BY  cd.rfp_target_category
        ORDER BY content_count DESC;
    `;

    const result = await queryAnalytics(queryString, []);
    if (result instanceof Error) {
      return {
        metric: this.metric,
        error:
          'There was an error while fetching data for this metric. Please try again.',
      };
    }

    return {
      metric: 'distribution_by_buyer',
      timeFrame: this.timeFilter?.timeRangeText,
      data: result.rows,
      summary: {
        total_buyers: result.rows?.length || 0,
        top_buyer: result.rows[0]?.rfp_target_category || 'N/A',
        top_buyer_rfp_count: result.rows[0]?.content_count || 0,
        total_rfps:
          result.rows?.reduce(
            (sum, row) => sum + parseInt(row.content_count),
            0,
          ) || 0,
      },
    };
  }

  private async getWinRatesByUseCase() {
    const dateFilter = this.getDateFilter('sft.opportunity_system_modstamp');

    const queryString = `
      WITH version_info AS (
        SELECT 
          cs.*,
          t.enabled,
          t.name AS task_name,
          COUNT(*) OVER () as total_versions,
          MAX(CASE WHEN t.enabled = TRUE THEN cs.version END) OVER () as max_enabled_version,
          MAX(cs.version) OVER () as max_version
        FROM ${this.schema}.nlp_category_set cs
        LEFT JOIN ${this.schema}.nlp_task t 
          ON t.name = CONCAT('TEXT_CAT_', cs.name) 
          AND t.version = cs.version 
        WHERE cs.name = 'RFP_PRIMARY_USE_CASE'
      ),
      selected_category_set_version AS (
        SELECT *
        FROM version_info
        WHERE (
          -- Case 1: If there's an enabled version, use the highest enabled version
          (enabled = TRUE AND version = max_enabled_version)
          OR
          -- Case 2: If no enabled versions exist and multiple versions available, 
          -- use the second highest version
          (max_enabled_version IS NULL 
            AND total_versions > 1 
            AND version = (
              SELECT MAX(version) 
              FROM version_info 
              WHERE version < max_version
          ))
          OR
          -- Case 3: If only one version exists, return it only if enabled
          (total_versions = 1 AND enabled = TRUE)
        )
      ),
      category_counts AS (
        SELECT
          cd.content_id,
          nc.name as category_name,
          COUNT(*) as category_count
        FROM ${this.schema}.nlp_category_source ncs
        JOIN ${this.schema}.content_detail cd ON cd.id = ncs.content_detail_id
        JOIN ${this.schema}.nlp_category nc ON nc.id = ncs.category_id
        WHERE nc.category_set_id = (SELECT id FROM selected_category_set_version)
        GROUP BY cd.content_id, nc.name
      ),
      primary_category AS (
        SELECT
          content_id,
          category_name as rfp_target_category
        FROM (
          SELECT
            content_id,
            category_name,
            category_count,
            ROW_NUMBER() OVER (PARTITION BY content_id ORDER BY category_count DESC) as rn
          FROM category_counts
          WHERE LOWER(category_name) != LOWER('not applicable')
        ) ranked
        WHERE rn = 1
      ),
      content_with_stages AS (
        -- Get opportunity stages for each content
        SELECT
          c.id as content_id,
          COALESCE(pc.rfp_target_category, 'Uncategorized') as rfp_target_category,
          sft.opportunity_name as opportunity_name,
          sft.opportunity_stage as opportunity_stage,
          sft.opportunity_amount as opportunity_amount,
          c.details as c_details,
          rfc.created_date as created_date
        FROM ${this.schema}.content c
        JOIN ${this.schema}.rfx_content rfc ON rfc.content_id = c.id
        LEFT JOIN primary_category pc ON c.id = pc.content_id
        JOIN ${this.schema}.salesforce_tribblytics sft ON sft.opportunity_id = c.details ->> 'opportunity_id'
        WHERE LOWER(sft.opportunity_stage) IN ('closed won', 'closed lost', 'closed dead')
          AND ${dateFilter}
          AND COALESCE(c.is_deleted, false) != true
      )
      SELECT
        rfp_target_category as use_case,
        opportunity_stage,
        COUNT(*) as count,
        ROUND(
          COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (PARTITION BY rfp_target_category),
          2
        ) as percentage,
        (
          SELECT jsonb_agg(
            DISTINCT jsonb_build_array(
              jsonb_build_object(
                'label', 'Customer', 
                'data', c_details -> 'customer_name'
              ),
              jsonb_build_object(
                'label', 'Opportunity', 
                'data', opportunity_name
              ),
              jsonb_build_object(
                'label', 'Opportunity Stage', 
                'data', opportunity_stage
              ),
              jsonb_build_object(
                'label', 'Opportunity Amount', 
                'data', opportunity_amount::money
              ),
              jsonb_build_object(
                'label', 'File', 
                'data', c_details -> 'fileName'
              ),
              jsonb_build_object(
                'label', 'Created Date', 
                'data', created_date::date
              )
            )
          )
          -- WARNING: client opportunity_stage might differ from client to client
          WHERE LOWER(opportunity_stage) = 'closed won'
        ) AS drilldown_primary,
        (
          SELECT jsonb_agg(
            DISTINCT jsonb_build_array(
              jsonb_build_object(
                'label', 'Customer', 
                'data', c_details -> 'customer_name'
              ),
              jsonb_build_object(
                'label', 'Opportunity', 
                'data', opportunity_name
              ),
              jsonb_build_object(
                'label', 'Opportunity Stage', 
                'data', opportunity_stage
              ),
              jsonb_build_object(
                'label', 'Opportunity Amount', 
                'data', opportunity_amount::money
              ),
              jsonb_build_object(
                'label', 'File', 
                'data', c_details -> 'fileName'
              ),
              jsonb_build_object(
                'label', 'Created Date', 
                'data', created_date::date
              )
            )
          )
          -- WARNING: client opportunity_stage might differ from client to client
          WHERE LOWER(opportunity_stage) IN ('closed lost', 'closed dead')
        ) AS drilldown_secondary
      FROM content_with_stages
      GROUP BY use_case, opportunity_stage
      ORDER BY use_case, opportunity_stage DESC
      LIMIT 100
    `;

    const result = await queryAnalytics(queryString, []);
    if (result instanceof Error) {
      return {
        metric: this.metric,
        error:
          'There was an error while fetching data for this metric. Please try again.',
      };
    }

    return {
      metric: 'win_rates_by_use_case',
      timeFrame: this.timeFilter?.timeRangeText,
      data: result.rows,
      summary: {
        use_cases_analyzed:
          new Set(result.rows?.map((row) => row.use_case)).size || 0,
        total_deals_analyzed:
          result.rows?.reduce((sum, row) => sum + parseInt(row.count), 0) || 0,
      },
    };
  }

  private async getResponseScoresByFeature() {
    const dateFilter = this.getDateFilter('rfc.created_date');

    const queryString = `
        -- Single version answers
        WITH single_answers AS (
          SELECT
            cd_single_answer.id as content_detail_id,
            CASE
              WHEN lower(cd_single_answer.answer_text) = 'could not provide an answer' THEN 'none'
              WHEN cd_single_answer.confidence_score::int >= 3 THEN 'high'
              WHEN cd_single_answer.confidence_score::int = 2 THEN 'medium'
              ELSE 'low'
            END AS confidence,
            DATE_TRUNC('month', rfc.created_date) AS month_start,
            1 AS row_count
          FROM ${this.schema}.rfx_content rfc
          JOIN ${this.schema}.content c
            ON c.id = rfc.content_id
            AND LOWER(c.type) ='rfp'
            AND COALESCE(c.is_deleted, false) != true
          JOIN ${this.schema}.content_detail cd_single_answer
            ON cd_single_answer.content_id = c.id
            AND (
              cd_single_answer.answer_versions_count = 0
              OR cd_single_answer.answer_versions_count IS NULL
            )
          WHERE ${dateFilter}
        ),
        -- Multi version answers
        multi_answers AS (
          SELECT
            cd_multi_answer.id as content_detail_id,
            CASE
              WHEN cd_multi_answer.max_answer_version_confidence <= 0 THEN 'none'
              WHEN cd_multi_answer.max_answer_version_confidence >= 3 THEN 'high'
              WHEN cd_multi_answer.max_answer_version_confidence = 2 THEN 'medium'
              WHEN cd_multi_answer.max_answer_version_confidence = 1 THEN 'low'
              ELSE 'ignore' -- need to handle null case special in this query for some reason
            END AS confidence,
            DATE_TRUNC('month', rfc.created_date) AS month_start,
            1 AS row_count
          FROM ${this.schema}.rfx_content rfc
          JOIN ${this.schema}.content c 
            ON c.id = rfc.content_id
            AND LOWER(c.type) = 'rfp'
            AND COALESCE(c.is_deleted, false) != true
          JOIN ${this.schema}.content_detail cd_multi_answer
            ON cd_multi_answer.content_id = c.id
            AND answer_versions_count > 0
          WHERE ${dateFilter}
        ),
        all_answers AS (
          SELECT * FROM single_answers
          UNION ALL
          SELECT * FROM multi_answers
        ),
        monthly_data AS (
          SELECT
            month_start,
            -- Excluding the none confidence level
            SUM(CASE WHEN confidence = 'low' 
                OR confidence = 'medium' 
                OR confidence = 'high' 
              THEN row_count ELSE 0 END) AS total_count,
            SUM(CASE WHEN confidence = 'low' THEN row_count ELSE 0 END) AS low_count,
            SUM(CASE WHEN confidence = 'medium' THEN row_count ELSE 0 END) AS medium_count,
            SUM(CASE WHEN confidence = 'high' THEN row_count ELSE 0 END) AS high_count,
            array_agg(DISTINCT content_detail_id) FILTER (WHERE confidence = 'low') as low_content_detail_ids,
            array_agg(DISTINCT content_detail_id) FILTER (WHERE confidence = 'medium') as medium_content_detail_ids,
            array_agg(DISTINCT content_detail_id) FILTER (WHERE confidence = 'high') as high_content_detail_ids
          FROM all_answers
          WHERE confidence <> 'ignore'
          GROUP BY month_start
          ORDER BY month_start
        ),
        monthly_data_percentages AS (
          SELECT 
            TO_CHAR(month_start, 'YYYY-MM') AS date_label,
            ROUND(low_count * 100.0 / NULLIF(total_count, 0), 0) AS low_percentage,
            ROUND(medium_count * 100.0 / NULLIF(total_count, 0), 0) AS medium_percentage,
            ROUND(high_count * 100.0 / NULLIF(total_count, 0), 0) AS high_percentage,
            COALESCE(low_content_detail_ids, '{}'::int[]) AS low_content_detail_ids,
            COALESCE(medium_content_detail_ids, '{}'::int[]) AS medium_content_detail_ids,
            COALESCE(high_content_detail_ids, '{}'::int[]) AS high_content_detail_ids
          FROM monthly_data
        )
        SELECT
          JSONB_BUILD_OBJECT(
            'labels', ARRAY_AGG(date_label ORDER BY date_label),
            'datasets', ARRAY[
            JSONB_BUILD_OBJECT(
              'label', 'Low', 
              -- 'modalData', jsonb_agg(low_content_detail_ids ORDER BY date_label),
              'data', ARRAY_AGG(low_percentage ORDER BY date_label)
            ),
            JSONB_BUILD_OBJECT(
              'label', 'Medium',
              -- 'modalData', jsonb_agg(medium_content_detail_ids ORDER BY date_label)
              'data', ARRAY_AGG(medium_percentage ORDER BY date_label)
            ),
            JSONB_BUILD_OBJECT(
              'label', 'High',
              -- 'modalData', jsonb_agg(high_content_detail_ids ORDER BY date_label)
              'data', ARRAY_AGG(high_percentage ORDER BY date_label)
            )
          ]) AS results_percentage
        FROM monthly_data_percentages;
    `;

    const result = await queryAnalytics(queryString, []);
    if (result instanceof Error) {
      return {
        metric: this.metric,
        error:
          'There was an error while fetching data for this metric. Please try again.',
      };
    }

    return {
      metric: 'response_scores_by_feature',
      timeFrame: this.timeFilter?.timeRangeText,
      data: result.rows,
      summary: {
        features_analyzed:
          new Set(result.rows?.map((row) => row.feature_category)).size || 0,
        total_responses:
          result.rows?.reduce(
            (sum, row) => sum + parseInt(row.total_responses),
            0,
          ) || 0,
        avg_confidence_across_features:
          result.rows?.length > 0
            ? Math.round(
                (result.rows.reduce(
                  (sum, row) => sum + parseFloat(row.avg_confidence_score),
                  0,
                ) /
                  result.rows.length) *
                  100,
              ) / 100
            : 0,
      },
    };
  }

  private async getProductOfferingBolster() {
    const dateFilter = this.getDateFilter('rfc.created_date');

    const queryString = `
      WITH target_matching_category AS (
        SELECT id
        FROM ${this.schema}.nlp_category
        WHERE name = 'Product Features'
      ),
      target_answer_category AS (
        SELECT id, name
        FROM ${this.schema}.nlp_category
        WHERE name = 'Strong Match'
      ),
      content_with_target_answer AS (
        SELECT DISTINCT ncs.content_detail_id
        FROM ${this.schema}.nlp_category_source ncs
        WHERE ncs.category_id = (SELECT id FROM target_matching_category)
      ),
      base_content AS (
        SELECT 
          cd.id as content_detail_id,
          answer_cat.name as match_category_name,
          answer_cat.id as match_category_id,
          c.id as content_id,
          cd.confidence_score
        FROM ${this.schema}.nlp_category_source ncs
        JOIN target_answer_category answer_cat
          ON answer_cat.id = ncs.category_id
        JOIN ${this.schema}.content_detail cd
          ON cd.id = ncs.content_detail_id
        JOIN ${this.schema}.content c
          ON c.id = cd.content_id
        JOIN ${this.schema}.rfx_content rfc
          ON rfc.content_id = c.id
        WHERE ${dateFilter}
          AND COALESCE(c.is_deleted, false) != true
          AND cd.confidence_score >= 3
      ),
      client_name_setting AS (
        SELECT value_string as client_name
        FROM ${this.schema}.client_setting
        WHERE setting_id = (
          SELECT id
          FROM tribble.setting
          WHERE name = 'client_name'
        )
      ),
      entity_data AS (
        SELECT 
          ents.content_detail_id,
          COALESCE(ent_canonical.name, ent.name) as entity_name,
          COALESCE(ent_canonical.id, ent.id) as entity_id
        FROM ${this.schema}.nlp_entity_source ents
        LEFT JOIN ${this.schema}.nlp_entity ent
          ON ent.id = ents.entity_id
        LEFT JOIN ${this.schema}.nlp_entity ent_canonical
          ON ent_canonical.id = ent.canonical_id
        JOIN client_name_setting cns ON 1=1
        WHERE COALESCE(ent_canonical.name, ent.name) IS NOT NULL
          AND LOWER(ent.name) != LOWER(cns.client_name)
          AND LOWER(ent.name) != (LOWER(cns.client_name) || ' bot')     
          AND LOWER(COALESCE(ent_canonical.name, ent.name)) != LOWER(cns.client_name)
          AND LOWER(COALESCE(ent_canonical.name, ent.name)) != (LOWER(cns.client_name) || ' bot')
      )
      SELECT 
        base_content.match_category_name,
        MAX(base_content.match_category_id) as match_category_id,
        entity_data.entity_name,
        MAX(entity_data.entity_id) as entity_id,
        COUNT(DISTINCT entity_data.content_detail_id)::int as count,
        ROUND(AVG(base_content.confidence_score), 2) as avg_confidence
      FROM base_content
      JOIN entity_data
        ON entity_data.content_detail_id = base_content.content_detail_id
      WHERE EXISTS (
        SELECT 1
        FROM content_with_target_answer cta
        WHERE cta.content_detail_id = base_content.content_detail_id
      )
      GROUP BY 
        base_content.match_category_name, 
        entity_data.entity_name
      ORDER BY 
        count DESC, 
        avg_confidence DESC,
        LOWER(entity_data.entity_name)
      LIMIT 25;
    `;

    const result = await queryAnalytics(queryString, []);
    if (result instanceof Error) {
      return {
        metric: this.metric,
        error:
          'There was an error while fetching data for this metric. Please try again.',
      };
    }

    return {
      metric: 'product_offering_to_bolster',
      timeFrame: this.timeFilter?.timeRangeText,
      data: result.rows,
      summary: {
        unique_categories: result.rows?.length || 0,
        avg_confidence_across_categories:
          result.rows?.length > 0
            ? Math.round(
                (result.rows.reduce(
                  (sum, row) => sum + parseFloat(row.avg_confidence),
                  0,
                ) /
                  result.rows.length) *
                  100,
              ) / 100
            : 0,
      },
    };
  }

  private async getDealsLostTopTopics() {
    const dateFilter = this.getDateFilter('sft.opportunity_system_modstamp');

    const queryString = `
      WITH lost_deals AS (
        SELECT DISTINCT
          c.id as content_id,
          sft.opportunity_id,
          sft.opportunity_stage,
          sft.opportunity_amount
        FROM ${this.schema}.content c
        JOIN ${this.schema}.salesforce_tribblytics sft ON sft.opportunity_id = c.details ->> 'opportunity_id'
        WHERE ${dateFilter}
          AND LOWER(sft.opportunity_stage) IN ('closed lost', 'closed dead')
          AND COALESCE(c.is_deleted, false) != true
      ),
      lost_deal_topics AS (
        SELECT 
          nc.name as topic_category,
          nc.id as category_id,
          COUNT(DISTINCT ld.opportunity_id)::int as lost_deals_count,
          SUM(COALESCE(ld.opportunity_amount::numeric, 0))::bigint as lost_opportunity_value,
          COUNT(DISTINCT ncs.content_detail_id)::int as question_instances,
          ROUND(AVG(cd.confidence_score), 2) as avg_confidence_score
        FROM lost_deals ld
        JOIN ${this.schema}.content_detail cd ON cd.content_id = ld.content_id
        JOIN ${this.schema}.nlp_category_source ncs ON ncs.content_detail_id = cd.id
        JOIN ${this.schema}.nlp_category nc ON nc.id = ncs.category_id
        WHERE nc.name NOT ILIKE '%not applicable%'
          AND nc.name NOT ILIKE '%other%'
        GROUP BY nc.name, nc.id
      )
      SELECT 
        topic_category,
        lost_deals_count,
        lost_opportunity_value,
        question_instances,
        avg_confidence_score,
        ROUND(lost_deals_count * 100.0 / SUM(lost_deals_count) OVER (), 2) as percentage_of_lost_deals
      FROM lost_deal_topics
      ORDER BY lost_deals_count DESC, lost_opportunity_value DESC
      LIMIT 20;
    `;

    const result = await queryAnalytics(queryString, []);
    if (result instanceof Error) {
      return {
        metric: this.metric,
        error:
          'There was an error while fetching data for this metric. Please try again.',
      };
    }

    return {
      metric: 'deals_lost_top_topics',
      timeFrame: this.timeFilter?.timeRangeText,
      data: result.rows,
      summary: {
        total_lost_deals:
          result.rows?.reduce(
            (sum, row) => sum + parseInt(row.lost_deals_count),
            0,
          ) || 0,
        total_lost_value:
          result.rows?.reduce(
            (sum, row) => sum + parseInt(row.lost_opportunity_value || 0),
            0,
          ) || 0,
        top_loss_topic: result.rows[0]?.topic_category || 'N/A',
        topics_analyzed: result.rows?.length || 0,
        avg_confidence_for_lost_topics:
          result.rows?.length > 0
            ? Math.round(
                (result.rows.reduce(
                  (sum, row) => sum + parseFloat(row.avg_confidence_score || 0),
                  0,
                ) /
                  result.rows.length) *
                  100,
              ) / 100
            : 0,
      },
    };
  }
}
