import { Tool } from '@tribble/chat-completion-service/client';
import { CONSTANTS, Logger } from '@tribble/common-utils';
import { ClientSchema } from '@tribble/tribble-db';
import { queryAnalytics } from '@tribble/tribble-db/db_ops';
import { ClientId, ConversationId, UserId } from '@tribble/tribble-db/types';
import { TimeFilter } from '../extract_temporal.ts';
import { ToolCall } from '../tool_call.ts';

export class ConversationIntelligence extends ToolCall {
  static tool_name = 'conversation_intelligence';
  static defaultTimeFilter = {
    timeRangeText: 'last 90 days',
    startDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
  };

  private clientId: ClientId;
  private clientName: string;
  private conversationId: ConversationId;
  private schema: ClientSchema;
  private tribbleUserId: UserId;

  private timeFilter?: TimeFilter;
  private metric: string;
  private userFilter?: string;
  private channelFilter?: string;
  private customInstructions?: string;
  constructor(
    config: {
      clientId: ClientId;
      clientName: string;
      conversationId: ConversationId;
      schema: ClientSchema;
      tribbleUserId: UserId;
      timeFilter?: TimeFilter;
      logger?: Logger;
    },
    args: {
      metric: string;
      userFilter?: string;
      channelFilter?: string;
      customInstructions?: string;
    },
  ) {
    super(config.logger);

    this.clientId = config.clientId;
    this.clientName = config.clientName;
    this.conversationId = config.conversationId;
    this.schema = config.schema;
    this.tribbleUserId = config.tribbleUserId;
    this.timeFilter = config.timeFilter || {};

    this.metric = args.metric;
    this.userFilter = args.userFilter;
    this.channelFilter = args.channelFilter;
    this.customInstructions = args.customInstructions;
  }

  static getPromptDescription(params: any): string {
    const clientName = params.clientName || 'our company';
    return (
      `${this.tool_name}: Retrieves conversation analytics and insights for ${clientName}. ` +
      `Can show metrics like user activity, popular topics from ingested Slack channels, popular keywords from conversations with Tribble, ` +
      `popular conversation topics with Tribble, and question patterns. ` +
      `Use this tool AFTER using extract_temporal to parse any time constraints from the user's query.`
    );
  }

  static getJsonSchema(params?: any): Tool {
    const clientName = params.clientName || 'our company';

    return {
      type: 'function',
      function: {
        name: 'conversation_intelligence',
        description:
          'Get analytics and insights about conversations in your organization.',
        parameters: {
          type: 'object',
          properties: {
            metric: {
              type: 'string',
              enum: [
                'tribble_active_users',
                'tribble_user_activity',
                'tribble_peak_hours',
                'tribble_question_types',
                'tribble_conversation_topics',
                'tribble_conversation_keywords',
                'tribble_conversations_custom_metric',
                'tribble_conversation_top_product_features',
              ],
              description:
                'The type of metric to retrieve. The metrics prefixed with "tribble_" relates to direct conversations with Tribble. ' +
                `The metrics prefixed with "ingested_" relates to ${clientName}'s Slack channels that Tribble can see.` +
                'You should determine if the user is asking about conversations with Tribble, or about general company conversations before determining which metric to use. If unclear, ask the user for clarification.' +
                'Use "tribble_conversations_custom_metric" for metrics relating to conversations with Tribble that don\'t have a specific metric. Pass through customInstructions based on the user query to help the LLM analyze the conversation data.' +
                '**IMPORTANT**: Never mix metrics from those prefixed with "ingested_" with metrics from those prefixed with "tribble_". They represent different data that should not be combined.',
            },
            customInstructions: {
              type: 'string',
              description:
                'For "tribble_conversations_custom_metric": Pass through custom instructions based on the user query to help the LLM analyze the conversation data.',
            },
            userFilter: {
              type: 'string',
              description:
                'Optional: Filter by specific user email or name pattern',
            },
            channelFilter: {
              type: 'string',
              description:
                'Optional: Filter by specific Slack channel name or ID',
            },
          },
          required: ['metric'],
        },
      },
    };
  }

  getPendingMessage(): string {
    const timeDescription = this.timeFilter?.timeRangeText
      ? `for ${this.timeFilter?.timeRangeText} `
      : '';
    return `_Analyzing ${this.metric.replace(/_/g, ' ')} ${timeDescription}..._ :chart_with_upwards_trend:`;
  }

  getCompletionMessage(): string {
    const timeDescription = this.timeFilter?.timeRangeText
      ? `for ${this.timeFilter?.timeRangeText} `
      : '';
    return `Analyzed ${this.metric.replace(/_/g, ' ')} analytics data ${timeDescription}:chart_with_upwards_trend:`;
  }

  async performCall(): Promise<string> {
    try {
      let result: any;

      switch (this.metric) {
        case 'tribble_active_users':
          result = await this.getActiveUsers();
          break;
        case 'tribble_user_activity':
          result = await this.getUserActivity();
          break;
        case 'tribble_peak_hours':
          result = await this.getPeakHours();
          break;
        case 'tribble_question_types':
          result = await this.getQuestionTypes();
          break;
        case 'tribble_conversation_topics':
          // getNlpTopics is the old way with pre-calculated NLP tables
          // Using nlpTableTopics produces the same bad results as the old Tribblytics dashboard
          // const nlpTableTopics = await this.getNlpTopics();
          // getConversationTopicsUsingFatContext gives the LLM the raw messages; topic extraction on runtime
          const convTopicsRawContext = await this.getConversationsRawContext(
            'tribble_conversation_topics',
          );
          result = convTopicsRawContext;
          break;
        case 'tribble_conversation_keywords':
          // getConversationKeywords is the old way with pre-calculated NLP tables
          const nlpTableKeywords = await this.getConversationKeywords();
          // getConversationKeywordsUsingFatContext gives the LLM the raw messages; keyword extraction on runtime
          const convKeywordsRawContext = await this.getConversationsRawContext(
            'tribble_conversation_keywords',
            '- Focus on patterns of interesting keywords and entities mentioned in the conversations\n' +
              `- Focus on entities that are Product names and companies, but not the client name (${this.clientName}).`,
          );
          // WCGW if we combine the two contexts 🤔
          result = {
            nlpTableKeywords: {
              ...nlpTableKeywords,
              summary: {
                ...nlpTableKeywords.summary,
                instructions:
                  'These are pre-calculated keywords from the NLP tables. Use them as a starting point to analyze the conversation data, or for additional context.',
              },
            },
            convKeywordsRawContext,
          };
          break;
        case 'tribble_conversation_top_product_features':
          result = await this.getConversationsRawContext(
            'tribble_conversation_top_product_features',
            '- Focus on questions and discussions about product features in the conversations\n' +
              `- Focus only on entities that are product names of our company (${this.clientName}).`,
          );
          break;
        case 'tribble_conversations_custom_metric':
          result = await this.getConversationsRawContext(
            'tribble_conversations_custom_metric',
            this.customInstructions,
          );
          break;
        default:
          throw new Error(`Unknown metric: ${this.metric}`);
      }

      return JSON.stringify(result);
    } catch (error) {
      console.error(`[ConversationIntelligence] Error: ${error.message}`);

      throw error;
    }
  }

  private getDateFilter(dateColumn: string = 'a.date_timestamp'): string {
    if (
      !this.timeFilter ||
      (!this.timeFilter.startDate && !this.timeFilter.endDate)
    ) {
      // Default time filter if no time filter specified by user
      this.timeFilter = ConversationIntelligence.defaultTimeFilter; // set timeFilter to default
      const defaultStartDate =
        ConversationIntelligence.defaultTimeFilter.startDate;
      return ` ${dateColumn} >= '${defaultStartDate.toISOString()}'::timestamp`;
    }

    const conditions: string[] = [];

    if (this.timeFilter.startDate) {
      conditions.push(
        `${dateColumn} >= '${this.timeFilter.startDate.toISOString()}'::timestamp`,
      );
    }

    if (this.timeFilter.endDate) {
      conditions.push(
        `${dateColumn} <= '${this.timeFilter.endDate.toISOString()}'::timestamp`,
      );
    }

    return conditions.length > 0 ? ` (${conditions.join(' AND ')})` : '';
  }

  private getOrderByClause(): string {
    if (
      !this.timeFilter?.sortOrder ||
      this.timeFilter.sortOrder === 'not_applicable'
    ) {
      return 'ORDER BY date DESC';
    }

    return this.timeFilter.sortOrder === 'newest'
      ? 'ORDER BY date DESC'
      : 'ORDER BY date ASC';
  }

  private getUserFilter(clientId: number): string {
    // If we're in a non-Tribble instance, filter out users with emails that end in @tribble.ai
    // If we're in the Tribble instance, then don't filter.
    function isQueryingTribbleInstance(clientId: number) {
      const envVarTribbleClientIds = process.env.TRIBBLE_CLIENT_IDS || '';
      const tribbleClientIds = envVarTribbleClientIds
        .split(',')
        .map((id: string) => parseInt(id));
      return (
        envVarTribbleClientIds == 'all' ||
        tribbleClientIds.includes(Number(clientId)) ||
        process.env.ENVIRONMENT === 'development'
      );
    }

    return isQueryingTribbleInstance(clientId)
      ? ''
      : ` AND u.email NOT LIKE '%${CONSTANTS.TRIBBLE_EMAIL_SUFFIX}' `;
  }

  private async getActiveUsers() {
    const dateFilter = this.getDateFilter();
    const channelFilter = this.channelFilter
      ? `AND c.channel = '${this.channelFilter}'`
      : '';
    const userFilter = this.getUserFilter(this.clientId);
    const extraUserFilter = this.userFilter
      ? `AND u.name ILIKE '%${this.userFilter}%'`
      : '';

    const queryString = `
      SELECT 
        u.name,
        COUNT(DISTINCT DATE(a.date_timestamp)) as active_days,
        COUNT(*) as total_messages
      FROM tribble.activity_log a
      JOIN tribble.user u ON u.id = a.user_id ${userFilter}
      JOIN ${this.schema}.conversation_detail cd ON cd.id = a.source_id::integer
      WHERE a.client_id = ${this.clientId}
        AND a.source_table = 'conversation_detail'
        AND a.event = 'slack_message'
        AND cd.is_background_agent_message = false
        AND ${dateFilter}
        ${channelFilter}
        ${extraUserFilter}
      GROUP BY u.id, u.name, u.email
      ORDER BY total_messages DESC
      LIMIT 20
    `;

    const result = await queryAnalytics(queryString, []);
    if (result instanceof Error) {
      return {
        metric: this.metric,
        error:
          'There was an error while fetching data for this metric. Please try again.',
      };
    }

    return {
      metric: 'tribble_active_users',
      timeFrame: this.timeFilter?.timeRangeText,
      data: result.rows,
      summary: {
        total_active_users: result.rows?.length || 0,
        most_active_user: result.rows[0]?.name || 'N/A',
      },
    };
  }

  private async getUserActivity() {
    const dateFilter = this.getDateFilter();
    const userFilter = this.getUserFilter(this.clientId);
    const extraUserFilter = this.userFilter
      ? `AND u.name ILIKE '%${this.userFilter}%'`
      : '';

    const queryString = `
      SELECT 
        EXTRACT(HOUR FROM a.date_timestamp) as hour,
        EXTRACT(DOW FROM a.date_timestamp) as day_of_week,
        COUNT(*) as message_count
      FROM tribble.activity_log a
      JOIN tribble.user u ON u.id = a.user_id ${userFilter}
      JOIN ${this.schema}.conversation_detail cd ON cd.id = a.source_id::integer
      WHERE a.client_id = ${this.clientId}
        AND a.source_table = 'conversation_detail'
        AND a.event IN ('slack_message', 'webchat_message', 'teams_message')
        AND cd.is_background_agent_message = false
        AND ${dateFilter}
        ${extraUserFilter}
      GROUP BY hour, day_of_week
      ORDER BY hour, day_of_week
    `;

    const result = await queryAnalytics(queryString, []);
    if (result instanceof Error) {
      return {
        metric: this.metric,
        error:
          'There was an error while fetching data for this metric. Please try again.',
      };
    }

    return {
      metric: 'tribble_user_activity',
      timeFrame: this.timeFilter?.timeRangeText,
      data: result.rows,
      summary: {
        peak_hour: this.findPeakHour(result.rows),
        peak_day: this.findPeakDay(result.rows),
      },
    };
  }

  private async getPeakHours() {
    const dateFilter = this.getDateFilter();
    const userFilter = this.getUserFilter(this.clientId);
    const extraUserFilter = this.userFilter
      ? `AND u.name ILIKE '%${this.userFilter}%'`
      : '';

    const queryString = `
      SELECT
        EXTRACT(HOUR FROM a.date_timestamp) as hour,
        COUNT(*) as message_count,
        COUNT(DISTINCT a.user_id) as unique_users
      FROM tribble.activity_log a
      JOIN ${this.schema}.conversation_detail cd ON cd.id = a.source_id::integer
      JOIN tribble.user u ON u.id = cd.user_id
        AND u.client_id = ${this.clientId}
        ${userFilter}
      WHERE a.client_id = ${this.clientId}
        AND a.source_table = 'conversation_detail'
        AND a.event IN ('slack_message', 'webchat_message', 'teams_message')
        AND cd.is_background_agent_message = false
        AND ${dateFilter}
        ${extraUserFilter}
      GROUP BY hour
      ORDER BY hour
    `;

    const result = await queryAnalytics(queryString, []);
    if (result instanceof Error) {
      return {
        metric: this.metric,
        error:
          'There was an error while fetching data for this metric. Please try again.',
      };
    }

    return {
      metric: 'tribble_peak_hours',
      timeFrame: this.timeFilter?.timeRangeText,
      data: result.rows,
      summary: {
        busiest_hour: this.findPeakHour(result.rows),
        quietest_hour: this.findQuietestHour(result.rows),
      },
    };
  }

  private async getQuestionTypes() {
    // This would analyze question patterns in conversations
    // For now, returning basic stats on conversations that might be questions
    const dateFilter = this.getDateFilter('cd.created_date');
    const userFilter = this.getUserFilter(this.clientId);
    const extraUserFilter = this.userFilter
      ? `AND u.name ILIKE '%${this.userFilter}%'`
      : '';

    const queryString = `
      SELECT 
        CASE 
          WHEN cd.message::jsonb->>'content' ILIKE '%how%' THEN 'how_to'
          WHEN cd.message::jsonb->>'content' ILIKE '%what%' THEN 'what_is'
          WHEN cd.message::jsonb->>'content' ILIKE '%why%' THEN 'why'
          WHEN cd.message::jsonb->>'content' ILIKE '%when%' THEN 'when'
          WHEN cd.message::jsonb->>'content' ILIKE '%where%' THEN 'where'
          ELSE 'other'
        END as question_type,
        COUNT(*) as count
      FROM ${this.schema}.conversation_detail cd
      JOIN ${this.schema}.conversation c ON c.id = cd.conversation_id
      JOIN tribble.user u ON u.id = cd.user_id
        AND u.client_id = ${this.clientId}
        ${userFilter}
      WHERE TRUE
        AND cd.message::jsonb->>'content' SIMILAR TO '%(how|what|why|when|where)%'
        AND LOWER(c.system) IN ('slack', 'webchat', 'teams')
        AND cd.is_background_agent_message = false
        AND cd.message::jsonb->>'role' = 'user'
        AND ${dateFilter}
        ${extraUserFilter}
      GROUP BY question_type
      ORDER BY count DESC
    `;

    const result = await queryAnalytics(queryString, []);
    if (result instanceof Error) {
      return {
        metric: this.metric,
        error:
          'There was an error while fetching data for this metric. Please try again.',
      };
    }

    return {
      metric: 'tribble_question_types',
      timeFrame: this.timeFilter?.timeRangeText,
      data: result.rows,
      summary: {
        most_common_type: result.rows[0]?.question_type || 'N/A',
        total_questions:
          result.rows?.reduce((sum, row) => sum + parseInt(row.count), 0) || 0,
      },
    };
  }

  private findPeakHour(data: any[]): number {
    if (!data.length) return 0;
    const peakRow = data.reduce((max, row) =>
      parseInt(row.message_count) > parseInt(max.message_count) ? row : max,
    );
    return parseInt(peakRow.hour);
  }

  private findQuietestHour(data: any[]): number {
    if (!data.length) return 0;
    const quietRow = data.reduce((min, row) =>
      parseInt(row.message_count) < parseInt(min.message_count) ? row : min,
    );
    return parseInt(quietRow.hour);
  }

  private findPeakDay(data: any[]): string {
    const dayNames = [
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ];
    const dayTotals = new Map<number, number>();

    data.forEach((row) => {
      const day = parseInt(row.day_of_week);
      const count = parseInt(row.message_count);
      dayTotals.set(day, (dayTotals.get(day) || 0) + count);
    });

    let peakDay = 0;
    let peakCount = 0;
    dayTotals.forEach((count, day) => {
      if (count > peakCount) {
        peakCount = count;
        peakDay = day;
      }
    });

    return dayNames[peakDay];
  }

  private async getNlpTopics() {
    const dateFilter = this.getDateFilter('c.created_date');

    const queryString = `
      SELECT 
        cat.name as category_name,
        MAX(cat.id) as category_id,
        COUNT(cats.*)::int as count,
        COUNT(DISTINCT c.id)::int as count_conversation
      FROM ${this.schema}.nlp_category_source cats
      JOIN ${this.schema}.nlp_category cat
        ON cat.id = cats.category_id
        AND cat.category_set_id = (
          SELECT id
          FROM ${this.schema}.nlp_category_set
          WHERE name = 'GENERAL'
        )
      JOIN ${this.schema}.conversation c
        ON c.id = cats.conversation_id
        AND c.system != 'UNKNOWN'
      WHERE ${dateFilter}
      GROUP BY cat.name
      HAVING COUNT(cats.*) > 1
      ORDER BY COUNT(cats.*) DESC, LOWER(cat.name)
      LIMIT 8
    `;

    const result = await queryAnalytics(queryString, []);
    if (result instanceof Error) {
      return {
        metric: this.metric,
        error:
          'There was an error while fetching data for this metric. Please try again.',
      };
    }

    return {
      metric: 'tribble_conversation_topics',
      timeFrame: this.timeFilter?.timeRangeText,
      data: result.rows,
      summary: {
        total_topics: result.rows?.length || 0,
        most_discussed_topic: result.rows[0]?.category_name || 'N/A',
        total_mentions:
          result.rows?.reduce((sum, row) => sum + parseInt(row.count), 0) || 0,
      },
    };
  }

  private async getConversationsRawContext(
    metric: string,
    extraInstructions: string = '',
  ) {
    const dateFilter = this.getDateFilter('c.created_date');
    const userFilter = this.getUserFilter(this.clientId);
    const extraUserFilter = this.userFilter
      ? `AND u.name ILIKE '%${this.userFilter}%'`
      : '';

    const queryString = `
      SELECT 
        -- @TODO: Map channel (channel id for Slack) to channel name
        c.channel,
        cd.message->>'content' as message_content,
        u.name as user_name,
        c.created_date
      FROM ${this.schema}.conversation c
      JOIN ${this.schema}.conversation_detail cd ON cd.conversation_id = c.id
      JOIN tribble.user u ON u.id = cd.user_id
        AND u.client_id = ${this.clientId}
        ${userFilter}
      WHERE cd.is_background_agent_message = false
        AND LOWER(c.system) IN ('slack', 'webchat', 'teams')
        AND cd.message->>'role' = 'user'
        AND ${dateFilter}
        ${extraUserFilter}
      ORDER BY c.created_date DESC
      LIMIT 500
    `;

    const result = await queryAnalytics(queryString, []);
    if (result instanceof Error) {
      return {
        metric: this.metric,
        error:
          'There was an error while fetching data for this metric. Please try again.',
      };
    }

    const imageUrlJsonRegex = /"data:image\/[^"]+;base64,[^"]+"/g;
    const resultLean = result.rows?.map((row) => ({
      ...row,
      message_content: row.message_content.replace(
        imageUrlJsonRegex,
        '"image_removed"',
      ),
    }));

    return {
      metric: metric,
      timeFrame: this.timeFilter?.timeRangeText,
      data: resultLean,
      summary: {
        instructions: `
          Use the following instructions to analyze the conversations:
          - Use the message_content to determine the topic of the conversation.
            - Remember: The message_content is the message that the user sent to the Tribble Bot.
          - Use the user_name to determine the user who sent the message.
          - Use the created_date to determine the date and time of the conversation.
          ${extraInstructions}
        `,
        total_messages: result.rows?.length || 0,
      },
    };
  }

  private async getConversationKeywords() {
    const dateFilter = this.getDateFilter('cd.created_date');
    const userFilter = this.getUserFilter(this.clientId);
    const extraUserFilter = this.userFilter
      ? `AND u.name ILIKE '%${this.userFilter}%'`
      : '';

    const queryString = `
      WITH client_name_setting AS (
        SELECT value_string as client_name
        FROM ${this.schema}.client_setting
        WHERE setting_id = (
          SELECT id
          FROM tribble.setting
          WHERE name = 'client_name'
        )
      ),
      entities AS (
        SELECT
          ent.id as base_entity_id,
          COALESCE(ent_canonical.name, ent.name) as entity_name,
          COALESCE(ent_canonical.id, ent.id) as entity_id
        FROM ${this.schema}.nlp_entity ent      
        LEFT JOIN ${this.schema}.nlp_entity ent_canonical
          ON ent.canonical_id = ent_canonical.id
        JOIN client_name_setting cns ON 1=1
        WHERE (ent_canonical.name IS NOT NULL or ent.name IS NOT NULL)
          AND LOWER(ent.name) != LOWER(cns.client_name)
          AND LOWER(ent.name) != (LOWER(cns.client_name) || ' bot')     
          AND LOWER(COALESCE(ent_canonical.name, ent.name)) != LOWER(cns.client_name)
          AND LOWER(COALESCE(ent_canonical.name, ent.name)) != (LOWER(cns.client_name) || ' bot')       
      ), 
      unique_conversation_count AS (
        SELECT COUNT(DISTINCT cd.id)::int as count
        FROM ${this.schema}.nlp_entity_source es
        JOIN ${this.schema}.conversation_detail cd
          ON cd.id = es.conversation_detail_id
          AND cd.is_background_agent_message = FALSE
          AND cd.is_injected_message = FALSE
        WHERE ${dateFilter}
      )
      SELECT 
        entities.entity_name as entity_name,
        MAX(entities.entity_id) as entity_id,
        COUNT(es.*)::int as count,
        COUNT(DISTINCT cd.id)::int as count_conversation_qa,
        LOG(COUNT(DISTINCT cd.id)::float / MAX(ucc.count))::float as idf_score
      FROM ${this.schema}.nlp_entity_source es
      JOIN entities 
        ON entities.base_entity_id = es.entity_id
      JOIN ${this.schema}.conversation_detail cd
        ON cd.id = es.conversation_detail_id
        AND cd.is_background_agent_message = FALSE
        AND cd.is_injected_message = FALSE
      JOIN tribble.user u ON u.id = cd.user_id
        AND u.client_id = ${this.clientId}
        ${userFilter}
      JOIN unique_conversation_count ucc 
        ON 1=1
      WHERE ${dateFilter}
        AND cd.message::jsonb->>'role' = 'user'
        ${extraUserFilter}
      GROUP BY entities.entity_name
      HAVING 
        COUNT(es.*) > 1
        AND LOG(COUNT(DISTINCT cd.id)::float / MAX(ucc.count))::float < LOG(10, 0.5)
      ORDER BY COUNT(es.*) DESC, LOWER(entities.entity_name)
      LIMIT 10
    `;

    const result = await queryAnalytics(queryString, []);
    if (result instanceof Error) {
      return {
        metric: this.metric,
        error:
          'There was an error while fetching data for this metric. Please try again.',
      };
    }

    return {
      metric: 'tribble_conversation_keywords',
      timeFrame: this.timeFilter?.timeRangeText,
      data: result.rows,
      summary: {
        total_keywords: result.rows?.length || 0,
        most_mentioned_keyword: result.rows[0]?.entity_name || 'N/A',
        total_mentions:
          result.rows?.reduce((sum, row) => sum + parseInt(row.count), 0) || 0,
      },
    };
  }
}
