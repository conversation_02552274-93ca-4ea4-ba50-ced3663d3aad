import { Tool } from '@tribble/chat-completion-service/client';
import { v4 as uuidv4 } from 'uuid';
import { ToolCall } from './tool_call.ts';

import { ConversationId } from '@tribble/tribble-db/types';
import { CreateImageFromPromptTool } from './create_image_from_prompt.ts';

interface GeneratePptxResponse {
  success: boolean;
  pptx_urls: string[]; // URL to the uploaded PPTX file
  error?: string;
}

export class CreatePptxFromImageTool extends ToolCall {
  static tool_name = 'create_pptx_from_image';

  private helpersUrl: string;
  private conversationId: ConversationId;
  private clientId: string;
  private userId: string;
  private databaseId: string;
  private helperFuncKey?: string;

  private imageIds: string[];
  private title: string;
  private description: string;

  constructor(
    config: {
      helpersUrl?: string;
      helperFuncKey?: string;
      conversationId: ConversationId;
      clientId: string;
      userId: string;
      databaseId: string;
    },
    args: {
      image_ids: string[];
      title: string;
      description: string;
    },
  ) {
    super();
    this.imageIds = args.image_ids;
    this.helpersUrl = config.helpersUrl;
    this.conversationId = config.conversationId;
    this.clientId = config.clientId;
    this.userId = config.userId;
    this.databaseId = config.databaseId;
    this.helperFuncKey = config.helperFuncKey;

    this.title = args.title;
    this.description = args.description;
  }

  static getPromptDescription(): string {
    return (
      `${this.tool_name}: This tool creates a PPTX file from one or more previously generated images, via the ${CreateImageFromPromptTool.tool_name} tool. ` +
      `When a user requests a presentation, slide deck, or Powerpoint/PPTX file to be created, first work with the user to create the image(s) for the deck: this may require several back and forths with the user. ` +
      `Only once the user is satisfied with the image(s), call this tool to create the PPTX file. After each turn generating an image, ask the user if they would like to create a PPTX file (assuming they originally requested a presentation/deck). If they do, call this tool.`
    );
  }

  static getJsonSchema(): Tool {
    return {
      type: 'function',
      function: {
        name: 'create_pptx_from_image',
        description: `Call this tool whenever the user asks for a presentation, deck, slide deck, or Powerpoint/PPTX file to be created.
        
        This tool creates a PPTX file from one or more previously generated images, via the ${CreateImageFromPromptTool.tool_name} tool. The images will be used as a template for the PPTX file. 
        
        When a user requests a presentation file, first work with the user to create the image(s) for the deck: this may require several back and forth turns with the user. 
        
        Only once the user is satisfied with the image(s), call this tool to create the PPTX file. 
        
        Whenever you finish one turn generating an image with the user, ask the user if they would like to create a PPTX file (assuming they originally requested a presentation/deck). If they do, call this tool. A user may have created multiple images; if so, pass the IDs of all the images to this tool.`,
        parameters: {
          type: 'object',
          properties: {
            image_ids: {
              type: 'array',
              description:
                'The IDs of the images to use as inputs for generating the PPTX file.',
              items: {
                type: 'string',
              },
            },
            title: {
              type: 'string',
              description:
                "A short title (8-10 words max) for the presentation, based on the user's request and the content of the slide images.",
            },
            description: {
              type: 'string',
              description:
                "A succinct 1-sentence description of the presentation, based on the user's request and the content of the slide images.",
            },
          },
          required: ['image_ids', 'title', 'description'],
        },
      },
    };
  }

  static getAdditionalInstructions(): string {
    return '';
  }

  static disableCache(): boolean {
    return true;
  }

  getPendingMessage(): string {
    return `_Creating PowerPoint file from ${this.imageIds.length} image${this.imageIds.length > 1 ? 's' : ''} _ :memo:`;
  }

  getCompletionMessage(): string {
    return `Generated PowerPoint file :art:`;
  }

  async performCall(): Promise<string> {
    const correlationId = uuidv4();

    let pptxRequestBody = {
      client_id: this.clientId,
      user_id: this.userId,
      schema: this.databaseId,
      image_ids: this.imageIds,
      title: this.title,
      description: this.description,
      conversation_id: this.conversationId,
    };

    try {
      console.log(
        `(${correlationId}) [create_pptx_from_image] Sending request to react-to-pptx`,
      );

      const pptxResponse = await fetch(`${this.helpersUrl}/api/react-to-pptx`, {
        method: 'POST',
        body: JSON.stringify(pptxRequestBody),
        headers: {
          ...(this.helperFuncKey && {
            'x-functions-key': this.helperFuncKey,
          }),
        },
      });

      const result = (await pptxResponse.json()) as GeneratePptxResponse;
      if (result.success && result.pptx_urls && result.pptx_urls.length > 0) {
        const pptxUrl = result.pptx_urls[0].replace(
          '.blob.core.windows.net',
          '.files.tribble.ai',
        );

        console.log(
          `(${correlationId}) [create_pptx_from_image] Created the following Pptx with the URL: ${pptxUrl}`,
        );

        return `I created a PowerPoint presentation with the slides based on your prompt:
        
        [Download PowerPoint (.pptx)](${pptxUrl}). YOU MUST include this link in your message back to the user.`;
      } else if (!result.success) {
        console.error(
          '(${correlationId}) [create_pptx_from_image] PPTX generation failed:',
          result.error,
        );
        return 'There was an error creating the PowerPoint file. Please try again.';
      }
    } catch (err) {
      console.error(
        `(${correlationId}) [create_pptx_from_image] Error calling react-to-pptx:`,
        err,
      );
      return 'There was an error creating the PowerPoint file. Please try again.';
    }
  }
}
