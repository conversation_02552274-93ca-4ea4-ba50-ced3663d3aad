import { Tool } from '@tribble/chat-completion-service/client';
import { Logger } from '@tribble/common-utils';
import { HubSpotClient, ObjectSchema } from '@tribble/hubspot';
import { ToolCall } from './tool_call.ts';

type Property = ObjectSchema['properties'][0];

export class HubSpotDescribe extends ToolCall {
  static tool_name = 'hubspot_describe';
  private hubspotClient: HubSpotClient;
  private objectType: string;
  private propertyName?: string;
  private includeProperties: boolean;
  private includeAssociations: boolean;

  constructor(
    config: {
      hubspotClient: HubSpotClient;
      logger?: Logger;
    },
    args: {
      objectType: string;
      propertyName?: string;
      includeProperties?: boolean;
      includeAssociations?: boolean;
    },
  ) {
    super(config.logger);
    this.hubspotClient = config.hubspotClient;
    this.objectType = args.objectType;
    this.propertyName = args.propertyName;
    this.includeProperties = args.includeProperties ?? true;
    this.includeAssociations = args.includeAssociations ?? false;
  }

  static getJsonSchema(): Tool {
    return {
      type: 'function',
      function: {
        name: 'hubspot_describe',
        description: `Get metadata about HubSpot CRM objects, including available properties, property types, and property groups. This helps understand the structure of HubSpot objects before querying or writing data.`,
        parameters: {
          type: 'object',
          properties: {
            objectType: {
              type: 'string',
              description:
                'The type of HubSpot object to describe (e.g., contacts, companies, deals, tickets, products, line_items, quotes)',
            },
            propertyName: {
              type: 'string',
              description:
                'Optional: Specific property name to get detailed information about',
            },
            includeProperties: {
              type: 'boolean',
              description:
                'Whether to include the full list of properties (default: true)',
              default: true,
            },
            includeAssociations: {
              type: 'boolean',
              description:
                'Whether to include available associations (default: false)',
              default: false,
            },
          },
          required: ['objectType'],
        },
      },
    };
  }

  getPendingMessage(): string {
    return `Looking up "${this.objectType}${this.propertyName ? `.${this.propertyName}` : ''}" in Hubspot... ⏳`;
  }

  getCompletionMessage(): string {
    return `Looked up "${this.objectType}${this.propertyName ? `.${this.propertyName}` : ''}" in Hubspot... ✅`;
  }

  async performCall(): Promise<string> {
    try {
      await this.hubspotClient.ensureValidToken();
      // Get object schema/properties
      const schema = await this.hubspotClient.getObjectSchema(this.objectType);
      const properties = schema.properties || [];

      let associations;
      if (this.includeAssociations) {
        associations = await this.getAssociations(this.objectType);
      }

      // If a specific property is requested, return detailed info about that property
      if (this.propertyName) {
        const property = properties.find((p) => p.name === this.propertyName);
        if (!property) {
          return `Property "${this.propertyName}" not found in ${this.objectType} object.`;
        }
        return this.formatPropertyDetails(property);
      }

      // Otherwise, return overview of the object
      return this.formatObjectDescription(properties, associations);
    } catch (error: any) {
      return `Error describing ${this.objectType}: ${error}`;
    }
  }

  private async getAssociations(objectType: string): Promise<string[]> {
    // HubSpot standard associations
    const standardAssociations: Record<string, string[]> = {
      contacts: ['companies', 'deals', 'tickets', 'quotes'],
      companies: ['contacts', 'deals', 'tickets', 'quotes'],
      deals: ['contacts', 'companies', 'line_items', 'tickets', 'quotes'],
      tickets: ['contacts', 'companies', 'deals'],
      products: ['line_items'],
      line_items: ['deals', 'quotes', 'products'],
      quotes: ['contacts', 'companies', 'deals', 'line_items'],
    };

    return standardAssociations[objectType] || [];
  }

  private formatPropertyDetails(property: Property): string {
    let output = `## Property: ${property.label} (${property.name})\n\n`;

    output += `- **Type**: ${property.type}\n`;
    output += `- **Field Type**: ${property.fieldType}\n`;
    output += `- **Group**: ${property.groupName}\n`;
    output += `- **Description**: ${property.description || 'No description available'}\n`;
    output += `- **Hidden**: ${property.hidden || false}\n`;
    output += `- **Form Field**: ${property.formField || false}\n`;

    if (property.options && property.options.length > 0) {
      output += `\n### Options:\n`;
      property.options.forEach((option) => {
        output += `- **${option.label}**: ${option.value}\n`;
      });
    }

    if (property.referencedObjectType) {
      output += `\n### Referenced Object: ${property.referencedObjectType}\n`;
    }

    return output;
  }

  private formatObjectDescription(
    properties: Property[],
    associations?: string[],
  ): string {
    let output = `# HubSpot Object: ${this.objectType}\n\n`;

    // Group properties by their group
    const propertyGroups: Record<string, Property[]> = {};
    properties.forEach((prop) => {
      const groupName = prop.groupName || 'Other';
      if (!propertyGroups[groupName]) {
        propertyGroups[groupName] = [];
      }
      propertyGroups[groupName].push(prop);
    });

    output += `## Summary\n`;
    output += `- **Total Properties**: ${properties.length}\n`;
    output += `- **Property Groups**: ${Object.keys(propertyGroups).length}\n`;

    if (associations && associations.length > 0) {
      output += `\n## Available Associations\n`;
      associations.forEach((assoc) => {
        output += `- ${assoc}\n`;
      });
    }

    if (this.includeProperties) {
      output += `\n## Properties by Group\n\n`;

      Object.entries(propertyGroups).forEach(([groupName, props]) => {
        output += `### ${groupName} (${props.length} properties)\n\n`;

        props.forEach((prop: Property) => {
          output += `- **${prop.label}** (\`${prop.name}\`): ${prop.type}`;

          if (prop.fieldType !== prop.type) {
            output += ` (${prop.fieldType})`;
          }

          if (prop.options && prop.options.length > 0) {
            output += ` - ${prop.options.length} options`;
          }

          output += '\n';
        });

        output += '\n';
      });
    } else {
      output += `\n## Property Groups\n`;
      Object.entries(propertyGroups).forEach(([groupName, props]) => {
        output += `- **${groupName}**: ${props.length} properties\n`;
      });
    }

    // Add common property tips
    output += `\n## Common Properties\n`;
    output += this.getCommonPropertiesForObject();

    return output;
  }

  private getCommonPropertiesForObject(): string {
    const commonProps: Record<string, string> = {
      contacts: `- \`email\`: Contact's email address
- \`firstname\`, \`lastname\`: Contact's name
- \`phone\`: Phone number
- \`company\`: Associated company name
- \`lifecyclestage\`: Where the contact is in your process`,

      companies: `- \`name\`: Company name
- \`domain\`: Company website domain
- \`industry\`: Industry type
- \`numberofemployees\`: Company size
- \`annualrevenue\`: Yearly revenue`,

      deals: `- \`dealname\`: Name of the deal
- \`amount\`: Deal value
- \`dealstage\`: Current stage in pipeline
- \`closedate\`: Expected close date
- \`pipeline\`: Which pipeline the deal is in`,

      tickets: `- \`subject\`: Ticket title
- \`content\`: Ticket description
- \`hs_ticket_priority\`: Priority level
- \`hs_pipeline_stage\`: Current stage
- \`hs_ticket_category\`: Ticket category`,

      products: `- \`name\`: Product name
- \`description\`: Product description
- \`price\`: Product price
- \`hs_sku\`: Stock keeping unit
- \`hs_cost_of_goods_sold\`: Product cost`,

      quotes: `- \`hs_title\`: Quote title
- \`hs_expiration_date\`: When the quote expires
- \`hs_status\`: Quote status
- \`hs_public_url_key\`: Public quote URL`,
    };

    return (
      commonProps[this.objectType] ||
      '- Use `includeProperties: true` to see all available properties'
    );
  }
}

export default HubSpotDescribe;
