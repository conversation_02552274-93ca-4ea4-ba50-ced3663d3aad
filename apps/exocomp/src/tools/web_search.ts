import { Tool } from '@tribble/chat-completion-service/client';
import { Logger } from '@tribble/common-utils';
import { format } from 'date-fns';
import { ConversationSourceFormat } from '../conversations/conversation_context.ts';
import { ConversationMessage } from '../conversations/message.ts';
import { telemetryClient } from '../telemetry.ts';
import { ExtractCitationFormat, ToolCall } from './tool_call.ts';

export class WebSearch extends ToolCall {
  static tool_name = 'web_search';

  private query: string;
  private serperKey: string;
  private sites: string[];

  // Take in all previous messages and
  // find all prior WebSearch messages to find the
  // greatest web_search citation index already used.
  // This is the offset for the current run of WebSearch citations.
  private currentMessages: ConversationMessage[];
  private webSearchCitationOffset: number;
  static citationIdPrefix = 'web_search_';

  constructor(
    config: {
      sites: string[];
      serperKey: string;
      currentMessages: ConversationMessage[];
      logger?: Logger;
    },
    args: { query: string },
  ) {
    super(config.logger);
    this.query = args.query;
    this.serperKey = config.serperKey;
    this.sites = config.sites;

    // find the offset
    this.currentMessages = config.currentMessages;
    this.webSearchCitationOffset = 0;
    this.currentMessages
      .filter(
        (msg) =>
          msg.role === 'tool' &&
          (msg.content as string).indexOf(WebSearch.citationIdPrefix) > -1,
      )
      .forEach((msg) => {
        try {
          // Parse the tool message content and find the greatest citation index
          const toolMessageContent = JSON.parse(msg.content as string);
          toolMessageContent.forEach((citation: any) => {
            if (citation.id.startsWith(WebSearch.citationIdPrefix)) {
              const citationIndex = parseInt(
                citation.id.replace(WebSearch.citationIdPrefix, ''),
              );
              if (citationIndex > this.webSearchCitationOffset) {
                this.webSearchCitationOffset = citationIndex + 1; // start from one more
              }
            }
          });
        } catch (e) {
          // no-op ignore
        }
      });
  }

  static getPromptDescription(params: any): string {
    const sites = params.sites || [];
    const clientName = params.clientName || 'our company';
    let desc = `You also have access to tools to search the web and fetch content from specific URLs.`;

    switch (sites.length) {
      case 0:
        break;
      case 1:
        desc += ` You can search one website: ${sites[0]}`;
        break;
      default:
        desc += ` You can search ${sites.length} websites: ${sites.join(', ')}`;
    }

    if (sites.length === 0) {
      desc += `. When asked about competitors, search the web and fetch content. But when asked about ${clientName}'s products, services, or operations, check ${clientName}'s knowledge base first.`;
    }

    return desc;
  }

  static getJsonSchema(params: any): Tool {
    const sites = params.sites || [];

    let funcDesc = 'Search the entire internet for relevant information.';
    if (sites.length) {
      funcDesc =
        `Search these ${sites.length} specific websites for ` +
        `relevant information: ` +
        sites.join(', ');
    }

    let paramDesc = 'A phrase and/or keywords to send to Google Web Search.';
    if (sites.length) {
      paramDesc =
        'A phrase and/or keywords to use to search the avaialble websites.';
    }

    return {
      type: 'function',
      function: {
        name: 'web_search',
        description: funcDesc,
        parameters: {
          type: 'object',
          properties: {
            query: {
              type: 'string',
              description: paramDesc,
            },
          },
          required: ['query'],
        },
      },
    };
  }

  getPendingMessage(): string {
    return `_Searching the web for "${this.query}"..._ :mag_right:`;
  }

  getCompletionMessage(): string {
    return `Searched the web for "${this.query}" :white_check_mark:`;
  }

  async performCall(): Promise<string> {
    let q = this.query;

    // Collect sites to search.
    if (this.sites && this.sites.length > 0) {
      q += ' ' + this.sites.map((site) => `site:${site}`).join(' OR ');
    }

    console.log('searching for:', q);

    // Define the request data and the URL
    const url = 'https://google.serper.dev/search';
    const data = JSON.stringify({ q: q });

    const headers = new Headers();
    headers.append('X-API-KEY', this.serperKey);
    headers.append('Content-Type', 'application/json');

    return fetch(url, {
      method: 'POST',
      headers: {
        'X-API-KEY': this.serperKey,
        'Content-Type': 'application/json',
      },
      body: data,
    })
      .then(
        (response) =>
          response.json() as Promise<{ organic: any; answerBox?: any }>,
      )
      .then((results) => {
        const organicLinks = results.organic.map((result: any, idx: number) => {
          const citationId = idx + 1 + this.webSearchCitationOffset;
          return {
            id: WebSearch.citationIdPrefix + citationId,
            text: result.snippet,
            relevance: 100 - (result.position ?? 0) * 10,
            file_name: result.title,
            url: result.link,
            date: format(new Date(), 'yyyy-MM-dd'),
            reference: `web result`,
          };
        });

        const answerBoxLink =
          results.answerBox && results.answerBox.snippet
            ? [
                {
                  text: results.answerBox.snippet,
                  relevance: 100,
                  file_name: results.answerBox.title,
                  url: results.answerBox.link,
                  date: format(new Date(), 'yyyy-MM-dd'),
                  reference: 'top web result',
                  id:
                    WebSearch.citationIdPrefix +
                    String(this.webSearchCitationOffset),
                },
              ]
            : [];

        return JSON.stringify([...answerBoxLink, ...organicLinks]);
      })
      .catch((error) => {
        console.error('Web search error:', error);
        return error.toString();
      });
  }

  static extractCitations(
    answer: string,
    messages: ConversationMessage[],
    initialContexts?: ConversationSourceFormat[],
  ): ExtractCitationFormat {
    const usedWebCitationsIndexMap = {};

    const web_search_re = new RegExp(
      '\\^' + WebSearch.citationIdPrefix + '\\d{1,}',
      'g',
    );
    const web_search_match = answer.match(web_search_re);
    if (web_search_match != null) {
      web_search_match.forEach((citation) => {
        const index = citation.replace('^' + WebSearch.citationIdPrefix, '');
        usedWebCitationsIndexMap[index] = null;
        answer = answer.replace(citation, '');
      });
    }

    const usedWebCitationIndexes = Object.keys(usedWebCitationsIndexMap).map(
      (index) => Number(index),
    );

    const usedWebCitations = [];

    // For each extract web citation index, loop through the messages to find the corresponding citation
    usedWebCitationIndexes.forEach((index) => {
      let webCitation;

      messages.forEach((msg) => {
        if (
          msg.role === 'tool' &&
          (msg.content as string).indexOf(WebSearch.citationIdPrefix) > -1 &&
          !webCitation
        ) {
          try {
            const toolMessageContent = JSON.parse(msg.content as string);
            webCitation = toolMessageContent.find(
              (citation: any) =>
                citation.id === WebSearch.citationIdPrefix + index,
            );

            if (
              webCitation &&
              webCitation.id?.length > 0 &&
              webCitation.url?.length > 0
            ) {
              usedWebCitations.push({
                id: index,
                file_name: webCitation.file_name,
                url: webCitation.url,
                reference: webCitation.reference,
              });
            }
          } catch (e) {
            telemetryClient.trackMetric({
              name: 'websearch_citation_extract_error',
              value: 1,
              properties: {
                error: e,
                content: msg?.content,
                tool_call_id: msg?.tool_call_id,
              },
            });
            return false;
          }
        }
        return false;
      });
    });

    return {
      citations: usedWebCitations,
      updatedAnswer: answer,
    };
  }
}
