import { Tool } from '@tribble/chat-completion-service/client';
import { Logger } from '@tribble/common-utils';
import { Connection, DescribeSObjectResult } from 'jsforce';
import { ToolCall } from './tool_call.ts';

export class SalesforceDescribe extends ToolCall {
  static tool_name = 'salesforce_describe';
  private sobject: string;
  private field: string;
  private conn: Connection;
  static citationIdPrefix = 'salesforce_';

  constructor(
    config: {
      conn: Connection;
      logger?: Logger;
    },
    args: { sObject: string; field?: string },
  ) {
    super(config.logger);
    this.conn = config.conn;
    this.sobject = args.sObject;
    this.field = args.field;
  }

  static getJsonSchema(): Tool {
    return {
      type: 'function',
      function: {
        name: SalesforceDescribe.tool_name,
        description:
          'Get a list of fields for a Salesforce object. Or, get details about a specific field.',
        parameters: {
          type: 'object',
          properties: {
            s_object: {
              type: 'string',
              description: 'The Salesforce object (sObject) to describe.',
            },
            field: {
              type: 'string',
              description:
                'Optional. The field to describe. If not provided, all fields for the object are returned.',
            },
          },
          required: ['s_object'],
        },
      },
    };
  }

  getPendingMessage(): string {
    return `Looking up "${this.sobject}${this.field ? `.${this.field}` : ''}" in Salesforce... :cloud:`;
  }

  getCompletionMessage(): string {
    return `Looked up "${this.sobject}${this.field ? `.${this.field}` : ''}" in Salesforce... :lightning_cloud:`;
  }

  static getPromptDescription(): string {
    return '';
  }

  async performCall(): Promise<string> {
    try {
      const res = await this.conn.describeSObject(this.sobject);
      //TODO: handle large record sizes that will obliterate the context window.

      // const apiUsage = this.conn.limitInfo.apiUsage;
      // const percent = (apiUsage.used ?? 0) / (apiUsage.limit ?? 1);
      // console.log('SF API Usage:', apiUsage, percent);

      return this.sanitizeDescribeResult(res);
    } catch (err) {
      return err.toString();
    }
  }

  sanitizeDescribeResult(result: DescribeSObjectResult): string {
    if (!result) {
      return JSON.stringify('No results found.');
    }

    const sanitizedFields = this.sanitizeFields(result.fields);
    if (this.field) {
      const field = sanitizedFields.find(
        (field) =>
          field.name.toLowerCase() === this.field.toLowerCase() ||
          field.label.toLowerCase() === this.field.toLowerCase(),
      );
      if (field) {
        return JSON.stringify(field);
      }
    }

    //Do the whole sObject
    const compactSObject: CompactSObject = {
      name: result.name,
      label: result.label,
      fields: sanitizedFields,
    };

    if (result.childRelationships) {
      compactSObject.childRelationships = result.childRelationships.map(
        (relationship) => {
          return {
            childSObject: relationship.childSObject,
            relationshipName: relationship.relationshipName,
            field: relationship.field,
          };
        },
      );
    }

    return JSON.stringify(compactSObject);
  }

  //Return a subset of properties from the DescribeSObjectResult['fields']
  sanitizeFields(fields: DescribeSObjectResult['fields']): sObjectField[] {
    const sanitized = fields.map((field) => {
      const fieldObject: sObjectField = {
        name: field.name,
        label: field.label,
        type: field.type,
      };
      try {
        if (field.picklistValues) {
          fieldObject.picklistValues = field.picklistValues.map((value) => {
            return { label: value.label, value: value.value };
          });
        }
      } catch (err) {
        console.log('Error parsing picklist values:', err);
      }

      if (field.referenceTo) {
        fieldObject.referenceTo = field.referenceTo;
      }

      if (field.relationshipName) {
        fieldObject.relationshipName = field.relationshipName;
      }

      return fieldObject;
    });

    return sanitized;
  }
}

type CompactSObject = {
  name: string;
  label: string;
  fields: sObjectField[];
  childRelationships?: Partial<
    DescribeSObjectResult['childRelationships'][number]
  >[];
};

type sObjectField = {
  name: string;
  label: string;
  type: string;
  referenceTo?: string[];
  relationshipName?: string;
  picklistValues?: { label: string; value: string }[];
};
