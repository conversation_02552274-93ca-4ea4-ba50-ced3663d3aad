import { Tool } from '@tribble/chat-completion-service/client';
import { Logger } from '@tribble/common-utils';
import { CustomTableSearch } from '../brain.ts';
import { TableCatalogResult } from '../structured_data.ts';
import { ToolCall } from './tool_call.ts';

export class SearchCustomTables extends ToolCall {
  static tool_name = 'search_custom_tables';
  private search_string: string;
  private schema: string;
  private tables: TableCatalogResult[] = [];

  constructor(
    config: { schema: string; logger?: Logger },
    args: { search_string: string },
  ) {
    super(config.logger);
    this.search_string = args.search_string;
    this.schema = config.schema;
  }

  static getJsonSchema(): Tool {
    return {
      type: 'function',
      function: {
        name: SearchCustomTables.tool_name,
        description: `Conducts a vector search across custom table descriptions. Returns table names, descriptions, and schema.`,
        parameters: {
          type: 'object',
          properties: {
            search_string: {
              type: 'string',
              description: 'Find tables aligned with this search.',
            },
          },
          required: ['search_string'],
        },
      },
    };
  }

  getPendingMessage(): string {
    return 'Searching ingested spreadsheets... :blue_book:';
  }

  getCompletionMessage(): string {
    return 'Searched ingested spreadsheets... :book:';
  }

  getTables() {
    return this.tables;
  }

  async performCall(): Promise<string> {
    this.tables = await CustomTableSearch({
      query: this.search_string,
      schema: this.schema,
    });

    console.log('found tables:', this.tables.length);

    return JSON.stringify(this.tables);
  }
}
