import { Tool } from '@tribble/chat-completion-service/client';
import { Logger } from '@tribble/common-utils';
import { FormRecognizedDoc } from '@tribble/formrecognizer';
import { ToolCall } from './tool_call.ts';

export class ExemplarSearch extends ToolCall {
  static tool_name = 'exemplar_doc_search';
  private pageQuery: number[];
  // private searchQuery: string;
  private exemplarFiles: FormRecognizedDoc[];

  constructor(
    config: { exemplarFiles: FormRecognizedDoc[]; logger?: Logger },
    args: { page_query: number[]; search_query?: string },
  ) {
    super(config.logger);
    this.pageQuery = args.page_query;
    this.exemplarFiles = config.exemplarFiles;
    // this.searchQuery = args.search_query || '';
  }

  static getPromptDescription(): string {
    return `Use ${ExemplarSearch.tool_name} to look at particular exemplar file pages.`;
  }

  static getJsonSchema(): Tool {
    return {
      type: 'function',
      function: {
        name: ExemplarSearch.tool_name,
        description: 'Search the exemplar file(s) for specific pages',
        strict: true,
        parameters: {
          type: 'object',
          properties: {
            page_numbers: {
              type: 'array',
              description:
                '1 or more page numbers to return from the exemplar file',
              items: {
                type: 'number',
              },
              required: ['page_numbers'],
              additionalProperties: false,
            },
          },
        },
      },
    };
  }

  getPendingMessage(): string {
    return `Searching the exemplar file for ${this.pageQuery?.length || 0} page(s)`;
  }

  getCompletionMessage(): string {
    return `Searched the exemplar packet for ${this.pageQuery?.length || 0} page(s)`;
  }

  getAvailablePages(): { file_name: string; num_pages: number }[] {
    return this.exemplarFiles.map((bidPacketFile) => {
      return {
        file_name: bidPacketFile.file_name,
        num_pages: bidPacketFile.pages.length,
      };
    });
  }

  async performCall(): Promise<string> {
    if (!this.exemplarFiles.length) {
      return 'No exemplar packet files found';
    }

    const pages = this.exemplarFiles.map((exemplarFile) => {
      return {
        fileName: exemplarFile.file_name,
        pages: exemplarFile.getPages(this.pageQuery).map((page) => {
          return { pageNumber: page.pageNumber, content: page.content };
        }),
      };
    });

    const hasPages = pages.some((p) => p.pages.some((p) => p.pageNumber));
    if (!hasPages) {
      return `Pages not found. The only pages available are the following\n${JSON.stringify(this.getAvailablePages(), null, 2)}`;
    }

    return JSON.stringify(pages, null, 2);
  }
}
