import { streamChatCompletion } from '@tribble/chat-completion-service';
import { Logger } from '@tribble/common-utils';
import { ClientId, ConversationId, UserId } from '@tribble/tribble-db/types';
import { Filter } from '../brain.ts';
import { ConversationContext } from '../conversations/conversation_context.ts';
import { MetadataFilterValue } from '../metadata_filters.ts';
import { ReRank } from '../reranker/index.ts';
import { ResolvedSettings } from '../resolved_settings.ts';
import { telemetryClient } from '../telemetry.ts';
import { TimeFilter } from './extract_temporal.ts';
import { ToolCall } from './tool_call.ts';

const agentModel = process.env.AGENT_MODEL ?? 'gpt-4.1';

export abstract class BaseSearchTool extends ToolCall {
  protected clientId: ClientId;
  protected clientName: string;
  protected conversationId: ConversationId;
  protected metadataFilters: MetadataFilterValue[];
  protected schema: string;
  protected strictMode: boolean;
  protected tribbleUserId: UserId;
  protected allowSlackRag: boolean;
  protected includeVerbatim: boolean;
  protected includeImages: boolean;
  protected timeFilter: TimeFilter;
  protected settings: ResolvedSettings;

  protected query: string;
  protected filters: Filter[];
  protected imageInfos?: Array<{ dataUrl: string; mimetype: string }>;

  protected contexts: ConversationContext[] = [];
  protected textContexts: ConversationContext[] = [];
  protected imageContexts: ConversationContext[] = [];

  constructor(
    config: {
      clientId: ClientId;
      clientName: string;
      conversationId: ConversationId;
      metadataFilters: MetadataFilterValue[];
      schema: string;
      tribbleUserId: UserId;
      includeVerbatim?: boolean;
      includeImages?: boolean;
      timeFilter?: TimeFilter;
      settings: ResolvedSettings;
      logger?: Logger;
    },
    args: {
      query: string;
      filters?: Filter[];
      imageInfos?: Array<{ dataUrl: string; mimetype: string }>;
    },
  ) {
    super(config.logger);

    this.query = args.query;
    this.filters = args.filters || [];
    this.imageInfos = args.imageInfos || [];

    this.clientId = config.clientId;
    this.clientName = config.clientName;
    this.conversationId = config.conversationId;
    this.metadataFilters = config.metadataFilters || [];
    this.schema = config.schema;
    this.strictMode = config.settings.strictMode;
    this.tribbleUserId = config.tribbleUserId;
    this.allowSlackRag = config.settings.allowSlackRag;
    this.includeVerbatim = config.includeVerbatim ?? false;
    this.includeImages = config.includeImages ?? true;
    this.timeFilter = config.timeFilter || {};
    this.settings = config.settings;
  }

  getContexts(): ConversationContext[] {
    return this.contexts;
  }

  /**
   * Abstract method that subclasses must implement to perform their specific search
   */
  protected abstract performTextSearch(
    query: string,
  ): Promise<ConversationContext[]>;

  /**
   * Abstract method to get the telemetry name prefix for the specific search type
   */
  protected abstract getTelemetryPrefix(): string;

  /**
   * Merges the raw results from text and image searches
   * @param textContexts Contexts from text search
   * @param imageContexts Contexts from image search
   * @returns Combined list of contexts
   */
  protected mergeContexts(
    textContexts: ConversationContext[],
    imageContexts: ConversationContext[],
  ): ConversationContext[] {
    const mergedContexts: ConversationContext[] = [...textContexts];

    const existingIds = new Set(mergedContexts.map((ctx) => ctx.embedding_id));

    let newFromImageCount = 0;
    let boostedCount = 0;

    imageContexts.forEach((imgContext) => {
      imgContext.text = `[Image-derived context] ${imgContext.text}`;

      if (!existingIds.has(imgContext.embedding_id)) {
        newFromImageCount++;
        mergedContexts.push(imgContext);
      } else {
        const existingContext = mergedContexts.find(
          (ctx) => ctx.embedding_id === imgContext.embedding_id,
        );

        if (existingContext) {
          boostedCount++;

          existingContext.similarity = Math.min(
            1.0,
            existingContext.similarity * 1.2,
          );
          existingContext.multiplier = (existingContext.multiplier || 1) * 1.2;

          existingContext.text = `[Found in both text and image searches] ${existingContext.text}`;

          if (process.env.NODE_ENV === 'development') {
            existingContext.label = `${existingContext.label} (text+image)`;
          }
        }
      }
    });

    telemetryClient.trackMetric({
      name: `${this.getTelemetryPrefix()}_context_merge`,
      value: 1,
      properties: {
        conversation_id: this.conversationId.toString(),
        text_contexts: textContexts.length.toString(),
        image_contexts: imageContexts.length.toString(),
        unique_from_images: newFromImageCount.toString(),
        boosted_contexts: boostedCount.toString(),
        total_merged: mergedContexts.length.toString(),
      },
    });

    return mergedContexts;
  }

  /**
   * Generates descriptions for images
   * @param imageInfos Array of image data URLs and mimetypes
   * @returns Array of image descriptions
   */
  protected async generateImageDescriptions(
    imageInfos: Array<{ dataUrl: string; mimetype: string }>,
  ): Promise<string[]> {
    const descriptions: string[] = [];

    for (const image of imageInfos) {
      try {
        let description = '';

        await streamChatCompletion(
          {
            schema: this.schema,
            timeout: 30000,
            activityLogData: {
              source_table: 'conversation',
              source_id: this.conversationId.toString(),
              type: 'image_description',
            },
            resourceUrl: '/openai/deployments/tribble-gpt4/chat/completions',
            apiVersion: process.env.AGENT_API_VERSION_LATEST ?? '2024-10-21',
            request: {
              model: agentModel,
              messages: [
                {
                  role: 'system',
                  content:
                    'Extract detailed, searchable information from this image. Focus on key content, text, data points, and visual elements that might be useful for information retrieval.',
                },
                {
                  role: 'user',
                  content: [
                    {
                      type: 'text',
                      text: 'Describe the content of this image in detail for search purposes.',
                    },
                    { type: 'image_url', image_url: { url: image.dataUrl } },
                  ],
                },
              ],
            },
          },
          (chunk) => {
            if (chunk.choices && chunk.choices.length > 0) {
              const messageDelta = chunk.choices[0].messageDelta;
              if (messageDelta && messageDelta.content_chunk) {
                description += messageDelta.content_chunk;
              }
            }
          },
        );

        descriptions.push(description);

        telemetryClient.trackMetric({
          name: `${this.getTelemetryPrefix()}_image_description_generated`,
          value: 1,
          properties: {
            conversation_id: this.conversationId.toString(),
            description_length: description.length.toString(),
            mime_type: image.mimetype,
          },
        });
      } catch (error) {
        console.error(`Error generating image description: ${error.message}`);

        telemetryClient.trackMetric({
          name: `${this.getTelemetryPrefix()}_image_description_error`,
          value: 1,
          properties: {
            conversation_id: this.conversationId.toString(),
            error_message: error.message,
            mime_type: image.mimetype,
          },
        });
      }
    }

    return descriptions;
  }

  /**
   * Common performCall implementation that handles image processing and context merging
   */
  protected async performBaseCall(): Promise<ConversationContext[]> {
    // Perform text search and store results
    this.textContexts = await this.performTextSearch(this.query);

    // Process images if present
    if (this.imageInfos && this.imageInfos.length > 0) {
      try {
        const imageDescriptions = await this.generateImageDescriptions(
          this.imageInfos,
        );

        if (imageDescriptions.length > 0) {
          try {
            const combinedDescription = imageDescriptions.join(' ');

            this.imageContexts =
              await this.performTextSearch(combinedDescription);

            this.imageContexts.forEach((context) => {
              context.multiplier = (context.multiplier || 1) * 1.1;

              if (process.env.NODE_ENV === 'development') {
                context.label = `${context.label} (via image)`;
              }
            });

            telemetryClient.trackMetric({
              name: `${this.getTelemetryPrefix()}_image_search_success`,
              value: 1,
              properties: {
                conversation_id: this.conversationId.toString(),
                image_count: this.imageInfos.length.toString(),
                contexts_found: this.imageContexts.length.toString(),
              },
            });

            this.contexts = this.mergeContexts(
              this.textContexts,
              this.imageContexts,
            );
          } catch (error) {
            console.error(`Error performing image search: ${error.message}`);
            this.contexts = this.textContexts;
            telemetryClient.trackMetric({
              name: `${this.getTelemetryPrefix()}_image_search_error`,
              value: 1,
              properties: {
                conversation_id: this.conversationId.toString(),
                error_message: error.message,
                stage: 'vector_search',
              },
            });
          }
        } else {
          this.contexts = this.textContexts;
          telemetryClient.trackMetric({
            name: `${this.getTelemetryPrefix()}_image_description_empty`,
            value: 1,
            properties: {
              conversation_id: this.conversationId.toString(),
              image_count: this.imageInfos.length.toString(),
            },
          });
        }
      } catch (error) {
        console.error(`Error processing images for search: ${error.message}`);
        this.contexts = this.textContexts;
        telemetryClient.trackMetric({
          name: `${this.getTelemetryPrefix()}_image_processing_error`,
          value: 1,
          properties: {
            conversation_id: this.conversationId.toString(),
            error_message: error.message,
            stage: 'overall_process',
          },
        });
      }
    } else {
      this.contexts = this.textContexts;
    }

    const imagePresent = this.imageInfos && this.imageInfos.length > 0;
    let reRankQuestion = this.query;

    if (imagePresent) {
      reRankQuestion = `${this.query} [Note: This query includes contexts from ${this.imageInfos.length} image(s) that were analyzed as part of the search process]`;
    }

    // Re-rank results
    this.contexts = await ReRank({
      contexts: this.contexts,
      clientName: this.clientName,
      question: reRankQuestion,
      schema: this.schema,
      clientId: this.clientId,
      userId: this.tribbleUserId,
      metadataFilters: this.metadataFilters,
      conversationId: this.conversationId,
    });

    if (imagePresent) {
      const imageContextIds = new Set(
        this.imageContexts.map((ctx) => ctx.embedding_id),
      );
      const survivingImageContexts = this.contexts.filter((ctx) =>
        imageContextIds.has(ctx.embedding_id),
      );

      telemetryClient.trackMetric({
        name: `${this.getTelemetryPrefix()}_rerank_results`,
        value: 1,
        properties: {
          conversation_id: this.conversationId.toString(),
          total_contexts: this.contexts.length.toString(),
          image_contexts_survived: survivingImageContexts.length.toString(),
          survival_rate: (
            survivingImageContexts.length /
            Math.max(1, this.imageContexts.length)
          ).toFixed(2),
        },
      });
    }

    return this.contexts;
  }
}
