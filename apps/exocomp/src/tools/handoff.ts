/**
 * Example handoff tool
 */

import { Tool } from '@tribble/chat-completion-service/client';
import { Cartridge as CartridgeEnum } from '@tribble/conversation-service';
import { ToolCall } from './tool_call.ts';

export class Handoff extends ToolCall {
  static tool_name = 'handoff';
  is_handoff = true;
  agent: string;

  constructor(config: any, args: { agent: string }) {
    super();
    this.agent = args.agent;
  }

  static getPromptDescription(): string {
    return `${this.tool_name}: Hands off the conversation to a different AI agent. Only do this when explicitly asked. You must respect the enum of the AI agent list in the tool JSON schema.`;
  }

  static getJsonSchema(allowedAgentsEnum: string[]): Tool {
    const schema = {
      type: 'function',
      function: {
        name: `${this.tool_name}`,
        description: `Hands off the conversation to a different AI agent. Only do this when explicitly asked. You must respect the enum of the AI agent list in the tool JSON schema.`,
        parameters: {
          type: 'object',
          properties: {
            agent: {
              type: 'string',
              enum: allowedAgentsEnum,
              description: 'The AI agent to handoff to.',
            },
          },
          required: ['agent'],
        },
      },
    };

    return schema;
  }

  getPendingMessage(): string {
    return `Switching to ${this.agent}...`;
  }

  getCompletionMessage(): string {
    return `Handoff complete.`;
  }

  async performCall(): Promise<string> {
    return `Handoff to ${this.agent} complete.`;
  }

  getTargetCartrigeForHandoff() {
    switch (this.agent) {
      case 'rfp_writer':
        return CartridgeEnum.RFP_WRITER;
      case 'researcher':
        return CartridgeEnum.RFP_RESEARCHER;
      default:
        return null;
    }
  }
}
