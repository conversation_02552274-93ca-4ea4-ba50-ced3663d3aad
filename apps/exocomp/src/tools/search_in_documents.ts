import { Tool } from '@tribble/chat-completion-service/client';
import { Logger } from '@tribble/common-utils';
import { FilteredContextSearchByDocumentId } from '../brain.ts';
import { ConversationContext } from '../conversations/conversation_context.ts';
import { MetadataFilterValue } from '../metadata_filters.ts';
import { TimeFilter } from './extract_temporal.ts';
import { FindDocuments } from './find_documents.ts';
import { ToolCall } from './tool_call.ts';

export type SearchParam = {
  document_name?: string;
  document_type?: string;
  document_platform?: string;
};

export class SearchInDocuments extends ToolCall {
  static tool_name = 'search_in_documents';
  private clientName: string;
  private schema: string;
  private metadataFilters: MetadataFilterValue[];
  private strictMode: boolean;
  private allowSlackRag: boolean;
  private timeFilter: TimeFilter;
  private isTimeFilterInQuery: boolean = false;

  private query: string;
  private document_ids: string[];
  private contexts: ConversationContext[] = [];

  constructor(
    config: {
      clientName: string;
      schema: string;
      metadataFilters: MetadataFilterValue[];
      strictMode: boolean;
      allowSlackRag: boolean;
      timeFilter?: TimeFilter;
      logger?: Logger;
    },
    args: { query: string; document_ids: string[] },
  ) {
    super(config.logger);
    this.query = args.query;
    this.document_ids = args.document_ids;

    this.clientName = config.clientName;
    this.schema = config.schema;
    this.metadataFilters = config.metadataFilters || [];
    this.strictMode = config.strictMode;
    this.allowSlackRag = config.allowSlackRag;
    this.timeFilter = config.timeFilter || {};

    this.checkTimeFilterInQuery();
  }

  private checkTimeFilterInQuery(): void {
    if (this.timeFilter?.timeRangeText && this.query) {
      this.isTimeFilterInQuery = this.query.includes(
        this.timeFilter.timeRangeText,
      );
      console.log(
        `[ SearchInDocuments tool] isTimeFilterInQuery: ${this.isTimeFilterInQuery}`,
        `(timeFilter: ${this.timeFilter.timeRangeText})`,
        `(query: ${this.query})`,
      );
    }
  }

  static getPromptDescription(params: any): string {
    const clientName = params.clientName || 'our company';
    return (
      `${this.tool_name}: Searches within specific documents to retrieve relevant information based on a query. ` +
      `This tool uses the document IDs returned from the ${FindDocuments.tool_name} tool.`
    );
  }

  static getJsonSchema(): Tool {
    const schema = {
      type: 'function',
      function: {
        name: `${this.tool_name}`,
        description: `Search for information in specific documents using the document IDs returned from the ${FindDocuments.tool_name} tool call.`,
        parameters: {
          type: 'object',
          properties: {
            query: {
              type: 'string',
              description:
                'The search query to find in the documents with specified IDs. ' +
                'Conducts a vector search on the specified documents. A long query phrase is preferable. ',
            },
            document_ids: {
              type: 'array',
              description: 'IDs of the documents to search for the query.',
              items: {
                type: 'string',
                description: 'The ID of the document to search for the query.',
              },
            },
          },
          required: ['query', 'document_ids'],
        },
      },
    };

    return schema;
  }

  getPendingMessage(): string {
    if (this.isTimeFilterInQuery) {
      return `_Getting "${this.timeFilter.timeRangeText}" records..._ :page_with_curl: :clock1:`;
    }
    return `_Searching documents for "${JSON.stringify(this.query)}"..._ :page_with_curl: :mag_right:`;
  }

  getCompletionMessage(): string {
    if (this.isTimeFilterInQuery) {
      return `Retrieved "${this.timeFilter.timeRangeText}" records :page_with_curl: :clock1:`;
    }
    return `Searched documents for "${JSON.stringify(this.query)}" :page_with_curl: :mag_right:`;
  }

  async performCall(): Promise<string> {
    if (!this.query || this.query.length === 0) {
      return 'Invalid search query. Please provide a valid search query.';
    }

    if (!this.document_ids || this.document_ids.length === 0) {
      return 'Invalid document IDs. Please provide at least one document ID.';
    }

    this.contexts = this.isTimeFilterInQuery
      ? await FilteredContextSearchByDocumentId({
          queryEmbedding: [], // This blanks out the query embedding, so that the vector search is not performed
          query: this.query, // This shouldn't matter in this case
          schema: this.schema,
          strictMode: this.strictMode,
          metadataFilters: this.metadataFilters,
          document_ids: this.document_ids,
          allowSlackRag: this.allowSlackRag,
          timeFilter: this.timeFilter,
        })
      : await FilteredContextSearchByDocumentId({
          query: this.query,
          schema: this.schema,
          strictMode: this.strictMode,
          metadataFilters: this.metadataFilters,
          document_ids: this.document_ids,
          allowSlackRag: this.allowSlackRag,
          timeFilter: this.timeFilter,
        });

    return JSON.stringify(
      this.contexts.map((ctx, idx) =>
        ctx.toSourceFormat(idx, this.metadataFilters),
      ),
    );
  }
}
