import { Tool } from '@tribble/chat-completion-service/client';
import { Logger } from '@tribble/common-utils';
import FormData from 'form-data';
import fetch from 'node-fetch';
import { ToolCall } from './tool_call.ts';

import { Redis, constants as RedisConstants } from '@tribble/redis';
import { jsonrepair } from 'jsonrepair';

// Analyze prompt to recommend appropriate dimensions
function analyzeDimensionsFromPrompt(prompt: string): {
  width: number;
  height: number;
  contentType: string;
} {
  const promptLower = prompt.toLowerCase();

  // Check for specific content types and recommend appropriate dimensions
  if (
    promptLower.includes('slide') ||
    promptLower.includes('presentation') ||
    promptLower.includes('powerpoint') ||
    promptLower.includes('deck')
  ) {
    return { width: 1920, height: 1080, contentType: 'presentation' };
  } else if (
    promptLower.includes('mobile') ||
    promptLower.includes('phone') ||
    promptLower.includes('app')
  ) {
    return { width: 375, height: 812, contentType: 'mobile' };
  } else if (
    promptLower.includes('dashboard') ||
    promptLower.includes('analytics') ||
    promptLower.includes('metrics')
  ) {
    return { width: 1600, height: 900, contentType: 'dashboard' };
  } else if (
    promptLower.includes('square') ||
    promptLower.includes('instagram') ||
    promptLower.includes('social')
  ) {
    return { width: 1080, height: 1080, contentType: 'social' };
  } else if (promptLower.includes('banner') || promptLower.includes('header')) {
    return { width: 1200, height: 300, contentType: 'banner' };
  } else if (
    promptLower.includes('portrait') ||
    promptLower.includes('poster')
  ) {
    return { width: 800, height: 1200, contentType: 'portrait' };
  } else if (
    promptLower.includes('infographic') ||
    promptLower.includes('vertical')
  ) {
    return { width: 800, height: 2000, contentType: 'infographic' };
  } else if (promptLower.includes('chart') || promptLower.includes('graph')) {
    return { width: 1200, height: 800, contentType: 'chart' };
  }

  // Default to a standard web layout
  return { width: 1200, height: 800, contentType: 'default' };
}

export class CreateImageFromPromptTool extends ToolCall {
  static tool_name = 'create_image_from_prompt';

  private initiatingError: string = '';

  private helpersUrl: string;
  private clientId: string;
  private userId: string;
  private databaseId: string;
  private helperFuncKey?: string;

  private prompts: string[] = [];
  private widths: number[] = [];
  private heights: number[] = [];
  private descriptions: string[] = [];
  private shortDescriptions: string[] = [];

  private previousImageIds: string[] = [];

  // The generated_asset id of an asset to use as a template
  // for the new image(s)
  private templateAssetId: number;

  private imageInfos?: Array<{ dataUrl: string; mimetype: string }>;

  constructor(
    config: {
      helpersUrl?: string;
      clientId: string;
      userId: string;
      databaseId: string;
      helperFuncKey?: string;
      logger?: Logger;
    },
    args: {
      images: Array<{
        prompt: string;
        width?: number;
        height?: number;
        description?: string;
        short_description?: string;
        previous_image_id?: string;
        template_asset_id?: number;
        // imageInfos?: Array<{ dataUrl: string; mimetype: string }>;
      }>;
      export_pptx?: boolean;
    },
  ) {
    super(config.logger);
    this.helpersUrl = config.helpersUrl || process.env.POSITRONIC_HELPERS_URL;
    this.clientId = config.clientId;
    this.userId = config.userId;
    this.databaseId = config.databaseId;
    this.helperFuncKey = config.helperFuncKey;
    this.helpersUrl = config.helpersUrl;

    if (!args || !args.images) {
      // Check -- is it just a single image and it forgot to wrap in an "image" key?
      if (
        args['prompt'] &&
        args['description'] &&
        args['short_description'] &&
        args['width'] &&
        args['height']
      ) {
        args.images = [
          {
            prompt: args['prompt'],
            description: args['description'],
            short_description: args['short_description'],
            width: args['width'],
            height: args['height'],
            ...(args['previous_image_id'] && {
              previous_image_id: args['previous_image_id'],
            }),
            ...(args['template_asset_id'] && {
              template_asset_id: args['template_asset_id'],
            }),
          },
        ];
      } else {
        console.error(
          '[CreateImageFromPromptTool] missing "images" property in the tool call',
        );
        this.initiatingError = `Invalid arguments: missing "images" property in the tool call`;
        return;
      }
    }

    // Check if images was passed as a stringified array
    if (typeof args.images === 'string') {
      const argsImagesStr = (args.images as string).replace(/\n/g, '');

      try {
        args.images = JSON.parse(argsImagesStr);
      } catch (err) {
        console.error(
          '[CreateImageFromPromptTool] Error parsing args.images string:',
          err,
        );

        // Try to repair
        try {
          const repaired = jsonrepair(argsImagesStr);
          args.images = JSON.parse(repaired);
        } catch (err) {
          console.error(
            '[CreateImageFromPromptTool] Error repairing args.images string:',
            err,
          );

          this.initiatingError = `Invalid JSON: Error parsing args.images string: ${err}`;
        }
      }
    }

    if (!Array.isArray(args.images)) {
      // Can't throw in a tool -- must return an error message to the LLM
      // But we're in a constructor, so just set the error state and performCall will return an error message

      // throw new Error(
      //   'Invalid arguments: "images" must be an array of objects with "prompt" property',
      // );
      this.initiatingError = `Invalid arguments: "images" must be an array of objects with a "prompt" property`;
    }

    if (this.initiatingError) {
      console.error('[CreateImageFromPromptTool] Failed to load properly');
      return;
    }

    const dimensions = analyzeDimensionsFromPrompt(args.images[0].prompt);

    args.images.forEach((img) => {
      this.prompts.push(img.prompt);
      this.widths.push(img.width || dimensions.width);
      this.heights.push(img.height || dimensions.height);
      this.descriptions.push(img.description || '');
      this.shortDescriptions.push(img.short_description || '');
      this.previousImageIds.push(img.previous_image_id || '');

      if (img.template_asset_id) {
        this.templateAssetId = img.template_asset_id;
      }
    });

    console.log(
      `[CreateImageFromPromptTool] Instantiated with ${this.prompts.length} images`,
    );
  }

  static getPromptDescription(): string {
    return (
      `${this.tool_name}: Generate beautiful, modern images by creating React components from text descriptions. ` +
      `Features aesthetic design system with gradients, glassmorphism, animations, and professional color schemes. ` +
      `Perfect for creating dashboards, UI mockups, visualizations, and presentation slides. ` +
      `Automatically detects appropriate dimensions and applies content-specific design patterns.`
    );
  }

  static getJsonSchema(): Tool {
    return {
      type: 'function',
      function: {
        name: 'create_image_from_prompt',
        description: `Generate beautiful, modern images by creating React components from text descriptions. 
This tool uses AI with an enhanced aesthetic design system to create stunning UI components and visualizations.

Perfect for creating:
- Dashboard mockups with card-based layouts and data visualizations
- Presentation slides with bold gradients and dramatic typography
- Mobile app screens with iOS/Android design patterns
- Social media graphics with trendy effects
- Interactive charts and graphs with modern styling
- Marketing materials with eye-catching designs

## Design Aesthetics

- Professional color schemes (Stripe, Linear, Vercel inspired)
- Content-aware styling and layouts

## Dimensions

The tool automatically detects appropriate dimensions and aesthetic styles:
- Presentations/slides: 1920x1080 (bold gradients, large typography)
- Mobile apps: 375x812 (iOS-inspired, glassmorphism)
- Dashboards: 1600x900 (card-based, professional)
- Social media: 1080x1080 (attention-grabbing, modern)
- Banners: 1200x300 (minimal, modern)
- Posters: 800x1200 (bold typography)
- Infographics: 800x2000 (data-focused)

IMPORTANT: regarding width and height:
1. When generating a new image, determine the appropriate width and height based on the prompt.
2. When editing a previously generated image, use the width and height of the previously generated image.
3. When leveraging a design/layout of a previously generated image to make a new image, determine the appropriate width and height based on the prompt.
- E.g. "Use the previous slide deck image to create a LNKD image...": pass in the previous image Id, but use the dimensions appropriate for a social media image.
4. When the user requests a specific width and height, use that.

## Prompt Guidelines

When you are unsure about the specifics of a user's request, ask the user for clarification. Examples:
- "Remove the background from the image": ask whether the user wants to set the background to a specific color (e.g. "white"), or to transparent. Or in the case of multiple card elements in the image, whether they're asking about specific element(s).
- "Zoom in on the image": ask whether the user wants to zoom in on a specific area of the image, or they want to make the existing content larger to fill up the empty space in the image.

Exclude describing the following elements, as they will be handled within the tool:
- Exclude references to company logos in the image prompt
- UNLESS explicitly specified by user instructions, exclude describing how the background or text elements should be colored: the tool will automatically apply modern color schemes

## Editing Previously Generated Images

IMPORTANT: when the user requests edits to a previously generated image:
1. If there were multiple images previously generated, AND if you're unclear about which image the user is referring to, ask the user to specify which image they want to edit.
2. If there were multiple images previously generated, AND the user refers to an image by its ordinal position, confirm the image to edit using the short description.
3. Populate the previous_image_id field with the id of the image the user is referring to.
4. If you are provided with a Template Asset Id, populate the template_asset_id field with that exact id value, EXCEPT if you've already generated an image with that template and the user is requesting edits to the newly generated image. In that case, do not populate the template_asset_id field.
`,
        parameters: {
          type: 'object',
          properties: {
            images: {
              type: 'array',
              description:
                'A list of images to generate. Each image is described by a prompt.',
              items: {
                type: 'object',
                properties: {
                  prompt: {
                    type: 'string',
                    description: `FOR NEW IMAGES: 
                      - A detailed description of the image or UI component to create.
                      - Include all data points, quotes, and information needed to create the image.
                      - The AI creating the image will not have the ability to access the database or query for external data.
                      
                      FOR EDITING A PREVIOUS IMAGE:
                      - Create a prompt based on the user's feedback/message.
                      - Do not include any other information in the prompt.`,
                  },
                  short_description: {
                    type: 'string',
                    description:
                      'A short description (8-10 words max) of the image or UI component to create. Avoid referencing design aspects or edits to the image: the description should focus on the informational content of the image. This will be used to reference the image in the response back to the user.',
                  },
                  description: {
                    type: 'string',
                    description:
                      "A succinct 1-sentence description of the image, based on the user's request and the content of the slide images. Avoid referencing design aspects or edits to the image.",
                  },
                  width: {
                    type: 'number',
                    description:
                      'Width of the generated image in pixels (auto-detected if not specified)',
                  },
                  height: {
                    type: 'number',
                    description:
                      'Height of the generated image in pixels (auto-detected if not specified)',
                  },
                  previous_image_id: {
                    type: 'string',
                    description:
                      'The id of the image the user is referring to if they are requesting edits to a previously generated image.',
                  },
                  template_asset_id: {
                    type: 'number',
                    description:
                      'The id of the asset to use as a template for creating a new image(s).',
                  },
                },
                required: ['prompt', 'short_description', 'description'],
              },
            },
          },
          required: ['images'],
        },
      },
    };
  }

  static getAdditionalInstructions(): string {
    return '';
  }

  static disableCache(): boolean {
    return true;
  }

  getPendingMessage(): string {
    const numImages = this.prompts.length;
    const numUpdating = this.previousImageIds.filter(Boolean).length;
    const numCreating = numImages - numUpdating;

    if (numUpdating == 0) {
      const disclaimer =
        numImages == 1
          ? ' (this may take a minute or two)'
          : ' (this may take a couple minutes)';
      return `_Creating ${numImages} image${numImages > 1 ? 's' : ''}${disclaimer}_ :art:`;
    } else if (numCreating == 0) {
      const disclaimer =
        numImages > 2 ? ' (this may take a couple minutes)' : '';
      return `_Updating ${numUpdating} image${numUpdating > 1 ? 's' : ''}${disclaimer}_ :art:`;
    } else {
      const disclaimer =
        numImages > 2
          ? ' (this may take a couple minutes)'
          : numCreating == 1
            ? ' (this may take a minute or two)'
            : '';
      return `_Creating ${numCreating} image${numCreating > 1 ? 's' : ''} and updating ${numUpdating} image${numUpdating > 1 ? 's' : ''}${disclaimer}_ :memo:`;
    }
  }

  getCompletionMessage(): string {
    const numImages = this.prompts.length;
    return `${numImages} image${numImages > 1 ? 's' : ''} generated :art:`;
  }

  async performCall(): Promise<string> {
    // If there was an error in the constructor, just return right away
    if (this.initiatingError) {
      return this.initiatingError;
    }

    let reactCodeSnippets: string[] = [];
    let imageUrls = [];
    let imageIds = [];
    let messages: string[] = [];
    let shortDescriptions: string[] = [];

    // Parallelize the image generation for each prompt
    const genResults = await Promise.all(
      this.prompts.map(async (prompt, index) => {
        const width = this.widths[index];
        const height = this.heights[index];
        const shortDescription = this.shortDescriptions[index];
        const imageInfo = this.imageInfos?.[index];

        return getReactCodeForPrompt({
          prompt,
          clientId: parseInt(this.clientId),
          userId: parseInt(this.userId),
          databaseId: this.databaseId,
          helperFuncKey: this.helperFuncKey,
          helpersUrl: this.helpersUrl,
          width,
          height,
          shortDescription,
          previousImageId: this.previousImageIds[index],
          templateAssetId: this.templateAssetId,
          imageInfos: imageInfo ? [imageInfo] : undefined,
        });
      }),
    );
    genResults.forEach((result) => {
      reactCodeSnippets.push(result.reactCode);
      imageUrls.push(result.imageUrl);
      imageIds.push(result.imageId);
      shortDescriptions.push(result.shortDescription);
      if (result.message) messages.push(result.message);
    });

    const redis = new Redis();

    //Ok now we can write a message back.
    let response = `I created the following images:\n`;
    await Promise.all(
      imageUrls.map(async (url, index) => {
        response += `\n\n![Image ${index + 1}${
          shortDescriptions[index] ? `: ${shortDescriptions[index]}` : ''
        }${imageIds[index] ? ` (id=${imageIds[index]})` : ''}](${
          url || 'Error creating image'
        })`;

        const cacheKey = `reactSlides-${imageIds[index]}`;
        await redis.set(
          cacheKey,
          JSON.stringify({
            reactCode: reactCodeSnippets[index],
            description: this.descriptions[index],
            shortDescription: shortDescriptions[index],
            dimensions: {
              width: this.widths[index],
              height: this.heights[index],
            },
          }),
          RedisConstants.EX_10_MINS,
        );
      }),
    );

    const someImagesHaveErrors = imageUrls.some((url) => url.length === 0);

    if (imageUrls.filter((url) => url.length > 0).length === 0) {
      response += `\n\n[No images were generated. Something may have gone wrong.]`;
    } else {
      response += `\n\n**CRITICAL:** You MUST include those image links as Markdown images in your response back to the user, otherwise the user will not be able to see the images.${
        someImagesHaveErrors
          ? '\n\nNote: some images were not created. Please notify the user of this.'
          : ''
      }`;
    }

    if (messages.length) {
      response += `\n\nAdditional messages:\n`;
      messages.forEach((msg, index) => {
        response += `\n- ${msg}`;
      });
    }

    return response;
  }
}

const getReactCodeForPrompt = async ({
  prompt,
  clientId,
  userId,
  databaseId,
  helperFuncKey,
  width,
  height,
  shortDescription,
  previousImageId,
  templateAssetId,
  imageInfos,
  helpersUrl,
}: {
  prompt: string;
  clientId: number;
  userId: number;
  databaseId: string;
  helperFuncKey: string;
  width: number;
  height: number;
  shortDescription: string;
  previousImageId: string;
  templateAssetId?: number;
  imageInfos?: Array<{ dataUrl: string; mimetype: string }>;
  helpersUrl: string;
}): Promise<{
  imageUrl: string;
  imageId: string;
  reactCode: string;
  message: string;
  shortDescription: string;
}> => {
  const data = {
    imageUrl: '',
    imageId: '',
    reactCode: '',
    message: '',
    shortDescription: '',
  };

  try {
    // Request the service to return a URL instead of base64
    const formData = new FormData();
    formData.append('prompt', prompt);
    formData.append('client_id', clientId);
    formData.append('user_id', userId);
    formData.append('database_id', databaseId);
    formData.append('job_id', Date.now().toString());
    formData.append('width', width.toString());
    formData.append('height', height.toString());
    formData.append('previous_image_id', previousImageId);
    formData.append('return_url', 'true'); // Always request URL to avoid token overflow

    // If imageInfos are provided, use the first image as reference
    if (imageInfos && imageInfos.length > 0) {
      try {
        // Extract the base64 data from the data URL
        const firstImage = imageInfos[0];
        const base64Match = firstImage.dataUrl.match(/^data:.*?;base64,(.*)$/);

        if (base64Match && base64Match[1]) {
          const imageBuffer = Buffer.from(base64Match[1], 'base64');
          formData.append('image', imageBuffer, {
            filename: 'reference.png',
            contentType: firstImage.mimetype || 'image/png',
          });

          console.log(
            '[CreateImageFromPromptTool] Using uploaded image as reference for generation',
          );
        }
      } catch (error) {
        console.warn(
          'Failed to process reference image, proceeding without it:',
          error,
        );
      }
    }

    if (templateAssetId) {
      formData.append('template_asset_id', templateAssetId.toString());
    }

    const response = await fetch(`${helpersUrl}/api/prompt-to-react-image`, {
      method: 'POST',
      body: formData,
      headers: {
        ...formData.getHeaders(),
        ...(helperFuncKey && {
          'x-functions-key': helperFuncKey,
        }),
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(
        '[CreateImageFromPromptTool] Call to prompt-to-react-image failed:',
        errorText,
      );
      data.message += `I was unable to generate the image.`;
    }

    const result = await response.json();
    data.reactCode = result.react_code;
    data.shortDescription = shortDescription;

    if (!result.success) {
      console.error(
        '[CreateImageFromPromptTool] Image generation failed:',
        result.error,
      );
      data.message += `I was unable to generate the image. Error: ${result.error}`;
    }

    // Check if we got an image URL back
    if (result.image_url) {
      // The Slack integration expects markdown images with files.tribble.ai URLs
      // but the actual blob URL needs to be accessible for download
      const imageUrl = result.image_url;
      const imageId = result.image_id;
      const filesUrl = imageUrl.replace(
        '.blob.core.windows.net',
        '.files.tribble.ai',
      );
      data.imageUrl = filesUrl;
      data.imageId = imageId;

      // Return the image in markdown format that will be processed by processMessageAndUploadImagesToSlack
      // The alt text is important as it's shown if image upload fails
    } else if (result.image_base64) {
      console.warn('Image generated but no URL returned');
      data.message += `Image was generated but no image_url was returned.`;
    } else {
      // No image data at all
      data.message += `I was unable to generate the image.`;
    }
  } catch (error) {
    console.error('Error in create_image_from_prompt:', error);
    data.message += `I was unable to generate the image.`;
  } finally {
    return data;
  }
};
