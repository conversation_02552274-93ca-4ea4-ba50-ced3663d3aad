import { Tool } from '@tribble/chat-completion-service/client';
import { Logger } from '@tribble/common-utils';
import { initializeReadOnlyPool } from '@tribble/tribble-db/db_ops';
import { Pool } from 'pg';
import { ConversationSourceFormat } from '../conversations/conversation_context.ts';
import { ConversationMessage } from '../conversations/message.ts';
import { BrainSearch } from './brain_search.ts';
import { CustomTableDescribe } from './custom_table_describe.ts';
import { SearchCustomTables } from './custom_table_search.ts';
import { ExtractCitationFormat, ToolCall } from './tool_call.ts';

export class StructuredQuery extends ToolCall {
  static tool_name = 'structured_query';
  static citationIdPrefix = 'struct_';
  private query: string;
  private table_name: string;
  private schema: string;

  constructor(
    config: { pool?: Pool; schema: string; logger?: Logger },
    args: { query: string; table_name: string },
  ) {
    super(config.logger);
    this.query = args.query;
    this.table_name = args.table_name;
    this.schema = config.schema;
  }

  static getJsonSchema(): Tool {
    return {
      type: 'function',
      function: {
        name: 'structured_query',
        description: `Query a structured data source in postgres.`,
        parameters: {
          type: 'object',
          properties: {
            table_name: {
              type: 'string',
              description: `The name of the table being queried. Must be fetched from the tool ${SearchCustomTables.tool_name}.`,
            },
            query: {
              type: 'string',
              description:
                'The SQL query to run on the structured data source. The query must be written in PostgreSQL.',
            },
          },
          required: ['query', 'table_name'],
        },
      },
    };
  }

  getPendingMessage(): string {
    return 'Querying structured data source... :mag:';
  }

  getCompletionMessage(): string {
    return 'Queried structured data source... :mag_right:';
  }

  static getPromptDescription(): string {
    let desc = `You have a set of tools for structured data. If a ${BrainSearch.tool_name} does not provide an answer try these tools as well. You must always use ${SearchCustomTables.tool_name} to get a list of tables before writing your query. Then use ${StructuredQuery.tool_name} to query them.`;
    desc += ` Pay special attention to picklist fields and make sure you consider synonyms and related field values. Example: if querying for a value 'IT services' use the ${CustomTableDescribe.tool_name} tool and see if there are related values in that field like 'I.T.', 'Internet Software', 'Information Technology', etc.`;
    return desc;
  }

  async performCall(): Promise<string> {
    try {
      const customSchema = getCustomSchema(this.schema);
      this.query = this.query.replaceAll(
        this.table_name,
        `${customSchema}.${this.table_name}`,
      );
      const res = await (await initializeReadOnlyPool()).query(this.query);

      if (res.rows) {
        console.log('Got structured query rows:', res.rows.length);
        const result = {
          reference: `${StructuredQuery.citationIdPrefix}[${this.table_name}]`,
          rows: res.rows,
        };
        if (result.rows.length === 0) {
          (result as any).message =
            'No results found. Do you need to describe the table?';
        }
        return JSON.stringify(result);
      }
    } catch (err) {
      return err.toString();
    }
  }

  static extractCitations(
    answer: string,
    messages: ConversationMessage[],
    initialContexts?: ConversationSourceFormat[],
  ): ExtractCitationFormat {
    const usedCitations = [];

    const structured_re = new RegExp(
      '\\^' + StructuredQuery.citationIdPrefix + '[[a-zA-Z0-9_]*]',
      'g',
    );

    const structured_match = answer.match(structured_re);
    if (structured_match != null) {
      structured_match.forEach((citation) => {
        const match = citation.match(/\[c_(\d+)_([a-zA-Z0-9_]+)\]/);
        if (match) {
          const [, docId, fileName] = match;
          usedCitations.push({
            id: docId,
            file_name: fileName,
            url: docId ? `${process.env.APP_PATH}/sources/source/${docId}` : '',
            reference: citation,
          });
        }
        answer = answer.replace(citation, '');
      });
    }

    return { citations: usedCitations, updatedAnswer: answer };
  }
}

export function getCustomSchema(schema: string) {
  return `${schema}_custom`;
}
