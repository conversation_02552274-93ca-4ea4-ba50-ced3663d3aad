import {
  ActivityLogData,
  chatCompletion,
  Tool,
} from '@tribble/chat-completion-service/client';
import { Logger } from '@tribble/common-utils';
import fetch from 'node-fetch';
import { get_encoding } from 'tiktoken';
import { ToolCall } from './tool_call.ts';

export interface FetchUrlConfig {
  schema: string;
  activityLogData: ActivityLogData;
  helperFuncKey: string;
  scrapeWebPageEndpoint: string;
  pageSummaryLength: number;
  pageSummaryModel: string;
  pageSummaryTemp: number;
  logger?: Logger;
}

export class FetchUrl extends ToolCall {
  static tool_name = 'fetch_url';
  static tokenizer = get_encoding('cl100k_base');

  private url: string;
  protected config: FetchUrlConfig;

  constructor(config: FetchUrlConfig, args: { url: string }) {
    super(config.logger);
    this.url = args.url;
    this.config = config;
  }

  static getPromptDescription(): string {
    return '';
  }

  static getJsonSchema(): Tool {
    return {
      type: 'function',
      function: {
        name: 'fetch_url',
        description: 'Fetch text content from the provided URL.',
        parameters: {
          type: 'object',
          properties: {
            url: {
              type: 'string',
              description:
                'A complete URL from which to fetch text content for analysis. Must be a valid URL.',
            },
          },
          required: ['url'],
        },
      },
    };
  }

  getPendingMessage(): string {
    return `_Retrieving content from <${this.url}|${this.url}>..._ :articulated_lorry:`;
  }

  getCompletionMessage(): string {
    return `Retrieved content from <${this.url}|website> :newspaper:`;
  }

  async performCall(): Promise<string> {
    console.log('fetching from url:', this.url);
    const data = {
      url: this.url,
      readerMode: true,
      schema: this.config.schema,
    };

    try {
      const resp = await fetch(this.config.scrapeWebPageEndpoint, {
        method: 'POST',
        headers: {
          'x-functions-key': this.config.helperFuncKey,
          'Content-Type': 'application/json',
          Accept: 'text/plain',
        },
        body: JSON.stringify(data),
      });

      if (!resp.ok) {
        throw new Error(`Error: ${resp.status} ${resp.statusText}`);
      }

      const pageResp = (await resp.json()) as { content: string },
        pageContents = pageResp.content;

      if (
        pageContents &&
        countTokens(pageContents) > this.config.pageSummaryLength
      ) {
        return summarize(
          this.config.schema,
          this.config.activityLogData,
          this.config.pageSummaryLength,
          this.config.pageSummaryModel,
          this.config.pageSummaryTemp,
          pageContents,
        ).then(
          (summary) =>
            `Text-only summary of the page content is below, enclosed in triple quotes.\n\n"""\n${summary}\n"""`,
        );
      }

      return `Text-only content of the page is below, enclosed in triple quotes.\n\n"""\n${pageContents}\n"""`;
    } catch (error) {
      if (error instanceof Error) {
        console.error('Error fetching URL:', error);
        return error.toString();
      }
    }
    return 'Unknown error occurred while fetching URL.';
  }
}

function countTokens(text: string): number {
  return FetchUrl.tokenizer.encode(text).length;
}

async function summarize(
  schema: string,
  activityLogData: ActivityLogData,
  length: number,
  model: string,
  temp: number,
  text: string,
): Promise<string> {
  try {
    const trimmedText = trimToTokenLength(text, 14336), // Fits GPT3.5
      response = await chatCompletion({
        schema,
        timeout: 60000,
        activityLogData: activityLogData,
        resourceUrl: '/openai/deployments/tribble-gpt4/chat/completions',
        apiVersion: process.env.AGENT_API_VERSION_LATEST ?? '2024-10-21',
        request: {
          model: model,
          messages: [
            {
              role: 'system',
              content: `Please summarize the following text enclosed in triple quotes. Provide some structure and include specifics where possible.
"""
${trimmedText}
"""`,
            },
          ],
          max_tokens: length,
          temperature: temp,
        },
      }),
      openAIResponse = response.response,
      respContent: string = openAIResponse.choices[0].message.content as string;
    if (respContent === null) {
      return 'No summary was generated.';
    }
    console.log('Summary: ', respContent);
    return respContent.trim();
  } catch (error) {
    if (error instanceof Error) {
      console.error('Error summarizing page:', error.message);
      return error.toString();
    }
  }
  return 'Unknown error occurred while summarizing page content.';
}

function trimToTokenLength(str: string, targetLength: number): string {
  const encoding = get_encoding('cl100k_base'),
    encoded = encoding.encode(str);
  if (encoded.length < targetLength) {
    return str;
  }
  const toDecode = encoded.slice(0, targetLength),
    decoded = encoding.decode(toDecode),
    utf8Decoder = new TextDecoder('utf-8');
  return utf8Decoder.decode(decoded);
}
