import { Tool } from '@tribble/chat-completion-service/client';
import { ClientSchema } from '@tribble/tribble-db';
import { ClientId, ConversationId, UserId } from '@tribble/tribble-db/types';
import { Filter, PlatformMapping, TieredContextSearch } from '../brain.ts';
import { ConversationContext } from '../conversations/conversation_context.ts';
import {
  getMetadataFilterRecords,
  MetadataFilterValue,
} from '../metadata_filters.ts';
import { ResolvedSettings } from '../resolved_settings.ts';
import { telemetryClient } from '../telemetry.ts';
import { BaseSearchTool } from './base_search_tool.ts';
import { TimeFilter } from './extract_temporal.ts';
import { FindDocuments } from './find_documents.ts';

export class TieredContextSearchTool extends BaseSearchTool {
  static tool_name = 'tiered_context_search';

  private tier: number;
  private agentType?: 'rfp_agent' | 'digital_se_agent';

  constructor(
    config: {
      clientId: ClientId;
      clientName: string;
      conversationId: ConversationId;
      metadataFilters: MetadataFilterValue[];
      schema: string;
      tribbleUserId: UserId;
      includeVerbatim?: boolean;
      includeImages?: boolean;
      timeFilter?: TimeFilter;
      settings: ResolvedSettings;
      agentType?: 'rfp_agent' | 'digital_se_agent';
    },
    args: {
      query: string;
      tier: number;
      filters?: Filter[];
      imageInfos?: Array<{ dataUrl: string; mimetype: string }>;
    },
  ) {
    super(config, args);

    this.tier = args.tier;
    this.agentType = config.agentType;
  }

  static getPromptDescription(params: any): string {
    const clientName = params.clientName || 'our company';
    return (
      `${this.tool_name}: Searches ` +
      `${clientName}'s knowledge base within a specific tier of the access sequence to retrieve relevant context based on a query. ` +
      `This tool performs context searches only on documents covered by sources in the specified tier. ` +
      `Start with tier 1 and only proceed to higher tiers if the current tier's results are insufficient to answer the user's question. ` +
      `Stop searching once you have found adequate information. ` +
      `Do not use this tool to locate an entire document by name or type. ` +
      `Instead, use the ${FindDocuments.tool_name} tool for that. ` +
      `Supports optional filters to further narrow results by source name, type, or platform.`
    );
  }

  static getJsonSchema(
    suppliedSourceTypes: any,
    enableExtraFilters = true, // Flag to disable filters for unavailable platforms
  ): Tool {
    const sourceTypes = suppliedSourceTypes || [];

    return {
      type: 'function',
      function: {
        name: 'tiered_context_search',
        description:
          "Search your company's internal knowledge base within a specific access tier.",
        parameters: {
          type: 'object',
          properties: {
            query: {
              type: 'string',
              description:
                'Find internal search results aligned with this string. ' +
                'Conducts a vector search on documents in the specified tier. A long query phrase is preferable.',
            },
            tier: {
              type: 'number',
              description:
                'The access sequence tier to search within (e.g., 1, 2, 3). ' +
                'Only documents from sources in this tier will be searched.',
            },
            ...(enableExtraFilters && {
              filters: {
                type: 'array',
                description:
                  'Optional additional filters to apply within the tier. These work alongside tier restrictions:\n' +
                  '- `source_name`: Filter by a search term for source names, e.g., "project plan", "my file pdf", "<slack channel id>".\n' +
                  '- `source_type`: Filter by source type, e.g., "Presentation", "Document", "Slack", "Website".\n' +
                  '- `source_platform`: Filter by platform, e.g., "GoogleDrive", "Box", "Notion", "Confluence".',
                items: {
                  type: 'object',
                  properties: {
                    source_name: {
                      type: 'string',
                      description:
                        'A search term to match partial or complete source names, e.g., "audit report", "pitch deck".',
                    },
                    source_type: {
                      type: 'string',
                      enum: sourceTypes,
                      description:
                        'The source type to filter, e.g. "Presentation" (slide, pptx etc.), "Document", or "Website".',
                    },
                    source_platform: {
                      type: 'string',
                      enum: Object.keys(PlatformMapping).filter(
                        (p) => p !== 'Slack' && p !== 'Website',
                      ),
                      description:
                        'The platform where the source is located, e.g., "GoogleDrive", "Box", or "Notion".',
                    },
                  },
                  additionalProperties: false,
                  description:
                    'Each filter object can include any combination of `source_type`, `source_name`, and `source_platform` fields.',
                },
              },
            }),
          },
          required: ['query', 'tier'],
        },
      },
    };
  }

  getPendingMessage(): string {
    return `_Searching Tier ${this.tier} Sources for "${this.query}"..._ :brain:`;
  }

  getCompletionMessage(): string {
    let matchString = '';
    switch (this.contexts.length) {
      case 0:
        matchString = ' found _no_ matches';
        break;
      case 1:
        matchString = ''; // ' found 1 match';
        break;
      default:
        matchString = ''; // ` found ${this.contexts.length} matches`;
    }
    const filterString =
      this.filters.length > 0 ? ` with ${this.filters.length} filter(s)` : '';
    // Only show "no matches" status to avoid misleading match counts
    return `Searched Tier ${this.tier} Sources for "${this.query}"${filterString}${matchString} :brain:`;
  }

  protected getTelemetryPrefix(): string {
    return 'tiered_context_search';
  }

  /**
   * Performs text search for the given query
   * @returns Array of search contexts
   */
  protected async performTextSearch(
    query: string,
  ): Promise<ConversationContext[]> {
    const startTime = Date.now();

    const results = await TieredContextSearch({
      query,
      schema: this.schema,
      tier: this.tier,
      agentType: this.agentType,
      filters: this.filters,
      strictMode: this.strictMode,
      metadataFilters: this.metadataFilters,
      allowSlackRag: this.allowSlackRag,
      includeVerbatim: this.includeVerbatim,
      timeFilter: this.timeFilter,
      allowGongRag: this.settings.enableGong,
      conversationId: this.conversationId,
    });

    const queryTime = Date.now() - startTime;

    console.log(
      `[TieredContextSearch] Search finished for tier ${this.tier} in ${queryTime}ms [schema=${this.schema} conversation=${this.conversationId}]`,
    );

    telemetryClient.trackMetric({
      name: 'tiered_context_search',
      value: queryTime,
      properties: {
        conversation_id: this.conversationId.toString(),
        tier: this.tier.toString(),
        agent_type: this.agentType || 'null',
        filters_used: this.filters.length > 0 ? 'true' : 'false',
        filter_count: this.filters.length.toString(),
      },
    });

    return results;
  }

  async performCall(): Promise<string> {
    // Use the shared base implementation
    await this.performBaseCall();

    if (!this.contexts.length) {
      return JSON.stringify([
        `No results found for query in tier ${this.tier}`,
      ]);
    }

    // Special handling for verbatim contexts in Agentic Answer RFP
    if (this.includeVerbatim) {
      const metadataFilterRecords = await getMetadataFilterRecords(
        this.schema as ClientSchema,
      );

      this.contexts.forEach((ctx) => {
        const verbatimMetadataFilter = metadataFilterRecords.find((mdf) => {
          const strMdfTypeId = String(mdf.type_id);
          return (
            ctx.metadata_filter &&
            ctx.metadata_filter[strMdfTypeId] &&
            ctx.metadata_filter[strMdfTypeId].includes(mdf.value) &&
            mdf.type.is_verbatim
          );
        });
        if (verbatimMetadataFilter) {
          ctx.is_verbatim_metadata_filter = true;
        }
      });
    }

    return JSON.stringify(
      this.contexts
        .filter(Boolean)
        .map((ctx, idx) =>
          ctx.toSourceFormat(idx, this.metadataFilters, this.includeImages),
        ),
    );
  }
}
