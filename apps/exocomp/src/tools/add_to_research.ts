import { Tool } from '@tribble/chat-completion-service/client';
import { Logger } from '@tribble/common-utils';
import { getDB } from '@tribble/tribble-db/db_ops';
import { ConversationId } from '@tribble/tribble-db/types';
import { getConversationState } from '../conversations/conversation_state.ts';
import { ToolCall } from './tool_call.ts';

export class AddToResearch extends ToolCall {
  static tool_name = 'add_to_research';
  conversationId: ConversationId;
  schema: string;
  ids: number[];
  note: string;
  came_from_exemplar: boolean;

  constructor(
    config: { schema: string; conversationId: ConversationId; logger?: Logger },
    args: { ids: number[]; note: string; came_from_exemplar: boolean },
  ) {
    super(config.logger);
    this.ids = args.ids;
    this.came_from_exemplar = args.came_from_exemplar;
    this.note = args.note;
    this.conversationId = config.conversationId;
    this.schema = config.schema;
  }

  static getPromptDescription(): string {
    return `Use ${AddToResearch.tool_name} to add chunks retrieved from search to the research packet.`;
  }

  static getJsonSchema(): Tool {
    return {
      type: 'function',
      function: {
        name: AddToResearch.tool_name,
        description: 'Add chunks to research packet',
        parameters: {
          type: 'object',
          properties: {
            ids: {
              type: 'array',
              description: '1 or ids to add to the research packet',
              items: {
                type: 'number',
              },
            },
            note: {
              type: 'string',
            },
            came_from_exemplar: {
              type: 'boolean',
              description: 'Whether the ids came from the exemplar',
            },
          },
          required: ['ids', 'came_from_exemplar', 'note'],
          additionalProperties: false,
        },
      },
    };
  }

  getPendingMessage(): string {
    return `Adding ids...`;
  }

  getCompletionMessage(): string {
    return `Added ids`;
  }

  async performCall(): Promise<string> {
    const db = await getDB(this.schema);
    let researchPacket = [];
    try {
      await db.transaction().execute(async (trx) => {
        const state = await getConversationState(
          this.schema,
          parseInt(this.conversationId) as number,
        );

        researchPacket = state.research || [];
        researchPacket.push({
          ids: this.ids,
          note: this.note,
          from_exemplar: this.came_from_exemplar,
        });
        await trx
          .updateTable('conversation_state')
          .set('research', JSON.stringify(researchPacket))
          .where('conversation_id', '=', this.conversationId)
          .execute();
      });
    } catch (err) {
      console.log(err);
      return JSON.stringify(['error adding ids to state: ' + err.message]);
    }

    return JSON.stringify([
      `Added ids. Research packet is now: ${JSON.stringify(researchPacket)}`,
    ]);
  }
}
