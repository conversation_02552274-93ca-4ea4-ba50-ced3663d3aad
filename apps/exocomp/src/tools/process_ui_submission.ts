/*
 * Process UI Submission Tool

 * Daniel says: This tool is an extraneous step: When a form is submitted, tribble-chat handles the UI updates, and then
 * "pokes" the agent to please run this tool against the form values. If agent decides to do it, this tool just
 * formats the form values and "speaks them to itself". We could save the step by having tribble-chat format the values 
 * itself, and poke the agent with them. I am leaving this here for now in case it's part of <PERSON>'s master plan
 * for learning management.
 *
 * This tool processes ephemeral UI form submissions and converts them
 * into structured instructions for the agent to execute. It retrieves
 * the UI state from Redis and formats the submitted data.
 *
 */

import { Tool } from '@tribble/chat-completion-service/client';
import { Logger } from '@tribble/common-utils';
import { Redis } from '@tribble/redis';
import { ClientSchema } from '@tribble/tribble-db';
import {
  ConversationDetailId,
  ConversationId,
} from '@tribble/tribble-db/types';
import { EphemeralUIState } from './generate_ephemeral_ui.ts';
import { ToolCall } from './tool_call.ts';

export interface ProcessUISubmissionArgs {
  ui_id: string;
  form_values: Record<string, any>;
  action: 'submit' | 'cancel';
}

export class ProcessUISubmission extends ToolCall {
  static tool_name = 'process_ui_submission';

  private uiSubmissionArgs: ProcessUISubmissionArgs;
  private conversationId: ConversationId;

  constructor(
    config: {
      schema: ClientSchema;
      conversationId: ConversationId;
      conversationDetailId: ConversationDetailId;
      logger?: Logger;
    },
    args: ProcessUISubmissionArgs,
  ) {
    super(config.logger);
    this.uiSubmissionArgs = args;
    this.conversationId = config.conversationId;
  }

  static getPromptDescription(_: any): string {
    return (
      `You have access to a tool that processes ephemeral UI form submissions. ` +
      `This tool receives form data from users and converts it into structured instructions.`
    );
  }

  static getJsonSchema(_: any): Tool {
    return {
      type: 'function',
      function: {
        name: ProcessUISubmission.tool_name,
        description:
          'Process ephemeral UI form submission and convert to structured instructions',
        parameters: {
          type: 'object',
          properties: {
            ui_id: {
              type: 'string',
              description: 'The unique ID of the UI form being submitted',
            },
            form_values: {
              type: 'object',
              description: 'The form field values submitted by the user',
              additionalProperties: true,
            },
            action: {
              type: 'string',
              enum: ['submit', 'cancel'],
              description: 'The action taken by the user',
            },
          },
          required: ['ui_id', 'form_values', 'action'],
        },
      },
    };
  }

  getPendingMessage(): string {
    return '⏳ Processing form submission...';
  }

  getCompletionMessage(): string {
    return '✅ Form processed successfully';
  }

  async performCall(): Promise<string> {
    try {
      if (this.uiSubmissionArgs.action === 'cancel') {
        // Clean up the UI state
        await this.cleanupUIState();
        return '❌ Form cancelled. No changes were made.';
      }

      // Retrieve UI state from Redis
      const redis = new Redis();
      const cacheKey = `ephemeral_ui:${this.uiSubmissionArgs.ui_id}`;
      const stateJson = await redis.get(cacheKey);

      if (!stateJson) {
        return '⚠️ The form has expired or was already submitted.';
      }

      const uiState: EphemeralUIState = JSON.parse(stateJson);

      // Validate that this submission is for the correct conversation
      if (uiState.conversation_id !== this.conversationId) {
        return '⚠️ This form submission does not match the current conversation.';
      }

      // Format the submission into structured instructions
      const instructions = this.formatInstructions(
        uiState,
        this.uiSubmissionArgs.form_values,
      );

      // Clean up the UI state since it's been submitted
      await this.cleanupUIState();

      // Return user-friendly message with instructions
      return `✅ **Form submitted successfully**\n\n${instructions}\n\n_Processing your request..._`;
    } catch (err) {
      console.error('Error processing UI submission:', err);
      return '⚠️ An error occurred while processing the form submission.';
    }
  }

  private formatInstructions(
    uiState: EphemeralUIState,
    formValues: Record<string, any>,
  ): string {
    // Convert form values into natural language instructions
    const lines: string[] = [
      `Execute ${uiState.purpose} with the following parameters:`,
    ];

    // Special handling for common purposes
    switch (uiState.purpose) {
      case 'data_migration_config':
        return this.formatMigrationInstructions(formValues);
      case 'bulk_update_params':
        return this.formatBulkUpdateInstructions(formValues);
      default:
        // Generic formatting
        for (const [key, value] of Object.entries(formValues)) {
          const field = uiState.fields.find((f) => f.name === key);
          if (field) {
            if (Array.isArray(value)) {
              lines.push(`- ${field.label}: ${value.join(', ')}`);
            } else {
              lines.push(`- ${field.label}: ${value}`);
            }
          }
        }
    }

    return lines.join('\n');
  }

  // If we don't see much use of migrations, we can take this out.
  private formatMigrationInstructions(formValues: Record<string, any>): string {
    const instructions: string[] = [];

    instructions.push(
      `Migrate data from ${formValues.source_system} to ${formValues.target_system}:`,
    );

    if (formValues.source_object) {
      instructions.push(`- Source object: ${formValues.source_object}`);
    }

    if (formValues.target_object) {
      instructions.push(`- Target object: ${formValues.target_object}`);
    }

    if (formValues.field_mappings) {
      instructions.push(
        `- Field mappings: ${formValues.field_mappings.join(', ')}`,
      );
    }

    if (formValues.filter_criteria) {
      instructions.push(`- Filter: ${formValues.filter_criteria}`);
    }

    if (formValues.conflict_resolution) {
      instructions.push(`- On conflict: ${formValues.conflict_resolution}`);
    }

    if (formValues.dry_run) {
      instructions.push(`- Mode: Dry run (no actual changes)`);
    }

    return instructions.join('\n');
  }

  private formatBulkUpdateInstructions(
    formValues: Record<string, any>,
  ): string {
    const instructions: string[] = [];

    instructions.push(`Perform bulk update in ${formValues.system}:`);

    if (formValues.object_type) {
      instructions.push(`- Object type: ${formValues.object_type}`);
    }

    if (formValues.filter_criteria) {
      instructions.push(`- Filter: ${formValues.filter_criteria}`);
    }

    if (formValues.updates) {
      instructions.push(`- Updates: ${JSON.stringify(formValues.updates)}`);
    }

    if (formValues.validate_before_apply) {
      instructions.push(`- Validate changes before applying`);
    }

    return instructions.join('\n');
  }

  private async cleanupUIState(): Promise<void> {
    const redis = new Redis();
    const cacheKey = `ephemeral_ui:${this.uiSubmissionArgs.ui_id}`;
    await redis.del(cacheKey);
  }
}
