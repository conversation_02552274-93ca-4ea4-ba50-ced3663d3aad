import { getDB, query } from '@tribble/tribble-db/db_ops';
import { TableCatalog } from '@tribble/types-shared';

export async function hasStructuredData(schema: string): Promise<boolean> {
  const db = await getDB(schema);

  const result = await db
    .selectFrom('table_catalog')
    .select(['table_name'])
    .limit(1)
    .execute();
  return result.length > 0;
}

const TABLE_SIMILARITY_THRESHOLD = parseFloat(
  process.env.TABLE_SIMILARITY_THRESHOLD || '0',
);

export type TableCatalogResult = Pick<
  TableCatalog,
  'table_name' | 'description' | 'structure' | 'label'
> & { similarity: number };

// Query the table catalog for tables that are similar to the given embedding
export async function queryTableCatalog(
  schema: string,
  embedding: number[],
): Promise<TableCatalogResult[]> {
  const embeddingString = JSON.stringify(embedding);
  const queryString = `
    SELECT 
      tc.table_name,
      tc.description,
      tc.structure as schema, 
      1 - (tc.description_embedding <=> '${embeddingString}'::vector) as similarity
    FROM ${schema}.table_catalog tc
    JOIN ${schema}.document d ON tc.document_id = d.id
    WHERE (1 - (tc.description_embedding <=> '${embeddingString}'::vector)) > ${TABLE_SIMILARITY_THRESHOLD}
    AND d.deleted_date IS NULL
    ORDER BY (1 - (tc.description_embedding <=> '${embeddingString}'::vector)) DESC
  `;
  try {
    const result = await query(queryString);
    if (result && result.rows) {
      return result.rows;
    }
    return [];
  } catch (err) {
    console.error(`[db/queryTableCatalog] ${err.message}`);
    return [];
  }
}

export async function describeCustomTable(schema: string, tableName: string) {
  const db = await getDB(schema);
  const result = await db
    .selectFrom('table_catalog')
    .innerJoin('document', 'table_catalog.document_id', 'document.id')
    .select([
      'table_catalog.description',
      'table_catalog.structure',
      'table_catalog.label',
    ])
    .where('table_catalog.table_name', '=', tableName)
    .where('document.deleted_date', 'is', null)
    .limit(1)
    .execute();
  return result[0];
}
