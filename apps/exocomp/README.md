# Exocomp

The Tribble agent server. This the server used to access <PERSON>bble's LLM agent
and make use of it's various tools.

Users converse with <PERSON>ocom<PERSON> using conversations. API users create a
conversation and are able to append messages to that conversation and get
multi-stage replies from Exocomp..

## Environment Variables

The following environment variables are used by Exocomp:

## Cartridges
Cartridges are a simple and powerful way to define agent behaviour. They consist of 
- a prompt
- a set of tools


Create a new cartridge by
1. Create a folder in src/cartridges with your definition file and prompt
2. Add a line to the Cartidge enum in service.proto
3. Add a line to the cartidge `switch` in conversation service



## Time-Based Filtering

Tribble supports time-based filtering using the `ExtractTemporal` tool in combination with both `BrainSearch` and `FindDocuments` tools. This allows users to search for information or documents within specific time ranges.

For example:
- "Show me documents from the last 3 months"
- "Find the most recent marketing plan"
- "Get financial reports from Q1 2023"

### How It Works

1. The `ExtractTemporal` tool extracts time constraints from user queries (e.g., "from last month", "newest first")
2. The time filter is saved to conversation state
3. Subsequent tool calls automatically receive the timeFilter through commonToolProps

### Supported Tools

Both these tools support time-based filtering:

- **BrainSearch**: Searches document content with time constraints
- **FindDocuments**: Lists documents matching time constraints

## Why call it Exocomp?

From Star Trek: TNG, Exocomps were tool-using robots with artificial
intelligence: <https://memory-alpha.fandom.com/wiki/Exocomp>.
