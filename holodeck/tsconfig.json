{
  "compilerOptions": {
    "strict": false,
    "noEmitOnError": false,
    "noImplicitAny": false,
    "strictNullChecks": false,
    "noImplicitThis": false,
    "baseUrl": "./src",
    "esModuleInterop": true,
    "module": "commonjs",
    "target": "ES2021",
    "allowJs": true,
    "jsx": "react",
    "sourceMap": true,
    "moduleResolution": "node",
    "allowSyntheticDefaultImports": true,
    "allowUmdGlobalAccess": true,
    "resolveJsonModule": true,
    "paths": {
      "utils/*": ["./utils/*"],
      "popup/*": ["./popup/*"],
      "background/*": ["./background/*"],
      "options/*": ["./options/*"],
      "content/*": ["./content/*"],
      "assets/*": ["./assets/*"],
      "components/*": ["./components/*"],
      "types/*": ["./types/*"],
      "@redux/*": ["./@redux/*"],
      "@hooks/*": ["./hooks/*"],
    },
    "types": ["chrome"],
    "typeRoots": ["node_modules/@types", "./src/types"],
  },
  "exclude": ["dist", "dev", "temp"],
}
