import TerserPlugin from 'terser-webpack-plugin';

import {
  Directories,
  config,
  getAnalyzerPlugins,
  getCleanWebpackPlugins,
  getCopyPlugins,
  getDefinePlugins,
  getEntry,
  getEslintPlugins,
  getExtensionManifestPlugins,
  getExtensionReloaderPlugins,
  getHTMLPlugins,
  getOutput,
  getProgressPlugins,
  getResolves,
  getZipPlugins,
} from './webpack.config.utils';

let generalConfig: any = {
  mode:
    config.NODE_ENV === 'production' || config.NODE_ENV === 'upload'
      ? 'production'
      : 'development',
  module: {
    rules: [
      {
        test: /\.(js|jsx|ts|tsx)$/,
        use: [
          {
            loader: 'ts-loader',
            // options: {
            //     transpileOnly: true,
            // },
          },
        ],
        exclude: /node_modules/,
      },
      {
        test: /\.(scss|css)$/,
        use: [
          {
            loader: 'style-loader',
          },
          {
            loader: 'css-loader',
          },
          {
            loader: 'sass-loader',
          },
        ],
      },
      {
        test: /\.(png|jpe?g|gif|svg)$/i,
        loader: 'file-loader',
        options: {
          outputPath: 'assets/images',
        },
      },
    ],
  },
  resolve: getResolves(),
  entry: getEntry(Directories.SRC_DIR),
  output: getOutput(config.TARGET, config.OUTPUT_DIR),
};

let plugins: any[] = [
  ...getCleanWebpackPlugins(
    `${config.OUTPUT_DIR}/${config.TARGET}`,
    `${Directories.DIST_DIR}/${config.TARGET}`,
  ),
  ...getProgressPlugins(),
  ...getEslintPlugins(),
  ...getDefinePlugins(),
  ...getExtensionManifestPlugins(),
  ...getHTMLPlugins(config.TARGET, config.OUTPUT_DIR, Directories.SRC_DIR),
  ...getCopyPlugins(config.TARGET, config.OUTPUT_DIR, Directories.SRC_DIR),
];

if (config.NODE_ENV === 'development') {
  generalConfig = {
    ...generalConfig,
    devtool: 'source-map',
    stats: {
      all: false,
      builtAt: true,
      errors: true,
      hash: true,
    },
    watch: true,

    /*
            I don't know why this combination works, but it does. Without this combination, webpack gets trapped in an infinite build loop.
            But with this combination,  the build loop seems to still occur, but is much less egregious --- it only happens once every few 
            saves,  and if it does occur, it seems to only loop 2 or 3 times before stopping.This only started happening as  the source 
            code for the chrome extension grew, so my guess is that the growing size of the project is causing webpack to take longer to 
            build, and that's causing the build loop to occur. Following in the grand old engineering tradition of not  looking a gift 
            horse in the mouth, I'm not going to question it and simply express gratitude to the universe for providing a solution to this
            problem during crunchtime.
        */
    watchOptions: {
      aggregateTimeout: 2000,
      poll: 5000,
      ignored: /(node_modules|dist|dev|\.storybook|temp)/,
    },
  };

  plugins = [...plugins, ...getExtensionReloaderPlugins()];
}

if (config.NODE_ENV === 'profile') {
  generalConfig = {
    ...generalConfig,
    devtool: 'source-map',
    stats: {
      all: false,
      builtAt: true,
      errors: true,
      hash: true,
    },
  };

  plugins = [...plugins, ...getAnalyzerPlugins()];
}

if (config.NODE_ENV === 'upload') {
  generalConfig = {
    ...generalConfig,
    optimization: {
      minimize: true,
      minimizer: [
        new TerserPlugin({
          parallel: true,
          terserOptions: {
            format: {
              comments: false,
            },
          },
          extractComments: false,
        }),
      ],
    },
  };

  plugins = [...plugins];
}

if (config.NODE_ENV === 'production') {
  generalConfig = {
    ...generalConfig,
    optimization: {
      minimize: true,
      minimizer: [
        new TerserPlugin({
          parallel: true,
          terserOptions: {
            format: {
              comments: false,
            },
          },
          extractComments: false,
        }),
      ],
    },
  };

  plugins = [...plugins, ...getZipPlugins(config.TARGET, Directories.DIST_DIR)];
}

export default [
  {
    ...generalConfig,
    plugins,
  },
];
