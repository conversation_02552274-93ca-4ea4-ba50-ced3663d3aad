- There is a peer dependencies issue when you run `npm install`, having to do with eslint versions. This forces us to use the `--legacy-peer-deps` flag. Any sensible solution is desirable, including the removal of various eslint-related packages.
- Livereload for extension dev purposes, probably using chokidar. See the readme, all that needs to be done is the `build` has to be run again on file change.
- need to track questionnaire status somewhere. (right now we use job status, which isn't great, because that should be for the job! see answer_rfp fuction)
