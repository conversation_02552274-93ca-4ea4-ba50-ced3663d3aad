import ThemeProvider from '@mui/material/styles/ThemeProvider';
import type { Preview } from '@storybook/react';
import React from 'react';
import { theme } from '../src/atomic-design/theme';

const preview: Preview = {
  parameters: {
    actions: { argTypesRegex: '^on[A-Z].*' },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/,
      },
    },
  },
};

export default preview;

export const decorators = [
  (Story) => (
    <ThemeProvider theme={theme}>
      <Story />
    </ThemeProvider>
  ),
];
