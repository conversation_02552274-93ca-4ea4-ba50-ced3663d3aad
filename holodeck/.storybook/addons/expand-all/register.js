// This is from https://github.com/storybookjs/storybook/issues/244#issuecomment-992084314
// It lets us expand all the stories in the sidebar by default

import { addons } from '@storybook/addons';
import { STORY_RENDERED } from '@storybook/core-events';

let hasExpanded = false;

addons.register('expand-all', (api) => {
  const emitter = addons.getChannel();

  emitter.on(STORY_RENDERED, () => {
    if (!hasExpanded) {
      setTimeout(api.expandAll); // Calling on the next tick after storyRendered seems to work reliably.
      hasExpanded = true;
    }
  });
});
