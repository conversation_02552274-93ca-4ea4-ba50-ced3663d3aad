{"name": "holodeck", "version": "0.0.1", "description": "Tribble Chrome Extension", "main": "webpack.config.js", "scripts": {"dev:chrome": "cross-env NODE_ENV=development TARGET=chrome webpack", "dev:firefox": "cross-env NODE_ENV=development TARGET=firefox webpack", "dev:opera": "cross-env NODE_ENV=development TARGET=opera webpack", "dev:edge": "cross-env NODE_ENV=development TARGET=edge webpack", "profile:chrome": "cross-env NODE_ENV=profile TARGET=chrome webpack", "profile:firefox": "cross-env NODE_ENV=profile TARGET=firefox webpack", "profile:opera": "cross-env NODE_ENV=profile TARGET=opera webpack", "profile:edge": "cross-env NODE_ENV=profile TARGET=edge webpack", "build:chrome": "cross-env NODE_ENV=production TARGET=chrome webpack", "build:chrome_prod": "cp src/_config/constants_auth.prod.ts src/utils/constants_auth.ts && cross-env NODE_ENV=production TARGET=chrome_prod webpack && cp src/_config/constants_auth.dev.ts src/utils/constants_auth.ts", "build:firefox": "cross-env NODE_ENV=production TARGET=firefox webpack", "build:opera": "cross-env NODE_ENV=production TARGET=opera webpack", "build:edge": "cross-env NODE_ENV=production TARGET=edge webpack", "upload:chrome": "cross-env NODE_ENV=upload TARGET=chrome webpack", "upload:firefox": "cross-env NODE_ENV=upload TARGET=firefox webpack", "upload:opera": "cross-env NODE_ENV=upload TARGET=opera webpack", "upload:edge": "cross-env NODE_ENV=upload TARGET=edge webpack", "build": "yarn run build:chrome && yarn run build:firefox && yarn run build:opera && yarn run build:edge", "upload": "yarn run upload:chrome && yarn run upload:firefox && yarn run upload:opera && yarn run upload:edge", "lint-fix": "eslint --ext js,jsx,ts,tsx, src --fix", "lint": "eslint --ext js,jsx,ts,tsx, src", "test": "jest", "tdd": "jest --watch", "storybook": "sb dev"}, "author": "Web Dev", "dependencies": {"@auth0/auth0-react": "^2.1.1", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.16", "@mui/material": "^5.14.17", "@mui/x-data-grid": "^6.18.1", "@mui/x-date-pickers": "^6.18.1", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^11.0.0", "core-js": "^3.29.0", "cross-env": "^7.0.3", "css-loader": "^6.7.3", "dayjs": "^1.11.10", "dayjs-plugin-utc": "^0.1.2", "dotenv": "^16.0.3", "html-webpack-plugin": "^5.5.0", "lodash.debounce": "^4.0.8", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-draggable": "^4.4.5", "react-hook-form": "^7.47.0", "react-rnd": "^10.4.1", "sass": "^1.58.3", "sass-loader": "^13.2.0", "style-loader": "^3.3.1", "swr": "^2.2.4", "terser-webpack-plugin": "^5.3.6", "ts-node": "^10.9.1", "ts-pattern": "^5.0.5", "url-join": "^5.0.0", "use-debounce": "^9.0.4", "uuid": "^9.0.0", "webextension-polyfill": "^0.10.0", "webpack": "^5.76.0", "webpack-bundle-analyzer": "^4.8.0", "webpack-ext-reloader-mv3": "^2.1.1", "webpack-extension-manifest-plugin": "^0.8.0", "zip-webpack-plugin": "^4.0.1", "zustand": "^4.4.3"}, "devDependencies": {"@babel/preset-env": "^7.23.2", "@babel/preset-react": "^7.22.15", "@babel/preset-typescript": "^7.23.2", "@storybook/addon-essentials": "^7.4.6", "@storybook/addon-interactions": "^7.4.6", "@storybook/addon-links": "^7.4.6", "@storybook/addon-onboarding": "^1.0.8", "@storybook/blocks": "^7.4.6", "@storybook/cli": "^7.4.6", "@storybook/react": "^7.4.6", "@storybook/react-webpack5": "^7.4.6", "@storybook/testing-library": "^0.2.2", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.0.0", "@types/chrome": "^0.0.246", "@types/inboxsdk": "^2.0.9", "@types/jest": "^29.5.5", "@types/jquery": "^3.5.16", "@types/lodash": "^4.14.191", "@types/lodash.debounce": "^4.0.7", "@types/node": "^18.14.4", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/react-router-dom": "^5.3.3", "@types/redux": "^3.6.31", "@types/uuid": "^9.0.2", "@types/webpack-bundle-analyzer": "^4.6.0", "@types/zip-webpack-plugin": "^3.0.3", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "eslint": "^8.35.0", "eslint-config-airbnb": "^18.2.1", "eslint-config-prettier": "^8.6.0", "eslint-plugin-babel": "^5.3.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.25.3", "eslint-plugin-react-hooks": "^4.6.0", "eslint-webpack-plugin": "^4.0.0", "file-loader": "^6.2.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "2.8.8", "prettier-plugin-organize-imports": "^3.2.4", "ts-jest": "^29.1.1", "ts-loader": "^9.4.2", "typescript": "^5.2.2", "webpack-cli": "^5.0.1"}, "license": "", "repository": {"type": "git", "url": "https://github.com/tribble-ai/holodeck"}}