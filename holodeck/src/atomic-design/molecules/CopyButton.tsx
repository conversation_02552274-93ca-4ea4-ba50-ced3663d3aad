import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import React, { FC } from 'react';
import { useRootStore } from '../../popup/state/useRootStore';
import { copyToClipboard } from '../../utils/helpers';
import { CircularIconButton } from './QuestionCard/CircularIconButton';

interface CopyProps {
  textToCopy: string;
  disableBorder?: boolean;
  size?: 'small' | 'medium' | 'large';
  color?: 'inherit' | 'primary' | 'secondary' | 'default' | 'error';
  disabled?: boolean;
}

export const CopyButton: FC<CopyProps> = (props) => {
  const rootStore = useRootStore();
  return (
    <CircularIconButton
      disabled={props.disabled}
      size={props.size || 'medium'}
      disableBorder={props.disableBorder}
      ariaLabel="Copy the answer to your clipboard"
      Icon={ContentCopyIcon}
      onClick={async () => {
        await copyToClipboard(props.textToCopy);
        await rootStore.setNotification({
          body: '',
          type: 'success',
          header: 'Copied',
          timeoutId: 500,
        });
      }}
      color={props.color}
    />
  );
};
