import { useTheme } from '@mui/material';
import React, { forwardRef } from 'react';
import { Chippy } from '../../atoms/Chips/BaseChip';
import { StyledTooltip } from '../../atoms/StyledTooltips';

interface ReviewedChipProps {
  reviewed: boolean;
  onUpdateReviewStatus: (reviewed: boolean) => void;
}

export const ReviewedChip = ({
  reviewed,
  onUpdateReviewStatus,
}: ReviewedChipProps) => {
  return (
    <StyledTooltip
      variant={reviewed ? 'primary' : 'secondary'}
      title={reviewed ? 'Mark as not reviewed' : 'Mark as reviewed'}
    >
      <Chip reviewed={reviewed} onUpdateReviewStatus={onUpdateReviewStatus} />
    </StyledTooltip>
  );
};

export const Chip = forwardRef<HTMLButtonElement, ReviewedChipProps>(
  ({ reviewed, onUpdateReviewStatus, ...props }, ref) => {
    const theme = useTheme();
    return (
      <button
        ref={ref}
        {...props}
        id="reviewed-chip"
        style={{
          border: 'none',
          background: 'none',
          cursor: 'pointer',
        }}
        onClick={() => {
          onUpdateReviewStatus(!reviewed);
        }}
      >
        <Chippy
          label={reviewed ? 'Reviewed' : 'Not Reviewed'}
          colorHex={
            reviewed ? theme.palette.success.main : theme.palette.grey[600]
          }
        />
      </button>
    );
  },
);
