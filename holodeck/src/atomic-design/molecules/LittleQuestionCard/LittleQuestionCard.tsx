import LanguageIcon from '@mui/icons-material/Language';
import {
  Avatar,
  Box,
  Card,
  Checkbox,
  Divider,
  FormControlLabel,
  LinearProgress,
  MenuItem,
  Stack,
  Typography,
  useTheme,
} from '@mui/material';
import React, { FC } from 'react';

import {
  ContentDetailState,
  QuestionDto,
} from '../../../popup/api/question.api';
import { processAnswerCitations } from '../../../popup/pages/HomePage/pages/ViewQuestionnaire/QuestionList/answerProcessing';
import { ConfidenceChip } from '../../atoms/Chips/ConfidenceChip';
import { QuestionStateChip } from '../../atoms/Chips/QuestionStateChip';
import { GreyTooltip, PrimaryTooltip } from '../../atoms/StyledTooltips';
import { TribbleAvatar } from '../../atoms/TribbleAvatar';

// Don't use fade text clamp anymore...people didn't like it.
// Instead, show up to 300 chars then ellipsis for overflow...
// import { FadingText } from '../../atoms/FadeClamp';
import { CopyButton } from '../CopyButton';

import { useGroupings } from '../../../popup/api/groupings.swr';
import { getGeneratedAnswer } from '../../../popup/pages/HomePage/pages/ViewQuestionnaire/QuestionList/answerProcessing';

import * as QA_UTILS from '../../../popup/utils/qa';
import { ReviewedChip } from './ReviewedChip';

type LittleQuestionProps = {
  question: QuestionDto;
  selected: boolean;
  selectMode: boolean;
  toggleSelected: (questionId: number) => void;
  justUpdatedId?: string | undefined;
  navigateToQuestion: (questionId: number) => void;
  updateQuestionStatus: (
    questionId: number,
    newState: ContentDetailState,
  ) => void;
};

export const LittleQuestionCard: FC<LittleQuestionProps> = (props) => {
  const theme = useTheme();

  const { question } = props;
  const { output_original, state } = question;
  const groupings = useGroupings();

  const removeCitations = true;

  const isProcessing = QA_UTILS.isProcessingAnswer(question);
  const isModified = QA_UTILS.isModifiedAnswer(question);

  const questionText = QA_UTILS.questionText(question);
  const generatedAnswer = getGeneratedAnswer(question);
  const translatedText = question.output_original?.translation?.translation;
  const translatedSourceText = processAnswerCitations(
    question.output_original?.translation?.source_text,
    removeCitations,
  );
  const shouldUseTranslated = QA_UTILS.shouldUseTranslatedAnswer(question);

  const processedAnswer = isProcessing
    ? '(question is being processed)'
    : shouldUseTranslated
      ? processAnswerCitations(translatedText, removeCitations)
      : isModified
        ? question.output_modified!.text
        : processAnswerCitations(generatedAnswer, removeCitations);

  const number = Number(question.index) + 1; //don't start at zero silly!
  const score = output_original?.confidence_score || 0;
  const couldNotAnswer = QA_UTILS.couldNotGenerateAnswer(question);

  const confidenceExplanation =
    question?.output_original?.confidence_score_explanation;

  let itemsToReview;
  if (question?.output_original?.items_to_review) {
    itemsToReview = Array.isArray(question?.output_original?.items_to_review)
      ? question?.output_original?.items_to_review.join('\n- ')
      : question?.output_original?.items_to_review;
  }

  // Show a fake interstitial spinner if the question was just updated.
  // Purely for UX as the screen does a refresh, so need to orient the user
  // back to where they just were.
  const wasJustUpdated =
    props.justUpdatedId && question!.id!.toString() == props.justUpdatedId;

  let groupingName = '';
  if (question.grouping_id) {
    const grouping = groupings.data?.find((g) => g.id == question.grouping_id);
    if (grouping) {
      groupingName = grouping.name;
    }
  }

  const handleCardClick = () => {
    // Don't allow nav'ing to question in select mode
    if (props.selectMode) {
      props.toggleSelected(question.id);
      return;
    }
    props.navigateToQuestion(question.id);
  };

  // Find "best" place to ellipsis (i.e. not mid-word): should be a space or period.
  // If not found, go until a max of 20 chars, then just ellipsis.
  const CARD_MAX_ANSWER_LENGTH_TO_SHOW = 350;
  function findEllipsisPoint(text: string, initialIndex: number) {
    const MAX_CHARS = 20;
    const punctuation = [' ', '.', ',', ':'];

    if (text.length < initialIndex + MAX_CHARS) {
      return text.length;
    }

    let index = initialIndex;
    while (index < initialIndex + MAX_CHARS) {
      if (punctuation.includes(text[index])) {
        return index;
      }
      index++;
    }

    // Worst case: just split mid-word.
    return initialIndex;
  }

  const Assignee = () => {
    return question.assignee_id &&
      (question.assignee_name || question.assignee_picture) ? (
      <Box
        sx={{
          width: '100%',
          display: 'flex',
          justifyContent: 'flex-end',
          mr: '6px',
        }}
      >
        {question.assignee_picture ? (
          <PrimaryTooltip
            title={`Question is assigned to ${question.assignee_name}`}
            enterDelay={250}
          >
            <Avatar
              src={question.assignee_picture}
              sx={{ height: 24, width: 24 }}
            >
              {question.assignee_name}
            </Avatar>
          </PrimaryTooltip>
        ) : (
          <PrimaryTooltip
            title={`Question is assigned to ${question.assignee_name}`}
            enterDelay={250}
          >
            <div>
              <TribbleAvatar
                username={question.assignee_name}
                height="24px"
                width="24px"
              />
            </div>
          </PrimaryTooltip>
        )}
      </Box>
    ) : null;
  };

  return (
    <Card
      variant="outlined"
      sx={{
        p: props.selectMode && props.selected ? '13px' : '14px',
        pt: props.selectMode ? '4px' : '14px',
        border:
          props.selectMode && props.selected
            ? `2px solid ${theme.palette.secondary.main}60`
            : `1px solid ${theme.palette.grey[400]}60`,
        cursor: props.selectMode ? 'pointer' : 'default',
      }}
      onClick={() => {
        if (props.selectMode) {
          props.toggleSelected(question.id);
        }
      }}
    >
      <Stack direction={'column'} gap={0.5}>
        {props.selectMode && (
          <Stack
            direction="column"
            gap={1}
            onClick={(e) => {
              handleCardClick();
              e.stopPropagation(); // prevents getting fired twice
            }}
          >
            <FormControlLabel
              style={{ pointerEvents: 'none' }}
              control={<Checkbox checked={props.selected} color="secondary" />}
              label={
                <Typography
                  variant="body2"
                  color="secondary"
                  sx={{ fontWeight: props.selected ? '600' : '400' }}
                >
                  {props.selected ? 'Selected' : 'Not Selected'}
                </Typography>
              }
            />
          </Stack>
        )}
        <Box sx={{ cursor: 'pointer' }} onClick={handleCardClick}>
          {groupingName != '' && (
            <Typography
              variant="body2"
              sx={{
                textTransform: 'uppercase',
                color: theme.palette.grey[500],
                mb: '4px',
                fontWeight: '300',
              }}
            >
              {groupingName}
            </Typography>
          )}

          <Typography variant="h6" sx={{ lineHeight: '20px' }}>
            <span
              style={{
                display: 'inline-block',
                lineHeight: '20px',
                color: theme.palette.grey[600],
              }}
            >
              {`#${number}`} &nbsp;
            </span>
            {`${questionText}`}
          </Typography>
        </Box>

        <Typography
          variant="body2"
          onClick={handleCardClick}
          sx={{
            cursor: 'pointer',
            whiteSpace: 'pre-line',
          }}
        >
          {processedAnswer.length <= CARD_MAX_ANSWER_LENGTH_TO_SHOW
            ? processedAnswer
            : processedAnswer.substring(
                0,
                findEllipsisPoint(
                  processedAnswer,
                  CARD_MAX_ANSWER_LENGTH_TO_SHOW,
                ),
              ) + '...'}
        </Typography>

        <Stack
          direction="row"
          justifyContent="flex-end"
          alignItems="center"
          sx={{ width: '100%' }}
        >
          {translatedText && shouldUseTranslated && (
            <GreyTooltip title={`Translated from:\n\n${translatedSourceText}`}>
              <LanguageIcon
                fontSize="small"
                sx={{ mr: 1, color: theme.palette.grey[500] }}
              />
            </GreyTooltip>
          )}
          {!props.selectMode && (
            <CopyButton
              size="small"
              disableBorder={true}
              textToCopy={processedAnswer}
            />
          )}
        </Stack>
      </Stack>

      <Divider sx={{ my: 1 }} />

      {!wasJustUpdated && (
        <Stack
          direction="row"
          alignItems="center"
          justifyContent="space-between"
          sx={{ mt: '1rem', mr: '-4px' }}
        >
          <Stack
            direction="row"
            alignItems="center"
            justifyContent="flex-start"
            gap={1}
          >
            {!isModified && (
              <ConfidenceChip
                score={score}
                couldNotAnswer={couldNotAnswer}
                explanation={
                  (confidenceExplanation
                    ? `EXPLANATION\n\n${confidenceExplanation}`
                    : '') +
                  (itemsToReview
                    ? `\n\nITEMS TO REVIEW\n\n- ${itemsToReview}`
                    : '')
                }
              />
            )}

            {!isProcessing ? (
              <ReviewedChip
                reviewed={state === 'accepted'}
                onUpdateReviewStatus={() => {
                  props.updateQuestionStatus(
                    question.id,
                    state === 'accepted' ? 'initial' : 'accepted',
                  );
                }}
              />
            ) : (
              <QuestionStateChip
                state={state}
                showToggle={!props.selectMode}
                question={question}
              />
            )}
          </Stack>

          {Assignee()}
        </Stack>
      )}
      {wasJustUpdated && <LinearProgress sx={{ mt: '1rem' }} />}
    </Card>
  );
};
