import {
  Autocomplete,
  Chip,
  CircularProgress,
  FormControl,
  ListItemText,
  Stack,
  TextField,
  useTheme,
} from '@mui/material';
import { FC, useRef } from 'react';
import { useDebouncedCallback } from 'use-debounce';

import React from 'react';
import { SalesforceOpportunityDto } from '../../popup/api/salesforce.api';
import { useSalesforceOpportunitiesSearch } from '../../popup/api/salesforce.swr';

interface Props {
  opportunities: SalesforceOpportunityDto[];
  selected: SalesforceOpportunityDto;
  onSelect: (opportunity: SalesforceOpportunityDto) => void;
  size?: 'small' | undefined;
  disabled?: boolean;
}
export const OpportunityPicker: FC<Props> = (props) => {
  const { opportunities, selected, onSelect } = props;

  const theme = useTheme();
  const selectRef = useRef(null);

  const [searchValue, setSearchValue] = React.useState('');

  const { data: searchResultsData, isLoading: isSearching } =
    useSalesforceOpportunitiesSearch(searchValue);

  const handleSearchTextInput = useDebouncedCallback(
    (event: React.SyntheticEvent<Element, Event>, newSearchValue: string) => {
      // onInputChange fires when the user select an item too.
      // So only set search value if the event type is 'change'
      // (vs. 'click')
      if (event?.type === 'change') {
        setSearchValue(newSearchValue);
      }
    },
    250,
  );

  const hasSearchResults = searchValue.length > 2 && !isSearching;
  let opptyOptions = hasSearchResults
    ? searchResultsData?.autoSuggestResults
    : opportunities;

  // If something was selected via a search, but isn't in the main
  // list of opptys (e.g. maybe it's not in the top 250), then add it
  if (
    !hasSearchResults &&
    selected &&
    !opptyOptions.find((o) => o.Id === selected.Id)
  ) {
    opptyOptions.push(selected);
  }

  opptyOptions?.sort((a, b) =>
    a.Name.toLowerCase().localeCompare(b.Name.toLowerCase()),
  );

  // Account for user searching for opptys, but there's a selcted item,
  // which isn't in the search results.
  if (hasSearchResults && selected) {
    opptyOptions = opptyOptions.filter((o) => o.Id !== selected.Id);
    opptyOptions.unshift(selected);
  }

  return (
    <FormControl sx={{ width: '100%' }}>
      <Autocomplete
        ref={selectRef}
        defaultValue={selected}
        size={props.size ? props.size : 'medium'}
        disabled={props.disabled === true}
        options={opptyOptions}
        clearOnBlur={false}
        onBlur={() => setSearchValue('')}
        getOptionLabel={(option) => searchValue || (option?.Name ?? '')}
        isOptionEqualToValue={(option, value) => option.Id === value.Id}
        filterOptions={(x) => x}
        onChange={(event, newValue) => {
          if (event?.type === 'click') {
            onSelect(newValue);
            setSearchValue('');
          }
        }}
        onInputChange={handleSearchTextInput}
        renderInput={(params) => (
          <TextField
            {...params}
            placeholder="Select an opportunity"
            InputProps={{
              ...params.InputProps,
              endAdornment: (
                <React.Fragment>
                  {isSearching ? (
                    <CircularProgress color="inherit" size={20} />
                  ) : null}
                  {params.InputProps.endAdornment}
                </React.Fragment>
              ),
            }}
            sx={{
              '& input::placeholder': {
                color: theme.palette.grey[500],
                opacity: 1,
              },
            }}
          />
        )}
        renderOption={(props, oppty: SalesforceOpportunityDto) => {
          const { ...optionProps } = props;
          return (
            <li
              {...optionProps}
              value={oppty.Id}
              key={oppty.Id}
              style={{ justifyContent: 'space-between' }}
            >
              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
                sx={{ width: '100%' }}
              >
                <Stack direction="column">
                  <ListItemText
                    primary={oppty.Name}
                    sx={{
                      '& .MuiTypography-root': {
                        fontSize: '0.85rem',
                        fontWeight: 500,
                      },
                    }}
                  />
                  <ListItemText
                    primary={`(Id: ${oppty.Id}, Amount: $${Number(
                      oppty.Amount,
                    ).toLocaleString()})`}
                    sx={{
                      '& .MuiTypography-root': {
                        fontSize: '0.75rem',
                        fontWeight: 300,
                        color: theme.palette.grey[600],
                      },
                    }}
                  />
                </Stack>
                {hasSearchResults && selected && oppty.Id === selected.Id && (
                  <Chip
                    label="Selected"
                    color="primary"
                    size="small"
                    variant="outlined"
                    sx={{
                      fontWeight: 500,
                    }}
                  />
                )}
              </Stack>
            </li>
          );
        }}
      />
    </FormControl>
  );
};
