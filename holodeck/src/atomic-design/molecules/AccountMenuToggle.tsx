import { <PERSON>, Button, MenuItem } from '@mui/material';
import React, { FC } from 'react';
import { MenuDropdown } from '../atoms/MenuDropdown';
import { TribbleAvatar } from '../atoms/TribbleAvatar';

type AccountMenuToggleProps = {
  username: string;
  showSettings: () => void;
  onLogout: () => void;
  onReload: () => void;
};

const Icon = () => (
  <svg
    width="12"
    height="10"
    viewBox="0 0 12 10"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M0.789474 0C0.35346 0 0 0.353459 0 0.789474C0 1.22549 0.353459 1.57895 0.789474 1.57895H11.2105C11.6465 1.57895 12 1.22549 12 0.789474C12 0.353459 11.6465 0 11.2105 0H0.789474ZM0 5C0 4.56399 0.35346 4.21053 0.789474 4.21053H11.2105C11.6465 4.21053 12 4.56399 12 5C12 5.43601 11.6465 5.78947 11.2105 5.78947H0.789474C0.353459 5.78947 0 5.43601 0 5ZM0 9.21053C0 8.77451 0.35346 8.42105 0.789474 8.42105H11.2105C11.6465 8.42105 12 8.77451 12 9.21053C12 9.64654 11.6465 10 11.2105 10H0.789474C0.353459 10 0 9.64654 0 9.21053Z"
      fill="#00091A"
    />
  </svg>
);

export const AccountMenuToggle: FC<AccountMenuToggleProps> = (props) => {
  const handleLogout = (handleClose: () => void) => {
    handleClose();
    props.onLogout();
  };

  const handleSettings = (handleClose: () => void) => {
    handleClose();
    props.showSettings();
  };

  return (
    <MenuDropdown
      trigger={
        <Button color="inherit" variant="contained" disableElevation>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-around',
              gap: '0.38rem',
            }}
          >
            <Icon />
            <TribbleAvatar username={props.username} />
          </Box>
        </Button>
      }
    >
      {(handleClose) => [
        <MenuItem key="menu-reload" onClick={props.onReload} disableRipple>
          Reload
        </MenuItem>,
        <MenuItem
          key="menu-settings"
          onClick={() => handleSettings(handleClose)}
          disableRipple
        >
          Settings
        </MenuItem>,
        <MenuItem
          key="menu-logout"
          onClick={() => handleLogout(handleClose)}
          disableRipple
        >
          Logout
        </MenuItem>,
      ]}
    </MenuDropdown>
  );
};
