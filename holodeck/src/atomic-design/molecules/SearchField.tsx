import CancelSearchIcon from '@mui/icons-material/Close';
import SearchIcon from '@mui/icons-material/Search';
import {
  FormControl,
  IconButton,
  InputAdornment,
  OutlinedInput,
  OutlinedInputProps,
} from '@mui/material';
import React, { FC, useRef } from 'react';

interface SFProps {
  handleChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => void;
  searchQuery: string;
  handleClearSearch: (
    e: React.MouseEvent<HTMLButtonElement, MouseEvent>,
  ) => void;
  extraProps?: OutlinedInputProps;
}

export const SearchField: FC<SFProps> = (props) => {
  const { handleChange, handleClearSearch, searchQuery, extraProps } = props;

  const inputRef = useRef<HTMLElement>(null);

  return (
    <FormControl sx={{ flexGrow: 1 }}>
      <OutlinedInput
        {...extraProps}
        size="small"
        placeholder="Search..."
        value={searchQuery || ''}
        onChange={(e) => handleChange(e)}
        ref={inputRef}
        startAdornment={
          <InputAdornment position="start" sx={{ mr: 0 }}>
            <IconButton
              edge="start"
              disableRipple
              sx={{
                '&:hover': { backgroundColor: 'inherit' },
                cursor: 'default',
              }}
              onClick={() => inputRef.current?.click()}
            >
              <SearchIcon />
            </IconButton>
          </InputAdornment>
        }
        endAdornment={
          <InputAdornment position="end">
            <IconButton
              aria-label="clear search query"
              onClick={handleClearSearch}
              edge="end"
            >
              {searchQuery && <CancelSearchIcon />}
            </IconButton>
          </InputAdornment>
        }
      />
    </FormControl>
  );
};
