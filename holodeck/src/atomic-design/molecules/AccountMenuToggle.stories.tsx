import type { Meta, StoryObj } from '@storybook/react';
import { AccountMenuToggle } from './AccountMenuToggle';

const meta = {
  title: 'Holodeck/Molecules/AccountMenuToggle',
  component: AccountMenuToggle,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/react/configure/story-layout
    layout: 'centered',
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/react/writing-docs/autodocs
  tags: ['autodocs'],
} satisfies Meta<typeof AccountMenuToggle>;
export default meta;

type Story = StoryObj<typeof meta>;

export const Simple: Story = {
  args: {
    username: '<PERSON>',
    onLogout: () => {},
  },
};
