import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import { Fab, useTheme } from '@mui/material';
import React, { FC, useEffect, useState } from 'react';

interface FabProps {
  divHeight?: string;
  postScroll?: () => void;
  onToggle?: (showing: boolean) => void;
}
export const FabScrollUp: FC<FabProps> = (props) => {
  const [showFab, setShowFab] = useState(false);
  const theme = useTheme();

  const handleScroll = () => {
    const currentScrollPos = window.scrollY;
    if (currentScrollPos > 300) {
      setShowFab(true);
      if (props.onToggle) {
        props.onToggle(true);
      }
    } else {
      setShowFab(false);
      if (props.onToggle) {
        props.onToggle(false);
      }
    }
  };

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });

    if (props.postScroll) {
      props.postScroll();
    }
  };

  useEffect(() => {
    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <div style={{ height: props.divHeight }}>
      {showFab && (
        <Fab
          size="large"
          color="primary"
          aria-label="scroll-up"
          style={{
            position: 'fixed',
            bottom: '16px',
            right: '16px',
            backgroundColor: theme.palette.primary.main,
          }}
          onClick={scrollToTop}
        >
          <KeyboardArrowUpIcon />
        </Fab>
      )}
    </div>
  );
};
