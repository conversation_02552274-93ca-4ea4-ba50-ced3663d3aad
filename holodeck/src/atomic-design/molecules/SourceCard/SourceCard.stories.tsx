import type { Meta, StoryObj } from '@storybook/react';
import { SourceCard } from './SourceCard';

const meta = {
  title: 'Holodeck/Molecules/SourceCard',
  component: SourceCard,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/react/configure/story-layout
    layout: 'centered',
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/react/writing-docs/autodocs
  tags: ['autodocs'],
} satisfies Meta<typeof SourceCard>;
export default meta;

type Story = StoryObj<typeof meta>;

export const Simple: Story = {
  args: {
    type: 'RFP',
    source: {
      document_id: 1,
      embedding_id: 1,
      file_name: 'Source file 1.pdf',
      metadata: {
        page_numbers: [1, 3],
        privacy: 'public',
      },
      text: 'SENSOR - VIRTUAL MODELS: [TABLE SUMMARY: The table provides a detailed comparison of four models: NS-25-DR, NS-100-SEL, NS-220-E, and NS-520-E. It includes 22 rows and 5 columns, covering various aspects such as rugged/industrial rating, estimated max monitored assets, max concurrent flow processing, max sustained monitoring bandwidth, monitoring interfaces, management interfaces, local console, remote management, form factor, power supply, operating temperature range, dimensions, weight, certifications, TAA compliance, and warranty period.] ',
      similarity_score: 0.99,
      uuid: '123223',
      context_index: 0,
    },
  },
};
