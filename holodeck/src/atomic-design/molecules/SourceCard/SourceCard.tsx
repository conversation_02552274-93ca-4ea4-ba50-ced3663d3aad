import {
  ContactPhone,
  Description,
  PublicOutlined,
  TableRowsOutlined,
} from '@mui/icons-material';
import { Box, Tooltip, Typography, useTheme } from '@mui/material';
import React, { FC } from 'react';
import * as constants from '../../../utils/constants';
import { ContentSourceDoc, ContentSourceQA } from '../../../utils/model';
import { FadingText } from '../../atoms/FadeClamp';

interface SourceCardParams {
  source: ContentSourceDoc | ContentSourceQA;
  type: 'DOC' | 'RFP';
  disableFade?: boolean;
  hideIcon?: boolean;
}
export const SourceCard: FC<SourceCardParams> = (params) => {
  const theme = useTheme();
  const { type, disableFade, hideIcon } = params;

  let source =
    type == 'DOC'
      ? (params.source as ContentSourceDoc)
      : (params.source as ContentSourceQA);

  const sourceText = source.text.replace('// ANSWER: ', '\n\nANSWER: ');
  const sourceDate = 'date' in source.metadata ? source.metadata['date'] : '';

  const sourceIsSlack =
    (source.document_type ?? '') === constants.DOCUMENT_TYPE_SLACK;

  const sourceIsCallTranscript =
    source?.document_type === constants.DOCUMENT_TYPE_CALL_TRANSCRIPT;

  const sourceHasUrl = source.file_name.startsWith('https://');

  const Icon = () => {
    if (source?.file_name?.startsWith('https://')) {
      return (
        <PublicOutlined sx={{ fontSize: '24px', mr: 1.5 }} color="primary" />
      );
    } else if (sourceIsCallTranscript) {
      return <ContactPhone sx={{ fontSize: '24px', mr: 1.5 }} />;
    } else if (source?.type?.toUpperCase() === 'DOC') {
      return <Description sx={{ fontSize: '24px', mr: 1.5 }} />;
    } else {
      return <TableRowsOutlined sx={{ fontSize: '24px', mr: 1.5 }} />;
    }
  };

  let sourceTitle = source.document_label || source.file_name;
  if (hideIcon) {
    sourceTitle = 'Source: ' + sourceTitle;
  } else if (sourceIsSlack) {
    sourceTitle =
      'Slack Channel: #' +
      sourceTitle +
      (sourceDate ? `\n(Date: ${sourceDate})` : '\n(date unknown)');
  }

  const title = sourceHasUrl ? (
    <Tooltip title={source.file_name}>
      <Typography
        variant="body2"
        sx={{
          fontSize: '0.9rem',
          lineHeight: 1.5,
          fontWeight: '600',
          wordBreak: 'break-word',
          whiteSpace: 'pre-wrap',
          '&:hover': {
            textDecoration: 'underline',
            cursor: 'pointer',
          },
        }}
        color="primary"
        onClick={() => window.open(source.file_name, '_blank')}
      >
        {sourceTitle}
      </Typography>
    </Tooltip>
  ) : (
    <Typography
      variant="body2"
      sx={{
        fontSize: '0.9rem',
        lineHeight: 1.5,
        fontWeight: '600',
        color: theme.palette.grey[800],
        wordBreak: 'break-word',
        whiteSpace: 'pre-wrap',
      }}
    >
      {sourceTitle}
    </Typography>
  );

  return (
    <Box id="container" sx={{ width: '100%' }}>
      <Box
        id="header"
        sx={{
          ml: sourceIsSlack ? '0px' : '-2px',
          mb: hideIcon ? 2 : 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'flex-start',
        }}
      >
        {!hideIcon && !sourceIsSlack && Icon()}
        {title}
      </Box>
      <Box>
        {disableFade ? (
          <Typography
            variant="body2"
            sx={{
              cursor: 'pointer',
              whiteSpace: 'pre-wrap',
              pt: 1,
            }}
          >
            {sourceIsCallTranscript && (
              <>
                <span
                  style={{
                    fontWeight: 'bold',
                    color: theme.palette.grey[500],
                  }}
                >
                  CALL TRANSCRIPT
                </span>
                <br />
                <br />
              </>
            )}
            {sourceText}
          </Typography>
        ) : (
          <FadingText
            variant="body2"
            text={sourceText}
            numLines={5}
            showLineBreaks={type == 'RFP'}
          />
        )}
      </Box>
    </Box>
  );
};
