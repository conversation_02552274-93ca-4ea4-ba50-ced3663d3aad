import {
  Avatar,
  Chip,
  FormControl,
  InputLabel,
  ListItemText,
  MenuItem,
} from '@mui/material';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import React, { FC, useRef } from 'react';
import { UserDto } from '../../popup/api/user.api';
import { TribbleAvatar } from '../atoms/TribbleAvatar';

interface UserPickListProps {
  multiple: boolean;
  updateSelectedUsers: (id: string | string[]) => void;
  selectedUser?: string;
  selectedUsers?: string[];
  availableUsers: UserDto[];
  label?: string;
  includeUnassign?: boolean;
}

export const UserPickList: FC<UserPickListProps> = (props) => {
  return (
    <FormControl fullWidth size="small" sx={{ mt: '1rem', mb: '2rem' }}>
      <InputLabel id="user-select-small-label">
        {props.label ?? 'Assignee'}
      </InputLabel>
      <Select
        labelId="user-select-small-label"
        label={props.label ?? 'Assignee'}
        multiple={props.multiple}
        value={props.multiple ? props.selectedUsers : props.selectedUser}
        onChange={(e: SelectChangeEvent) =>
          props.updateSelectedUsers(e.target.value)
        }
        renderValue={(selected) => {
          if (Number(selected) == -1) {
            return '(unassign)';
          }

          const selectedUser = props.availableUsers.find(
            (user) => String(user.id) == selected,
          );

          return selectedUser ? (
            <Chip
              color="primary"
              variant="outlined"
              label={selectedUser?.name}
              onDelete={() => props.updateSelectedUsers(selected)}
              onMouseDown={(e) => e.stopPropagation()}
              sx={{ my: '4px' }}
            />
          ) : null;
        }}
        MenuProps={{
          sx: {
            maxHeight: '400px',
          },
          anchorOrigin: {
            vertical: 'bottom',
            horizontal: 'left',
          },
        }}
      >
        {props.includeUnassign && (
          <MenuItem value={-1} key="userlist_unassign">
            <ListItemText primary="(unassign)" />
          </MenuItem>
        )}
        {props.availableUsers
          .sort((a, b) =>
            a.name.toLowerCase() < b.name.toLowerCase() ? -1 : 1,
          )
          .map((user) => (
            <MenuItem value={user.id} key={user.id}>
              <div style={{ marginRight: '1rem' }}>
                {user.picture && user.picture != '' && (
                  <Avatar src={user.picture} sx={{ height: 24, width: 24 }}>
                    {user.name}
                  </Avatar>
                )}
                {(!user.picture || user.picture == '') && (
                  <TribbleAvatar
                    username={user.name}
                    height="24px"
                    width="24px"
                  />
                )}
              </div>
              <ListItemText
                primary={user.name}
                sx={{ '& .MuiTypography-root': { fontSize: '0.85rem' } }}
              />
            </MenuItem>
          ))}
      </Select>
    </FormControl>
  );
};
