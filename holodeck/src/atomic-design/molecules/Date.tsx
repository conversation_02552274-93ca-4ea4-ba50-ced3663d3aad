import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import dayjs, { Dayjs } from 'dayjs';
import utc from 'dayjs/plugin/utc';
dayjs.extend(utc);

import { CalendarToday } from '@mui/icons-material';
import { Stack, Typography, useTheme } from '@mui/material';
import React, { FC } from 'react';

import {
  displayUTCDateWithDefaultLocale,
  processUTCDate,
} from '../../popup/utils/dateTime';

interface DateProps {
  date: Date;
  mode: 'view' | 'edit';
  onChange: (date: Date | null) => void;
}
export const DueDate: FC<DateProps> = (props) => {
  const theme = useTheme();
  const { mode, date } = props;

  let dateText = '(not specified)';
  let dateColor = theme.palette.grey[500];
  let dueDate = date;

  if (dueDate !== undefined && dueDate != null) {
    const today = new Date();
    dateText = displayUTCDateWithDefaultLocale(processUTCDate(dueDate));

    if (dueDate < today) {
      dateColor = theme.palette.error.main;
    }
  }

  if (mode == 'view') {
    return (
      <Stack direction="row" alignItems="center">
        <CalendarToday
          fontSize="small"
          sx={{ mr: '4px', color: theme.palette.grey[500] }}
        />
        <Typography variant="body2" sx={{ mr: '4px', fontWeight: '300' }}>
          Due:
        </Typography>
        <Typography variant="body2" sx={{ color: dateColor }}>
          {dateText}
        </Typography>
      </Stack>
    );
  }
  if (mode == 'edit') {
    // <DatePicker value={dayJs(field.value)} onChange={(newValue: Dayjs) => field.onChange(formatDate(newValue.toDate()))} />;

    return (
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <DatePicker
          label="Due Date"
          value={dayjs.utc(date)}
          onChange={(newValue: Dayjs) => props.onChange(newValue.toDate())}
          sx={{ width: '100%' }}
        />
      </LocalizationProvider>
    );
  }
};
