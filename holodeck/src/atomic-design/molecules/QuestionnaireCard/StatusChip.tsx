import { Chip, useTheme } from '@mui/material';
import React, { FC } from 'react';
import { RfpStatusValue } from '../../../popup/api/questionnaires.api';
import { PrettyToggleButtonGroup } from '../PrettyToggleButtonGroup';

type StatusChipProps = {
  jobStatus: RfpStatusValue;
  overrideLabel?: string;
  mode?: 'edit' | 'view';
  onChange?: (newStatus: RfpStatusValue) => void;
  showToggle?: boolean;
};
export const StatusChip: FC<StatusChipProps> = ({
  jobStatus,
  overrideLabel,
  mode,
  onChange,
  showToggle,
}) => {
  const theme = useTheme();

  if (mode == 'edit') {
    const buttons = [
      { value: 'in_review', label: 'Active' },
      { value: 'finished', label: 'Finished' },
    ];

    return (
      <PrettyToggleButtonGroup
        buttons={buttons}
        onChange={(e) => {
          onChange(e.target.value);
        }}
        value={jobStatus}
      />
    );
  }

  const toggleSuffix = showToggle ? ' ▾' : '';

  // For the warning chips
  const lowerCaseLabelMap = {
    analyzing: 'Analyzing',
    generating: 'Generating',
    generating_response: 'Generating Response',
    generating_outline: 'Generating Outline',
    in_review: 'In Review',
    in_review_response: 'In Review Response',
    in_review_outline: 'In Review Outline',
    spreadsheet_confirm_analysis: 'Spreadsheet Confirm Analysis',
    pending: 'Pending',
  };

  switch (jobStatus) {
    case 'error':
      return (
        <Chip
          label="Error"
          color="error"
          sx={{ borderRadius: '8px', height: '24px', color: 'white' }}
        />
      );
    case 'in_review_response':
    case 'in_review':
      return (
        <Chip
          label={`Active${toggleSuffix}`}
          color="primary"
          sx={{ borderRadius: '8px', height: '24px' }}
        />
      );
    case 'finished':
      return (
        <Chip
          label="Finished"
          color="success"
          sx={{ borderRadius: '8px', height: '24px' }}
        />
      );
    case 'generating':
    case 'generating_outline':
    case 'generating_response':
      return (
        <Chip
          label={overrideLabel ?? lowerCaseLabelMap[jobStatus]}
          color="warning"
          variant="outlined"
          sx={{
            borderRadius: '8px',
            height: '24px',
            '& .MuiChip-label': { color: theme.palette.warning.main },
          }}
        />
      );
    case 'spreadsheet_confirm_analysis':
    case 'in_review_outline':
      return (
        <Chip
          label={overrideLabel ?? lowerCaseLabelMap[jobStatus]}
          color="error"
          variant="outlined"
          sx={{
            borderRadius: '8px',
            height: '24px',
            '& .MuiChip-label': { color: theme.palette.error.main },
          }}
        />
      );
    default:
      return (
        <Chip
          label={jobStatus}
          color="primary"
          sx={{ borderRadius: '8px', height: '24px' }}
        />
      );
  }
};
