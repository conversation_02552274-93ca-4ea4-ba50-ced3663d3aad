import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import {
  Avatar,
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Divider,
  MenuItem,
  Stack,
  Typography,
  useTheme,
} from '@mui/material';
import React, { FC, useEffect, useState } from 'react';

import {
  patchQuestionnaire,
  PatchQuestionnaireDto,
  QuestionnaireDto,
  RfpStatusValue,
} from '../../../popup/api/questionnaires.api';
import { UserDto } from '../../../popup/api/user.api';
import { useUsers } from '../../../popup/api/user.swr';
import { getEnv } from '../../../popup/state/auth.storage';
import { useRootStore } from '../../../popup/state/useRootStore';
import {
  displayUTCDateWithDefaultLocale,
  processUTCDate,
} from '../../../popup/utils/dateTime';
import { TRIBBLE_APP_URL } from '../../../utils/constants_auth';
import { convertTimestampToDate } from '../../../utils/helpers';
import { MenuDropdown } from '../../atoms/MenuDropdown';
import { PrimaryTooltip } from '../../atoms/StyledTooltips';
import { TribbleAvatar } from '../../atoms/TribbleAvatar';
import { StatusChip } from './StatusChip';

export const QUESTIONNAIRE_ERROR_MAP = {
  NO_QUESTIONS: 'No questions were found during analysis',
};

export const isQuestionnaireAnalyzing = (questionnaire: QuestionnaireDto) => {
  return (
    questionnaire.project_content_status.startsWith('analyzing') ||
    questionnaire.project_content_status.startsWith('generating')
  );
};

export const isQuestionnairePendingXLSXAnalysisConfirmation = (
  questionnaire: QuestionnaireDto,
) => {
  return (
    questionnaire.project_content_status === 'spreadsheet_confirm_analysis'
  );
};

export const questionnaireHasError = (questionnaire: QuestionnaireDto) => {
  return (
    questionnaire.details?.agentParams?.error?.length > 0 ||
    questionnaire.project_content_status === 'error'
  );
};

export const getQuestionnaireCreationDate = (
  questionnaire: QuestionnaireDto,
) => {
  let createdDate;
  if (questionnaire.created_date) {
    createdDate = new Date(questionnaire.created_date);
  } else if (questionnaire.details.agentParams?.thread) {
    // If can't find created_date, then see if we can find via the agentParams timestamp
    const thread_ts = String(questionnaire.details.agentParams.thread).split(
      '.',
    )[0];
    createdDate = convertTimestampToDate(Number(thread_ts));
  }
  return createdDate;
};

// This is only for E2E Spreadsheet tasks
export const isQuestionnaireInErrorState = (
  questionnaire: QuestionnaireDto,
) => {
  // const createdDate = getQuestionnaireCreationDate(questionnaire);
  // const isAnalyzing = isQuestionnaireAnalyzing(questionnaire);

  if (questionnaireHasError(questionnaire)) {
    return true;
    // Don't do this time check anymore (for now...)
    // } else if (createdDate !== undefined && createdDate != null) {
    //   const today = new Date();
    //   if (createdDate < today) {
    //     // If it's been more than 2 days since the RFP was created, it's probably an error
    //     if (
    //       isAnalyzing &&
    //       today.getTime() - createdDate.getTime() > MILLIS_TWO_DAYS
    //     ) {
    //       return true;
    //     }
    //   }
  }
  return false;
};

type QuestionnaireCardProps = {
  questionnaire: QuestionnaireDto;
  onUpdate?: (questionnaire: QuestionnaireDto) => void;
  toggleViewError: (questionnaire: QuestionnaireDto, error: string) => void;
};
export const QuestionnaireCard: FC<QuestionnaireCardProps> = ({
  questionnaire,
  onUpdate,
  toggleViewError,
}) => {
  const { navigate, setNotification } = useRootStore();
  const users = useUsers();
  const theme = useTheme();

  const [availableUsers, setAvailableUsers] = useState<UserDto[]>([]);

  useEffect(() => {
    if (users.data) {
      setAvailableUsers(users.data);
    }
  }, [users.data]);

  if (!questionnaire) return <></>;

  let dueDateText = '(not specified)';
  let dueDateColor = theme.palette.grey[500];
  let dueDate = questionnaire.due_date;

  if (dueDate !== undefined && dueDate != null) {
    const today = new Date();
    dueDateText = displayUTCDateWithDefaultLocale(processUTCDate(dueDate));

    if (dueDate < today) {
      dueDateColor = theme.palette.error.main;
    }
  }

  const owner = questionnaire.owner;
  const ownerUser = owner
    ? availableUsers.find((user) => user.id == owner.id)
    : null;
  const ownerAvatar = ownerUser ? (
    <Box sx={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>
      {ownerUser?.picture && ownerUser.picture != '' ? (
        <PrimaryTooltip
          title={`Sub-Project Owner: ${ownerUser.name}`}
          enterDelay={250}
        >
          <Avatar src={ownerUser.picture} sx={{ height: 24, width: 24 }}>
            {ownerUser.name}
          </Avatar>
        </PrimaryTooltip>
      ) : (
        <PrimaryTooltip
          title={`Sub-Project Owner: ${ownerUser.name}`}
          enterDelay={250}
        >
          <div>
            <TribbleAvatar
              username={ownerUser.name}
              height="24px"
              width="24px"
            />
          </div>
        </PrimaryTooltip>
      )}
    </Box>
  ) : (
    <></>
  );

  const updateQuestionnaireStatus = async (status: RfpStatusValue) => {
    const details: PatchQuestionnaireDto = {
      content_id: questionnaire.content_id,
      document_id: questionnaire.document_id,
      job_id: questionnaire.job_id,
      label: questionnaire.label || questionnaire.file_name,
      status,
      details: questionnaire.details,
      due_date: questionnaire.due_date,
      assignees: questionnaire.assignees,
      metadata_filter: questionnaire.metadata_filter,
      opportunity_id: questionnaire.opportunity_id,
    };
    const result = await patchQuestionnaire(
      details,
      questionnaire.rfx_content_id,
    );

    if (result.success) {
      onUpdate({ ...questionnaire, project_content_status: status });
      setNotification({
        type: 'success',
        header: 'Ok',
        body: 'Questionnaire updated.',
      });
    } else {
      setNotification({
        type: 'error',
        header: 'Oops',
        body: 'Something went wrong.',
      });
    }
  };

  const statusesToFinishInWebApp = [
    'spreadsheet_confirm_analysis',
    'in_review_outline',
    'in_review_response',
  ];

  const handleCardClick = async () => {
    const goToWebAppToFinishAnalysis = statusesToFinishInWebApp.includes(
      questionnaire.project_content_status,
    );

    if (goToWebAppToFinishAnalysis) {
      const env = await getEnv();
      const appUrl = TRIBBLE_APP_URL[env ?? 'localhost'];
      window.open(
        `${appUrl}/projects?forward_to=${questionnaire.rfx_content_id}`,
        '_blank',
      );

      return;
    }

    // Questionnaire to be reviewed in the extension
    if (questionnaire.project_content_status === 'in_review') {
      navigate({
        name: 'viewQuestionnaire',
        id: questionnaire.rfx_content_id,
      });
    }

    if (!!questionnaire.job_id && !!questionnaire.document_id) {
      navigate({
        name: 'viewQuestionnaire',
        id: questionnaire.rfx_content_id,
      });
    }
  };

  const numQuestionsReviewed = Number(questionnaire.num_questions_accepted);
  const numQuestions = Number(questionnaire.num_questions);

  const isPreProcessing = ['new', 'analyzing'].includes(
    questionnaire.project_content_status,
  );

  const showPointer = [
    ...statusesToFinishInWebApp,
    'in_review',
    'finished',
  ].includes(questionnaire.project_content_status);

  const isActive =
    !isPreProcessing &&
    ['in_review'].includes(questionnaire.project_content_status);
  const isReviewed =
    !isNaN(numQuestions) &&
    numQuestions > 0 &&
    numQuestionsReviewed == numQuestions;

  const isAnalyzing = isQuestionnaireAnalyzing(questionnaire);
  const probablyError = isQuestionnaireInErrorState(questionnaire);
  const questionnaireError = questionnaire.details?.agentParams?.error;

  return (
    <Card
      variant="outlined"
      sx={{
        maxWidth: '100%',
        border: `1px solid ${
          isPreProcessing
            ? isQuestionnairePendingXLSXAnalysisConfirmation(questionnaire)
              ? theme.palette.error.main
              : theme.palette.warning.main
            : theme.palette.grey[400] + '60'
        }`,
        cursor: showPointer ? 'pointer' : 'default',
        backgroundColor: 'white',
        '&:hover': {},
      }}
      onClick={() => {
        handleCardClick();
      }}
    >
      <CardContent
        sx={{ display: 'flex', flexDirection: 'column', pb: '1rem' }}
      >
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: '1rem',
          }}
        >
          {isPreProcessing ? (
            <Stack direction="row" gap="10px" alignItems="center">
              <StatusChip
                jobStatus={questionnaire.project_content_status}
                overrideLabel={questionnaire.project_content_status_label}
              />

              {isAnalyzing && !probablyError && (
                <CircularProgress size="18px" color="warning" />
              )}
            </Stack>
          ) : isActive ? (
            <MenuDropdown
              trigger={
                <StatusChip
                  jobStatus={questionnaire.project_content_status}
                  showToggle={true}
                />
              }
              disableTrigger={!isActive}
              preventDefault={true}
            >
              {(handleClose) => {
                const menuItems = [
                  <MenuItem
                    disabled={true}
                    key="status_empty"
                    sx={{
                      minHeight: '24px',
                      p: '4px 12px',
                      fontSize: '0.75rem',
                      textTransform: 'uppercase',
                      color: theme.palette.grey[900],
                    }}
                  >
                    Change Status
                  </MenuItem>,
                  <MenuItem
                    onClick={async (event: React.MouseEvent<HTMLElement>) => {
                      try {
                        event.stopPropagation();
                        event.preventDefault();
                      } catch (err) {
                        // no-op
                      }
                      await updateQuestionnaireStatus('finished');
                      handleClose();
                    }}
                    autoFocus={false}
                    key={`status_finished`}
                    sx={{
                      minHeight: '24px',
                      p: '4px 12px',
                      fontSize: '0.85rem',
                      '&:hover': { fontWeight: 500 },
                    }}
                  >
                    Mark as Finished
                  </MenuItem>,
                ];

                return menuItems;
              }}
            </MenuDropdown>
          ) : (
            <StatusChip jobStatus={questionnaire.project_content_status} />
          )}

          <Stack direction={'row'}>
            <Typography
              variant="body2"
              sx={{
                mr: 1,
                color: isReviewed
                  ? theme.palette.success.main
                  : theme.palette.grey[500],
              }}
            >
              {isReviewed
                ? 'Reviewed'
                : numQuestions == 0
                  ? 'Not Started'
                  : isNaN(numQuestions)
                    ? ''
                    : 'Reviewed'}
            </Typography>

            {numQuestions > 0 && (
              <Typography
                variant="body2"
                sx={{
                  fontWeight: '600',
                  color: isReviewed
                    ? theme.palette.success.main
                    : theme.palette.common.black,
                }}
              >
                {numQuestionsReviewed} / {questionnaire.num_questions}
              </Typography>
            )}
          </Stack>
        </Box>

        <Typography
          variant="h6"
          sx={{
            fontWeight: '600',
          }}
        >
          {questionnaire.label ?? questionnaire.file_name ?? 'Untitled'}
        </Typography>

        <Typography variant="subtitle2" color="textSecondary">
          {questionnaire.details?.customer_name || ''}
        </Typography>

        {isAnalyzing && probablyError && (
          <Stack
            direction="column"
            alignItems="center"
            sx={{
              width: '100%',
              mr: '-8px',
              my: 1,
              p: 2,
              backgroundColor: theme.palette.error.main + '20',
              borderRadius: '8px',
            }}
          >
            <Typography
              variant="body2"
              sx={{ mb: 1, color: theme.palette.error.main, fontWeight: 500 }}
            >
              ⚠️ Something may have gone wrong.
            </Typography>

            <Button
              onClick={() =>
                toggleViewError(
                  questionnaire,
                  questionnaireError &&
                    QUESTIONNAIRE_ERROR_MAP[questionnaireError]
                    ? `Error: ${QUESTIONNAIRE_ERROR_MAP[questionnaireError]}`
                    : 'Error: questionnaire is taking too long to analyze.',
                )
              }
              variant="outlined"
              sx={{
                fontSize: '12px',
                width: 'fit-content',
                borderRadius: 1,
                textTransform: 'uppercase',
                mr: '-8px',
              }}
              color="error"
            >
              Review
            </Button>
          </Stack>
        )}

        <Divider sx={{ my: '1rem' }} />

        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Stack direction="row" alignItems="center">
            <CalendarTodayIcon
              fontSize="small"
              sx={{ mr: '4px', color: theme.palette.grey[500] }}
            />
            <Typography variant="body2" sx={{ mr: '4px', fontWeight: '300' }}>
              Due:
            </Typography>
            <Typography variant="body2" sx={{ color: dueDateColor }}>
              {dueDateText}
            </Typography>
          </Stack>

          <div style={{ display: 'flex', gap: '0px' }}>{ownerAvatar}</div>
        </div>
      </CardContent>
    </Card>
  );
};

export default QuestionnaireCard;
