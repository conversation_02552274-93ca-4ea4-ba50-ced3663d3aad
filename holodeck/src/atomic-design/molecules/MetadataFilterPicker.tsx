import {
  Autocomplete,
  Checkbox,
  FormControl,
  ListItemText,
  ListSubheader,
  TextField,
  useTheme,
} from '@mui/material';
import { FC, useRef } from 'react';

import React from 'react';
import {
  MetadataFilterValueDto,
  getMappedMetdataFilterValues,
} from '../../popup/api/metadata.api';
import { useMetadataFilters } from '../../popup/api/metadata.swr';

interface Props {
  selections: MetadataFilterValueDto[];
  onSelect: (selections: number[]) => void;
  size?: 'small' | undefined;
  hideInactive?: boolean;
  hideVerbatim?: boolean;
  disabled?: boolean;
}
export const MetadataFilterPicker: FC<Props> = (props) => {
  const theme = useTheme();

  const metadataFilters = useMetadataFilters();
  const metadataFilterValuesById = getMappedMetdataFilterValues(
    metadataFilters.data.metadata_filters.filter(
      (mdft) =>
        !props.hideVerbatim || (props.hideVerbatim && !mdft.is_verbatim),
    ),
  );

  const selectRef = useRef(null);
  const options = Object.values(metadataFilterValuesById).filter((mdf) => {
    if (props.hideInactive) {
      return mdf.is_active;
    } else {
      return true;
    }
  });

  const handleChange = (
    event: React.SyntheticEvent,
    value: MetadataFilterValueDto[],
  ) => {
    props.onSelect(value.map((v) => v.id));
  };

  return (
    <FormControl sx={{ width: '100%' }}>
      <Autocomplete
        ref={selectRef}
        size={props.size ? props.size : 'medium'}
        multiple
        disabled={props.disabled === true}
        disableCloseOnSelect
        disableClearable
        disableListWrap
        options={options.sort((a, b) => {
          if ((a.is_active && b.is_active) || (!a.is_active && !b.is_active)) {
            if (a.type_name!.localeCompare(b.type_name!) === 0) {
              return a.value.localeCompare(b.value);
            } else {
              return a.type_name!.localeCompare(b.type_name!);
            }
          } else if (a.is_active) {
            return -1;
          } else {
            return 1;
          }
        })}
        getOptionLabel={(option) => option.value}
        groupBy={(option) => option.type_name || ''}
        value={props.selections}
        onChange={handleChange}
        renderGroup={(params) => {
          return (
            <React.Fragment key={params.key}>
              <ListSubheader
                sx={{
                  top: '-8px',
                  fontSize: '0.9rem',
                  backgroundColor: '#fff',
                  color: theme.palette.primary.main,
                }}
              >
                {params.group}
              </ListSubheader>
              {params.children}
            </React.Fragment>
          );
        }}
        renderInput={(params) => {
          return (
            <TextField
              sx={{
                pb: 0,
                '& .MuiInputBase-input': {
                  minWidth: '180px !important',
                },
                '&::placeholder': {
                  fontSize: '0.8rem',
                },
              }}
              {...params}
              placeholder="Search for a tag"
              label="Tags"
            />
          );
        }}
        renderOption={(props, option, { selected }) => (
          <li
            {...props}
            style={{
              color: option.is_active
                ? theme.palette.common.black
                : theme.palette.grey[500],
            }}
          >
            <Checkbox checked={selected} />
            <ListItemText
              primary={
                option.is_active ? option.value : `(inactive) ${option.value}`
              }
            />
          </li>
        )}
        ChipProps={{ color: 'secondary', size: 'small', variant: 'outlined' }}
      />
    </FormControl>
  );
};
