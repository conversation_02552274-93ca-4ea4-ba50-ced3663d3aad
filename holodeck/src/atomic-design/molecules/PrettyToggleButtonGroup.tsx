import {
  ToggleButton,
  ToggleButtonGroup,
  styled,
  useTheme,
} from '@mui/material';
import React, { FC } from 'react';

interface Props {
  value: any;
  onChange: (e: any, value?: any) => void;
  buttons: { value: string; label: string }[];
}
export const PrettyToggleButtonGroup: FC<Props> = (props) => {
  const theme = useTheme();

  const StyledToggleButtonGroup = styled(ToggleButtonGroup)(({ theme }) => ({
    backgroundColor: theme.palette.grey[300],
    borderRadius: '16px',
    '& .MuiToggleButton-root': {
      borderRadius: '16px !important',
      border: '0',
      margin: '3px',
    },
    '& .MuiToggleButton-root.Mui-selected': {
      backgroundColor: '#fff',
      color: theme.palette.primary.main,
    },
    '& .MuiToggleButton-root.Mui-selected:hover': {
      backgroundColor: '#fff',
      color: theme.palette.primary.main,
    },
  }));

  return (
    <StyledToggleButtonGroup
      exclusive={true}
      value={props.value}
      onChange={props.onChange}
    >
      {props.buttons.map(({ value, label }) => (
        <ToggleButton key={value} value={value}>
          {label}
        </ToggleButton>
      ))}
    </StyledToggleButtonGroup>
  );
};
