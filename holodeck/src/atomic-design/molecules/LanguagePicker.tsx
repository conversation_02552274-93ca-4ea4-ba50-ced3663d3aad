import ClearIcon from '@mui/icons-material/Clear';
import {
  FormControl,
  IconButton,
  InputAdornment,
  MenuItem,
  Select,
  SxProps,
} from '@mui/material';

import React, { FC } from 'react';
import { SUPPORTED_LANGUAGES } from '../../popup/utils';

interface Props {
  selected: string;
  onSelect: (language: string) => void;
  size?: 'small' | undefined;
  disabled?: boolean;
  sx?: SxProps;
}
export const LanguagePicker: FC<Props> = (props) => {
  return (
    <FormControl sx={{ ...props.sx, width: '100%' }}>
      <Select
        id="sel_language"
        value={props.selected}
        onChange={(e) => {
          props.onSelect(e.target.value);
        }}
        size={props.size}
        endAdornment={
          !!props.selected && (
            <InputAdornment
              sx={{ position: 'absolute', right: 32 }}
              position="end"
            >
              <IconButton
                onClick={() => {
                  props.onSelect('');
                }}
              >
                <ClearIcon></ClearIcon>
              </IconButton>
            </InputAdornment>
          )
        }
      >
        {SUPPORTED_LANGUAGES.map((language) => (
          <MenuItem key={`sel_language_${language}`} value={language}>
            {language}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};
