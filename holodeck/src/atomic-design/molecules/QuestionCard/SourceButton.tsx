import { ArrowRight } from '@mui/icons-material';
import { Avatar, Box, Button, SxProps, useTheme } from '@mui/material';
import React, { FC } from 'react';

interface SourcesParams {
  numSources: number;
  onClick: () => void;
}
export const SourcesButton: FC<SourcesParams> = ({ numSources, onClick }) => {
  const theme = useTheme();
  const sx: SxProps = {
    borderRadius: '8px',
    justifyContent: 'space-between',
    fontSize: '14px',

    color: theme.palette.common.black,
    backgroundColor: theme.palette.grey[600] + '20',
    width: 'fit-content',
    minWidth: '200px',
  };
  return (
    <Button
      onClick={onClick}
      variant="text"
      disableRipple
      disableElevation
      sx={sx}
      endIcon={<ArrowRight />}
    >
      <Box sx={{ display: 'flex' }}>
        Sources{' '}
        <Avatar
          sx={{
            ml: 1,
            mt: '-2px',
            width: '22px',
            height: '22px',
            fontSize: '12px',
            fontWeight: 500,
            color: theme.palette.common.black,
          }}
        >
          {numSources}
        </Avatar>
      </Box>
    </Button>
  );
};
