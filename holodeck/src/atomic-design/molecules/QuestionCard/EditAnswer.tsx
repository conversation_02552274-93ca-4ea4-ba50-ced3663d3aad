import { Stack, useTheme } from '@mui/material';
import React, { FC } from 'react';
import { StyledTextArea } from '../../../popup/pages/HomePage/pages/ViewQuestionnaireAddQuestions/styledComponents';

interface EditableAnswerProps {
  text: string;
  handleChange: (text: string) => void;
}

export const EditAnswer: FC<EditableAnswerProps> = (props) => {
  //TODO - merge this with AddQuestionFormCard.tsx
  const theme = useTheme();

  const TextArea = (
    <StyledTextArea
      sx={{
        border: `2px solid ${theme.palette.primary.main + '50'}`,
        p: '4px 8px',
        borderRadius: '8px',
        fontWeight: '400',
        overflowY: 'initial !important',
      }}
      maxRows={15}
      aria-label="Enter your new answer"
      placeholder="Enter your new answer here"
      onChange={(e) => {
        props.handleChange(e.target.value);
      }}
      value={props.text}
    />
  );

  return (
    <Stack direction={'column'} justifyContent={'space-between'} gap={2}>
      {TextArea}
    </Stack>
  );
};
