import Check from '@mui/icons-material/Check';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import EditIcon from '@mui/icons-material/Edit';
import {
  Avatar,
  Box,
  Button,
  Chip,
  FormControl,
  InputLabel,
  ListItemText,
  MenuItem,
  Stack,
  Typography,
  useTheme,
} from '@mui/material';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import React, { FC, useEffect, useState } from 'react';

import { useMetadataFilters } from '../../../popup/api/metadata.swr';
import { QuestionDto, deleteQuestion } from '../../../popup/api/question.api';
import { QuestionnaireDto } from '../../../popup/api/questionnaires.api';

import ConfirmationModal from '../../../components/ConfirmationModal';
import {
  useContentGroupings,
  useGroupings,
} from '../../../popup/api/groupings.swr';
import { UserDto } from '../../../popup/api/user.api';
import { useUsers } from '../../../popup/api/user.swr';
import {
  processAnswerCitations,
  reorderCitations,
} from '../../../popup/pages/HomePage/pages/ViewQuestionnaire/QuestionList/answerProcessing';
import { useRootStore } from '../../../popup/state/useRootStore';
import { copyToClipboard } from '../../../utils/helpers';
import { Chippy } from '../../atoms/Chips/BaseChip';
import { ConfidenceChip } from '../../atoms/Chips/ConfidenceChip';
import { ModifiedChip } from '../../atoms/Chips/QuestionStateChip';
import { PageHeaderBar } from '../../atoms/PageHeaderBar';
import { PrimaryTooltip, SecondaryTooltip } from '../../atoms/StyledTooltips';
import { Title } from '../../atoms/Title';
import { TribbleAvatar } from '../../atoms/TribbleAvatar';
import { UtilityButton } from '../../atoms/buttons/UtilityButton';
import { CopyButton } from '../CopyButton';
import { SourceCard } from '../SourceCard/SourceCard';
import { CircularIconButton } from './CircularIconButton';
import { EditAnswer } from './EditAnswer';
import { PrevNextQuestionNavigation } from './PrevNextQuestionNavigation';
import { SourcesButton } from './SourceButton';

import * as QA_UTILS from '../../../popup/utils/qa';
import {
  DEFAULT_PRIVATE_SOURCE_WARNING_MESSAGE,
  DOCUMENT_TYPE_CALL_TRANSCRIPT,
} from '../../../utils/constants';
import { SendToSlackModal } from './SendToSlackModal';

type QuestionCardProps = {
  question: QuestionDto;
  questionnaire: QuestionnaireDto;
  showSources: (answerIdx: number) => void;
  isAccepted: boolean;
  answerText: string;
  handleRegenButton: () => void;
  handleMarkAsReviewed: () => void;
  mode: 'edit' | 'view';
  handleEditMode: () => void;
  handleSaveEditedAnswer: (newAnswer: string) => Promise<void>;
  handleStopEditing: () => void;
  handleRevert: () => Promise<void>;
  handleUpdateQuestion: (
    groupingId: number,
    assigneeId: number,
  ) => Promise<void>;
  handleUseAsAnswer: (modified_text: string) => void;
};

export const QuestionMultiCard: FC<QuestionCardProps> = (props) => {
  const { question, questionnaire, answerText } = props;

  const rootStore = useRootStore();
  const metadataFilters = useMetadataFilters();
  const groupings = useGroupings();
  const contentGroupings = useContentGroupings(question.content_id);
  const sortContentGroupings = [...contentGroupings.data].sort((a, b) =>
    a.grouping_name.toLowerCase() < b.grouping_name.toLowerCase() ? -1 : 1,
  );

  const users = useUsers();
  const theme = useTheme();

  const [slackModalOpen, setSlackModalOpen] = useState(false);
  const hasSlack =
    rootStore.slackSettings != null &&
    rootStore.slackSettings?.collaboration_channel?.length > 0;
  const showSlackButton = hasSlack;

  const [showDelete, setShowDelete] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isReverting, setIsReverting] = useState(false);

  const [origAnswer, setOrigAnswer] = useState(answerText);
  const [newAnswer, setNewAnswer] = useState(answerText);
  const [editAnswerIdx, setEditAnswerIdx] = useState(-1);

  const [origGroupingId, setOrigGroupingId] = useState('');
  const [newGroupingName, setNewGroupingName] = useState('');
  const [newGroupingId, setNewGroupingId] = useState(''); // Select needs a string; parse to int before calling API

  const [availableUsers, setAvailableUsers] = useState<UserDto[]>([]);
  const [origAssigneeId, setOrigAssigneeId] = useState('');
  const [newAssigneeName, setNewAssigneeName] = useState('');
  const [newAssigneeId, setNewAssigneeId] = useState('');

  // This will be an array of arrays due to multi answer versions
  const [usedDocuments, setUsedDocuments] = useState([]);
  const [privateSourceWarningMessage, setPrivateSourceWarningMessage] =
    useState(DEFAULT_PRIVATE_SOURCE_WARNING_MESSAGE);

  const m = '1rem'; //margin

  useEffect(() => {
    const loadData = async () => {
      const answers = question.output_original!.answer_versions || [];
      const usedDocsAll = [];
      for (const answer of answers) {
        const usedDocs = await QA_UTILS.getUsedSourceMetadata(answer);
        usedDocsAll.push(usedDocs);
      }
      setUsedDocuments(usedDocsAll);

      const privacyMessage = await QA_UTILS.getPrivateSourceWarningMessage();
      if (privacyMessage) {
        setPrivateSourceWarningMessage(privacyMessage);
      }
    };
    if (question) {
      loadData();
    }
  }, [question]);

  useEffect(() => {
    if (question.grouping_id) {
      const grouping = groupings.data?.find(
        (g) => g.id == question.grouping_id,
      );
      if (grouping) {
        setNewGroupingName(grouping.name);
        setNewGroupingId(String(grouping.id));
        setOrigGroupingId(String(grouping.id));
      }
    } else {
      // In case we updated the grouping to None, reset everything on refresh
      setNewGroupingName('');
      setNewGroupingId('');
      setOrigGroupingId('');
    }
  }, [question, groupings.data]);

  useEffect(() => {
    if (users.data) {
      setAvailableUsers(users.data);
      if (question.assignee_id) {
        setNewAssigneeName(question.assignee_name);
        setNewAssigneeId(String(question.assignee_id));
        setOrigAssigneeId(String(question.assignee_id));
      } else {
        // In case we updated the assignee to None, reset everything on refresh
        setNewAssigneeName('');
        setNewAssigneeId('');
        setOrigAssigneeId('');
      }
    }
  }, [question, users.data]);

  const navBack = () => {
    rootStore.navigate({
      name: 'viewQuestionnaire',
      id: questionnaire.rfx_content_id,
      scrollToId: question.id?.toString(),
    });
  };

  const handleChangeAnswer = (text: string) => {
    setNewAnswer(text);
  };

  const isEditMode = props.mode == 'edit';
  const hasConfidence = QA_UTILS.hasConfidenceScore(question);
  const isModified = QA_UTILS.isModifiedAnswer(question);
  const isProcessing = QA_UTILS.isProcessingAnswer(question);

  const groupingsDefined = groupings.data !== undefined;
  const oldGrouping = contentGroupings.data?.find(
    (grouping) => grouping.grouping_id == question.grouping_id,
  );
  const newGrouping = contentGroupings.data?.find(
    (grouping) => grouping.grouping_id == Number(newGroupingId),
  );

  const showRegenerationMessage =
    question.grouping_id != Number(newGroupingId) &&
    !_.isEqual(
      oldGrouping?.grouping_metadata_filter,
      newGrouping?.grouping_metadata_filter,
    );

  const answers = question.output_original!.answer_versions || [];

  const questionText = QA_UTILS.questionText(question);

  // Filter out citations when copying
  const origAnswerText = isModified
    ? question.output_modified!.text
    : isEditMode && editAnswerIdx > -1
      ? answers[editAnswerIdx].answer
      : '';
  const answerNoCitations = processAnswerCitations(origAnswerText, true);

  const showConfidenceChip = hasConfidence && !isModified;
  const isAssigned = props.question.assignee_name != undefined;

  const handleCancel = () => {
    setNewGroupingId(origGroupingId);
    setNewAnswer(origAnswer);
    setNewAssigneeId(origAssigneeId);
    setEditAnswerIdx(-1);
    props.handleStopEditing();
  };

  const handleDelete = async () => {
    const result = await deleteQuestion(question.id);
    if (result.success) {
      rootStore.setNotification({
        body: '',
        header: 'Deleted',
        type: 'success',
      });
      navBack();
    }
  };

  const handleSave = async () => {
    setIsSaving(true);

    // Did answer change? If so, call modify answer
    const processOrigAnswer = processAnswerCitations(origAnswerText, true);
    if (
      newAnswer != '' &&
      newAnswer != origAnswer &&
      newAnswer != processOrigAnswer
    ) {
      await props.handleSaveEditedAnswer(newAnswer);
    }

    // Did grouping or assignee change? If so, call updateQuestion
    if (newGroupingId != origGroupingId || newAssigneeId != origAssigneeId) {
      await props.handleUpdateQuestion(
        Number(newGroupingId),
        Number(newAssigneeId),
      );
    }

    setIsSaving(false);
  };

  const DeleteConfirmation = (
    <ConfirmationModal
      cancelText="Cancel"
      confirmText="Delete"
      handleCancel={() => {
        setShowDelete(false);
      }}
      handleConfirm={handleDelete}
      message="Delete this question?"
      open={showDelete}
      title="Delete"
      destructive={true}
    />
  );

  const HeaderButtons = () => {
    if (isEditMode) {
      return (
        <Stack
          direction="row"
          alignContent="center"
          width="100%"
          justifyContent="space-between"
        >
          <Button
            sx={{ width: 'fit-content', ml: '-6px' }}
            variant="outlined"
            size="small"
            onClick={handleCancel}
          >
            Cancel
          </Button>
          <Button
            sx={{ width: 'fit-content' }}
            variant="contained"
            size="small"
            onClick={handleSave}
            disabled={isSaving || isReverting}
          >
            {isSaving ? 'Saving...' : 'Save'}
          </Button>
        </Stack>
      );
    } else {
      return (
        <Stack direction="row" width="100%" justifyContent="flex-end">
          <PrevNextQuestionNavigation
            question={question}
            questionnaire={questionnaire}
          />
        </Stack>
      );
    }
  };

  const Grouping = () => {
    if (!isEditMode && newGroupingName != '') {
      return (
        <Typography
          variant="body2"
          sx={{
            textTransform: 'uppercase',
            color: theme.palette.grey[500],
            mb: '4px',
            fontWeight: '300',
          }}
        >
          {newGroupingName}
        </Typography>
      );
    } else if (isEditMode) {
      return (
        <>
          <FormControl
            sx={{
              mb: showRegenerationMessage ? '0.5rem' : '1.5rem',
              width: '100%',
            }}
            size="small"
            variant="standard"
          >
            <InputLabel id="grouping-select-small-label">Grouping</InputLabel>
            <Select
              labelId="grouping-select-small-label"
              id="grouping-select-small"
              value={newGroupingId}
              label="Grouping"
              onChange={(e: SelectChangeEvent) =>
                setNewGroupingId(e.target.value)
              }
              renderValue={(selected) => {
                const groupingName =
                  contentGroupings.data.find(
                    (grouping) => String(grouping.grouping_id) == selected,
                  )?.grouping_name || ''; // Should always find, but just case.
                return (
                  <Chip
                    color="success"
                    variant="outlined"
                    label={groupingName}
                    onDelete={() => setNewGroupingId('')}
                    onMouseDown={(e) => e.stopPropagation()}
                    sx={{ my: '4px', mr: 1 }}
                  />
                );
              }}
              MenuProps={{
                sx: {
                  maxHeight: '400px',
                },
                anchorOrigin: {
                  vertical: 'bottom',
                  horizontal: 'left',
                },
              }}
            >
              <MenuItem value="">
                <ListItemText
                  primary="(no grouping)"
                  sx={{ '& .MuiTypography-root': { fontSize: '0.85rem' } }}
                />
              </MenuItem>
              {sortContentGroupings.map((grouping) => {
                return (
                  <MenuItem
                    key={`grouping_${grouping.grouping_name}`}
                    value={grouping.grouping_id}
                  >
                    <ListItemText
                      primary={grouping.grouping_name}
                      sx={{ '& .MuiTypography-root': { fontSize: '0.85rem' } }}
                    />
                  </MenuItem>
                );
              })}
            </Select>
          </FormControl>
          {showRegenerationMessage ? (
            <Typography
              variant="body2"
              sx={{ color: theme.palette.success.main, mb: '1.5rem' }}
            >
              Changing groupings may result in the answer being regenerated.
            </Typography>
          ) : (
            <></>
          )}
        </>
      );
    }
  };

  const Assignee = () => {
    if (isEditMode) {
      return (
        <FormControl fullWidth size="small" sx={{ mt: '1rem', mb: '2rem' }}>
          <InputLabel id="assignee-select-small-label">Assignee</InputLabel>
          <Select
            labelId="assignee-select-small-label"
            label="Assignee"
            value={newAssigneeId}
            onChange={(e: SelectChangeEvent) =>
              setNewAssigneeId(e.target.value)
            }
            renderValue={(selected) => {
              const selectedUser = availableUsers.find(
                (user) => String(user.id) == selected,
              );

              return selectedUser ? (
                <Chip
                  color="primary"
                  variant="outlined"
                  label={selectedUser?.name}
                  onDelete={() => setNewAssigneeId('')}
                  onMouseDown={(e) => e.stopPropagation()}
                  sx={{ my: '4px' }}
                />
              ) : null;
            }}
            MenuProps={{
              sx: {
                maxHeight: '400px',
              },
              anchorOrigin: {
                vertical: 'bottom',
                horizontal: 'left',
              },
            }}
          >
            {availableUsers
              .sort((a, b) =>
                a.name.toLowerCase() < b.name.toLowerCase() ? -1 : 1,
              )
              .map((user) => (
                <MenuItem value={user.id} key={user.id}>
                  <div style={{ marginRight: '1rem' }}>
                    {user.picture && user.picture != '' && (
                      <Avatar src={user.picture} sx={{ height: 24, width: 24 }}>
                        {user.name}
                      </Avatar>
                    )}
                    {(!user.picture || user.picture == '') && (
                      <TribbleAvatar
                        username={user.name}
                        height="24px"
                        width="24px"
                      />
                    )}
                  </div>
                  <ListItemText
                    primary={user.name}
                    sx={{ '& .MuiTypography-root': { fontSize: '0.85rem' } }}
                  />
                </MenuItem>
              ))}
          </Select>
        </FormControl>
      );
    } else {
      if (isAssigned && availableUsers.length > 0) {
        const assignedUser = availableUsers.find(
          (user) => String(user.id) == origAssigneeId,
        );
        return assignedUser ? (
          <Box
            sx={{
              width: '100%',
              mt: m,
              display: 'flex',
              justifyContent: 'flex-end',
            }}
          >
            {assignedUser?.picture && assignedUser.picture != '' && (
              <PrimaryTooltip
                title={`Question is assigned to ${question.assignee_name}`}
                enterDelay={250}
              >
                <Avatar
                  src={assignedUser.picture}
                  sx={{ height: 24, width: 24 }}
                >
                  {question.assignee_name}
                </Avatar>
              </PrimaryTooltip>
            )}
            {(!assignedUser.picture || assignedUser.picture == '') && (
              <PrimaryTooltip
                title={`Question is assigned to ${question.assignee_name}`}
                enterDelay={250}
              >
                <div>
                  <TribbleAvatar
                    username={question.assignee_name}
                    height="24px"
                    width="24px"
                  />
                </div>
              </PrimaryTooltip>
            )}
          </Box>
        ) : null;
      } else {
        return <></>;
      }
    }
  };

  const sendToSlackButton = () => {
    return showSlackButton ? (
      <Box>
        <Button
          onClick={() => {
            setSlackModalOpen(true);
          }}
          variant="outlined"
          size="small"
        >
          Loop in an Expert
        </Button>
      </Box>
    ) : (
      <div></div>
    );
  };

  const Lower = () => {
    if (!isEditMode && !isProcessing) {
      return (
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
          id="lower"
        >
          {sendToSlackButton()}

          {props.isAccepted ? (
            <SecondaryTooltip
              title={
                'Answer marked as reviewed. Click to change back to not reviewed.'
              }
              enterDelay={250}
            >
              <Button
                variant="outlined"
                color="secondary"
                size="small"
                onClick={props.handleMarkAsReviewed}
                startIcon={<Check />}
                sx={{ minWidth: '150px ' }}
              >
                Reviewed
              </Button>
            </SecondaryTooltip>
          ) : (
            <PrimaryTooltip
              title={'Click to mark answer as reviewed'}
              enterDelay={250}
            >
              <Button
                variant="outlined"
                color="warning"
                size="small"
                onClick={props.handleMarkAsReviewed}
                sx={{ minWidth: '150px ' }}
              >
                Not Reviewed
              </Button>
            </PrimaryTooltip>
          )}
        </Stack>
      );
    } else {
      //big old delete button
      return (
        <Button
          color="error"
          onClick={() => setShowDelete(true)}
          variant="outlined"
          sx={{ maxWidth: '100%' }}
        >
          Delete Question
        </Button>
      );
    }
  };

  const VerbatimSection = (verbatimSources) => {
    return (
      <Stack
        direction="column"
        sx={{
          p: '12px',
          mt: 3,
          borderRadius: 1,
          backgroundColor: theme.palette.grey[600] + '20',
        }}
      >
        <Stack direction="row">
          <Typography variant="body1" color="primary" sx={{ fontWeight: 500 }}>
            Verbatim Sources
          </Typography>
          <Avatar
            sx={{
              ml: 1,
              mt: '-2px',
              width: '22px',
              height: '22px',
              fontSize: '12px',
              fontWeight: 600,
              color: theme.palette.primary.main,
              bgcolor: theme.palette.primary.main + '20',
            }}
          >
            {verbatimSources.length}
          </Avatar>
        </Stack>
        <Typography
          variant="body2"
          sx={{ color: theme.palette.grey[500], mt: '4px', mb: 1 }}
        >
          The following sources were retrieved during the answer process; these
          sources are marked verbatim, so you may want to use their text
          directly, instead of the AI generated answer.
        </Typography>

        <Stack direction="column" sx={{ maxHeight: '30vh', overflowY: 'auto' }}>
          {verbatimSources.map((source, idx) => (
            <Stack
              direction="column"
              key={`source_${idx + 1}`}
              sx={{
                mt: '1rem',
                backgroundColor: 'white',
                borderRadius: 1,
                p: '12px',
              }}
              gap="1rem"
            >
              <SourceCard
                source={source}
                type={source.type == 'qa' ? 'RFP' : 'DOC'}
                disableFade={true}
                hideIcon={true}
              />
              <Button
                size="small"
                variant="outlined"
                color="primary"
                onClick={() => props.handleUseAsAnswer(source.text)}
                sx={{ width: 'fit-content' }}
              >
                Use as Answer
              </Button>
            </Stack>
          ))}
        </Stack>
      </Stack>
    );
  };

  return (
    <Stack sx={{ minHeight: '100%', p: '1rem', pt: '0' }} direction={'column'}>
      <Box
        id="header-container"
        sx={{
          position: 'sticky',
          pt: '1rem',
          top: '0px',
          backgroundColor: '#FFF',
          zIndex: 1200,
          minHeight: '56.6px',
          mb: '1rem',
        }}
      >
        <PageHeaderBar
          disableBackButton={props.mode == 'edit'}
          onClickBack={navBack}
          tooltipMessage="Go back to questionnaire"
        >
          {HeaderButtons()}
        </PageHeaderBar>
        <SendToSlackModal
          handleClose={() => setSlackModalOpen(false)}
          open={slackModalOpen}
          question={question}
        />
      </Box>

      {groupingsDefined && Grouping()}

      <Title
        title={questionText}
        sx={{ maxHeight: '20vh', overflow: 'auto', whiteSpace: 'pre-wrap' }}
      />

      {!isEditMode && Assignee()}

      {isProcessing && (
        <Stack direction="column" sx={{ height: '100%', mt: '1rem' }}>
          <Typography variant="body2">(question is being processed)</Typography>
        </Stack>
      )}

      {!isProcessing &&
        !isModified &&
        !isEditMode &&
        answers.map((answer, idx) => {
          let answerText = processAnswerCitations(answer.answer, true);
          const usedDocs = usedDocuments[idx];

          // Check for private docs
          const privateDocs =
            usedDocs?.filter((doc) => doc.privacy === 'private') ?? [];

          const usesCallTranscript = answer?.documents?.docs?.some(
            (doc) => doc.document_type === DOCUMENT_TYPE_CALL_TRANSCRIPT,
          );

          // Reorder citations so they're sequential for the end user
          let answerTextWithCitations = processAnswerCitations(
            answer.answer,
            false,
          );
          const { usedCitationIndexes, renumberedProcessedAnswer } =
            reorderCitations(answerTextWithCitations);
          answerTextWithCitations = renumberedProcessedAnswer;

          const isMultiTypeMDF = answer?.metadata_filter?.multiple_types;
          const chipLabel = isMultiTypeMDF
            ? `Answer Version ${idx + 1}`
            : answer?.metadata_filter.value;
          const chipTooltip = isMultiTypeMDF
            ? answer?.metadata_filter?.multiple_types.reduce(
                (acc, mdf_type, idx) => {
                  return (
                    acc +
                    `${idx > 0 ? '\n\n' : ''}${mdf_type.type_name}:\n- ${
                      mdf_type.value
                    }`
                  );
                },
                '',
              )
            : '';

          const couldNotAnswer = answer?.review_message?.includes(
            'Could not generate an answer to the question',
          );

          const confidenceExplanation = answer?.confidence_score_explanation;

          let itemsToReview;
          if (answer?.items_to_review) {
            itemsToReview = Array.isArray(answer?.items_to_review)
              ? answer?.items_to_review.join('\n- ')
              : answer?.items_to_review;
          }

          // Handle verbatim sources
          const verbatimSources = QA_UTILS.verbatimSources(
            answer,
            metadataFilters.data?.metadata_filters ?? [],
          );

          return (
            <Stack
              key={`answer_${idx}`}
              direction="column"
              sx={{ mt: '2rem', mb: '1rem' }}
            >
              {isMultiTypeMDF && (
                <PrimaryTooltip
                  title={`Answer generated using this context:\n\n${chipTooltip}`}
                  enterDelay={250}
                >
                  <Box sx={{ width: '100%' }}>
                    <Chippy
                      label={chipLabel}
                      colorHex={theme.palette.primary.main}
                      sx={{
                        width: '100%',
                        fontSize: '0.85rem',
                        lineHeight: '1rem',
                        fontWeight: '600',
                        border: '1px solid ' + theme.palette.primary.main,
                      }}
                    />
                  </Box>
                </PrimaryTooltip>
              )}
              {!isMultiTypeMDF && (
                <Chippy
                  label={chipLabel}
                  colorHex={theme.palette.primary.main}
                  sx={{
                    fontWeight: '600',
                    border: '1px solid ' + theme.palette.primary.main,
                  }}
                />
              )}

              <Box sx={{ mt: '1rem', maxHeight: '45vh', overflow: 'auto' }}>
                <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                  {answerTextWithCitations}
                </Typography>
              </Box>

              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
                sx={{ mt: '1rem', mb: '2rem', width: '100%' }}
              >
                {showConfidenceChip && (
                  <ConfidenceChip
                    score={answer.confidence_score}
                    couldNotAnswer={couldNotAnswer}
                    explanation={
                      (confidenceExplanation
                        ? `EXPLANATION\n\n${confidenceExplanation}`
                        : '') +
                      (itemsToReview
                        ? `\n\nITEMS TO REVIEW\n\n- ${itemsToReview}`
                        : '')
                    }
                  />
                )}
                {!showConfidenceChip && <div></div>}

                <Stack direction="row" gap="0.5rem">
                  <CircularIconButton
                    size="small"
                    disableBorder={false}
                    ariaLabel="Edit this answer"
                    Icon={EditIcon}
                    onClick={() => {
                      setEditAnswerIdx(idx);
                      setNewAnswer(answerText);
                      props.handleEditMode();
                    }}
                  />
                  <CircularIconButton
                    size="small"
                    disableBorder={false}
                    ariaLabel="Copy the answer to your clipboard"
                    Icon={ContentCopyIcon}
                    onClick={async () => {
                      await copyToClipboard(answerText);
                      await rootStore.setNotification({
                        body: '',
                        type: 'success',
                        header: 'Copied',
                        timeoutId: 500,
                      });
                    }}
                  />
                </Stack>
              </Stack>

              {usedCitationIndexes.length > 0 && (
                <>
                  <SourcesButton
                    onClick={() => props.showSources(idx)}
                    numSources={usedCitationIndexes.length}
                  />
                  {privateDocs.length > 0 && (
                    <Typography
                      variant="body2"
                      color="error"
                      sx={{ p: '12px', mb: 2 }}
                    >
                      {privateSourceWarningMessage}
                    </Typography>
                  )}
                  {usesCallTranscript && (
                    <Typography
                      variant="body2"
                      color="error"
                      sx={{ p: '12px', mb: 2 }}
                    >
                      ⚠ WARNING: Answer cites a call transcript as a source.
                    </Typography>
                  )}
                </>
              )}
              {usedCitationIndexes.length == 0 && (
                <Typography variant="body2" color="error">
                  (no sources were retrieved)
                </Typography>
              )}
              {!isModified && verbatimSources.length > 0
                ? VerbatimSection(verbatimSources)
                : null}

              {/* Add some padding to the last element to account for the bottom bar */}
              {idx == answers.length - 1 && <Box sx={{ minHeight: '4rem' }} />}
            </Stack>
          );
        })}

      {!isProcessing && isModified && !isEditMode && (
        <Stack direction={'column'} sx={{ height: '100%', mt: '1rem' }}>
          <Typography variant="body2">{origAnswerText}</Typography>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            sx={{ mt: '1rem', mb: '2rem' }}
          >
            <ModifiedChip question={question} />
            <Stack direction="row" gap="0.5rem" alignItems="center">
              <CircularIconButton
                size="small"
                disableBorder={false}
                ariaLabel="Edit this answer"
                Icon={EditIcon}
                onClick={props.handleEditMode}
              />
              <PrimaryTooltip
                title={'Copy the answer to your clipboard'}
                enterDelay={250}
              >
                <div>
                  <CopyButton size="small" textToCopy={answerNoCitations} />
                </div>
              </PrimaryTooltip>
            </Stack>
          </Stack>
        </Stack>
      )}

      {!isProcessing && isEditMode && (
        <Stack direction="column" sx={{ my: '1rem' }} gap="1rem">
          <EditAnswer handleChange={handleChangeAnswer} text={newAnswer} />
          {isModified && (
            <>
              <UtilityButton
                label={isReverting ? 'Reverting...' : 'Revert to original'}
                onClick={async () => {
                  setIsReverting(true);
                  await props.handleRevert();
                  setIsReverting(false);
                }}
                disabled={isSaving || isReverting}
              />
              <Box>
                <ModifiedChip question={question} />
              </Box>
            </>
          )}
          {Assignee()}
        </Stack>
      )}

      <Box
        sx={{
          position: 'fixed',
          bottom: 0,
          left: 0,
          height: '72px',
          width: '100%',
          p: '1rem',
          backgroundColor: '#fff',
          borderTopWidth: '1px',
          borderTopColor: theme.palette.grey[300],
          borderTopStyle: 'solid',
        }}
      >
        {Lower()}
      </Box>
      {DeleteConfirmation}
    </Stack>
  );
};
