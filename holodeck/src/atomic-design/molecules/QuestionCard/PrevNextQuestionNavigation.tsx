import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import { Stack } from '@mui/material';
import React, { FC } from 'react';

import { QuestionDto } from '../../../popup/api/question.api';
import { QuestionnaireDto } from '../../../popup/api/questionnaires.api';

import { useRootStore } from '../../../popup/state/useRootStore';

import { GreyTooltip } from '../../atoms/StyledTooltips';

interface Props {
  question: QuestionDto;
  questionnaire: QuestionnaireDto;
}

export const PrevNextQuestionNavigation: FC<Props> = ({
  question,
  questionnaire,
}) => {
  const rootStore = useRootStore();

  const navigateToQuestion = (questionId: number) => {
    rootStore.navigate({
      name: 'viewQuestion',
      questionId,
      questionnaire,
    });
  };

  return (
    <Stack direction="row" gap="1rem" alignItems="center" sx={{ mt: '4px' }}>
      {question.prev_content_detail_id ? (
        <GreyTooltip title="View previous question">
          <ChevronLeftIcon
            sx={{ cursor: 'pointer' }}
            onClick={() => navigateToQuestion(question.prev_content_detail_id)}
          />
        </GreyTooltip>
      ) : (
        <ChevronLeftIcon color="disabled" />
      )}
      {question.next_content_detail_id ? (
        <GreyTooltip title="View next question">
          <ChevronRightIcon
            sx={{ cursor: 'pointer' }}
            onClick={() => navigateToQuestion(question.next_content_detail_id)}
          />
        </GreyTooltip>
      ) : (
        <ChevronRightIcon color="disabled" />
      )}
    </Stack>
  );
};
