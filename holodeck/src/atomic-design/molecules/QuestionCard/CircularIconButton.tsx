import { Box, SvgIconProps, SxProps, useTheme } from '@mui/material';
import IconButton from '@mui/material/IconButton';
import React, { FC } from 'react';
import { PrimaryTooltip } from '../../atoms/StyledTooltips';

interface CircularIconButtonParams {
  ariaLabel?: string;
  Icon: React.ElementType<SvgIconProps>;
  onClick: (event?: React.MouseEvent<HTMLButtonElement>) => void;
  disableBorder?: boolean;
  size: 'small' | 'medium' | 'large';
  color?: 'inherit' | 'primary' | 'secondary' | 'default' | 'error';
  disabled?: boolean;
}

export const CircularIconButton: FC<CircularIconButtonParams> = (params) => {
  const theme = useTheme();
  const sx: SxProps = {
    display: 'inline-block',
    borderRadius: '50%',
    border: params.disableBorder ? 'none' : 'solid',
    borderWidth: '2px',
    borderColor: theme.palette.grey[600] + '30',
  };
  return (
    <Box sx={sx}>
      {!params.disabled && (
        <PrimaryTooltip title={params.ariaLabel} enterDelay={250}>
          <IconButton
            size={params.size}
            aria-label={params.ariaLabel}
            onClick={params.onClick}
            color={params.color ?? 'default'}
          >
            <params.Icon fontSize="inherit" />
          </IconButton>
        </PrimaryTooltip>
      )}

      {params.disabled && (
        <IconButton
          size={params.size}
          disabled={true}
          title={params.ariaLabel}
          aria-label={params.ariaLabel}
          onClick={params.onClick}
        >
          <params.Icon fontSize="inherit" />
        </IconButton>
      )}
    </Box>
  );
};
