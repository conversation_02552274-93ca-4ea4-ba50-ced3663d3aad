import Check from '@mui/icons-material/Check';
import EditIcon from '@mui/icons-material/Edit';
import LanguageIcon from '@mui/icons-material/Language';
import {
  Avatar,
  Box,
  Button,
  Card,
  Chip,
  FormControl,
  InputLabel,
  ListItemText,
  MenuItem,
  Stack,
  Typography,
  useTheme,
} from '@mui/material';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import _ from 'lodash';
import React, { FC, useEffect, useState } from 'react';

import { useMetadataFilters } from '../../../popup/api/metadata.swr';
import { QuestionDto, deleteQuestion } from '../../../popup/api/question.api';
import { QuestionnaireDto } from '../../../popup/api/questionnaires.api';

import ConfirmationModal from '../../../components/ConfirmationModal';
import {
  useContentGroupings,
  useGroupings,
} from '../../../popup/api/groupings.swr';
import { UserDto } from '../../../popup/api/user.api';
import { useUsers } from '../../../popup/api/user.swr';
import {
  processAnswerCitations,
  reorderCitations,
} from '../../../popup/pages/HomePage/pages/ViewQuestionnaire/QuestionList/answerProcessing';
import { useRootStore } from '../../../popup/state/useRootStore';
import { ConfidenceChip } from '../../atoms/Chips/ConfidenceChip';
import { ModifiedChip } from '../../atoms/Chips/QuestionStateChip';
import { PageHeaderBar } from '../../atoms/PageHeaderBar';
import {
  GreyTooltip,
  PrimaryTooltip,
  SecondaryTooltip,
} from '../../atoms/StyledTooltips';
import { Title } from '../../atoms/Title';
import { TribbleAvatar } from '../../atoms/TribbleAvatar';
import { UtilityButton } from '../../atoms/buttons/UtilityButton';
import { CopyButton } from '../CopyButton';
import { SourceCard } from '../SourceCard/SourceCard';
import { CircularIconButton } from './CircularIconButton';
import { EditAnswer } from './EditAnswer';
import { PrevNextQuestionNavigation } from './PrevNextQuestionNavigation';
import { SendToSlackModal } from './SendToSlackModal';
import { SourcesButton } from './SourceButton';

import * as QA_UTILS from '../../../popup/utils/qa';
import {
  DEFAULT_PRIVATE_SOURCE_WARNING_MESSAGE,
  DOCUMENT_TYPE_CALL_TRANSCRIPT,
} from '../../../utils/constants';

type QuestionCardProps = {
  question: QuestionDto;
  questionnaire: QuestionnaireDto;
  showSources: () => void;
  isAccepted: boolean;
  answerText: string;
  handleRegenButton: () => void;
  handleMarkAsReviewed: () => void;
  mode: 'edit' | 'view';
  handleEditMode: () => void;
  handleSaveEditedAnswer: (newAnswer: string) => Promise<void>;
  handleStopEditing: () => void;
  handleRevert: () => Promise<void>;
  handleUpdateQuestion: (
    groupingId: number,
    assigneeId: number,
  ) => Promise<void>;
  handleUseAsAnswer: (modified_text: string) => void;
};

export const QuestionCard: FC<QuestionCardProps> = (props) => {
  const { question, questionnaire, answerText } = props;

  const rootStore = useRootStore();
  const metadataFilters = useMetadataFilters();
  const groupings = useGroupings();
  const contentGroupings = useContentGroupings(question.content_id);
  const sortContentGroupings = [...contentGroupings.data].sort((a, b) =>
    a.grouping_name.toLowerCase() < b.grouping_name.toLowerCase() ? -1 : 1,
  );

  const users = useUsers();
  const theme = useTheme();

  const [slackModalOpen, setSlackModalOpen] = useState(false);
  const hasSlack =
    rootStore.slackSettings != null &&
    rootStore.slackSettings?.collaboration_channel?.length > 0;
  const showSlackButton = hasSlack;

  const [showDelete, setShowDelete] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isReverting, setIsReverting] = useState(false);

  const [origAnswer, setOrigAnswer] = useState(answerText);
  const [newAnswer, setNewAnswer] = useState(answerText);

  const [origGroupingId, setOrigGroupingId] = useState('');
  const [newGroupingName, setNewGroupingName] = useState('');
  const [newGroupingId, setNewGroupingId] = useState(''); // Select needs a string; parse to int before calling API

  const [availableUsers, setAvailableUsers] = useState<UserDto[]>([]);
  const [origAssigneeId, setOrigAssigneeId] = useState('');
  const [newAssigneeName, setNewAssigneeName] = useState('');
  const [newAssigneeId, setNewAssigneeId] = useState('');

  const [usedDocuments, setUsedDocuments] = useState([]);
  const [privateSourceWarningMessage, setPrivateSourceWarningMessage] =
    useState(DEFAULT_PRIVATE_SOURCE_WARNING_MESSAGE);

  const m = '1rem'; //margin

  useEffect(() => {
    const loadData = async () => {
      const usedDocs = await QA_UTILS.getUsedSourceMetadata(
        question.output_original,
      );
      setUsedDocuments(usedDocs);

      const privacyMessage = await QA_UTILS.getPrivateSourceWarningMessage();
      if (privacyMessage) {
        setPrivateSourceWarningMessage(privacyMessage);
      }
    };
    if (question) {
      loadData();
    }
  }, [question]);

  useEffect(() => {
    if (question.grouping_id) {
      const grouping = groupings.data?.find(
        (g) => g.id == question.grouping_id,
      );
      if (grouping) {
        setNewGroupingName(grouping.name);
        setNewGroupingId(String(grouping.id));
        setOrigGroupingId(String(grouping.id));
      }
    } else {
      // In case we updated the grouping to None, reset everything on refresh
      setNewGroupingName('');
      setNewGroupingId('');
      setOrigGroupingId('');
    }
  }, [question, groupings.data]);

  useEffect(() => {
    if (users.data) {
      setAvailableUsers(users.data);
      if (question.assignee_id) {
        setNewAssigneeName(question.assignee_name);
        setNewAssigneeId(String(question.assignee_id));
        setOrigAssigneeId(String(question.assignee_id));
      } else {
        // In case we updated the assignee to None, reset everything on refresh
        setNewAssigneeName('');
        setNewAssigneeId('');
        setOrigAssigneeId('');
      }
    }
  }, [question, users.data]);

  const navBack = () => {
    rootStore.navigate({
      name: 'viewQuestionnaire',
      id: questionnaire.rfx_content_id,
      scrollToId: question.id?.toString(),
    });
  };

  const handleChangeAnswer = (text: string) => {
    setNewAnswer(text);
  };

  const isEditMode = props.mode == 'edit';
  const isProcessing = QA_UTILS.isProcessingAnswer(question);
  const hasConfidence = question.output_original.confidence_score != undefined;
  const isModified = QA_UTILS.isModifiedAnswer(question);
  const couldNotAnswer = QA_UTILS.couldNotGenerateAnswer(question);

  const translatedText = question.output_original?.translation?.translation;
  const translatedSourceText = processAnswerCitations(
    question.output_original?.translation?.source_text,
    true,
  );

  const groupingsDefined =
    groupings.data !== undefined && groupings.data.length > 0;
  const oldGrouping = contentGroupings.data?.find(
    (grouping) => grouping.grouping_id == question.grouping_id,
  );
  const newGrouping = contentGroupings.data?.find(
    (grouping) => grouping.grouping_id == Number(newGroupingId),
  );

  const showRegenerationMessage =
    question.grouping_id != Number(newGroupingId) &&
    !_.isEqual(
      oldGrouping?.grouping_metadata_filter,
      newGrouping?.grouping_metadata_filter,
    );

  const questionText = QA_UTILS.questionText(question);

  // Filter out citations when copying
  const origAnswerText = QA_UTILS.shouldUseTranslatedAnswer(question)
    ? translatedText
    : isModified
      ? question.output_modified!.text
      : question.output_original?.answer;
  const answerNoCitations = processAnswerCitations(origAnswerText, true);

  // Show translation icon only if:
  // - translation exists AND
  // - not modified answer, OR, modified answer and translated source text is the modified answer
  // (if latter is false, it means translation occurred, but then user modified so...won't match translation anymore)
  const showTranslatedIcon = QA_UTILS.shouldUseTranslatedAnswer(question);

  // Used to count the # of used sources
  let processedAnswer = processAnswerCitations(origAnswerText ?? '', false);
  const { usedCitationIndexes } = reorderCitations(processedAnswer);

  // Check for private docs
  const privateDocs = usedDocuments.filter((doc) => doc.privacy === 'private');

  const usesCallTranscript = question.output_original?.documents?.docs?.some(
    (doc) => doc.document_type === DOCUMENT_TYPE_CALL_TRANSCRIPT,
  );

  const AnswerText = () => {
    const answerTextProcessed =
      question.state == 'processing'
        ? '(question is being processed)'
        : answerText;

    return !isEditMode ? (
      <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
        {answerTextProcessed}
      </Typography>
    ) : (
      <>
        <EditAnswer handleChange={handleChangeAnswer} text={newAnswer} />

        {isModified && (
          <Box sx={{ mt: '1rem' }}>
            <UtilityButton
              label={isReverting ? 'Reverting...' : 'Revert to original'}
              onClick={async () => {
                setIsReverting(true);
                await props.handleRevert();
                setIsReverting(false);
              }}
              disabled={isSaving || isReverting}
            />
          </Box>
        )}
      </>
    );
  };

  const handleCancel = () => {
    setNewGroupingId(origGroupingId);
    setNewAnswer(origAnswer);
    setNewAssigneeId(origAssigneeId);
    props.handleStopEditing();
  };

  const handleDelete = async () => {
    const result = await deleteQuestion(question.id);
    if (result.success) {
      rootStore.setNotification({
        body: '',
        header: 'Deleted',
        type: 'success',
      });
      navBack();
    }
  };

  const handleSave = async () => {
    setIsSaving(true);

    // Did answer change? If so, call modify answer
    const processOrigAnswer = processAnswerCitations(origAnswerText, true);
    if (
      newAnswer != '' &&
      newAnswer != origAnswer &&
      newAnswer != processOrigAnswer
    ) {
      await props.handleSaveEditedAnswer(newAnswer);
    }

    // Did grouping or assignee change? If so, call updateQuestion
    if (newGroupingId != origGroupingId || newAssigneeId != origAssigneeId) {
      await props.handleUpdateQuestion(
        Number(newGroupingId),
        Number(newAssigneeId),
      );
    }

    setIsSaving(false);
  };

  const DeleteConfirmation = (
    <ConfirmationModal
      cancelText="Cancel"
      confirmText="Delete"
      handleCancel={() => {
        setShowDelete(false);
      }}
      handleConfirm={handleDelete}
      message="Delete this question?"
      open={showDelete}
      title="Delete"
      destructive={true}
    />
  );

  const HeaderButtons = () => {
    if (isEditMode) {
      return (
        <Stack
          direction="row"
          alignContent="center"
          width="100%"
          justifyContent="space-between"
        >
          <Button
            sx={{ width: 'fit-content', ml: '-6px' }}
            variant="outlined"
            size="small"
            onClick={handleCancel}
          >
            Cancel
          </Button>
          <Button
            sx={{ width: 'fit-content' }}
            variant="contained"
            size="small"
            onClick={handleSave}
            disabled={isSaving || isReverting}
          >
            {isSaving ? 'Saving...' : 'Save'}
          </Button>
        </Stack>
      );
    } else {
      return (
        <Stack direction="row" width="100%" justifyContent="flex-end">
          <PrevNextQuestionNavigation
            question={question}
            questionnaire={questionnaire}
          />
        </Stack>
      );
    }
  };

  const Grouping = () => {
    if (!isEditMode && newGroupingName != '') {
      return (
        <Typography
          variant="body2"
          sx={{
            textTransform: 'uppercase',
            color: theme.palette.grey[500],
            mb: '4px',
            fontWeight: '300',
          }}
        >
          {newGroupingName}
        </Typography>
      );
    } else if (isEditMode) {
      return (
        <>
          <FormControl
            sx={{
              mb: showRegenerationMessage ? '0.5rem' : '1.5rem',
              width: '100%',
            }}
            size="small"
            variant="standard"
          >
            <InputLabel id="grouping-select-small-label">Grouping</InputLabel>
            <Select
              labelId="grouping-select-small-label"
              id="grouping-select-small"
              value={newGroupingId}
              label="Grouping"
              onChange={(e: SelectChangeEvent) =>
                setNewGroupingId(e.target.value)
              }
              renderValue={(selected) => {
                const groupingName =
                  contentGroupings.data.find(
                    (grouping) => String(grouping.grouping_id) == selected,
                  )?.grouping_name || ''; // Should always find, but just case.
                return (
                  <Chip
                    color="success"
                    variant="outlined"
                    label={groupingName}
                    onDelete={() => setNewGroupingId('')}
                    onMouseDown={(e) => e.stopPropagation()}
                    sx={{ my: '4px', mr: 1 }}
                  />
                );
              }}
              MenuProps={{
                sx: {
                  maxHeight: '400px',
                },
                anchorOrigin: {
                  vertical: 'bottom',
                  horizontal: 'left',
                },
              }}
            >
              <MenuItem value="">
                <ListItemText
                  primary="(no grouping)"
                  sx={{ '& .MuiTypography-root': { fontSize: '0.85rem' } }}
                />
              </MenuItem>
              {sortContentGroupings.map((grouping) => {
                return (
                  <MenuItem
                    key={`grouping_${grouping.grouping_name}`}
                    value={grouping.grouping_id}
                  >
                    <ListItemText
                      primary={grouping.grouping_name}
                      sx={{ '& .MuiTypography-root': { fontSize: '0.85rem' } }}
                    />
                  </MenuItem>
                );
              })}
            </Select>
          </FormControl>
          {showRegenerationMessage ? (
            <Typography
              variant="body2"
              sx={{ color: theme.palette.success.main, mb: '1.5rem' }}
            >
              Changing groupings may result in the answer being regenerated.
            </Typography>
          ) : (
            <></>
          )}
        </>
      );
    }
  };

  const Assignee = () => {
    if (isEditMode) {
      return (
        <FormControl fullWidth size="small" sx={{ mt: '1rem', mb: '2rem' }}>
          <InputLabel id="assignee-select-small-label">Assignee</InputLabel>
          <Select
            labelId="assignee-select-small-label"
            label="Assignee"
            value={newAssigneeId}
            onChange={(e: SelectChangeEvent) =>
              setNewAssigneeId(e.target.value)
            }
            renderValue={(selected) => {
              const selectedUser = availableUsers.find(
                (user) => String(user.id) == selected,
              );

              return selectedUser ? (
                <Chip
                  color="primary"
                  variant="outlined"
                  label={selectedUser?.name}
                  onDelete={() => setNewAssigneeId('')}
                  onMouseDown={(e) => e.stopPropagation()}
                  sx={{ my: '4px' }}
                />
              ) : null;
            }}
            MenuProps={{
              sx: {
                maxHeight: '400px',
              },
              anchorOrigin: {
                vertical: 'bottom',
                horizontal: 'left',
              },
            }}
          >
            {availableUsers
              .sort((a, b) =>
                a.name.toLowerCase() < b.name.toLowerCase() ? -1 : 1,
              )
              .map((user) => (
                <MenuItem value={user.id} key={user.id}>
                  <div style={{ marginRight: '1rem' }}>
                    {user.picture && user.picture != '' && (
                      <Avatar src={user.picture} sx={{ height: 24, width: 24 }}>
                        {user.name}
                      </Avatar>
                    )}
                    {(!user.picture || user.picture == '') && (
                      <TribbleAvatar
                        username={user.name}
                        height="24px"
                        width="24px"
                      />
                    )}
                  </div>
                  <ListItemText
                    primary={user.name}
                    sx={{ '& .MuiTypography-root': { fontSize: '0.85rem' } }}
                  />
                </MenuItem>
              ))}
          </Select>
        </FormControl>
      );
    } else {
      if (isAssigned && availableUsers.length > 0) {
        const assignedUser = availableUsers.find(
          (user) => String(user.id) == origAssigneeId,
        );
        return assignedUser ? (
          <Box
            sx={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}
          >
            {assignedUser?.picture && assignedUser.picture != '' && (
              <PrimaryTooltip
                title={`Question is assigned to ${question.assignee_name}`}
                enterDelay={250}
              >
                <Avatar
                  src={assignedUser.picture}
                  sx={{ height: 24, width: 24 }}
                >
                  {question.assignee_name}
                </Avatar>
              </PrimaryTooltip>
            )}
            {(!assignedUser.picture || assignedUser.picture == '') && (
              <PrimaryTooltip
                title={`Question is assigned to ${question.assignee_name}`}
                enterDelay={250}
              >
                <div>
                  <TribbleAvatar
                    username={question.assignee_name}
                    height="24px"
                    width="24px"
                  />
                </div>
              </PrimaryTooltip>
            )}
          </Box>
        ) : null;
      } else {
        return <></>;
      }
    }
  };

  const sendToSlackButton = () => {
    return showSlackButton ? (
      <Box>
        <Button
          onClick={() => {
            setSlackModalOpen(true);
          }}
          variant="outlined"
          size="small"
        >
          Loop in an Expert
        </Button>
      </Box>
    ) : (
      <div></div>
    );
  };

  const Lower = () => {
    if (!isEditMode && !isProcessing) {
      return (
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
          id="lower"
          gap="1rem"
          sx={{ m: m }}
        >
          {sendToSlackButton()}
          {props.isAccepted ? (
            <SecondaryTooltip
              title={
                'Answer marked as reviewed. Click to change back to not reviewed.'
              }
              enterDelay={250}
            >
              <Button
                variant="outlined"
                color="secondary"
                size="small"
                onClick={props.handleMarkAsReviewed}
                startIcon={<Check />}
                sx={{ minWidth: '150px ' }}
              >
                Reviewed
              </Button>
            </SecondaryTooltip>
          ) : (
            <PrimaryTooltip
              title={'Click to mark answer as reviewed'}
              enterDelay={250}
            >
              <Button
                variant="outlined"
                color="warning"
                size="small"
                onClick={props.handleMarkAsReviewed}
                sx={{ minWidth: '150px ' }}
              >
                Not Reviewed
              </Button>
            </PrimaryTooltip>
          )}
        </Stack>
      );
    } else {
      //big old delete button
      return (
        <Box sx={{ m: m }}>
          <Button
            color="error"
            onClick={() => setShowDelete(true)}
            variant="outlined"
            sx={{ maxWidth: '100%' }}
          >
            Delete Question
          </Button>
        </Box>
      );
    }
  };

  const showConfidenceChip = hasConfidence && !isModified;

  const confidenceExplanation =
    question?.output_original?.confidence_score_explanation;

  let itemsToReview;
  if (question?.output_original?.items_to_review) {
    itemsToReview = Array.isArray(question?.output_original?.items_to_review)
      ? question?.output_original?.items_to_review.join('\n- ')
      : question?.output_original?.items_to_review;
  }

  const isAssigned = props.question.assignee_name != undefined;

  // Handle verbatim sources
  const verbatimSources = QA_UTILS.verbatimSources(
    question.output_original,
    metadataFilters.data?.metadata_filters ?? [],
  );

  const VerbatimSection = () => {
    return (
      <Stack
        direction="column"
        sx={{
          p: '12px',
          mt: 3,
          borderRadius: 1,
          backgroundColor: theme.palette.grey[600] + '20',
        }}
      >
        <Stack direction="row">
          <Typography variant="body1" color="primary" sx={{ fontWeight: 500 }}>
            Verbatim Sources
          </Typography>
          <Avatar
            sx={{
              ml: 1,
              mt: '-2px',
              width: '22px',
              height: '22px',
              fontSize: '12px',
              fontWeight: 600,
              color: theme.palette.primary.main,
              bgcolor: theme.palette.primary.main + '20',
            }}
          >
            {verbatimSources.length}
          </Avatar>
        </Stack>
        <Typography
          variant="body2"
          sx={{ color: theme.palette.grey[500], mt: '4px', mb: 1 }}
        >
          The following sources were retrieved during the answer process; these
          sources are marked verbatim, so you may want to use their text
          directly, instead of the AI generated answer.
        </Typography>

        <Stack direction="column" sx={{ maxHeight: '30vh', overflowY: 'auto' }}>
          {verbatimSources.map((source, idx) => (
            <Stack
              direction="column"
              key={`source_${idx + 1}`}
              sx={{
                mt: '1rem',
                backgroundColor: 'white',
                borderRadius: 1,
                p: '12px',
              }}
              gap="1rem"
            >
              <SourceCard
                source={source}
                type={source.type == 'qa' ? 'RFP' : 'DOC'}
                disableFade={true}
                hideIcon={true}
              />
              <Button
                size="small"
                variant="outlined"
                color="primary"
                onClick={() => props.handleUseAsAnswer(source.text)}
                sx={{ width: 'fit-content' }}
              >
                Use as Answer
              </Button>
            </Stack>
          ))}
        </Stack>
      </Stack>
    );
  };

  return (
    <Card variant="outlined" sx={{ maxWidth: '100%', height: '100%' }}>
      <Stack
        id="container"
        direction={'column'}
        justifyContent={'space-between'}
        height="100%"
      >
        <Box id="upper" sx={{ m: m }}>
          <PageHeaderBar
            disableBackButton={props.mode == 'edit'}
            onClickBack={navBack}
            tooltipMessage="Go back to questionnaire"
          >
            {HeaderButtons()}
          </PageHeaderBar>

          <SendToSlackModal
            handleClose={() => setSlackModalOpen(false)}
            open={slackModalOpen}
            question={question}
          />

          <Box sx={{ mb: m }}></Box>

          {groupingsDefined && Grouping()}

          <Title
            title={questionText}
            sx={{ maxHeight: '20vh', overflow: 'auto', whiteSpace: 'pre-wrap' }}
          />

          <Box sx={{ my: m, maxHeight: '45vh', overflow: 'auto' }}>
            {AnswerText()}
          </Box>

          {!isEditMode && !isProcessing && (
            <>
              <Stack direction="row" justifyContent="flex-end">
                {Assignee()}
              </Stack>
              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
                sx={{ mt: '1rem', mb: '2rem' }}
              >
                <Stack direction="row" gap="0.5rem" alignItems="center">
                  {showConfidenceChip && (
                    <ConfidenceChip
                      score={question.output_original.confidence_score}
                      couldNotAnswer={couldNotAnswer}
                      explanation={
                        (confidenceExplanation
                          ? `EXPLANATION\n\n${confidenceExplanation}`
                          : '') +
                        (itemsToReview
                          ? `\n\nITEMS TO REVIEW\n\n- ${itemsToReview}`
                          : '')
                      }
                    />
                  )}
                  {isModified && <ModifiedChip question={question} />}
                  {translatedText && showTranslatedIcon && (
                    <GreyTooltip
                      title={`Translated from:\n\n${translatedSourceText}`}
                    >
                      <LanguageIcon
                        fontSize="small"
                        sx={{ mr: 1, color: theme.palette.grey[500] }}
                      />
                    </GreyTooltip>
                  )}
                </Stack>

                <Stack direction="row" gap="0.5rem" alignItems="center">
                  <CircularIconButton
                    size="small"
                    disableBorder={true}
                    ariaLabel="Edit this answer"
                    color="primary"
                    Icon={EditIcon}
                    onClick={() => {
                      setNewAnswer(answerNoCitations);
                      props.handleEditMode();
                    }}
                  />
                  <PrimaryTooltip
                    title={'Copy the answer to your clipboard'}
                    enterDelay={250}
                  >
                    <div>
                      <CopyButton
                        size="small"
                        textToCopy={answerNoCitations}
                        disableBorder={true}
                        color="primary"
                      />
                    </div>
                  </PrimaryTooltip>
                </Stack>
              </Stack>
            </>
          )}

          {isEditMode && Assignee()}

          {!isProcessing && !isEditMode && usedCitationIndexes.length > 0 ? (
            <>
              <SourcesButton
                onClick={props.showSources}
                numSources={usedCitationIndexes.length}
              />
              {!isModified && privateDocs.length > 0 && (
                <Typography
                  variant="body2"
                  color="error"
                  sx={{ p: '12px', mb: 2 }}
                >
                  {privateSourceWarningMessage}
                </Typography>
              )}
              {!isModified && usesCallTranscript && (
                <Typography
                  variant="body2"
                  color="error"
                  sx={{ p: '12px', mb: 2 }}
                >
                  ⚠ WARNING: Answer cites a call transcript as a source.
                </Typography>
              )}
            </>
          ) : !isModified ? (
            <Typography variant="body2" color="error">
              (no sources were retrieved)
            </Typography>
          ) : null}
          {!isModified && verbatimSources.length > 0 ? VerbatimSection() : null}
        </Box>
        {Lower()}
      </Stack>
      {DeleteConfirmation}
    </Card>
  );
};
