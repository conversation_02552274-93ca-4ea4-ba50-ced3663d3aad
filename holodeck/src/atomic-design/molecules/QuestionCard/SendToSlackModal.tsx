import {
  Chip,
  FormControl,
  FormHelperText,
  MenuItem,
  Stack,
  Typography,
} from '@mui/material';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import { useTheme } from '@mui/system';
import React, { FC, useState } from 'react';

import ConfirmationModal from '../../../components/ConfirmationModal';
import { QuestionDto } from '../../../popup/api/question.api';
import { sendToSlackGetHelp } from '../../../popup/api/slack.api';
import { StyledTextArea } from '../../../popup/pages/HomePage/pages/ViewQuestionnaireAddQuestions/styledComponents';
import { useRootStore } from '../../../popup/state/useRootStore';

interface Props {
  handleClose: () => void;
  open: boolean;
  question: QuestionDto;
}
export const SendToSlackModal: FC<Props> = (props) => {
  const theme = useTheme();
  const rootStore = useRootStore();

  const [note, setNote] = useState('');
  const [collabChannel, setCollabChannel] = useState('');
  const [error, setError] = useState('');

  const allCollabChannels =
    rootStore.slackSettings?.collaboration_channel ?? [];

  const handleSend = async () => {
    if (allCollabChannels.length > 1 && !collabChannel) {
      setError('Please select a channel');
      return;
    }

    // There should be at least one channel available in allCollabChannels
    const destinationChannel =
      collabChannel !== '' ? collabChannel : allCollabChannels?.[0]?.id;

    const noteText = note;
    const content_detail_id = props.question.id;
    const result = await sendToSlackGetHelp(
      content_detail_id,
      noteText,
      destinationChannel,
    );
    if (result.success) {
      rootStore.setNotification({
        body: 'Sent to Slack',
        header: 'Ok',
        type: 'success',
      });
      setNote('');
      setCollabChannel('');
      props.handleClose();
    } else {
      const errorMessage = `Sending to Slack failed: ${
        (result as any).error || ''
      }`;
      rootStore.setNotification({
        header: 'Oops',
        body: errorMessage,
        type: 'error',
      });
    }
  };

  return (
    <ConfirmationModal
      fullWidth={true}
      cancelText="Cancel"
      confirmText="Send Message"
      handleCancel={() => {
        setNote('');
        setCollabChannel('');
        setError('');
        props.handleClose();
      }}
      handleConfirm={handleSend}
      message="Need assistance from colleagues to answer this question? Describe what you need help with, then click Send to post a message to Slack."
      open={props.open}
      title="Loop in an Expert"
    >
      <Stack gap="1rem">
        <StyledTextArea
          sx={{
            border: `2px solid ${theme.palette.primary.main + '50'}`,
            mt: 2,
            p: '4px 8px',
            borderRadius: '8px',
            fontWeight: '400',
            width: '100%',
            overflowY: 'initial !important',
          }}
          minRows={4}
          maxRows={15}
          value={note}
          placeholder="What do you need help with?"
          onChange={(e) => {
            setNote(e.target.value);
          }}
        />

        {allCollabChannels.length > 1 && (
          <>
            <Typography variant="body2">
              Select a channel to post to:
            </Typography>
            <FormControl error={error !== ''}>
              <Select
                labelId="select-collab-channel"
                label=""
                size="small"
                value={collabChannel}
                onChange={(e: SelectChangeEvent) => {
                  setCollabChannel(e.target.value);
                  setError('');
                }}
                renderValue={(selected) => {
                  const selectedChannel = allCollabChannels.find(
                    (channel) => String(channel.id) == selected,
                  );

                  return selectedChannel ? (
                    <Chip
                      color="primary"
                      variant="outlined"
                      label={`#${selectedChannel?.name}`}
                      onDelete={() => setCollabChannel('')}
                      onMouseDown={(e) => e.stopPropagation()}
                      sx={{ height: '26px' }}
                    />
                  ) : null;
                }}
                MenuProps={{
                  sx: {
                    maxHeight: '400px',
                  },
                  anchorOrigin: {
                    vertical: 'bottom',
                    horizontal: 'center',
                  },
                }}
              >
                {allCollabChannels
                  .sort((a, b) =>
                    a.name.toLowerCase() < b.name.toLowerCase() ? -1 : 1,
                  )
                  .map((channel) => (
                    <MenuItem value={channel.id} key={channel.id}>
                      #{channel.name}
                    </MenuItem>
                  ))}
              </Select>
              {error !== '' && <FormHelperText>{error}</FormHelperText>}
            </FormControl>
          </>
        )}

        <Typography variant="body2">
          Note: the question and answer will be included in the Slack message.
        </Typography>
      </Stack>
    </ConfirmationModal>
  );
};
