import { TreeSearchNode, TreeSearchOptionNode } from './types';

/**
 * This function takes a list of selected IDs and a list of available options, and returns a flat array of matching options.
 * @param selectedIds The IDs of the options that are selected.
 * @param availableOptions The options that are available to select from.
 * @returns A flat array of matching options that are selected. Does NOT match groups.
 */
export const getMatchingTreeNodes = (
  selectedIds: string[],
  availableOptions: TreeSearchNode[],
): TreeSearchOptionNode[] => {
  // Dictionaries have uniqueness-detecting properties, which we're using here.
  const results: Record<string, TreeSearchOptionNode> = {};

  // Loop over the available options.
  availableOptions.forEach((option) => {
    if (option.type === 'group') {
      // Ah, it's a group! We want to match its kids. Let's recurse! ➰
      const matchingOptions = getMatchingTreeNodes(
        selectedIds,
        option.children || [],
      );

      // Then, we put all the matching options into the dictionary.
      matchingOptions.forEach((matchingOption) => {
        // We're using the option's ID as the key.
        // If there is a duplicate, the last one wins.
        results[matchingOption.id] = matchingOption;
      });
    }
    if (option.type === 'option') {
      // It's an option! Let's see if it's selected.
      if (selectedIds.includes(option.id)) {
        // It is! Let's put it in the dictionary.
        results[option.id] = option;
      }
    }
  });

  // It's time to say goodbye to the dictionary...
  // ...and hello to the flat array that we promised to return.
  return Object.entries(results).map(([, option]) => option);
};
