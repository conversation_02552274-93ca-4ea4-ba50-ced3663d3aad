import { Box, Checkbox, Typography, useTheme } from '@mui/material';
import React, { FC } from 'react';
import { TreeSearchNode } from './types';

export type TreeSearchSelectNodeProps = {
  treeNode: TreeSearchNode;
  selectedValues: string[];
  onChange: (newValue: string[]) => void;
};
export const TreeSearchSelectNode: FC<TreeSearchSelectNodeProps> = ({
  treeNode,
  selectedValues,
  onChange,
}) => {
  const theme = useTheme();

  const handleToggle = (e: React.ChangeEvent<HTMLInputElement>, id: string) => {
    e.preventDefault();
    if (e.target.checked) {
      onChange([...selectedValues, id]);
    } else {
      onChange(selectedValues.filter((value) => value !== id));
    }
  };

  if (treeNode.type === 'option') {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: '0' }}>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            gap: '0.5rem',
            alignItems: 'center',
          }}
        >
          <Checkbox
            sx={{ padding: 0, margin: 0 }}
            checked={!!selectedValues.includes(treeNode.id)}
            onChange={(e) => handleToggle(e, treeNode.id)}
          />
          <Typography
            variant="body1"
            sx={{ fontWeight: '300', fontSize: '0.9rem', lineHeight: '1.1rem' }}
          >
            {treeNode.label}
          </Typography>
        </Box>
      </Box>
    );
  }

  if (treeNode.type === 'group') {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: '0' }}>
        <Typography
          variant="body1"
          sx={{
            fontWeight: '400',
            fontSize: '0.9rem',
            lineHeight: '1.1rem',
            color: theme.palette.grey[500],
            textTransform: 'uppercase',
            mt: '0.5rem',
          }}
        >
          {treeNode.label}
        </Typography>
        {treeNode.children.map((child) => (
          <Box
            sx={{ pl: '0.5rem', pt: '0.25rem' }}
            key={`${child.id}-${Math.random()}`}
          >
            <TreeSearchSelectNode
              treeNode={child}
              selectedValues={selectedValues}
              onChange={onChange}
            />
          </Box>
        ))}
      </Box>
    );
  }

  // if neither a group nor an option, this is an error
  console.error('TreeSearchSelectNode: unknown node type', treeNode);
  return <></>;
};
