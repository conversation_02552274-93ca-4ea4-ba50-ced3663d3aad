import { SyntheticEvent } from 'react';

type DefaultPreventable = SyntheticEvent | Event;
export function preventDefault<
  T extends (event: DefaultPreventable) => void = any,
>(fn: T): T {
  return ((event: DefaultPreventable) => {
    event.preventDefault();
    fn(event);
  }) as T;
}

// export const preventDefault = <T, U>(e: T extends DefaultPreventableFunction) => (cb: U) => {
//     e.preventDefault();
//     return cb(e);
// }
