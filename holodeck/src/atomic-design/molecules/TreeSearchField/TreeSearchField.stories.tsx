import type { <PERSON>a, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { TreeSearchField } from './TreeSearchField';
import { TreeSearchNode } from './types';

const meta = {
  title: 'Holodeck/Molecules/TreeSearchField',
  component: TreeSearchField,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/react/configure/story-layout
    layout: 'centered',
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/react/writing-docs/autodocs
  tags: ['autodocs'],
  decorators: [
    (Story) => {
      const [selectedIds, setSelectedIds] = useState<string[]>([]);

      const options: TreeSearchNode[] = [
        {
          id: 'technology',
          label: 'Technology',
          type: 'group',
          children: [
            // json, python
            { id: 'json', label: 'JSON', type: 'option' },
            { id: 'python', label: 'Python', type: 'option' },
          ],
        },
        {
          id: 'system',
          label: 'System',
          type: 'group',
          children: [
            // os, windows
            { id: 'os', label: 'OS', type: 'option' },
            { id: 'windows', label: 'Windows', type: 'option' },
          ],
        },
      ];

      return (
        <Story
          args={{
            options: options,
            selectedValues: selectedIds,
            onChange: (newIds: string[]) => setSelectedIds(newIds),
          }}
        />
      );
    },
  ],
} satisfies Meta<typeof TreeSearchField>;
export default meta;

type Story = StoryObj<typeof meta>;

export const Simple: Story = {
  args: {
    options: [
      {
        id: 'technology',
        label: 'Technology',
        type: 'group',
        children: [
          // json, python
          { id: 'json', label: 'JSON', type: 'option' },
          { id: 'python', label: 'Python', type: 'option' },
        ],
      },
      {
        id: 'system',
        label: 'System',
        type: 'group',
        children: [
          // os, windows
          { id: 'os', label: 'OS', type: 'option' },
          { id: 'windows', label: 'Windows', type: 'option' },
        ],
      },
    ],
    selectedValues: [],
    onChange: () => {},
  },
  // args: {
  //   options: ,
  //   selectedValues: ["json", "os"],

  // }
};
