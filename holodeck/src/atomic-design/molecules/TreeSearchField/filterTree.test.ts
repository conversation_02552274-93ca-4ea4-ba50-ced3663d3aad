import { filterTree } from './filterTree';
import { TreeSearchNode } from './types';

describe('filterNodes', () => {
  const sampleNodes: TreeSearchNode[] = [
    { id: '1', label: 'Apple', type: 'option' },
    { id: '2', label: '<PERSON>ana', type: 'option' },
    {
      id: '3',
      label: 'Fruits',
      type: 'group',
      children: [
        { id: '4', label: 'Orange', type: 'option' },
        { id: '5', label: 'Pear', type: 'option' },
      ],
    },
    {
      id: '6',
      label: 'Electronics',
      type: 'group',
      children: [
        { id: '7', label: 'Phone', type: 'option' },
        { id: '8', label: 'Laptop', type: 'option' },
      ],
    },
  ];

  it('should return the same nodes when the search term is empty', () => {
    expect(filterTree(sampleNodes, '')).toEqual(sampleNodes);
  });

  it('should return nodes that match the search term directly', () => {
    expect(filterTree(sampleNodes, 'Apple')).toEqual([
      { id: '1', label: 'Apple', type: 'option' },
    ]);
  });

  it('should return parent nodes when their children match the search term', () => {
    const result = filterTree(sampleNodes, 'Orange');
    const expected = [
      {
        id: '3',
        label: 'Fruits',
        type: 'group',
        children: [{ id: '4', label: 'Orange', type: 'option' }],
      },
    ];
    expect(result).toEqual(expected);
  });

  it('should not return parent nodes if none of their children match', () => {
    const result = filterTree(sampleNodes, 'Monitor');
    expect(result).toEqual([]);
  });

  it('should handle case-insensitive search', () => {
    const result = filterTree(sampleNodes, 'aPplE');
    expect(result).toEqual([{ id: '1', label: 'Apple', type: 'option' }]);
  });

  it('should handle search terms with leading or trailing whitespaces', () => {
    const result = filterTree(sampleNodes, '  apple  ');
    expect(result).toEqual([{ id: '1', label: 'Apple', type: 'option' }]);
  });
});
