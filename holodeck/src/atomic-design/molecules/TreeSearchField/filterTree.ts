import { TreeSearchNode } from './types';

/**
 * This handy dandy function filters all the nice nodes and their kids based on the search term.
 * @param node A list of nodes to filter.
 * @param searchTerm The search term to filter the nodes by.
 * @returns A tree of nodes that match the search term.
 */
export const filterTree = (
  node: TreeSearchNode[],
  searchTerm: string,
): TreeSearchNode[] => {
  // Give the searchTerm a haircut.
  const lowercasedSearchTerm = searchTerm.trim().toLowerCase();

  // If the searchTerm is empty, do nothing.
  if (!lowercasedSearchTerm) {
    return node;
  }

  // otherwise, filter the nodes.
  const matchingNodes: TreeSearchNode[] = [];

  node.forEach((node) => {
    // if the label matches the search term, add it to the filteredNodes as-is.
    if (node.label.toLowerCase().includes(lowercasedSearchTerm)) {
      matchingNodes.push(node);
      return;
    }

    // Groups might have kids that match the search term.
    if (node.type !== 'group') {
      // Nope, it's not a group. There's no more work to be done here.
      return;
    }

    // This is a group. Maybe its kids contain a match.... Let's pull out all the matching kids.
    const matchingKids = filterTree(
      node.children,
      searchTerm, // principal of least surprise - we're reusing the same searchTerm.
    );

    // Did any kids match?
    if (!matchingKids.length) {
      // Nope. We're done here.
      return;
    }

    // Looks like at least one kid matched, which means this node is a match, too.
    // Add it to the filteredNodes, with only the matching kids.
    // (It would be weird if non-matching kids showed up in the search results, right?)
    matchingNodes.push({ ...node, children: matchingKids });
  });

  // finally, return the filteredNodes.
  return matchingNodes;
};
