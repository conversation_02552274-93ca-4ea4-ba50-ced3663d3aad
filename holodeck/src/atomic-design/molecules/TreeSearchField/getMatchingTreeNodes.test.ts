import { getMatchingTreeNodes } from './getMatchingTreeNodes';
import { TreeSearchNode } from './types';

describe('getMatchingTreeNodes', () => {
  let sampleNodes: TreeSearchNode[] = [
    {
      id: '1',
      type: 'group',
      label: 'Foo Group',
      children: [
        {
          id: '2',
          type: 'option',
          label: 'Bar Option',
        },
        {
          id: '3',
          type: 'option',
          label: 'Baz Thingie',
        },
      ],
    },
  ];
  it('should return an empty array if there are no selectedIds', () => {
    expect(getMatchingTreeNodes([], [])).toEqual([]);
  });

  it('should return the matching options', () => {
    expect(getMatchingTreeNodes(['3'], sampleNodes)).toEqual([
      {
        id: '3',
        type: 'option',
        label: 'Baz Thingie',
      },
    ]);
  });

  it('should return multiple matching options', () => {
    expect(getMatchingTreeNodes(['2', '3'], sampleNodes)).toEqual([
      {
        id: '2',
        type: 'option',
        label: 'Bar Option',
      },
      {
        id: '3',
        type: 'option',
        label: 'Baz Thingie',
      },
    ]);
  });

  it('should not return groups, even if they match', () => {
    expect(getMatchingTreeNodes(['1'], sampleNodes)).toEqual([]);
  });
});
