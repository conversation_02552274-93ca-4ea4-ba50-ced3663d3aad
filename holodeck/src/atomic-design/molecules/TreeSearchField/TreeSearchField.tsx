import CancelSearchIcon from '@mui/icons-material/Close';
import SearchIcon from '@mui/icons-material/Search';
import {
  Box,
  FormControl,
  IconButton,
  InputAdornment,
  OutlinedInput,
} from '@mui/material';
import React, { FC, SyntheticEvent, useRef, useState } from 'react';
import { TreeSearchSelectNode } from './TreeSearchSelectNode';
import { filterTree } from './filterTree';
import { TreeSearchNode } from './types';

export type TreeSearchFieldProps = {
  options: TreeSearchNode[];
  selectedValues: string[];
  onChange: (value: string[]) => void;
  hideSearchBar?: boolean;
};
export const TreeSearchField: FC<TreeSearchFieldProps> = (props) => {
  const inputRef = useRef<HTMLElement>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');

  const { options, selectedValues, onChange } = props;

  const filteredTree = searchQuery ? filterTree(options, searchQuery) : options;

  const handleSearchQueryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handleSearchIconClick = (e: SyntheticEvent) => {
    inputRef.current?.click();
  };

  const handleCancelSearchIconClick = (e: SyntheticEvent) => {
    setSearchQuery('');
  };

  const handleCheckboxClick = (newSelectedValues: string[]) => {
    onChange(newSelectedValues);
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
      {props.hideSearchBar !== true && (
        <FormControl sx={{ flexGrow: 1, mb: '0.5rem' }}>
          <OutlinedInput
            size="small"
            placeholder="Search"
            value={searchQuery}
            onChange={handleSearchQueryChange}
            ref={inputRef}
            startAdornment={
              <InputAdornment position="start">
                <IconButton
                  edge="start"
                  disableRipple
                  sx={{
                    '&:hover': { backgroundColor: 'inherit' },
                    cursor: 'default',
                  }}
                  onClick={handleSearchIconClick}
                >
                  <SearchIcon />
                </IconButton>
              </InputAdornment>
            }
            endAdornment={
              <InputAdornment position="end">
                <IconButton
                  aria-label="clear search query"
                  onClick={handleCancelSearchIconClick}
                  edge="end"
                >
                  {searchQuery && <CancelSearchIcon />}
                </IconButton>
              </InputAdornment>
            }
          />
        </FormControl>
      )}

      {filteredTree.map((child) => (
        <TreeSearchSelectNode
          treeNode={child}
          selectedValues={selectedValues}
          onChange={handleCheckboxClick}
          key={`${child.id}-${Math.random()}`}
        />
      ))}
    </Box>
  );
};
