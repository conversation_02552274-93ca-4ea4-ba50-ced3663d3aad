import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { useTheme } from '@mui/material';
import React, { FC } from 'react';
import { Chippy } from './BaseChip';
interface ChipParams {
  score: number | null;
  couldNotAnswer: boolean;
  explanation?: string;
  endIcon?: React.ReactElement<any>;
}
export const ConfidenceChip: FC<ChipParams> = (props) => {
  const { score, couldNotAnswer, explanation } = props;
  if (isNaN(score) || score == null) return;

  const theme = useTheme();

  let confidenceScoreScaled = score || 0;
  confidenceScoreScaled =
    confidenceScoreScaled > 3
      ? 3
      : confidenceScoreScaled < 0
        ? 0
        : confidenceScoreScaled;

  const confidenceScoreColorArr = [
    theme.palette.error.main,
    theme.palette.error.main,
    theme.palette.warning.main,
    theme.palette.success.main,
  ];
  const confidenceScoreColor = confidenceScoreColorArr[confidenceScoreScaled];
  const confidenceScoreLabel = couldNotAnswer
    ? 'Could not answer'
    : ['', 'Low Confidence', 'Medium Confidence', 'High Confidence'][
        confidenceScoreScaled
      ];

  if (confidenceScoreLabel != '') {
    return (
      <Chippy
        colorHex={confidenceScoreColor}
        label={confidenceScoreLabel}
        tooltip={explanation}
        endIcon={
          confidenceScoreLabel !== 'High Confidence' ? (
            <InfoOutlinedIcon
              fontSize="small"
              sx={{ fill: confidenceScoreColor }}
            />
          ) : undefined
        }
      />
    );
  } else {
    return <></>;
  }
};
