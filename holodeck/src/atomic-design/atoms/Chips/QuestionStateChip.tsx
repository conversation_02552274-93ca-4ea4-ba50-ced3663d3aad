import { Stack, Theme, useTheme } from '@mui/material';
import React, { FC } from 'react';
import {
  ContentDetailState,
  QuestionDto,
} from '../../../popup/api/question.api';
import * as QA_UTILS from '../../../popup/utils/qa';
import { SecondaryTooltip } from '../StyledTooltips';
import { Chippy } from './BaseChip';

export const ModifiedChip = ({ question }: { question: QuestionDto }) => {
  const theme = useTheme();

  const modifiedBy = question.modified_by_name;

  return (
    <SecondaryTooltip
      title={`Modified by ${question.modified_by_name} (at ${
        question.modified_at
          ? new Date(question.modified_at).toLocaleString()
          : 'unknown date'
      })`}
    >
      <div>
        <Chippy
          label={
            modifiedBy
              ? `Modified by: ${
                  modifiedBy.length > 20
                    ? modifiedBy.slice(0, 20) + '...'
                    : modifiedBy
                }`
              : 'Modified'
          }
          colorHex={theme.palette.secondary.main}
        />
      </div>
    </SecondaryTooltip>
  );
};

const StatusChip = ({
  state,
  showToggle,
}: {
  state: ContentDetailState;
  showToggle?: boolean;
}) => {
  const theme = useTheme();

  const toggleSuffix = showToggle ? ' ▾' : '';

  switch (state) {
    case 'processing':
      return;
    case 'accepted':
      return (
        <Chippy
          label={`Reviewed${toggleSuffix}`}
          colorHex={theme.palette.success.main}
        />
      );
    // the 'modified' state is shown as 'not reviewed'
    // when reviewed, the state would be 'accepted'
    case 'modified':
    case 'initial':
      return (
        <Chippy
          label={`Not Reviewed${toggleSuffix}`}
          colorHex={theme.palette.grey[600]}
        />
      );
    case 'rejected':
      return (
        <Chippy label={`Rejected`} colorHex={theme.palette.warning.main} />
      );
  }
};

interface ChipParams {
  state: ContentDetailState;
  question: QuestionDto;
  showToggle?: boolean;
}
export const QuestionStateChip: FC<ChipParams> = ({
  state,
  question,
  showToggle,
}) => {
  const answerIsModified = QA_UTILS.isModifiedAnswer(question);

  return (
    <Stack
      direction="row"
      alignItems="center"
      justifyContent="flex-start"
      gap={1}
    >
      {/* Modified chip is shown if answer is modified, regardless of state */}
      {answerIsModified && <ModifiedChip question={question} />}
      <StatusChip state={state} showToggle={showToggle} />
    </Stack>
  );
};
