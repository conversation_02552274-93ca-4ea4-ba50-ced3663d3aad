import { Chip, SxProps, useTheme } from '@mui/material';
import React, { FC } from 'react';
import { GreyTooltip } from '../StyledTooltips';

interface ChipProps {
  label: string;
  colorHex: string;
  sx?: SxProps;
  tooltip?: string;
  endIcon?: React.ReactElement<any>;
}

export const Chippy: FC<ChipProps> = ({
  label,
  colorHex: color,
  sx,
  tooltip,
  endIcon,
}) => {
  const theme = useTheme();
  const bgColor = color + '20';
  const elChip = endIcon ? (
    <Chip
      sx={{
        color: color,
        fontWeight: 500,
        backgroundColor: bgColor,
        ...sx,
      }}
      size="small"
      label={label}
      deleteIcon={endIcon}
    />
  ) : (
    <Chip
      sx={{
        color: color,
        fontWeight: 500,
        backgroundColor: bgColor,
        ...sx,
      }}
      size="small"
      label={label}
    />
  );

  if (tooltip) {
    return <GreyTooltip title={tooltip}>{elChip}</GreyTooltip>;
  }

  return elChip;
};
