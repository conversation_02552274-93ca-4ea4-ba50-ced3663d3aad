import { Button, MenuItem } from '@mui/material';
import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { MenuDropdown } from './MenuDropdown';

const meta = {
  title: 'Holodeck/Atoms/MenuDropdown',
  component: MenuDropdown,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/react/configure/story-layout
    layout: 'centered',
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/react/writing-docs/autodocs
  tags: ['autodocs'],
} satisfies Meta<typeof MenuDropdown>;

export default meta;

export const Simple: StoryObj<typeof meta> = {
  args: {
    trigger: <But<PERSON>>Click me to open the menu</Button>,
    children: (handleClose) => (
      <>
        <MenuItem onClick={handleClose}>Close menu</MenuItem>
        <MenuItem>Don't close menu</MenuItem>
      </>
    ),
  },
};
