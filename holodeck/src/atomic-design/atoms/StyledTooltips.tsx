import React from 'react';

import Tooltip, { TooltipProps, tooltipClasses } from '@mui/material/Tooltip';
import { styled } from '@mui/material/styles';

const commonStyles = {
  color: '#fff',
  maxWidth: 350,
  fontSize: 'max(0.9em, 14px)',
  fontWeight: 500,
  lineHeight: 1.4,
  padding: '10px',
  whiteSpace: 'pre-line',
};

export type TooltipVariant = 'primary' | 'secondary' | 'error' | 'grey';

export interface StyledTooltipProps extends Omit<TooltipProps, 'classes'> {
  variant?: TooltipVariant;
}

export const StyledTooltip = styled(
  ({ className, variant = 'primary', ...props }: StyledTooltipProps) => (
    <Tooltip {...props} classes={{ popper: className }} />
  ),
)(({ theme, variant = 'primary' }) => {
  const getBackgroundColor = () => {
    switch (variant) {
      case 'primary':
        return theme.palette.primary.main;
      case 'secondary':
        return theme.palette.secondary.main;
      case 'error':
        return theme.palette.error.main;
      case 'grey':
        return theme.palette.grey[700];
      default:
        return theme.palette.primary.main;
    }
  };

  const backgroundColor = getBackgroundColor();

  return {
    [`& .${tooltipClasses.tooltip}`]: {
      backgroundColor,
      ...commonStyles,
    },
    [`& .${tooltipClasses.arrow}`]: {
      color: backgroundColor,
      zIndex: 1000001,
    },
  };
});

// Keep existing exports for backward compatibility
export const PrimaryTooltip = (props: TooltipProps) => (
  <StyledTooltip variant="primary" {...props} />
);
export const SecondaryTooltip = (props: TooltipProps) => (
  <StyledTooltip variant="secondary" {...props} />
);
export const ErrorTooltip = (props: TooltipProps) => (
  <StyledTooltip variant="error" {...props} />
);
export const GreyTooltip = (props: TooltipProps) => (
  <StyledTooltip variant="grey" {...props} />
);
