import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { TribbleAvatar } from './TribbleAvatar';

const meta = {
  title: 'Holodeck/Atoms/TribbleAvatar',
  component: TribbleAvatar,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/react/configure/story-layout
    layout: 'centered',
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/react/writing-docs/autodocs
  tags: ['autodocs'],
} satisfies Meta<typeof TribbleAvatar>;
export default meta;

type Story = StoryObj<typeof meta>;

export const Simple: Story = {
  args: {
    username: '<PERSON>',
  },
};
