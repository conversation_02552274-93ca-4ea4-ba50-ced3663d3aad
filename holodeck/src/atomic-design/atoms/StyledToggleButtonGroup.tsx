import { styled, ToggleButtonGroup } from '@mui/material';

export const StyledToggleButtonGroup = styled(ToggleButtonGroup)(
  ({ theme }) => ({
    backgroundColor: theme.palette.grey[300],
    borderRadius: '16px',
    '& .MuiToggleButton-root': {
      borderRadius: '16px !important',
      border: '0',
      margin: '3px',
    },
    '& .MuiToggleButton-root.Mui-selected': {
      backgroundColor: '#fff',
      color: theme.palette.primary.main,
    },
    '& .MuiToggleButton-root.Mui-selected:hover': {
      backgroundColor: '#fff',
      color: theme.palette.primary.main,
    },
  }),
);
