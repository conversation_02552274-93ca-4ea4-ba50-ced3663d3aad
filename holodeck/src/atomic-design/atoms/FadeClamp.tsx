import { Typography as Type } from '@mui/material';
import { SxProps } from '@mui/material/styles';
import { Variant } from '@mui/material/styles/createTypography';
import React, { FC, useState } from 'react';

interface FadeClampProps {
  text: string;
  numLines?: number;
  variant: Variant;
  showLineBreaks?: boolean;
  defaultFade?: boolean;
}

export const FadingText: FC<FadeClampProps> = (props) => {
  const [fade, setFade] = useState(props.defaultFade ?? true);

  const minHeight = '4.5em';

  const numLines = props.numLines || 3;
  const sx: SxProps = {
    cursor: 'pointer',
    position: 'relative',
    display: '-webkit-box',
    WebkitLineClamp: `${numLines}`,
    WebkitBoxOrient: 'vertical',
    overflow: 'hidden',
    whiteSpace: props.showLineBreaks ? 'pre-line' : 'inherit',
    minHeight,
    '&::after': {
      content: '""',
      position: 'absolute',
      bottom: 0,
      left: 0,
      width: '100%',
      minHeight,
      height: '4.5em', // roughly the height of one line, you can adjust as needed
      backgroundImage: 'linear-gradient(to top, white 0%, transparent 25%)',
    },
  };
  const toggleFade = () => {
    setFade(!fade);
  };

  if (fade)
    return (
      <Type onClick={toggleFade} variant={props.variant} sx={sx}>
        {props.text}
      </Type>
    );
  else
    return (
      <Type
        onClick={toggleFade}
        variant={props.variant}
        sx={{
          cursor: 'pointer',
          minHeight,
          whiteSpace: props.showLineBreaks ? 'pre-line' : 'inherit',
        }}
      >
        {props.text}
      </Type>
    );
};
