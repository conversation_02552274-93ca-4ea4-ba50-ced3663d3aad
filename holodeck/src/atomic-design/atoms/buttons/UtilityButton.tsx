import { Button, SvgIconProps, SxProps, useTheme } from '@mui/material';
import React, { FC } from 'react';

interface UtilityButtonParams {
  Icon?: React.ElementType<SvgIconProps>;
  onClick: () => void;
  label: string;
  disabled?: boolean;
}

export const UtilityButton: FC<UtilityButtonParams> = (params) => {
  const theme = useTheme();
  const buttonStyles: SxProps = {
    color: theme.palette.grey[600],
    width: '100%',
    borderColor: theme.palette.grey[600] + '30',
    fontWeight: '600',
    flexGrow: 0,
    '&:hover': {
      color: theme.palette.primary.main,
      borderColor: theme.palette.primary.main,
    },
  };
  if (params.Icon) {
    return (
      <Button
        size="small"
        sx={buttonStyles}
        variant="outlined"
        startIcon={<params.Icon fontSize="inherit" />}
        onClick={params.onClick}
        disabled={params.disabled}
      >
        {params.label}
      </Button>
    );
  } else {
    return (
      <Button
        size="small"
        sx={buttonStyles}
        variant="outlined"
        onClick={params.onClick}
        disabled={params.disabled}
      >
        {params.label}
      </Button>
    );
  }
};
