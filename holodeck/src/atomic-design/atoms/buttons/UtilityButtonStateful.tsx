import { CheckBox, CheckBoxOutlineBlank } from '@mui/icons-material/';
import { Button, SxProps, useTheme } from '@mui/material';
import React, { FC } from 'react';

interface UtilityButtonParams {
  onClick: () => void;
  label: string;
  checked: boolean;
  colorHexChecked?: string;
  colorHexNotChecked?: string;
}

export const UtilityButtonStateful: FC<UtilityButtonParams> = (params) => {
  const theme = useTheme();
  const notCheckedColor = params.colorHexNotChecked || theme.palette.grey[600];
  const checkedColor = params.colorHexChecked || theme.palette.grey[600];

  const color = params.checked ? checkedColor : notCheckedColor;

  const buttonStyles: SxProps = {
    color: color,
    width: '100%',
    borderColor: color + '30',
    fontWeight: 600,
    flexGrow: 0,
  };
  const icon = params.checked ? <CheckBox /> : <CheckBoxOutlineBlank />;
  return (
    <Button
      size="small"
      sx={buttonStyles}
      variant="outlined"
      startIcon={icon}
      onClick={params.onClick}
    >
      {params.label}
    </Button>
  );
};
