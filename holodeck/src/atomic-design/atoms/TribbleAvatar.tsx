import { Avatar, SxProps } from '@mui/material';
import React, { FC } from 'react';

type TribbleAvatar = {
  username?: string;
  height?: string;
  width?: string;
};

export const TribbleAvatar: FC<TribbleAvatar> = (props) => {
  const username = props.username || '';
  const height = props.height || '1.5rem';
  const width = props.width || '1.5rem';
  const initial = username?.[0]?.toUpperCase() || '';

  // Choose color deterministically based on username
  // https://stackoverflow.com/a/66494926/1204556
  const acceptedColors = [0, 20, 70, 120, 180, 240, 270, 300];
  const colorValue = username
    ? acceptedColors[
        username.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) %
          acceptedColors.length
      ]
    : 'grey.500';

  const sx: SxProps = {
    backgroundColor: `hsl(${colorValue}, 80%, 40%, 0.15)`,
    color: `hsl(${colorValue}, 80%, 40%)`,
    fontWeight: 'bold',
    fontSize: '0.95rem',
    height,
    width,
  };
  return initial ? (
    <Avatar sx={sx}>{initial}</Avatar>
  ) : (
    <Avatar sx={{ backgroundColor: 'grey.700' }} />
  );
};
