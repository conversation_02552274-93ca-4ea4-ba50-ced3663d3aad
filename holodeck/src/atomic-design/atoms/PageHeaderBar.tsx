import React, { ComponentProps, FC } from 'react';

import LeftIcon from '@mui/icons-material/ArrowBack';
import { Box, Stack, Typography } from '@mui/material';
import styled from '@mui/material/styles/styled';
import { GreyTooltip } from './StyledTooltips';

const ParentContainer = styled(Stack)(({ theme }) => ({
  alignItems: 'center',
  flexDirection: 'row',
  borderBottomWidth: '1px',
  borderBottomStyle: 'solid',
  borderBottomColor: theme.palette.grey[300],
  paddingBottom: '1rem',
  justifyContent: 'space-between',
  width: '100%',
  maxWidth: '100%',
  // This oddly causes the Add Questions screen to jiggle / stutter in height
  // minHeight: theme.mixins.toolbar.height,
}));

type PageHeaderBarProps = {
  onClickBack: () => void;
  title?: string;
  tooltipMessage?: string;
  children?: React.ReactNode;
  disableBackButton?: boolean;
  parentContainerProps?: ComponentProps<typeof Stack>;
};

export const PageHeaderBar: FC<PageHeaderBarProps> = ({
  title,
  tooltipMessage,
  onClickBack,
  children,
  parentContainerProps,
  disableBackButton,
}) => {
  const showBackButton = !disableBackButton;

  return (
    <ParentContainer {...parentContainerProps}>
      {showBackButton && (
        <GreyTooltip title={tooltipMessage || 'Go back to previous screen'}>
          <LeftIcon sx={{ cursor: 'pointer' }} onClick={onClickBack} />
        </GreyTooltip>
      )}

      <Typography variant="h6" fontWeight="bold" minHeight="1rem">
        {title ? title : <>&nbsp;</>}
      </Typography>
      {/* Little hacky minHeight: 30px so that the page header renders the same height with or without action buttons*/}
      <Box
        sx={{
          flexDirection: 'row',
          flex: 1,
          alignItems: 'center',
          minHeight: '30px',
        }}
      >
        {children}
      </Box>
    </ParentContainer>
  );
};
