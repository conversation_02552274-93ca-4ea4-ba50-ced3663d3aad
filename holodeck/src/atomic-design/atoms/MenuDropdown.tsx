import Menu, { MenuProps } from '@mui/material/Menu';
import { alpha, styled } from '@mui/material/styles';
import * as React from 'react';
import { useRef } from 'react';

const StyledMenu = styled((props: MenuProps) => (
  <Menu
    elevation={0}
    disableAutoFocusItem
    anchorOrigin={{
      vertical: 'bottom',
      horizontal: 'right',
    }}
    transformOrigin={{
      vertical: 'top',
      horizontal: 'right',
    }}
    {...props}
  />
))(({ theme }) => ({
  '& .MuiPaper-root': {
    borderRadius: 6,
    marginTop: theme.spacing(1),
    minWidth: 180,
    color:
      theme.palette.mode === 'light'
        ? 'rgb(55, 65, 81)'
        : theme.palette.grey[300],
    boxShadow:
      'rgb(255, 255, 255) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px',
    '& .MuiMenu-list': {
      padding: '4px 0',
    },
    '& .MuiMenuItem-root': {
      '& .MuiSvgIcon-root': {
        fontSize: 18,
        color: theme.palette.text.secondary,
        marginRight: theme.spacing(1.5),
      },
      '&:active': {
        backgroundColor: alpha(
          theme.palette.primary.main,
          theme.palette.action.selectedOpacity,
        ),
      },
    },
  },
}));

type MenuDropdownProps = {
  /**
   * The element that will trigger the dropdown. The menu will be anchored to this element. The menu will open when this element is clicked
   * and close when the user clicks outside of the menu.
   */
  trigger: React.ReactElement;
  /**
   * The child of the MenuDropdown element should be a function that takes a function that closes the menu, and returns the menu items to be
   * displayed in the dropdown.
   */
  children: (
    closeMenu: (event?: React.MouseEvent<HTMLElement>) => void,
  ) => React.ReactNode;
  disableTrigger?: boolean;
  preventDefault?: boolean;
};

/**
 * A dropdown menu that can be used in the popup. The menu will open when the trigger element is clicked, and close when the user clicks outside.
 * The menu items are passed in as children in a HOC that exposes controls for actions like closing the menu. More controls can be added to this HOC
 * in the future.
 *
 * @example
 *
 * ```javascript
 * const Example = () => {
 *   // define a trigger button component
 *   const TriggerButton = () => <Button>Open</Button>;
 *
 *   return (
 *     // then, inside the return of a component, use the MenuDropdown component
 *     <MenuDropdown trigger={<TriggerButton />}>
 *       {(handleClose) => (
 *         <>
 *           <MenuItem onClick={handleClose} disableRipple>
 *             Close
 *           </MenuItem>
 *         </>
 *       )}
 *     </MenuDropdown>
 *   )
 * }
 * ```
 */
export const MenuDropdown: React.FC<MenuDropdownProps> = ({
  trigger,
  children,
  disableTrigger,
  preventDefault,
}) => {
  const menuId = useRef('menu-id-' + Math.random());
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const isOpen = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    if (disableTrigger) return;
    setAnchorEl(event.currentTarget);

    if (preventDefault) {
      try {
        event.stopPropagation();
        event.preventDefault();
      } catch (err) {
        // no-op
      }
    }
  };
  const handleClose = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(null);

    if (preventDefault) {
      try {
        event.stopPropagation();
        event.preventDefault();
      } catch (err) {
        // no-op
      }
    }
  };

  return (
    <div>
      <div
        onClick={handleClick}
        aria-controls={isOpen ? menuId.current : undefined}
        aria-haspopup="true"
        aria-expanded={isOpen ? 'true' : undefined}
        style={{
          display: 'inline-block',
          width: 'fit',
          cursor: 'pointer',
        }}
      >
        {trigger}
      </div>

      <StyledMenu
        id={menuId.current}
        anchorEl={anchorEl}
        open={isOpen}
        onClose={handleClose}
      >
        {children(handleClose)}
      </StyledMenu>
    </div>
  );
};
