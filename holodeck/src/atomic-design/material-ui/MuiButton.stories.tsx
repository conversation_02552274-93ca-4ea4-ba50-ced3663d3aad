import { But<PERSON> } from '@mui/material';
import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';

const meta = {
  title: 'Holodeck/MaterialUI/MuiButton',
  component: Button,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/react/configure/story-layout
    layout: 'centered',
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/react/writing-docs/autodocs
  tags: ['autodocs'],
} satisfies Meta<typeof Button>;
export default meta;

type Story = StoryObj<typeof meta>;

export const Simple: Story = {
  args: {
    children: 'Click Me',
  },
};
