import { createTheme, Theme, ThemeOptions } from '@mui/material';

// Workaround for adding custom styles to typography. The new property has to be added in 5 places.
// Just Ctrl+F to see where each of these properties is used, and you'll probably understand how to use this.
// I have concluded that Typescript is basically a demonic pact that trades freedom for safety.
// Either that, or MUI's types were poorly designed. Or both. Probably both.
// taken from https://stackoverflow.com/a/70612746/1204556
declare module '@mui/material/styles' {
  interface TypographyVariants {
    tHeadline2: React.CSSProperties;
  }

  interface TypographyVariantsOptions {
    tHeadline2?: React.CSSProperties;
  }
}

declare module '@mui/material/Typography' {
  interface TypographyPropsVariantOverrides {
    tHeadline2: true;
  }
}

// =========== Begin Theme ===========

const $ = createTheme({
  palette: {
    primary: {
      main: '#3263E9',
      dark: '#174cbd',
      light: '#5071b8',
      contrastText: '#FFFFFF',
    },
    secondary: {
      main: '#357a38',
    },
    success: {
      main: '#36A248',
    },
    grey: {
      50: '#FFFFFF', // original from Figma
      100: '#FAFBFC',
      200: '#F5F7FB', // original from Figma
      300: '#EEEFF1', // original from Figma
      400: '#BDC4CB',
      500: '#8C96A3',
      600: '#737C8D', // original from Figma
      700: '#52586A',
      800: '#313445',
      900: '#00091A', // original from Figma
    },
  },
});
export const theme: Theme = createTheme($, {
  palette: $,
  mixins: {
    toolbar: {
      height: '32px',
    },
  },
  shape: {
    borderRadius: 8,
  },
  // shadows: Array(25).fill('none') as Theme['shadows'],
  components: {
    MuiAlert: {
      styleOverrides: {
        root: {
          borderRadius: 0,
        },
      },
    },
    MuiButton: {
      defaultProps: {
        variant: 'contained',
      },
      styleOverrides: {
        root: ({ ownerState }) => ({
          ...(ownerState.size == 'small' && {
            fontFamily: 'Inter',
            borderRadius: '8px',
          }),
          ...(ownerState.size == 'medium' && {
            textTransform: 'none',
            borderRadius: 100,
            width: '100%',
            maxWidth: '400px',
            height: '40px',
            padding: '12px',
            gap: '10px',

            fontFamily: 'Inter',
            fontWeight: 500,
            fontSize: '1rem',
            lineHeight: '1.2rem',
            letterSpacing: 0,
          }),
        }),
      },
    },
    MuiCardContent: {
      styleOverrides: {
        root: {
          '&:last-child': {
            paddingBottom: '1rem',
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          border: 0,
          boxShadow: '0px 1px 0px 0px ' + $.palette.grey[400] + '50',
        },
      },
    },
    MuiContainer: {
      defaultProps: {
        disableGutters: true,
      },
      styleOverrides: {
        root: {
          width: '100%',
          maxWidth: '100%',
          // Required for the container to be full width on larger widths
          [$.breakpoints.only('md')]: { maxWidth: '100%' },
          [$.breakpoints.only('lg')]: { maxWidth: '100%' },
          [$.breakpoints.only('xl')]: { maxWidth: '100%' },
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: ({ ownerState }) => ({
          fontSize: '12px',
          lineHeight: '16px',
          letterSpacing: '0.5px',
          color: $.palette.grey[600],
          fontWeight: 500,
          ...(ownerState.color === 'primary' && {
            backgroundColor: '#eaeffd',
            color: $.palette.primary.main,
          }),
          ...(ownerState.color === 'success' && {
            backgroundColor: '#eefaf0',
            color: $.palette.success.main,
          }),
        }),
      },
    },
    MuiDivider: {
      styleOverrides: {
        root: {
          borderColor: 'rgba(0, 0, 0, 0.07)',
        },
      },
    },
    MuiToggleButtonGroup: {
      defaultProps: {
        size: 'small',
      },
      styleOverrides: {
        root: {
          flexGrow: 1,
          justifyContent: 'space-between',
        },
      },
    },
    MuiFab: {
      defaultProps: {
        size: 'small',
        variant: 'circular',
      },
      styleOverrides: {
        root: {
          border: '1px solid ' + $.palette.grey[400],
          backgroundColor: $.palette.grey[200],
          // color: $.grey[400]
        },
      },
    },
    MuiToggleButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          flexGrow: 1,
          fontWeight: 'bold',
          // color: $.grey[400],
          // backgroundColor: $.grey[300],
          // border: '1px solid transparent',
          // '&:hover': {
          //     border: '1px solid transparent',
          //     borderLeft: '1px solid transparent',
          //     borderRight: '1px solid transparent',
          //     backgroundColor: $.grey[200]
          // },
          // '&.Mui-selected': {
          //     color: $.primary.main,
          //     backgroundColor: $.grey[50],
          //     border: '1px solid transparent',
          //     borderLeft: '1px solid transparent',
          //     borderRight: '1px solid transparent'
          // },
          // '&.Mui-selected:hover': {
          //     backgroundColor: $.grey[100],
          //     border: '1px solid transparent',
          //     borderLeft: '1px solid transparent',
          //     borderRight: '1px solid transparent'
          // },
        },
      },
    },
    MuiTypography: {
      defaultProps: {
        variantMapping: {
          tHeadline2: 'h1',
        },
      },
      styleOverrides: {
        root: {
          fontFamily: 'Inter',
          overflowWrap: 'anywhere',
          whiteSpace: 'normal',
        },
      },
    },
  },
  typography: {
    allVariants: {
      fontFamily: 'Inter',
    },
    tHeadline2: {
      fontWeight: 'bold',
    },
    h6: {
      fontWeight: 500,
      lineHeight: '1.15rem',
      fontSize: '1rem',
    },
    body1: {
      lineHeight: '1.1rem',
      fontSize: '0.9rem',
      fontWeight: 400,
    },
    body2: {
      color: $.palette.grey[600],
      lineHeight: '1rem',
      fontSize: '0.8rem',
      fontWeight: 400,
    },
    subtitle2: {
      color: $.palette.grey[600],
      fontWeight: 300,
      fontSize: '0.8rem',
    },
  },
} as ThemeOptions);
