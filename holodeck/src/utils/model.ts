import { AlertColor } from '@mui/material/Alert';

export interface CachedView {
  mode: string;
  params?: any;
  timestamp: number;
}

export interface AlertProps {
  message: string;
  severity: AlertColor;
}

export interface RFP {
  id: number;
  content_id: number;
  label: string;
  source: 'APP' | 'EXTENSION' | 'SLACK';
  is_active: boolean;
  num_questions: number;
  metadata_filter?: any;
  details?: any;
}

export interface ConfirmationModalBaseProps {
  title: string;
  message: string;
  confirmText: string;
  cancelText: string;
  handleConfirm: () => void;
  handleCancel: () => void;
  children?: any;
}

export interface ContentDetail {
  id: number;
  content_id: number;
  index: number;
  input: ContentInput;
  output_original?: ContentOutputOriginal;
  output_modified?: {
    text: string;
  };
  modified_by_id?: number;
  modified_by_name?: string;
  statistics?: {
    token_usage: TokenUsage;
    duration: { start: Date; end: Date };
  };
  state: 'processing' | 'initial' | 'accepted' | 'rejected' | 'modified';
}

export interface ContentInput {
  query_string: string;
  regenerated_query_string?: string;
  regenerated_extra_context?: string;
  meta?: object;
}

export interface ContentOutputOriginal {
  answer: string;
  answer_versions?: ContentOutputOriginal[];
  content_detail_id: number;
  documents: ContentDocumentSources;
  documents_similarity_score: number;
  duration?: { start: Date; end: Date };
  index: number;
  metadata_filter?: {
    value?: string;
    type_id?: number;
    type_name?: string;
    multiple_types?: {
      type_id?: number;
      type_name?: string;
      value?: string;
    }[];
  };
  prompt: string;
  question: string;
  token_usage?: TokenUsage;
  review_message?: string | string[];
  confidence_score?: number;
  confidence_score_explanation?: string;
  items_to_review?: string[];
  should_review: boolean;
  identical_question?: any;
  translation?: {
    translation: string;
    language: string;
    source_text: string;
  };
}

export interface ContentDocumentSources {
  docs: ContentSourceDoc[];
  qa: ContentSourceQA[];
}

export interface ContentSourceDoc {
  document_id: number;
  document_label?: string;
  document_type?: string;
  document_privacy?: string;
  embedding_id: number;
  similarity_score: number;
  text: string;
  type?: string;
  file_name: string;
  metadata:
    | {
        privacy: 'public' | 'private' | undefined;
        page_numbers?: number[];
      }
    | {
        privacy: 'public' | 'private' | undefined;
        row_index?: number;
      };
  metadata_filter?: any;
  uuid: string;
  context_index?: number;
}

export interface ContentSourceQA extends ContentSourceDoc {}

export type ContentReviewStatus = 'pending' | 'approved' | 'rejected';
export interface ContentReview {
  id: number;
  embedding_id?: number;
  content_detail_id?: number;
  created_date: Date;
  created_by_id: number;
  description?: string;
  status: ContentReviewStatus;
  processed_date: Date;
  processed_by_id?: number;
}

interface TokenUsage {
  completion_tokens: number;
  prompt_tokens: number;
  total_cost: number;
  total_tokens: number;
}

export interface ActionMenuItem {
  id: string;
  label: string;
}

export interface Citation {
  source: string;
  text: string;
  metadata?: any;
  metadata_filter?: any;
  similarity_score: number;
  document_id?: number;
  context_index?: number;
}
