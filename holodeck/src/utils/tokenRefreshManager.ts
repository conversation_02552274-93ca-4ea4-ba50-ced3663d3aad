import { jwtDecode } from 'jwt-decode';
import { getAccessToken } from '../popup/state/auth.storage';
import { isTokenExpired, refreshAccessToken } from './auth0';

const REFRESH_BUFFER_TIME = 5 * 60 * 1000; // 5 minutes in ms

export class TokenRefreshManager {
  private static refreshPromise: Promise<string | null> | null = null;
  private static refreshTimeout: NodeJS.Timeout | null = null;

  static async getValidToken(): Promise<string | null> {
    const currentToken = await getAccessToken();

    if (!currentToken) {
      return null;
    }

    if (!isTokenExpired(currentToken, REFRESH_BUFFER_TIME)) {
      this.scheduleNextRefresh(currentToken);
      return currentToken;
    }

    // If a refresh is already in progress, wait for it
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    // Start a new refresh
    this.refreshPromise = refreshAccessToken();
    const newToken = await this.refreshPromise;
    this.refreshPromise = null;

    if (newToken) {
      this.scheduleNextRefresh(newToken);
    }

    return newToken;
  }

  private static scheduleNextRefresh(token: string) {
    if (this.refreshTimeout) {
      clearTimeout(this.refreshTimeout);
    }

    try {
      const decoded = jwtDecode(token);
      const expiryTime = decoded.exp * 1000; // Convert to ms
      const refreshTime = expiryTime - REFRESH_BUFFER_TIME;
      const now = Date.now();

      if (refreshTime > now) {
        this.refreshTimeout = setTimeout(
          () => this.getValidToken(),
          refreshTime - now,
        );
      }
    } catch (e) {
      console.error('Failed to schedule token refresh:', e);
    }
  }

  static clearScheduledRefresh() {
    if (this.refreshTimeout) {
      clearTimeout(this.refreshTimeout);
      this.refreshTimeout = null;
    }
    this.refreshPromise = null;
  }
}
