import createTheme from '@mui/material/styles/createTheme';

export const customTheme = () => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#1A56DB',
      },
      secondary: {
        main: '#357a38',
      },
      background: {
        default: '#F8F9FF',
      },
    },
    typography: {
      fontFamily: [
        'Poppins',
        'ui-sans-serif',
        'system-ui',
        '-apple-system',
        'BlinkMacSystemFont',
        'Segoe UI',
        'Roboto',
        'Helvetica Neue',
        'Arial',
        'Noto Sans',
        'sans-serif',
        'Apple Color Emoji',
        'Segoe UI Emoji',
        'Segoe UI Symbol',
        'Noto Color Emoji',
      ].join(','),
      body1: {
        fontSize: '0.9em',
        fontWeight: '300',
        lineHeight: '1.2em',
        color: '#000000de',
      },
      h6: {
        fontSize: '1em',
        lineHeight: '1.25em',
        fontWeight: '500',
        color: '#1A56DB',
        textTransform: 'uppercase',
      },
    },
    components: {
      MuiButton: {
        styleOverrides: {
          root: {
            fontSize: '0.9em',
          },
        },
      },
      MuiIconButton: {
        styleOverrides: {
          root: {
            fontSize: '0.9em',
          },
        },
      },
      MuiCard: {
        styleOverrides: {
          root: {
            borderRadius: 18,
          },
        },
      },
      MuiSvgIcon: {
        styleOverrides: {
          root: {
            fontSize: '1.5em',
          },
        },
      },
      MuiAlert: {
        styleOverrides: {
          root: {
            alignItems: 'center',
            fontSize: '0.9em',
          },
        },
      },
    },
  });

  return theme;
};
