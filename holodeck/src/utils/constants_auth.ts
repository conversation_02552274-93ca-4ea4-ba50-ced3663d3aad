// TODO: Incorporate config params into Webpack build (for dev, prod builds)
export const AUTH0_CONFIG = {
  localhost: {
    client_id: 'tXw2bMIe1YM32KSU0xtgPB1hcFOyxlfP',
    audience: 'https://tribble-test.azurewebsites.net/api/',
    domain: 'login-dev.tribble.ai',
  },
  test: {
    client_id: 'tXw2bMIe1YM32KSU0xtgPB1hcFOyxlfP',
    audience: 'https://tribble-test.azurewebsites.net/api/',
    domain: 'login-dev.tribble.ai',
  },
  dev: {
    client_id: 'tXw2bMIe1YM32KSU0xtgPB1hcFOyxlfP',
    audience: 'https://tribble-test.azurewebsites.net/api/',
    domain: 'login-dev.tribble.ai',
  },
  staging: {
    client_id: 'xMwmSIGyvm33UgrZx3K0MPXz27FAbton',
    audience: 'https://tribble-staging.azurewebsites.net/api/',
    domain: 'login-staging.tribble.ai',
  },
  prod: {
    client_id: 'ocgup17FeVqimIuNC8RCwM0Z0eBmP2Oz',
    audience: 'https://my.tribble.ai/api/',
    domain: 'login.tribble.ai',
  },
};

// bake into build workflow later...
export const TRIBBLE_APP_URL = {
  localhost: 'http://localhost:5173',
  test: 'https://tribble-test.azurewebsites.net',
  dev: 'https://tribble-dev.azurewebsites.net',
  staging: 'https://tribble-staging.azurewebsites.net',
  prod: 'https://my.tribble.ai',
};
