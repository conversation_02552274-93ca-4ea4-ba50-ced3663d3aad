import { LOGGER_ENABLED, TRIBBLE_LOGO_URL } from './constants';

export const createNotification = (title: string, message: string) => {
  chrome.notifications.create({
    type: 'basic',
    iconUrl: TRIBBLE_LOGO_URL,
    title,
    message,
  });
};

export const logger = (message: string, isError = false) => {
  if (LOGGER_ENABLED) {
    if (isError) {
      console.error(message);
    } else {
      console.log(message);
    }
  }
};
