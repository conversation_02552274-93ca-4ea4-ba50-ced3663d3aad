import { v4 as uuidv4 } from 'uuid';
import { doFetch, doFetchNoContentType, get } from '../popup/api/apiUtils';
import { MetadataFilterValueDB } from '../popup/api/metadata.api';
import { getAuthConfig } from './auth0';
import {
  CONTENT_SOURCE_EXTENSION,
  KEY_AUTH_ACCESS_TOKEN,
  KEY_AUTH_ENV,
} from './constants';
import { TRIBBLE_APP_URL } from './constants_auth';
import { logger } from './notification';

export const getTribbleUrlPrefix = async () => {
  const authConfig = await getAuthConfig();
  if (authConfig && KEY_AUTH_ENV in authConfig) {
    return TRIBBLE_APP_URL[authConfig[KEY_AUTH_ENV]];
  }
  return null;
};

const errorTribbleUrlLookup = () => {
  return { response: null, error: 'Could not lookup Tribble URL' };
};

const handleApiResponse = (response: any) => {
  if (response.success) {
    return { response: response.response || {}, error: null };
  } else {
    return { response: null, error: response.error };
  }
};

export const getUserDetails = async (accessToken: string) => {
  try {
    const urlPrefix = await getTribbleUrlPrefix();
    if (urlPrefix) {
      const raw = await fetch(`${urlPrefix}/api/`, {
        headers: { Authorization: `Bearer ${accessToken}` },
      });

      const resp = await raw.json();
      if (resp.response === 'OK') {
        return resp.user;
      } else {
        return null;
      }
    } else {
      return null;
    }
  } catch (e) {
    console.error(e);
    return null;
  }
};

export const createNewQuestionnaire = async (
  label: string,
  customerName: string,
  metadataFilter: any,
  accessToken: string,
) => {
  try {
    const body = JSON.stringify({
      source: CONTENT_SOURCE_EXTENSION,
      metadata: {
        uuid: uuidv4(),
        label,
        customerName,
        metadataFilter,
        privacy: 'public', // TODO
      },
    });

    const urlPrefix = await getTribbleUrlPrefix();
    if (urlPrefix) {
      const raw = await fetch(`${urlPrefix}/api/rfp_external_create`, {
        method: 'POST',
        body,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const response = await raw.json();
      return handleApiResponse(response);
    } else {
      return errorTribbleUrlLookup();
    }
  } catch (e) {
    logger(`[createNewQuestionnaire] ${e.message}`, true);
    return { response: null, error: e };
  }
};

export const loadQuestionnaires = async (accessToken: string) => {
  try {
    const urlPrefix = await getTribbleUrlPrefix();
    if (urlPrefix) {
      const raw = await fetch(
        `${urlPrefix}/api/get_rfps?source=${CONTENT_SOURCE_EXTENSION}`,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${accessToken}`,
          },
        },
      );

      const response = await raw.json();
      return handleApiResponse(response);
    } else {
      return errorTribbleUrlLookup();
    }
  } catch (e) {
    logger(`[loadQuestionnaires] ${e.message}`, true);
    return { response: null, error: e };
  }
};

export const loadQuestionnaireDetails = async (
  id: number,
  accessToken: string,
) => {
  try {
    const urlPrefix = await getTribbleUrlPrefix();
    if (urlPrefix) {
      const raw = await fetch(
        `${urlPrefix}/api/get_content_detail?content_id=${String(id)}`,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${accessToken}`,
          },
        },
      );

      const response = await raw.json();
      return handleApiResponse(response);
    } else {
      return errorTribbleUrlLookup();
    }
  } catch (e) {
    logger(`[loadQuestionnaireDetails] ${e.message}`, true);
    return { response: null, error: e };
  }
};

export const loadMetadataFilters = async (accessToken: string) => {
  try {
    const urlPrefix = await getTribbleUrlPrefix();
    if (urlPrefix) {
      const raw = await fetch(`${urlPrefix}/api/metadata_filters`, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const response = await raw.json();
      return handleApiResponse(response);
    } else {
      return errorTribbleUrlLookup();
    }
  } catch (e) {
    logger(`[loadMetadataFilters] Could not fetch metadata filters.`, true);
    return { response: null, error: e };
  }
};

export const getContentReviewRequests = async (
  embeddingIds: number[],
  accessToken: string,
) => {
  try {
    const body = JSON.stringify({
      embedding_ids: embeddingIds,
    });

    const urlPrefix = await getTribbleUrlPrefix();
    if (urlPrefix) {
      const res = await fetch(`${urlPrefix}/api/get_content_reviews`, {
        method: 'POST',
        body,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const response = await res.json();
      return handleApiResponse(response);
    } else {
      return errorTribbleUrlLookup();
    }
  } catch (e) {
    logger(`[updateQuestionnaireStatus] ${e.message}`, true);
    return { response: null, error: e };
  }
};

export const updateQuestionnaireStatus = async (
  id: number,
  active: boolean,
  accessToken: string,
) => {
  try {
    const body = JSON.stringify({
      metadata: {
        jobId: id,
        active,
      },
    });

    const urlPrefix = await getTribbleUrlPrefix();
    if (urlPrefix) {
      const raw = await fetch(`${urlPrefix}/api/update_rfp_status`, {
        method: 'POST',
        body,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const response = await raw.json();
      return handleApiResponse(response);
    } else {
      return errorTribbleUrlLookup();
    }
  } catch (e) {
    logger(`[updateQuestionnaireStatus] ${e.message}`, true);
    return { response: null, error: e };
  }
};

export const addQuestionsQuestionnaire = async (
  id: number,
  contentId: number,
  queryStrings: string[],
  accessToken: string,
) => {
  try {
    const body = JSON.stringify({
      metadata: {
        jobId: id,
        contentId,
        queryStrings,
      },
    });

    const urlPrefix = await getTribbleUrlPrefix();
    if (urlPrefix) {
      const raw = await fetch(`${urlPrefix}/api/rfp_external_add_question`, {
        method: 'POST',
        body,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const response = await raw.json();
      return handleApiResponse(response);
    } else {
      return errorTribbleUrlLookup();
    }
  } catch (e) {
    logger(`[addQuestionQuestionnaire] ${e.message}`, true);
    return { response: null, error: e };
  }
};

export const deleteQuestionnaire = async (
  contentId: number,
  accessToken: string,
) => {
  try {
    const urlPrefix = await getTribbleUrlPrefix();
    if (urlPrefix) {
      const raw = await fetch(
        `${urlPrefix}/api/delete_rfp?content_id=${contentId}`,
        {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${accessToken}`,
          },
        },
      );

      const response = await raw.json();
      return handleApiResponse(response);
    } else {
      return errorTribbleUrlLookup();
    }
  } catch (e) {
    logger(`[deleteQuestionnaire] ${e.message}`, true);
    return { response: null, error: e };
  }
};

export const deleteOneQuestion = async (
  contentDetailId: number,
  accessToken: string,
) => {
  try {
    const urlPrefix = await getTribbleUrlPrefix();
    if (urlPrefix) {
      const raw = await fetch(
        `${urlPrefix}/api/delete_rfp_question?content_detail_id=${contentDetailId}`,
        {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${accessToken}`,
          },
        },
      );

      const response = await raw.text();
      return handleApiResponse(response);
    } else {
      return errorTribbleUrlLookup();
    }
  } catch (e) {
    logger(`[deleteQuestionnaire] ${e.message}`, true);
    return { response: null, error: e };
  }
};

export const getQuestionsStatus = async (
  contentDetailIds: number[],
  accessToken: string,
) => {
  try {
    const body = JSON.stringify({
      content_detail_ids: contentDetailIds,
    });

    const urlPrefix = await getTribbleUrlPrefix();
    if (urlPrefix) {
      const raw = await fetch(`${urlPrefix}/api/get_content_details_status`, {
        method: 'POST',
        body,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const response = await raw.json();
      return handleApiResponse(response);
    } else {
      return errorTribbleUrlLookup();
    }
  } catch (e) {
    logger(`[getQuestionsStatus] ${e.message}`, true);
    return { response: null, error: e };
  }
};

export const saveModifiedAnswer = async (
  contentDetailId: number,
  modifiedAnswer: string,
  accessToken: string,
) => {
  try {
    const body = JSON.stringify({
      content_detail_id: contentDetailId,
      modified_text: modifiedAnswer,
    });

    const urlPrefix = await getTribbleUrlPrefix();
    if (urlPrefix) {
      const raw = await fetch(`${urlPrefix}/api/modify_content_detail`, {
        method: 'POST',
        body,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const response = await raw.json();
      return handleApiResponse(response);
    } else {
      return errorTribbleUrlLookup();
    }
  } catch (e) {
    logger(`[saveModifiedAnswer] ${e.message}`, true);
    return { response: null, error: e };
  }
};

export const copyAnswer = async (
  contentDetailId: number,
  accessToken: string,
) => {
  try {
    const body = JSON.stringify({
      content_detail_id: contentDetailId,
    });

    const urlPrefix = await getTribbleUrlPrefix();
    if (urlPrefix) {
      const raw = await fetch(`${urlPrefix}/api/copy_content_detail`, {
        method: 'POST',
        body,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const response = await raw.json();
      return handleApiResponse(response);
    } else {
      return errorTribbleUrlLookup();
    }
  } catch (e) {
    logger(`[saveModifiedAnswer] ${e.message}`, true);
    return { response: null, error: e };
  }
};

export const revertModifiedAnswer = async (
  contentDetailId: number,
  accessToken: string,
) => {
  try {
    const body = JSON.stringify({
      content_detail_id: contentDetailId,
    });

    const urlPrefix = await getTribbleUrlPrefix();
    if (urlPrefix) {
      const raw = await fetch(`${urlPrefix}/api/revert_content_detail`, {
        method: 'POST',
        body,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const response = await raw.json();
      return handleApiResponse(response);
    } else {
      return errorTribbleUrlLookup();
    }
  } catch (e) {
    logger(`[saveModifiedAnswer] ${e.message}`, true);
    return { response: null, error: e };
  }
};

export const doSearch = async (queryString: string) => {
  try {
    const auth = await getAuthConfig();
    const urlPrefix = await getTribbleUrlPrefix();
    if (urlPrefix) {
      const raw = await fetch(
        `${urlPrefix}/api/search?query_string=${encodeURIComponent(
          queryString,
        )}`,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${auth[KEY_AUTH_ACCESS_TOKEN]}`,
          },
        },
      );

      const response = await raw.json();
      return handleApiResponse(response);
    } else {
      return errorTribbleUrlLookup();
    }
  } catch (e) {
    logger(`[doSearch] ${e.message}`, true);
    return { response: null, error: e };
  }
};

export const regenerateAnswer = async (
  contentDetailId: number,
  contentId: number,
  newQueryString: string,
  extraContext: string,
  accessToken: string,
) => {
  try {
    const body = JSON.stringify({
      content_detail_id: Number(contentDetailId),
      content_id: Number(contentId),
      new_query_string: newQueryString,
      extra_context: extraContext,
    });

    const urlPrefix = await getTribbleUrlPrefix();
    if (urlPrefix) {
      const raw = await fetch(`${urlPrefix}/api/regenerate_answer`, {
        method: 'POST',
        body,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const response = await raw.json();
      return handleApiResponse(response);
    } else {
      return errorTribbleUrlLookup();
    }
  } catch (e) {
    logger(`[regenerateAnswer] ${e.message}`, true);
    return { response: null, error: e };
  }
};

export type UploadFileParams = {
  files: File[];
  projectName: string;
  projectDescription?: string;
  customerName: string;
  dueDate: string;
  tags: MetadataFilterValueDB;
  criteria: string;
  criteriaIds: number[];
  ownerId: number | undefined;
  opportunityId?: string | undefined;

  // Sub-projects configuration
  subProjects?: Array<{
    mainFile: string | null;
    supportingFiles: string[];
    outputType: 'spreadsheet' | 'longform';
    verbosityWords?: string;
    beTerse?: boolean;
    targetLanguage?: string;
    customInstructions?: string;
    isGartnerMQ?: boolean;
    excludedPlatforms?: string[];
    manualMappingMode?: boolean;
    exemplarId?: string | null;
  }>;
};

export const uploadFiles = async ({
  files,
  projectName,
  projectDescription,
  customerName,
  dueDate,
  tags,
  criteria,
  criteriaIds,
  ownerId,
  opportunityId,
  subProjects,
}: UploadFileParams) => {
  const formData = new FormData();

  files.forEach((file) => {
    formData.append('files', file);
  });

  formData.append('projectName', projectName);
  formData.append('projectDescription', projectDescription);
  formData.append('customerName', customerName);
  formData.append('dueDate', dueDate);
  formData.append('source', 'EXTENSION');
  formData.append('tags', JSON.stringify(tags));
  formData.append('criteria', criteria);
  formData.append('criteriaIds', JSON.stringify(criteriaIds));
  formData.append('ownerId', String(ownerId));
  if (opportunityId) {
    formData.append('opportunityId', opportunityId);
  }
  formData.append('subProjects', JSON.stringify(subProjects));

  return await doFetchNoContentType('/upload-and-init-e2e', {
    method: 'POST',
    body: formData,
  });
};

export const getRfpResponses = async () => {
  const extensions = (await get(
    '/rfp-responses?agentSource=EXTENSION&includeType=true',
  )) as {
    response: any[];
  };
  const teams = (await get(
    '/rfp-responses?agentSource=TEAMS&includeType=true',
  )) as {
    response: any[];
  };
  const slack = (await get(
    '/rfp-responses?agentSource=SLACK&includeType=true',
  )) as {
    response: any[];
  };
  const result = await Promise.all([extensions, teams, slack]);
  return {
    response: result.flatMap((r) => r.response),
  };
};

export const getE2eLongformV2Rfps = async () => {
  const result = (await get(`rfp-e2e-longform-v2`)) as {
    response: any[];
  };
  return result;
};

export const downloadRfp = async (
  rfxContentId: number,
  type?: 'pdf' | 'docx' | 'xlsx',
) => {
  return await get(`rfx/content/${rfxContentId}/${type ? type : ''}`);
};

export const getUnfinishedRfps = async () => {
  return await get('/rfp-unfinished');
};

export const updateE2ERfpStatus = async (rfpId: string, status: string) => {
  return await doFetch('/rfp_status', {
    method: 'PATCH',
    body: JSON.stringify({
      contentId: rfpId,
      status,
    }),
  });
};
