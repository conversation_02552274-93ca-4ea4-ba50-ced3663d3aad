// set to false for production
export const LOGGER_ENABLED = true;

export const TRIBBLE_CDN_URL = 'https://tribble-cdn.azureedge.net';

export const getExtensionRedirectUrl = () => {
  return chrome.identity.getRedirectURL();
};

export const TRIBBLE_LOGO_URL = '../assets/tribble_logo.png';
export const CONTENT_SOURCE_EXTENSION = 'EXTENSION';

export const EXTENSION_MODE = {
  QUESTIONNAIRE: 'questionnaire',
  QUESTIONNAIRE_DETAILS: 'details',
  SEARCH: 'search',
};

// Chrome local storage
const KEY_PREFIX = 'TRIBBLE_EXT_';
export const KEY_AUTH = `${KEY_PREFIX}auth`;
export const KEY_AUTH_ACCESS_TOKEN = `${KEY_PREFIX}access_token`;
export const KEY_AUTH_REFRESH_TOKEN = `${KEY_PREFIX}refresh_token`;
export const KEY_AUTH_ENV = `${KEY_PREFIX}env`;

export const KEY_USER_PREFERENCES = `${KEY_PREFIX}user_preferences`;

export const KEY_CACHED_VIEW = `${KEY_PREFIX}cached_view`;
export const KEY_CACHED_SCROLL = `${KEY_PREFIX}cached_scroll`;
export const KEY_CACHED_PAGE = `${KEY_PREFIX}cached_page`;

// Used by background task to check for answered questions
export const INTERVAL_COUNTER_MAX = 12; // with a 10 second interval check, corresponds to 2 min
export const KEY_INTERVAL_COUNTER = `${KEY_PREFIX}interval_counter`;
export const KEY_INTERVAL_ID = `${KEY_PREFIX}interval_id`;
export const KEY_QUESTION_ID_LIST = `${KEY_PREFIX}question_ids`;

export const MESSAGE_TYPE_SET_DOM_MODE = 'SET_DOM_MODE';
export const MESSAGE_TYPE_DOM_ACTION = 'DOM_ACTION';
export const MESSAGE_TYPE_NEW_QUESTION = 'NEW_QUESTION';
export const MESSAGE_TYPE_ERROR = 'ERROR';

export const DOM_ACTION = {
  LOAD_QUESTIONNAIRES: 'LOAD_QUESTIONNAIRES',
  LOAD_QUESTIONNAIRE: 'LOAD_QUESTIONNAIRE',
  ADD_QUESTIONS: 'ADD_QUESTIONS',
  DELETE_QUESTION: 'DELETE_QUESTION',
  MODIFY_ANSWER: 'MODIFY_ANSWER',
  CREATE_QUESTIONNAIRE: 'CREATE_QUESTIONNAIRE',
  DOWNLOAD_CSV: 'DOWNLOAD_CSV',
  LOAD_METADATA_FILTER: 'LOAD_METADATA_FILTER',
  SAVE_MODIFIED_ANSWER: 'SAVE_MODIFIED_ANSWER',
  REVERT_ANSWER: 'REVERT_ANSWER',
  COPY_ANSWER: 'COPY_ANSWER',
  REGENERATE_ANSWER: 'REGENERATE_ANSWER',
};

export const USER_SETTING_NOTIFICATION_PREFERENCES = 'user_notification_pref';
export const USER_SETTING_E2E_RFP_EXTENSION_ENABLED =
  'enable_e2e_rfp_extension';
export const CLIENT_SETTING_PRIVATE_SOURCE_WARNING = 'source_privacy_warning';
export const DEFAULT_PRIVATE_SOURCE_WARNING_MESSAGE =
  'Note: some sources are marked as Private. Please review the answer for any sensitive information.';

export const MIME_TYPE = {
  pdf: 'application/pdf',
  docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
};

export const MILLIS_TWO_DAYS = 2 * 24 * 60 * 60 * 1000;

export const DOCUMENT_TYPE_CALL_TRANSCRIPT = 'Call Transcript';
export const DOCUMENT_TYPE_SLACK = 'Slack';
