import { jwtDecode } from 'jwt-decode';
import { RootStore } from '../popup/state/useRootStore';
import { decryptToken, encryptToken } from '../popup/state/utils.encryption';
import {
  KEY_AUTH,
  KEY_AUTH_ACCESS_TOKEN,
  KEY_AUTH_ENV,
  KEY_AUTH_REFRESH_TOKEN,
} from './constants';
import { AUTH0_CONFIG, TRIBBLE_APP_URL } from './constants_auth';

function getRandomBytes() {
  const rndArray = new Uint8Array(44);
  window.crypto.getRandomValues(rndArray);
  return rndArray;
}

function buf2Base64(buffer: any) {
  return btoa(String.fromCharCode.apply(null, new Uint8Array(buffer)))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

async function windowSha256(buffer: any) {
  const bytes = new TextEncoder().encode(buffer);
  return await window.crypto.subtle.digest('SHA-256', bytes);
}

export const getCodeChallenge = async () => {
  const inputBytes = getRandomBytes();
  const verifier = buf2Base64(inputBytes);

  const shaHash = await windowSha256(verifier);
  const codeChallenge = buf2Base64(shaHash);

  return { codeChallenge, verifier };
};

export const getParameterByName = (
  name: string,
  url = window.location.href,
) => {
  name = name.replace(/[[\]]/g, '\\$&');
  const regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)');
  const results = regex.exec(url);

  if (!results) return null;
  if (!results[2]) return '';

  return decodeURIComponent(results[2].replace(/\+/g, ' '));
};

export const isTokenExpired = (
  token: string,
  expiryBuffer: number,
): boolean => {
  try {
    const decoded = jwtDecode(token);
    const expiryTime = decoded.exp * 1000;
    const currentTime = Date.now();

    // Count as expired at expiryBuffer seconds before actual expiry
    return expiryTime - expiryBuffer < currentTime;
  } catch {
    return true;
  }
};

export const checkConnection = async (
  accessToken: string,
  rootStore?: RootStore,
) => {
  try {
    const authConfig = await getAuthConfig();
    if (authConfig && authConfig[KEY_AUTH_ENV] in TRIBBLE_APP_URL) {
      const raw = await fetch(
        TRIBBLE_APP_URL[authConfig[KEY_AUTH_ENV]] + '/api/',
        {
          headers: { Authorization: `Bearer ${accessToken}` },
        },
      );

      const resp = await raw.json();
      const noAccessAllowed = resp.user.no_admin_console === true;

      if (noAccessAllowed && rootStore) {
        rootStore.setNotification({
          type: 'error',
          header: 'Error',
          body: 'Your user account does not have access to this resource.',
        });
      }

      return resp.response === 'OK' && !noAccessAllowed;
    } else {
      return false;
    }
  } catch (e) {
    console.info(e);
    return false;
  }
};

export const getAuthConfig = async () => {
  const result = await chrome.storage.local.get([KEY_AUTH]);
  if (
    KEY_AUTH in result &&
    result[KEY_AUTH] != null &&
    KEY_AUTH_ACCESS_TOKEN in result[KEY_AUTH]
  ) {
    return result[KEY_AUTH];
  } else {
    return null;
  }
};

export const getSavedAccessToken = async () => {
  const authConfig = await getAuthConfig();
  return authConfig?.[KEY_AUTH_ACCESS_TOKEN] ?? null;
};

export const getSavedEnv = async () => {
  const authConfig = await getAuthConfig();
  return authConfig?.[KEY_AUTH_ENV] ?? null;
};

export const getSavedRefreshToken = async () => {
  const authConfig = await getAuthConfig();
  const encryptedRefreshToken = authConfig?.[KEY_AUTH_REFRESH_TOKEN] ?? null;
  return encryptedRefreshToken
    ? await decryptToken(encryptedRefreshToken)
    : null;
};

export const saveAuthConfig = async (
  accessToken: string,
  env: string,
  refreshToken?: string,
) => {
  const keyAuthValue = {
    [KEY_AUTH_ACCESS_TOKEN as string]: accessToken, // @TODO: encrypt this too
    [KEY_AUTH_ENV as string]: env,
    ...(refreshToken && {
      [KEY_AUTH_REFRESH_TOKEN as string]: await encryptToken(refreshToken),
    }),
  };

  try {
    await chrome.storage.local.set({ [KEY_AUTH as string]: keyAuthValue });
    return keyAuthValue;
  } catch (e) {
    console.error('[saveAuthConfig] Error saving auth settings: ', e);
    return null;
  }
};

export const clearAuthConfig = async () => {
  await chrome.storage.local.set({ [KEY_AUTH as string]: null });
};

export const refreshAccessToken = async () => {
  const refreshToken = await getSavedRefreshToken();
  const env = await getSavedEnv();

  if (!refreshToken || !env) {
    return null;
  }

  const body = JSON.stringify({
    grant_type: 'refresh_token',
    client_id: AUTH0_CONFIG[env].client_id,
    refresh_token: refreshToken,
  });

  try {
    const raw = await fetch(`https://${AUTH0_CONFIG[env].domain}/oauth/token`, {
      method: 'POST',
      body,
      headers: { 'Content-Type': 'application/json' },
    });

    const response = await raw.json();
    if (response.access_token) {
      await saveAuthConfig(response.access_token, env, response.refresh_token);
      return response.access_token;
    }
  } catch (error) {
    console.error('[refreshAccessToken] Error refreshing token:', error);
  }

  return null;
};
