import { QuestionDto } from 'popup/api/question.api';
import { QuestionnaireFilters } from '../popup/pages/HomePage/useFilterStore';
import { ContentDetail, ContentOutputOriginal } from './model';

import { KEY_USER_PREFERENCES } from './constants';

export const getEmbeddingIdsFromContentDetails = (
  contentDetails: ContentDetail[],
) => {
  const embeddingIds = contentDetails.map((cd) => {
    const embeddingIds: Array<number> = [];

    if (!cd.output_original?.documents) {
      return [];
    }

    const docs = cd.output_original?.documents.docs;
    const qa = cd.output_original?.documents.qa;

    if (docs && docs.length) {
      docs.forEach((doc) => embeddingIds.push(doc.embedding_id));
    }
    if (qa && qa?.length) {
      qa.forEach((qa) => embeddingIds.push(qa.embedding_id));
    }

    return embeddingIds;
  });

  return embeddingIds.filter((e) => e != undefined && e != null).flat();
};

export function answerNeedsReview(cd: ContentDetail) {
  if (cd.state == 'accepted') {
    return false;
  } else if (
    cd.output_original?.answer_versions &&
    cd.output_original?.answer_versions.length > 0
  ) {
    return cd.output_original?.answer_versions.some((answer) => {
      const reviewMessagesRaw: string | string[] = answer.review_message || [];
      const reviewMessages =
        typeof reviewMessagesRaw == 'string'
          ? reviewMessagesRaw.split('\n\n')
          : reviewMessagesRaw;
      const onlyMissingPrevQuestionnaire =
        reviewMessages.length == 1 &&
        reviewMessages[0] == 'No previous questionnaire answers found';
      return !onlyMissingPrevQuestionnaire && answer.should_review;
    });
  } else {
    const reviewMessagesRaw: string | string[] =
      cd.output_original?.review_message || [];
    const reviewMessages =
      typeof reviewMessagesRaw == 'string'
        ? reviewMessagesRaw.split('\n\n')
        : reviewMessagesRaw;
    const onlyMissingPrevQuestionnaire =
      reviewMessages.length == 1 &&
      reviewMessages[0] == 'No previous questionnaire answers found';
    return !onlyMissingPrevQuestionnaire && cd.output_original?.should_review;
  }
}

export function objectExistsWithValues(obj: any) {
  return obj !== undefined && obj != null && Object.keys(obj).length > 0;
}

export function getNumSources(question: QuestionDto) {
  let numSources = 0;
  const hasSources =
    question.output_original && question.output_original.documents;
  if (hasSources && question.output_original?.documents.docs) {
    numSources += question.output_original?.documents.docs.length;
  }
  if (hasSources && question.output_original?.documents.qa) {
    numSources += question.output_original?.documents.qa.length;
  }
  return numSources;
}

export function getMultiAnswerNumSources(answer: ContentOutputOriginal) {
  let numSources = 0;
  const hasSources = answer && answer.documents;
  if (hasSources && answer?.documents.docs) {
    numSources += answer?.documents.docs.length;
  }
  if (hasSources && answer?.documents.qa) {
    numSources += answer?.documents.qa.length;
  }
  return numSources;
}

export async function copyToClipboard(text: string) {
  try {
    await navigator.clipboard.writeText(text);
  } catch (err) {
    console.error('Failed to copy text: ', err);
  }
}

export const saveUserPreferences = async (
  currentFilters: QuestionnaireFilters,
) => {
  try {
    await chrome.storage.local.set({
      [KEY_USER_PREFERENCES as string]: currentFilters,
    });
    return true;
  } catch (e) {
    console.error('[saveUserPreferences] Error saving user preferences: ', e);
    return null;
  }
};

export const getUserPreferences = async () => {
  const result = await chrome.storage.local.get([KEY_USER_PREFERENCES]);
  return result[KEY_USER_PREFERENCES];
};

// https://stackoverflow.com/a/37511463
export const removeAccents = (str: string) => {
  if (str !== undefined && str !== null && str !== '') {
    return str.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
  }
  return '';
};

export const convertTimestampToDate = (ts: number): Date | undefined => {
  const EPOCH_NO_MILLI = 1700000000;
  const EPOCH_W_MILLI = EPOCH_NO_MILLI * 1000;

  // Make sure it looks like a valid timestamp
  if (!isNaN(Number(ts)) && Number(ts) > EPOCH_NO_MILLI) {
    const ts_valued = Number(ts);
    if (ts_valued > EPOCH_NO_MILLI && ts_valued < EPOCH_W_MILLI) {
      return new Date(Number(ts) * 1000);
    } else {
      return new Date(Number(ts));
    }
  } else {
    return undefined;
  }
};
