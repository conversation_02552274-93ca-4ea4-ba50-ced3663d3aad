import { runtime, tabs, Tabs } from 'webextension-polyfill';

import { getAuthConfig } from 'utils/auth0';
import { createNotification } from 'utils/notification';
import { sendMessageToActiveTab } from 'utils/sendMessages';

import {
  addQuestionsQuestionnaire,
  copyAnswer,
  createNewQuestionnaire,
  deleteOneQuestion,
  getContentReviewRequests,
  getQuestionsStatus,
  getUserDetails,
  loadMetadataFilters,
  loadQuestionnaireDetails,
  loadQuestionnaires,
  regenerateAnswer,
  revertModifiedAnswer,
  saveModifiedAnswer,
} from 'utils/api';

import {
  DOM_ACTION,
  INTERVAL_COUNTER_MAX,
  KEY_AUTH_ACCESS_TOKEN,
  KEY_INTERVAL_COUNTER,
  KEY_INTERVAL_ID,
  KEY_QUESTION_ID_LIST,
  MESSAGE_TYPE_DOM_ACTION,
  MESSAGE_TYPE_ERROR,
  MESSAGE_TYPE_NEW_QUESTION,
} from 'utils/constants';

import { getEmbeddingIdsFromContentDetails } from 'utils/helpers';
import { ContentDetail } from 'utils/model';

/**
 * Define background script functions
 * @type {class}
 */
class Background {
  _port: number;
  onInstalled = () => {};
  constructor() {
    this.init();
  }

  /**
   * Document Ready
   *
   * @returns {void}
   */
  init = () => {
    //When extension installed
    runtime.onInstalled.addListener(this.onInstalled);

    //Add message listener in Browser.
    runtime.onMessage.addListener(this.onMessage);

    //Add Update listener for tab
    tabs.onUpdated.addListener(this.onUpdatedTab);

    //Add New tab create listener
    tabs.onCreated.addListener(this.onCreatedTab);

    // When the user clicks on the extension icon, open the side panel
    chrome.sidePanel.setPanelBehavior({ openPanelOnActionClick: true });
  };

  //TODO: Listeners

  // /**
  //  * Extension Installed
  //  */
  // onInstalled = () => {
  // };

  /**
   * Message Handler Function
   *
   * @param message
   * @param sender
   * @returns
   */
  onMessage = async (
    message: EXTMessage /*, sender: Runtime.MessageSender*/,
  ) => {
    try {
      switch (message.type) {
        case MESSAGE_TYPE_NEW_QUESTION:
          this.handleNewQuestionRequest(message.data.contentDetailIds);
          break;

        case MESSAGE_TYPE_DOM_ACTION:
          const auth = await getAuthConfig();
          const accessToken = auth[KEY_AUTH_ACCESS_TOKEN];

          switch (message.data.action) {
            case DOM_ACTION.LOAD_QUESTIONNAIRES: {
              const { response, error } = await loadQuestionnaires(accessToken);
              this.sendMessageToDOM(
                MESSAGE_TYPE_DOM_ACTION,
                DOM_ACTION.LOAD_QUESTIONNAIRES,
                response,
                error,
              );
              break;
            }
            case DOM_ACTION.LOAD_QUESTIONNAIRE: {
              const resQues = await loadQuestionnaireDetails(
                message.data.id,
                accessToken,
              );

              if (!resQues.error) {
                const embeddingIds = getEmbeddingIdsFromContentDetails(
                  resQues.response,
                );
                const resRev = await getContentReviewRequests(
                  embeddingIds,
                  accessToken,
                );

                if (!resRev.error) {
                  const response = {
                    contentDetails: resQues.response,
                    contentReviews: resRev.response,
                  };
                  this.sendMessageToDOM(
                    MESSAGE_TYPE_DOM_ACTION,
                    DOM_ACTION.LOAD_QUESTIONNAIRE,
                    response,
                    null,
                  );
                } else {
                  this.sendMessageToDOM(
                    MESSAGE_TYPE_ERROR,
                    DOM_ACTION.LOAD_QUESTIONNAIRE,
                    resRev.response,
                    resRev.error,
                  );
                }
              } else {
                this.sendMessageToDOM(
                  MESSAGE_TYPE_ERROR,
                  DOM_ACTION.LOAD_QUESTIONNAIRE,
                  resQues.response,
                  resQues.error,
                );
              }
              break;
            }
            case DOM_ACTION.ADD_QUESTIONS: {
              const { id, contentId, queryStrings } = message.data;
              const { response, error } = await addQuestionsQuestionnaire(
                id,
                contentId,
                queryStrings,
                accessToken,
              );

              this.sendMessageToDOM(
                MESSAGE_TYPE_DOM_ACTION,
                DOM_ACTION.ADD_QUESTIONS,
                response,
                error,
              );

              if (!error) {
                this.handleNewQuestionRequest(
                  response.contentDetails.map((cd: ContentDetail) => cd.id),
                );
              }

              break;
            }
            case DOM_ACTION.DELETE_QUESTION: {
              await deleteOneQuestion(message.data.id, accessToken);
              const { response, error } = await loadQuestionnaires(accessToken);
              this.sendMessageToDOM(
                MESSAGE_TYPE_DOM_ACTION,
                DOM_ACTION.LOAD_QUESTIONNAIRES,
                response,
                error,
              );
              break;
            }
            case DOM_ACTION.CREATE_QUESTIONNAIRE: {
              const { response, error } = await createNewQuestionnaire(
                message.data.label,
                message.data.customerName,
                message.data.metadataFilter,
                accessToken,
              );

              if (error) {
                this.sendMessageToDOM(
                  MESSAGE_TYPE_ERROR,
                  DOM_ACTION.CREATE_QUESTIONNAIRE,
                  response,
                  error,
                );
              } else {
                const { response, error } =
                  await loadQuestionnaires(accessToken);
                this.sendMessageToDOM(
                  MESSAGE_TYPE_DOM_ACTION,
                  DOM_ACTION.CREATE_QUESTIONNAIRE,
                  response,
                  error,
                );
              }
              break;
            }
            case DOM_ACTION.LOAD_METADATA_FILTER: {
              const { response, error } =
                await loadMetadataFilters(accessToken);

              if (error) {
                this.sendMessageToDOM(
                  MESSAGE_TYPE_ERROR,
                  DOM_ACTION.LOAD_METADATA_FILTER,
                  response,
                  error,
                );
              } else {
                this.sendMessageToDOM(
                  MESSAGE_TYPE_DOM_ACTION,
                  DOM_ACTION.LOAD_METADATA_FILTER,
                  response,
                  error,
                );
              }
              break;
            }
            case DOM_ACTION.SAVE_MODIFIED_ANSWER: {
              const { contentDetails, contentDetailId, modifiedAnswer } =
                message.data;
              const { response, error } = await saveModifiedAnswer(
                Number(contentDetailId),
                modifiedAnswer,
                accessToken,
              );

              if (error) {
                this.sendMessageToDOM(
                  MESSAGE_TYPE_ERROR,
                  DOM_ACTION.SAVE_MODIFIED_ANSWER,
                  response,
                  error,
                );
              } else {
                const respUser = await getUserDetails(accessToken);
                const data = {
                  cachedContentDetails: contentDetails,
                  contentDetailId: Number(contentDetailId),
                  modifiedAnswer,
                  userDetails: respUser,
                };
                this.sendMessageToDOM(
                  MESSAGE_TYPE_DOM_ACTION,
                  DOM_ACTION.SAVE_MODIFIED_ANSWER,
                  data,
                  error,
                );
              }

              break;
            }
            case DOM_ACTION.REVERT_ANSWER: {
              const { contentDetails, contentDetailId } = message.data;
              const { response, error } = await revertModifiedAnswer(
                Number(contentDetailId),
                accessToken,
              );

              if (error) {
                this.sendMessageToDOM(
                  MESSAGE_TYPE_ERROR,
                  DOM_ACTION.REVERT_ANSWER,
                  response,
                  error,
                );
              } else {
                const data = {
                  cachedContentDetails: contentDetails,
                  contentDetailId: Number(contentDetailId),
                };
                this.sendMessageToDOM(
                  MESSAGE_TYPE_DOM_ACTION,
                  DOM_ACTION.REVERT_ANSWER,
                  data,
                  error,
                );
              }

              break;
            }
            case DOM_ACTION.COPY_ANSWER: {
              const { contentDetailId } = message.data;
              await copyAnswer(Number(contentDetailId), accessToken);

              break;
            }
            case DOM_ACTION.REGENERATE_ANSWER: {
              const {
                contentDetailId,
                contentId,
                newQueryString,
                extraContext,
              } = message.data;
              const { response, error } = await regenerateAnswer(
                contentDetailId,
                contentId,
                newQueryString,
                extraContext,
                accessToken,
              );
              if (error) {
                this.sendMessageToDOM(
                  MESSAGE_TYPE_ERROR,
                  DOM_ACTION.REGENERATE_ANSWER,
                  response,
                  error,
                );
              } else {
                this.sendMessageToDOM(
                  MESSAGE_TYPE_DOM_ACTION,
                  DOM_ACTION.REGENERATE_ANSWER,
                  response,
                  error,
                );
              }

              break;
            }
            case DOM_ACTION.DOWNLOAD_CSV: {
              // Note: this doesn't work as background worker doesn't have URL.createObjectURL()
              // Need to use an offscreen page approach...but...do that another time.

              // const { contentDetails, content } = message.data!;

              // const qaList = contentDetails.map((cd: ContentDetail) => {
              //   return {
              //     question: cd.input.query_string,
              //     answer: (cd.state == 'modified') ? cd.output_modified!.text : cd.output_original!.answer
              //   }
              // });

              // const keys = Object.keys(qaList[0]);
              // const csvContents = `${keys.join(',')}\n`
              //                       + qaList.map((qa: any) => {
              //                           return keys.map(k => {
              //                             let cell = qa[k].replace(/"/g, '""');
              //                             if (cell.search(/("|,|\n)/g) >= 0) {
              //                               cell = `"${cell}"`;
              //                             }
              //                             return cell;
              //                           }).join(',');
              //                         }).join('\n');

              // const csvData = new Blob([csvContents], { type: 'text/csv' });
              // var csvUrl = URL.createObjectURL(csvData);
              // chrome.downloads.download({ filename: content!.label.replace(/\s+/g, '_') + '.csv', url: csvUrl });

              break;
            }
          }
          break;

        default:
          return;
      }

      return true; // result to reply
    } catch (error) {
      console.log('[===== Error in MessageListener =====]', error);
      return error;
    }
  };

  sendMessageToDOM = (
    type: EXTMessageType,
    action: string,
    data: any,
    error: any,
  ) => {
    const msg: EXTMessage = {
      type: error ? MESSAGE_TYPE_ERROR : type,
      data: {
        action,
        response: error ? null : data,
        error,
      },
    };
    sendMessageToActiveTab(msg);
  };

  handleNewQuestionRequest = async (contentDetailIds: number[]) => {
    // Clear any current interval IDs
    let keyIntervalId = await chrome.storage.local.get([KEY_INTERVAL_ID]);
    if (!isNaN(keyIntervalId[KEY_INTERVAL_ID])) {
      clearInterval(keyIntervalId[KEY_INTERVAL_ID]);
      keyIntervalId = {};
    }

    // Add new question id (content_detail_id) to list of ids to check
    const keyPendingQuestionIds = await chrome.storage.local.get([
      KEY_QUESTION_ID_LIST,
    ]);

    console.log(
      `[background onMessage] New questions have been added (ids=${contentDetailIds})`,
    );

    if (KEY_QUESTION_ID_LIST in keyPendingQuestionIds) {
      keyPendingQuestionIds[KEY_QUESTION_ID_LIST] =
        keyPendingQuestionIds[KEY_QUESTION_ID_LIST].concat(contentDetailIds);
    } else {
      keyPendingQuestionIds[KEY_QUESTION_ID_LIST] = contentDetailIds;
    }
    await chrome.storage.local.set(keyPendingQuestionIds);

    // Reset sanity counter
    await chrome.storage.local.set({ [KEY_INTERVAL_COUNTER as string]: 0 });

    keyIntervalId[KEY_INTERVAL_ID] = setInterval(async () => {
      const keyIntervalCounter = await chrome.storage.local.get([
        KEY_INTERVAL_COUNTER,
      ]);
      keyIntervalCounter[KEY_INTERVAL_COUNTER] += 1;

      const keyIntervalId = await chrome.storage.local.get([KEY_INTERVAL_ID]);

      const keyPendingQuestionIds = await chrome.storage.local.get([
        KEY_QUESTION_ID_LIST,
      ]);

      const auth = await getAuthConfig();
      const questionStatuses = await getQuestionsStatus(
        keyPendingQuestionIds[KEY_QUESTION_ID_LIST],
        auth[KEY_AUTH_ACCESS_TOKEN],
      );

      if (questionStatuses.error == null) {
        const pendingQuestions = questionStatuses.response.filter((q: any) => {
          if (q.state == 'initial') {
            const msgQueryString =
              q.input.query_string.length > 40
                ? q.input.query_string.substring(0, 35) + '...'
                : q.input.query_string;

            createNotification(
              'Tribble Notification',
              `The question "${msgQueryString}" has been processed. Please open the Tribble Chrome Extension to view details.`,
            );

            return false;
          } else {
            return true;
          }
        });

        keyPendingQuestionIds[KEY_QUESTION_ID_LIST] = pendingQuestions.map(
          (cd: any) => parseInt(cd.id),
        );
      }

      await chrome.storage.local.set(keyPendingQuestionIds);

      // Chrome will kill the background task at some point. But 2 mins seems sufficient time to check.
      // This is reset as new questions are asked as well.
      if (
        keyIntervalCounter[KEY_INTERVAL_COUNTER] > INTERVAL_COUNTER_MAX ||
        keyPendingQuestionIds[KEY_QUESTION_ID_LIST].length == 0
      ) {
        console.log('All questions have been processed. Clearing timer.');
        clearInterval(keyIntervalId[KEY_INTERVAL_ID]);
      } else {
        await chrome.storage.local.set(keyIntervalCounter);
      }
    }, 10000);

    await chrome.storage.local.set(keyIntervalId);
  };

  /**
   * Message from Long Live Connection
   *
   * @param msg
   */
  onMessageFromExtension = (/*msg: EXTMessage*/) => {};

  /**
   *
   * @param tab
   */
  onCreatedTab = (/*tab: Tabs.Tab*/) => {};

  /**
   * When changes tabs
   *
   * @param {*} tabId
   * @param {*} changeInfo
   * @param {*} tab
   */
  onUpdatedTab =
    (/*tabId: number, changeInfo: Tabs.OnUpdatedChangeInfoType, tab: Tabs.Tab*/) => {};

  /**
   * Get url from tabId
   *
   */
  getURLFromTab = async (tabId: number) => {
    try {
      const tab = await tabs.get(tabId);
      return tab.url || '';
    } catch (error) {
      console.log(
        `[===== Could not get Tab Info$(tabId) in getURLFromTab =====]`,
        error,
      );
      throw '';
    }
  };

  /**
   * Open new tab by url
   *
   */
  openNewTab = async (url: string) => {
    try {
      const tab = await tabs.create({ url });
      return tab;
    } catch (error) {
      console.log(`[===== Error in openNewTab =====]`, error);
      return null;
    }
  };

  /**
   * Close specific tab
   *
   * @param {number} tab
   */
  closeTab = async (tab: Tabs.Tab) => {
    try {
      await tabs.remove(tab.id ?? 0);
    } catch (error) {
      console.log(`[===== Error in closeTab =====]`, error);
    }
  };

  /**
   * send message
   */
  sendMessage = async (tab: Tabs.Tab, msg: EXTMessage) => {
    try {
      const res = await tabs.sendMessage(tab.id ?? 0, msg);
      return res;
    } catch (error) {
      console.log(`[===== Error in sendMessage =====]`, error);
      return null;
    }
  };
}

export const background = new Background();
