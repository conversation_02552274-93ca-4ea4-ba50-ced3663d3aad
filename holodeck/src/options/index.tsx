/* global document */
import React from 'react';
import ReactDOM from 'react-dom/client';

import ThemeProvider from '@mui/material/styles/ThemeProvider';
import { customTheme } from 'utils/theme';
const theme = customTheme();

import Options from './options';

const Index = () => {
  return (
    <ThemeProvider theme={theme}>
      <Options />
    </ThemeProvider>
  );
};

const root = ReactDOM.createRoot(document.getElementById('app')!);
root.render(<Index />);
