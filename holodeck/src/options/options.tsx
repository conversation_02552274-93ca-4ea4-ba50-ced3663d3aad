import {
  getCodeChallenge,
  getParameterByName,
  saveAuthConfig,
} from 'utils/auth0';
import { getExtensionRedirectUrl } from 'utils/constants';

import { AUTH0_CONFIG } from 'utils/constants_auth';

// import './options.scss';

export async function doAuth(
  env: keyof typeof AUTH0_CONFIG,
  callback: (result: boolean) => void,
) {
  const redirectUrl = getExtensionRedirectUrl();
  const { codeChallenge, verifier } = await getCodeChallenge();

  const options = {
    client_id: AUTH0_CONFIG[env].client_id,
    redirect_uri: redirectUrl,
    response_type: 'code',
    audience: AUTH0_CONFIG[env].audience,
    // Auth0 refresh_token needs offline_access scope
    // previous code did not specify email and profile scopes, so will not add
    scope: 'openid offline_access',
    code_challenge: codeChallenge,
    code_challenge_method: 'S256',
  };

  const resultUrl = await new Promise<string | undefined>((resolve, reject) => {
    const queryString = new URLSearchParams(options).toString();
    const url = `https://${AUTH0_CONFIG[env].domain}/authorize?${queryString}`;

    chrome.identity.launchWebAuthFlow(
      { url, interactive: true },
      (callbackUrl: string | undefined) => {
        resolve(callbackUrl);
      },
    );
  });

  if (resultUrl) {
    const code = getParameterByName('code', resultUrl);
    const body = JSON.stringify({
      redirect_uri: redirectUrl,
      grant_type: 'authorization_code',
      client_id: AUTH0_CONFIG[env].client_id,
      code_verifier: verifier,
      code: code,
    });

    const raw = await fetch(`https://${AUTH0_CONFIG[env].domain}/oauth/token`, {
      method: 'POST',
      body,
      headers: { 'Content-Type': 'application/json' },
    });

    const response = await raw.json();
    if (response.access_token !== undefined) {
      // Response will look like:
      // {
      //   "access_token": "some_token",
      //   "id_token": "another_token",
      //   "scope": "openid",
      //   "expires_in": 86400,
      //   "token_type": "Bearer"
      // }
      const saveResponse = await saveAuthConfig(
        response.access_token,
        env,
        response.refresh_token,
      );
      if (saveResponse) {
        callback(true);
      }
    } else {
      // an error occurred
      console.error('[doAuth] Error getting access token');
      callback(false);
    }
  } else {
    console.error(
      '[doAuth] Authentication process was cancelled or errored out: ',
      resultUrl,
    );
    callback(false);
  }
}

export const Options = () => {
  return null;
};

export default Options;
