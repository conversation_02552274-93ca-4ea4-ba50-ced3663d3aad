import React from 'react';

import Tooltip, { TooltipProps, tooltipClasses } from '@mui/material/Tooltip';
import { styled } from '@mui/material/styles';

export const PrimaryTooltip = styled(
  ({ className, ...props }: TooltipProps) => (
    <Tooltip {...props} classes={{ popper: className }} />
  ),
)(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: theme.palette.primary.main,
    color: '#fff',
    maxWidth: 300,
    fontSize: 'max(0.85em, 13px)',
    lineHeight: '1.2em',
    padding: 8,
    whiteSpace: 'pre-line',
  },
  [`& .${tooltipClasses.arrow}`]: {
    color: theme.palette.primary.main,
  },
}));

export const SecondaryTooltip = styled(
  ({ className, ...props }: TooltipProps) => (
    <Tooltip {...props} classes={{ popper: className }} />
  ),
)(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: theme.palette.secondary.main,
    color: '#fff',
    maxWidth: 300,
    fontSize: 'max(0.85em, 13px)',
    lineHeight: '1.2em',
    padding: 8,
    whiteSpace: 'pre-line',
  },
  [`& .${tooltipClasses.arrow}`]: {
    color: theme.palette.secondary.main,
  },
}));

export const ErrorTooltip = styled(({ className, ...props }: TooltipProps) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: theme.palette.error.main,
    color: '#fff',
    maxWidth: 300,
    fontSize: 'max(0.85em, 13px)',
    lineHeight: '16px',
    whiteSpace: 'pre-line',
    zIndex: 1000001,
  },
  [`& .${tooltipClasses.arrow}`]: {
    color: theme.palette.error.main,
    zIndex: 1000001,
  },
}));
