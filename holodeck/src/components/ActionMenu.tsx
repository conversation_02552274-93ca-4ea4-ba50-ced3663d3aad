import * as React from 'react';

import MoreVertIcon from '@mui/icons-material/MoreVert';
import IconButton from '@mui/material/IconButton';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';

import { ActionMenuItem } from '../utils/model';

interface ActionMenuProps {
  id: number;
  options: ActionMenuItem[];
  onSelect: (id: number, action: string) => void;
}

const ActionMenu: React.FC<ActionMenuProps> = ({ id, options, onSelect }) => {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  // const [anchorPosition, setAnchorPosition] = React.useState({ left: 0, top: 0});

  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    // const { x, y } = event.currentTarget.getBoundingClientRect();
    setAnchorEl(event.currentTarget);
    // setAnchorPosition({ left: x, top: y });
  };

  const handleSelect = (event: React.MouseEvent<HTMLElement>) => {
    const action = event.currentTarget.id.split('_')[0];
    const id = parseInt(event.currentTarget.id.split('_')[1]);

    setAnchorEl(null);
    onSelect(id, action);
  };

  return (
    <div>
      <IconButton
        aria-label="more"
        id="long-button"
        aria-controls={open ? 'long-menu' : undefined}
        aria-expanded={open ? 'true' : undefined}
        aria-haspopup="true"
        onClick={handleClick}
      >
        <MoreVertIcon />
      </IconButton>
      <Menu
        id="long-menu"
        MenuListProps={{ 'aria-labelledby': 'long-button' }}
        anchorEl={anchorEl}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        open={open}
        onClose={() => setAnchorEl(null)}
        sx={{ zIndex: 1000001, fontSize: '16px' }}
      >
        {options.map((option) => (
          <MenuItem
            key={`${option.id}_${id}`}
            id={`${option.id}_${id}`}
            onClick={handleSelect}
            sx={{ fontSize: '0.8em' }}
          >
            {option.label}
          </MenuItem>
        ))}
      </Menu>
    </div>
  );
};

export default ActionMenu;
