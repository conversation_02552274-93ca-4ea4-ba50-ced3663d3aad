import {
  CalendarToday,
  KeyboardArrowDown as KeyboardArrowDownIcon,
  KeyboardArrowUp as KeyboardArrowUpIcon,
  TextSnippetOutlined,
} from '@mui/icons-material';
import {
  Avatar,
  Box,
  Button,
  Card,
  CircularProgress,
  Collapse,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  MenuItem,
  Stack,
  Typography,
  useTheme,
} from '@mui/material';
import React, { useEffect, useState } from 'react';

import { MenuDropdown } from '../atomic-design/atoms/MenuDropdown';
import {
  GreyTooltip,
  PrimaryTooltip,
} from '../atomic-design/atoms/StyledTooltips';
import { TribbleAvatar } from '../atomic-design/atoms/TribbleAvatar';
import { StatusChip } from '../atomic-design/molecules/QuestionnaireCard/StatusChip';
import { post } from '../popup/api/apiUtils';
import {
  LongformStatusValue,
  RfpStatusLabelValue,
} from '../popup/api/questionnaires.api';
import { UserDto } from '../popup/api/user.api';
import { useUsers } from '../popup/api/user.swr';
import { MoreDrawer } from '../popup/pages/HomePage/pages/MoreDrawer';
import { getEnv } from '../popup/state/auth.storage';
import { useRootStore } from '../popup/state/useRootStore';
import {
  displayUTCDateWithDefaultLocale,
  processUTCDate,
} from '../popup/utils/dateTime';
import { downloadRfp, updateE2ERfpStatus } from '../utils/api';
import * as constants from '../utils/constants';
import { TRIBBLE_APP_URL } from '../utils/constants_auth';
import { convertTimestampToDate } from '../utils/helpers';
import { Row } from './copied';

interface QualificationViolation {
  criterionText: string;
  explanation: string;
  severity: 'HIGH' | 'MEDIUM' | 'LOW';
  violationText?: string;
  isViolation?: boolean;
  location?: string;
  criterionId?: number;
  explicit?: boolean;
  fullPartialMatch?: string;
}

interface Qualification {
  decision: string;
  rationale: string;
  rejectionCriteria: string[];
  questionCount: number;
  sections: string[];
  violations?: QualificationViolation[];
  sectionDetails?: Array<{
    title: string;
    requirements: Array<{
      id: string;
      text: string;
      requirementType: string;
      responseCategory: string;
    }>;
  }>;
}

export type Rfp = {
  has_e2e_workbook: boolean;
  details: {
    customer_name: string;
    project_name?: string;
    project_description?: string;
    custom_instructions?: string;
    rfpId: string;
    files: {
      name: string;
    }[];
    agentParams: {
      error?: string;
      finalReview: {
        strengths: string;
        areasToImprove: string;
        overall: string;
      };
      schema: string;
      qualification: Qualification;
      userId?: number;
      thread?: string;
      contentId: number;
    };
    parentContentId?: string;
    fileName?: string;
    opportunity_id?: string;
  };
  id: string;
  status: LongformStatusValue;
  type?: 'analyze_pdf' | 'answer_rfp' | 'generate_rfp_response';
  created_date: string;
  due_date?: Date;
  assignees?: {
    assignee_id: number;
    user_name: string;
    is_owner: boolean;
  }[];
  rfp_status?: string;
  rfp_status_label?: string;
  project_content_status_label: RfpStatusLabelValue;
  longForm: boolean;
  isE2E: boolean;
  rfx_content_id: number;
  rfx_id: number;
};

export const downloadRfpInBrowser = async (
  rfxContentId: number,
  type: 'docx' | 'xlsx',
  fileName: string,
  filledIn: boolean = false,
) => {
  const result = (await downloadRfp(rfxContentId, type)) as {
    response: { data: Buffer; type: string };
  };

  if (
    result.response &&
    result.response.data &&
    result.response.type === 'Buffer'
  ) {
    const arrayBuffer = new Uint8Array(result.response.data).buffer;
    const blob = new Blob([arrayBuffer], {
      type:
        type === 'xlsx'
          ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          : 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;

    let outputFileName = fileName;
    if (outputFileName.endsWith(`.${type}`)) {
      outputFileName = outputFileName.slice(0, -1 * (type.length + 1));
    }
    a.download = outputFileName + `${filledIn ? '_complete' : ''}.${type}`;

    document.body.appendChild(a); // Required for Firefox
    a.click();
    window.URL.revokeObjectURL(url);
    a.remove();
  }
};

export const getRfpCreationDate = (rfp: Rfp) => {
  let createdDate: Date = !!rfp.created_date
    ? new Date(rfp.created_date)
    : null;
  if (createdDate == null && rfp.details.agentParams?.thread) {
    // If can't find created_date, then see if we can find via the agentParams timestamp
    const thread_ts = String(rfp.details.agentParams.thread).split('.')[0];
    createdDate = convertTimestampToDate(Number(thread_ts));
  }
  return createdDate;
};

export const isRfpAnalyzing = (rfp: Rfp) => {
  return [
    'Pending',
    'Analyzing',
    'Gathering Answers',
    'Generating Response',
  ].includes(rfp.status);
};

export const isRfpInErrorState = (rfp: Rfp) => {
  // Don't check date logic for now
  // const today = new Date();
  // const createdDate = getRfpCreationDate(rfp);
  // if (createdDate && createdDate < today) {
  //   // If it's been more than 2 days since the RFP was created, it's probably an error
  //   if (
  //     isRfpAnalyzing(rfp) &&
  //     today.getTime() - createdDate.getTime() > MILLIS_TWO_DAYS
  //   ) {
  //     return true;
  //   }
  // }
  return false;
};

export const isRfpPastDueDate = (rfp: Rfp) => {
  let dueDate = rfp.due_date;
  if (dueDate !== undefined && dueDate != null) {
    if (typeof dueDate == 'string') dueDate = new Date(dueDate);
    if (dueDate < new Date()) {
      return true;
    }
  }
  return false;
};

export const isRfpPendingQualification = (rfp: Rfp) => {
  return (
    rfp.status === 'in_review_outline' &&
    !rfp.type &&
    !!rfp.details.agentParams.qualification
  );
};

type RfpCardProps = {
  rfp: Rfp;
  toggleViewQualification: (rfp: Rfp) => void;
  toggleViewError: (rfp: Rfp, error: string) => void;
  onUpdate: (rfp: Rfp) => void;
};
export const RFPCard: React.FC<RfpCardProps> = ({
  rfp,
  toggleViewQualification,
  toggleViewError,
  onUpdate,
}) => {
  const theme = useTheme();
  const rootStore = useRootStore();
  const users = useUsers();

  const [availableUsers, setAvailableUsers] = useState<UserDto[]>([]);

  useEffect(() => {
    if (users.data) {
      setAvailableUsers(users.data);
    }
  }, [users.data]);

  const isProcessed =
    rfp.status === 'in_review_response' || rfp.status === 'finished';

  const isPendingReview = rfp.status === 'in_review_outline';

  const pendingQualification = isRfpPendingQualification(rfp);
  const isAnalyzing = isRfpAnalyzing(rfp);
  let probablyError = isRfpInErrorState(rfp);

  let dateColor = isRfpPastDueDate(rfp)
    ? theme.palette.error.main
    : theme.palette.grey[500];

  let projectLabel;
  if (rfp.details.project_name) {
    projectLabel = rfp.details.project_name;
  } else if (rfp.details.fileName) {
    projectLabel = rfp.details.fileName;
  } else if (rfp.details.files?.length > 0) {
    projectLabel = rfp.details.files[0].name;
  }

  const ownerAvatar = () => {
    let owner = rfp.assignees?.find((user) => user.is_owner);
    const ownerId = owner ? owner.assignee_id : rfp.details.agentParams.userId;

    const ownerUser = ownerId
      ? availableUsers.find((user) => user.id == ownerId)
      : null;

    return ownerUser ? (
      <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
        {ownerUser?.picture && ownerUser.picture != '' ? (
          <PrimaryTooltip
            title={`Project Owner: ${ownerUser.name}`}
            enterDelay={250}
          >
            <Avatar src={ownerUser.picture} sx={{ height: 24, width: 24 }}>
              {ownerUser.name}
            </Avatar>
          </PrimaryTooltip>
        ) : (
          <PrimaryTooltip
            title={`Project Owner: ${ownerUser.name}`}
            enterDelay={250}
          >
            <div>
              <TribbleAvatar
                username={ownerUser.name}
                height="24px"
                width="24px"
              />
            </div>
          </PrimaryTooltip>
        )}
      </Box>
    ) : (
      <></>
    );
  };

  const filesUsed =
    '• ' + (rfp.details.files?.map((f, i) => f.name) || []).join('\n• ');
  const qualification: any = rfp.details.agentParams.qualification ?? {};

  const explicitViolations =
    qualification?.violations?.filter(
      (v) =>
        (v.explicit === undefined || v.explicit === true) &&
        v.isViolation === true,
    ) ?? [];

  const numQuestions = qualification.questionCount ?? 0;

  const isRecommended = explicitViolations.length === 0 && numQuestions > 0;

  return (
    <Card
      sx={{
        maxWidth: '100%',
        flexShrink: 0,
        border: `1px solid ${
          probablyError || isPendingReview
            ? theme.palette.warning.main
            : theme.palette.grey[400] + '60'
        }`,
        cursor: isProcessed || isPendingReview ? 'pointer' : 'default',
      }}
      onClick={async () => {
        if (isProcessed || isPendingReview) {
          // rootStore.navigate({ name: 'e2eLongForm', rfp: rfp });
          const env = await getEnv();
          const appUrl = TRIBBLE_APP_URL[env ?? 'localhost'];
          window.open(
            `${appUrl}/projects?forward_to=${rfp.rfx_content_id}`,
            '_blank',
          );
        }
      }}
    >
      <Stack
        justifyContent={'space-between'}
        sx={{
          gap: '10px',
          p: '16px',
          pb: probablyError ? '4px' : '20px',
        }}
      >
        <Row justifyContent="space-between">
          {isProcessed ? (
            <MenuDropdown
              trigger={
                <StatusChip
                  jobStatus={rfp.status.toLowerCase() as any}
                  showToggle={true}
                />
              }
              disableTrigger={!isProcessed}
              preventDefault={true}
            >
              {(handleClose) => {
                const menuItems = [
                  <MenuItem
                    disabled={true}
                    key="status_empty"
                    sx={{
                      minHeight: '24px',
                      p: '4px 12px',
                      fontSize: '0.75rem',
                      textTransform: 'uppercase',
                      color: theme.palette.grey[900],
                    }}
                  >
                    Change Status
                  </MenuItem>,
                  <MenuItem
                    onClick={async (event: React.MouseEvent<HTMLElement>) => {
                      try {
                        event.stopPropagation();
                        event.preventDefault();
                      } catch (err) {
                        // no-op
                      }
                      await updateE2ERfpStatus(rfp.id, 'finished');

                      rfp.status = 'finished';
                      onUpdate(rfp);
                      handleClose();
                    }}
                    autoFocus={false}
                    key={`status_finished`}
                    sx={{
                      minHeight: '24px',
                      p: '4px 12px',
                      fontSize: '0.85rem',
                      '&:hover': { fontWeight: 500 },
                    }}
                  >
                    Mark as Finished
                  </MenuItem>,
                ];

                return menuItems;
              }}
            </MenuDropdown>
          ) : (
            <Stack direction="row" gap="10px" alignItems="center">
              <StatusChip
                jobStatus={rfp.status.toLowerCase() as any}
                overrideLabel={rfp.rfp_status_label}
              />
              {isAnalyzing && !probablyError && !pendingQualification && (
                <CircularProgress size="18px" color="warning" />
              )}
            </Stack>
          )}
          <GreyTooltip
            title={`The following files are used in this RFP:\n\n${filesUsed}`}
          >
            <TextSnippetOutlined sx={{ color: theme.palette.grey[400] }} />
          </GreyTooltip>
        </Row>
        <Stack direction="column">
          <Typography
            variant="h6"
            sx={{
              fontWeight: '600',
            }}
          >
            {projectLabel ?? rfp.details?.customer_name}
          </Typography>

          {projectLabel && (
            <Typography variant="subtitle2" color="textSecondary">
              {rfp.details?.customer_name || ''}
            </Typography>
          )}
        </Stack>

        <Divider sx={{ my: '8px' }} />

        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
          sx={{ width: '100%' }}
        >
          <Stack direction="row" alignItems="center">
            <CalendarToday
              fontSize="small"
              sx={{ mr: '4px', color: theme.palette.grey[500] }}
            />
            <Typography variant="body2" sx={{ mr: '4px', fontWeight: '300' }}>
              Due:
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: dateColor,
              }}
            >
              {!!rfp.due_date
                ? displayUTCDateWithDefaultLocale(processUTCDate(rfp.due_date))
                : '(not specified)'}
            </Typography>
          </Stack>

          {ownerAvatar()}
        </Stack>

        {rfp.status === 'error' && (
          <>
            <Divider />
            <Typography variant="subtitle2" color="textSecondary">{`Unable to ${
              rfp.type === 'generate_rfp_response'
                ? 'generate response'
                : rfp.type === 'answer_rfp'
                  ? 'gather answers'
                  : 'analyze documents'
            }`}</Typography>
          </>
        )}
        {
          /* Shunt the user over to Admin Console for Outline UI */ false &&
            pendingQualification && (
              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
                sx={{ mr: '-8px' }}
              >
                <Typography
                  variant="body2"
                  sx={{ color: theme.palette.warning.main, fontWeight: 500 }}
                >
                  Recommendation:{' '}
                  {rfp.details.agentParams.qualification?.decision
                    ? isRecommended
                      ? 'Yes'
                      : 'No'
                    : 'Pending'}
                </Typography>
                <MoreButton
                  text="Review"
                  onClick={() => toggleViewQualification(rfp)}
                />
              </Stack>
            )
        }
        {isAnalyzing && probablyError && (
          <Stack
            direction="column"
            alignItems="center"
            sx={{
              width: '100%',
              mr: '-8px',
              mb: 2,
              p: 2,
              backgroundColor: theme.palette.error.main + '20',
              borderRadius: '8px',
            }}
          >
            <Typography
              variant="body2"
              sx={{
                my: 1,
                mb: 2,
                color: theme.palette.error.main,
                fontWeight: 500,
              }}
            >
              ⚠️ Something may have gone wrong.
            </Typography>

            <Button
              onClick={() =>
                toggleViewError(
                  rfp,
                  'Error: RFP is taking too long to analyze or has been in the pending state for a while.',
                )
              }
              variant="outlined"
              sx={{
                fontSize: '12px',
                width: 'fit-content',
                borderRadius: 1,
                textTransform: 'uppercase',
                mr: '-8px',
              }}
              color="error"
            >
              Review
            </Button>
          </Stack>
        )}
      </Stack>
    </Card>
  );
};

type MoreButton = {
  text: string;
  onClick: () => void;
};
const MoreButton: React.FC<MoreButton> = ({ text, onClick }) => {
  return (
    <Button
      onClick={onClick}
      variant="text"
      sx={{
        fontSize: '12px',
        width: 'fit-content',
        borderRadius: 1,
        textTransform: 'uppercase',
        ml: '-12px',
      }}
      color="primary"
    >
      {text}
    </Button>
  );
};

type QualificationDrawerProps = {
  open: boolean;
  close: () => void;
  rfp: Rfp;
  handleProceed: () => void;
};

interface RequirementsModalProps {
  isOpen: boolean;
  onClose: () => void;
  sectionName: string;
  requirements: string;
  violations?: QualificationViolation[];
}

const RequirementsModal: React.FC<RequirementsModalProps> = ({
  isOpen,
  onClose,
  sectionName,
  requirements,
  violations,
}) => {
  const theme = useTheme();

  if (!isOpen) return null;

  const displayViolation = (violation: QualificationViolation, idx: number) => {
    return (
      <li
        key={`${idx}_${violation.criterionId}`}
        style={{ marginBottom: '8px' }}
      >
        <Typography
          sx={{
            mt: 2,
            fontSize: '0.8rem',
            fontWeight: 500,
          }}
        >
          • ({violation.location}) {violation.explanation}
        </Typography>
        <Typography
          sx={{
            mt: 1,
            fontSize: '0.8rem',
            fontStyle: 'italic',
            color: theme.palette.grey[500],
          }}
        >
          Text from RFP: {violation.violationText}
        </Typography>
        {violation.fullPartialMatch === 'partial' && (
          <Typography
            sx={{
              my: 2,
              pl: 2,
              fontSize: '0.8rem',
              color: theme.palette.primary.main,
            }}
          >
            Note: this was marked as a "partial" violation. Please double-check
            whether the criterion was truly violated.
          </Typography>
        )}
      </li>
    );
  };

  // Function to clean requirement text
  const cleanRequirementText = (text: string) => {
    // Remove the metadata in parentheses at the end
    return text.replace(/\s*\([^)]*\)\s*$/, '').trim();
  };

  const hasViolations = violations && violations.length > 0;

  return (
    <Dialog
      fullWidth={true}
      open={isOpen}
      onClose={onClose}
      container={document.body}
      aria-labelledby="responsive-dialog-title"
      sx={{ '& .MuiDialog-paper': { m: 2, width: 'calc(100% - 32px)' } }}
    >
      <DialogTitle
        id="responsive-dialog-title"
        sx={{ fontSize: '1.2rem', fontWeight: '600', py: 3, px: 2 }}
      >
        {sectionName}
      </DialogTitle>

      <DialogContent sx={{ p: 2, pb: 1 }}>
        <Box sx={{ maxHeight: '60vh', overflow: 'auto' }}>
          {hasViolations && (
            <>
              <Box sx={{ mb: 3 }}>
                <Typography
                  sx={{
                    color: theme.palette.grey[500],
                    fontWeight: 500,
                    textTransform: 'uppercase',
                    fontSize: '0.875rem',
                  }}
                >
                  Details
                </Typography>
                <Typography
                  sx={{
                    mt: 2,
                    fontSize: '0.8rem',
                    fontWeight: 500,
                  }}
                >
                  {requirements}
                </Typography>
              </Box>
              <div>
                <Typography
                  sx={{
                    color: theme.palette.error.main,
                    fontWeight: 500,
                    textTransform: 'uppercase',
                    fontSize: '0.875rem',
                  }}
                >
                  Violations detected
                </Typography>
                <ul className="mt-1">
                  {violations.map((v, idx) => displayViolation(v, idx))}
                </ul>
              </div>
            </>
          )}
          {!hasViolations &&
            requirements.split('\n').map((line, i) => {
              const cleanedText = cleanRequirementText(line);
              return (
                cleanedText && (
                  <Box
                    key={i}
                    sx={{ display: 'flex', alignItems: 'start', mb: 2 }}
                  >
                    <Typography sx={{ color: 'text.secondary' }}>
                      {line.startsWith('•')
                        ? cleanRequirementText(line.substring(1))
                        : cleanedText}
                    </Typography>
                  </Box>
                )
              );
            })}
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 2 }}>
        <Stack sx={{ width: '100%' }}>
          <Button variant="outlined" autoFocus onClick={onClose} size="small">
            Close
          </Button>
        </Stack>
      </DialogActions>
    </Dialog>
  );
};

export const QualificationDrawer: React.FC<QualificationDrawerProps> = ({
  open,
  close,
  rfp,
  handleProceed,
}) => {
  const [confirmDelete, setConfirmDelete] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [expandedSection, setExpandedSection] = useState<string | null>(
    'Violations',
  );
  const [selectedSection, setSelectedSection] = useState<{
    name: string;
    requirements: string;
    violations?: QualificationViolation[];
  } | null>(null);
  const theme = useTheme();

  const qualification = rfp.details.agentParams.qualification;
  if (!qualification) return null;

  // Determine recommendation based on qualification.decision
  // const isRecommended = qualification.decision.toLowerCase() === 'yes';

  const explicitViolations =
    qualification.violations?.filter(
      (v) =>
        (v.explicit === undefined || v.explicit === true) &&
        v.isViolation === true,
    ) ?? [];

  const numQuestions = qualification.questionCount ?? 0;

  const isRecommended = explicitViolations.length === 0 && numQuestions > 0;

  // Set the recommendation label and styles
  const recommendationLabel = isRecommended ? 'Recommended' : 'Not Recommended';
  const recommendationBgColor = isRecommended
    ? 'rgb(236, 253, 245)'
    : 'rgb(254, 242, 242)'; // Green or Red background
  const recommendationTextColor = isRecommended
    ? 'rgb(16, 185, 129)'
    : 'rgb(185, 28, 28)'; // Green or Red text

  const recommendationText = isRecommended
    ? 'You should respond to the RFP!'
    : 'Do not respond to this RFP.';

  const subText = isRecommended
    ? 'This opportunity is a good fit.'
    : 'This opportunity may need additional consideration.';

  const onContinue = async () => {
    setProcessing(true);
    await post('/advance-rfp/answer_rfp', {
      contentId: rfp.id,
      schema: rfp.details.agentParams.schema,
    });
    handleProceed();
    close();
  };

  const onDelete = async () => {
    setProcessing(true);
    await post('/delete-rfp', {
      contentId: rfp.id,
    });

    if (rfp.details.parentContentId) {
      await post('/delete-rfp', {
        contentId: rfp.details.parentContentId,
      });
    }

    window.location.reload();
  };

  const parseRationale = (rationale: string) => {
    const sections: Record<string, string[]> = {};
    let currentSection = '';

    rationale.split('\n').forEach((line) => {
      if (line.endsWith(':')) {
        currentSection = line.slice(0, -1);
        sections[currentSection] = [];
      } else if (line.trim() && currentSection) {
        sections[currentSection].push(line.trim());
      }
    });

    return sections;
  };

  const rationaleSections = parseRationale(qualification.rationale);

  const getMetricValue = (metricKey: string): string => {
    const line = rationaleSections['Key Metrics']?.find((l) =>
      l.startsWith(metricKey),
    );
    if (!line) return '0';
    const value = line.split(':')[1].trim();
    return value.endsWith('%') ? value : value;
  };

  const getSectionTooltip = (
    sections: string[],
    requirements: string[],
    types: string[],
  ): string => {
    let tooltip = '';

    // Add sections with requirements
    if (sections && sections.length) {
      tooltip += 'Sections:\n';
      sections.forEach((section) => {
        tooltip += `• ${section}\n`;
      });
    }

    // Add requirements breakdown
    if (requirements && requirements.length) {
      tooltip += '\nRequirements:\n';
      requirements.forEach((req) => {
        tooltip += `• ${req}\n`;
      });
    }

    // Add requirement types
    if (types && types.length) {
      tooltip += '\nTypes:\n';
      types.forEach((type) => {
        tooltip += `• ${type}\n`;
      });
    }

    return tooltip;
  };

  interface SectionItem {
    sectionName: string;
    requirements: string;
    points?: string;
    tooltip?: string;
    violations?: QualificationViolation[];
  }

  type SectionItemOrNull = SectionItem | null;

  const formatSectionContent = (
    content: string,
    sectionTitle: string,
    rationale: Record<string, any>,
  ): SectionItem[] => {
    const lines = content.split('\n');

    switch (sectionTitle) {
      case 'Section Breakdown':
        // Get the section breakdown directly from rationale
        const sectionBreakdown: string[] = rationale['Section Breakdown'] || [];
        return sectionBreakdown
          .map((line): SectionItemOrNull => {
            if (!line.trim()) return null;
            const match = line.match(/^-?\s*(.+?):\s*(\d+)\s*requirements/);
            if (!match) return null;

            const sectionName = match[1].trim();
            const reqCount = parseInt(match[2]);

            // Get the requirements for this section from the rationale
            const sectionContent = rationale[sectionName];
            if (!sectionContent) return null;

            // Format requirements for tooltip
            const requirements = sectionContent
              .filter((line: string) => line.trim().startsWith('-'))
              .map((line: string) => line.substring(1).trim());

            return {
              sectionName,
              requirements: `${reqCount} requirements`,
              tooltip: requirements.map((req) => `• ${req}`).join('\n'),
            };
          })
          .filter((item): item is SectionItem => item !== null);

      case 'Understanding Check':
        const understandingSection =
          rationale['Understanding Check']?.join('\n') || '';
        const coverageMatch = understandingSection.match(
          /Requirements Coverage: ([\d.]+)% \((\d+) out of (\d+)\)/,
        );
        const knowledgeQualityMatch = understandingSection.match(
          /Knowledge Quality: (.+)$/m,
        );

        const stats = {
          coverage: coverageMatch ? `${coverageMatch[1]}%` : '0%',
          covered: coverageMatch ? parseInt(coverageMatch[2]) : 0,
          total: coverageMatch ? parseInt(coverageMatch[3]) : 0,
          knowledgeQuality: knowledgeQualityMatch
            ? knowledgeQualityMatch[1]
            : 'Limited experience',
        };

        return [
          {
            sectionName: 'Requirements Coverage',
            requirements: `${stats.covered} out of ${stats.total} total requirements can be answered based on how you have trained Tribble`,
            tooltip: `Requirements Coverage Analysis:

• Total Requirements Needing Response: ${stats.total}
• Requirements We Can Answer: ${stats.covered}
• Coverage Score: ${stats.coverage}
• Knowledge Quality: ${stats.knowledgeQuality}

This score indicates how many requirements we can effectively address based on our analysis of the RFP content. Each requirement is evaluated against our knowledge base to determine if we can provide a comprehensive and accurate response.

Our confidence in answering these requirements is based on:
• The strength of matches in our knowledge base
• The clarity and specificity of each requirement
• Our experience with similar requirements
• The technical complexity of the questions

Note: Requirements we cannot confidently answer may need additional clarification or fall outside our current knowledge domain.`,
          },
        ];

      case 'Violations':
        if (
          !lines.length ||
          (lines.length === 1 && lines[0].includes('No violations found'))
        ) {
          return [
            {
              sectionName: 'Status',
              requirements: 'No violations found',
              tooltip: 'No violations were detected in this RFP.',
            },
          ];
        }
        return lines
          .map<SectionItemOrNull>((line) => {
            if (!line.trim() || !line.startsWith('-')) return null;
            const content = line.replace(/^-\s*/, '').trim();
            if (!content) return null;
            return {
              sectionName: 'Violation Details',
              requirements: content,
              tooltip: content,
            };
          })
          .filter((item): item is SectionItem => item !== null);

      default:
        return [];
    }
  };

  // Update the section requirements parsing
  const getSectionRequirements = (
    sectionName: string,
    qualification: Qualification,
  ): string => {
    const sectionDetail = qualification.sectionDetails?.find(
      (s) => s.title === sectionName,
    );

    if (!sectionDetail) {
      return 'No specific requirements found';
    }

    const requirements = sectionDetail.requirements.map(
      (req) => `• ${req.text.replace(/\s*\([^)]*\)\s*$/, '').trim()}`,
    );

    return requirements.length > 0
      ? 'The following requirements were extracted from this section:\n\n' +
          requirements.join('\n')
      : 'No specific requirements found';
  };

  interface Metric {
    value: string;
    label: string;
    color: string;
    dotColor: string;
    textColor: string;
    details: SectionItem[];
    tooltip?: string;
  }

  const getMetrics = (
    rationale: Record<string, any>,
    qualification: Qualification,
  ): Metric[] => {
    // Parse the Understanding Check section from rationale
    const parseUnderstandingCheck = (
      rationale: Record<string, any>,
    ): {
      coverage: string;
      knowledgeQuality: string;
      covered: number;
      total: number;
    } => {
      const understandingSection =
        rationale['Understanding Check']?.join('\n') || '';
      const coverageMatch = understandingSection.match(
        /Requirements Coverage: ([\d.]+)% \((\d+) out of (\d+)\)/,
      );
      const knowledgeQualityMatch = understandingSection.match(
        /Knowledge Quality: (.+)$/m,
      );

      return {
        coverage: coverageMatch ? `${coverageMatch[1]}%` : '0%',
        covered: coverageMatch ? parseInt(coverageMatch[2]) : 0,
        total: coverageMatch ? parseInt(coverageMatch[3]) : 0,
        knowledgeQuality: knowledgeQualityMatch
          ? knowledgeQualityMatch[1]
          : 'Limited experience',
      };
    };

    const understandingCheck = parseUnderstandingCheck(rationale);

    const getDetailedCoverageExplanation = (
      stats: typeof understandingCheck,
    ) => {
      return `Requirements Coverage Analysis:

• Total Requirements Needing Response: ${stats.total}
• Requirements We Can Answer: ${stats.covered}
• Coverage Score: ${stats.coverage}
• Knowledge Quality: ${stats.knowledgeQuality}

This score indicates how many requirements we can effectively address based on our analysis of the RFP content. Each requirement is evaluated against our knowledge base to determine if we can provide a comprehensive and accurate response.

Our confidence in answering these requirements is based on:
• The strength of matches in our knowledge base
• The clarity and specificity of each requirement
• Our experience with similar requirements
• The technical complexity of the questions

Note: Requirements we cannot confidently answer may need additional clarification or fall outside our current knowledge domain.`;
    };

    // Filter violations by explicit / not explicit[ly mentioned]
    // Then group by the criterion text

    // Note: to be backwards compatible, if v.explicit is undefined, treat it as true.
    // (These shouldn't be valid, but, it's more inline with previous behaviour)
    const explicitViolations =
      qualification.violations?.filter(
        (v) =>
          (v.explicit === undefined || v.explicit === true) &&
          v.isViolation === true,
      ) ?? [];

    // Treat these as warnings to the user
    const nonExplicitViolations =
      qualification.violations?.filter(
        (v) => v.explicit !== undefined && v.explicit === false,
      ) ?? [];

    const cleanUpViolationKey = (key: string) => {
      return key
        .replaceAll('"', '')
        .replace(/\n/g, ' ')
        .replace(/\s{2,}/g, ' ');
    };

    const groupedExplicitViolations = explicitViolations.reduce(
      (acc, violation) => {
        const key = cleanUpViolationKey(violation.criterionText);
        acc[key] = (acc[key] || []).concat(violation);
        return acc;
      },
      {} as Record<string, Qualification['violations']>,
    );

    const explicitViolationDetails = Object.entries(
      groupedExplicitViolations,
    ).map(([key, value], idx) => {
      return {
        sectionName: `Violation ${idx + 1} (# occurrences: ${
          groupedExplicitViolations[key]!.length
        })`,
        requirements: `Criterion: ${key}`,
        violations: value,
        tooltip: `Criterion:\n\n${key}`,
      } as SectionItem;
    });

    // For non-explicit violations, it should be the absence of a requirement linked to a violation.
    // Look for "Page null" as the location. Also, as a double-check, ensure the criterion text (key)
    // does not appear in the explicit violation list.
    const groupedNonExplicitViolations = nonExplicitViolations.reduce(
      (acc, violation) => {
        const key = cleanUpViolationKey(violation.criterionText);
        if (groupedExplicitViolations[key]) {
          return acc;
        }

        if (
          !violation.location ||
          violation.location.toLowerCase() !== 'page null'
        ) {
          return acc;
        }

        acc[key] = (acc[key] || []).concat(violation);
        return acc;
      },
      {} as Record<string, Qualification['violations']>,
    );

    const nonExplicitViolationDetails = Object.entries(
      groupedNonExplicitViolations,
    ).map(([key, value], idx) => {
      return {
        sectionName: `Warning ${idx + 1}`,
        requirements: `Criterion not found in requirements: ${key}`,
        tooltip: `The criterion (${key}) was not found in the requirements. While this may not indicate a violation of this criterion, please review the RFP to ensure this criterion is not violated.`,
      } as SectionItem;
    });

    return [
      // {
      //   label: 'Sections Detected',
      //   value: qualification.sections.length.toString(),
      //   color: 'rgb(59, 130, 246)',
      //   dotColor: 'rgb(59, 130, 246)',
      //   textColor: 'rgb(59, 130, 246)',
      //   details: qualification.sections.map((section) => {
      //     const sectionDetail = qualification.sectionDetails?.find(
      //       (s) => s.title === section,
      //     );
      //     return {
      //       sectionName: section,
      //       requirements: sectionDetail
      //         ? `${sectionDetail.requirements.length} requirements`
      //         : 'Click to view requirements',
      //       tooltip: getSectionRequirements(section, qualification),
      //     };
      //   }),
      // },
      // {
      //   label: 'Ability to Answer',
      //   value: `${understandingCheck.coverage}`,
      //   color: 'rgb(34, 197, 94)',
      //   dotColor: 'rgb(34, 197, 94)',
      //   textColor: 'rgb(34, 197, 94)',
      //   details: [
      //     {
      //       sectionName: 'Requirements Coverage',
      //       requirements: `${understandingCheck.covered} out of ${understandingCheck.total} total requirements can be answered based on how you have trained Tribble`,
      //       tooltip: getDetailedCoverageExplanation(understandingCheck),
      //     },
      //   ],
      // },
      {
        label: 'Violations',
        value: Object.keys(groupedExplicitViolations).length
          ? String(Object.keys(groupedExplicitViolations).length)
          : 'None',
        color: 'rgb(239, 68, 68)',
        dotColor: 'rgb(239, 68, 68)',
        textColor: 'rgb(239, 68, 68)',
        details:
          explicitViolationDetails?.length > 0
            ? explicitViolationDetails
            : [
                {
                  sectionName: 'Details',
                  requirements: 'No violations found',
                  tooltip: 'No violations were detected in this RFP.',
                },
              ],
      },
      ...(Object.keys(groupedNonExplicitViolations).length > 0
        ? [
            {
              label: 'Warnings',
              value: Object.keys(groupedNonExplicitViolations).length
                ? String(Object.keys(groupedNonExplicitViolations).length)
                : 'None',
              color: 'rgb(241, 147, 25)',
              dotColor: 'rgb(241, 147, 25)',
              textColor: 'rgb(241, 147, 25)',
              details:
                nonExplicitViolationDetails?.length > 0
                  ? nonExplicitViolationDetails
                  : [
                      {
                        sectionName: 'Status',
                        requirements: 'No warnings found',
                        tooltip: 'No warnings were detected in this RFP.',
                      },
                    ],
            },
          ]
        : []),
    ];
  };

  const PROCEED_COLOR = '#E8F5E9'; // pastel green
  const PROCEED_TEXT_COLOR = '#1B5E20'; // dark green
  const DO_NOT_PROCEED_COLOR = '#FFEBEE'; // pastel red
  const DO_NOT_PROCEED_TEXT_COLOR = '#B71C1C'; // dark red

  return (
    <MoreDrawer title="RFP Evaluation" open={open} close={close}>
      {confirmDelete ? (
        <Box sx={{ p: 3 }}>
          <Typography
            variant="h6"
            sx={{ fontWeight: '600', textTransform: 'uppercase' }}
            color="error"
          >
            Are you sure you want to delete this RFP?
          </Typography>
          <Row gap={'1rem'} sx={{ mt: 4, justifyContent: 'center' }}>
            <Button
              onClick={() => setConfirmDelete(false)}
              color="primary"
              variant="outlined"
            >
              Cancel
            </Button>
            <Button
              onClick={onDelete}
              disabled={processing}
              color="error"
              variant="contained"
            >
              Delete
            </Button>
          </Row>
        </Box>
      ) : (
        <Box sx={{ bgcolor: 'white' }}>
          {/* Hero Section */}
          <Box
            sx={{
              px: 1,
              pt: 4,
              py: 6,
              bgcolor: 'white',
            }}
          >
            {/* Critter and chat bubble section */}
            <Box sx={{ maxWidth: '4xl', mx: 'auto' }}>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'row',
                  width: '100%',
                  justifyContent: 'center',
                  gap: '32px',
                  mb: '32px',
                }}
              >
                <Box
                  component="img"
                  src={`${constants.TRIBBLE_CDN_URL}/images/tribble_critter_glasses_transparent.png`}
                  sx={{ height: '80px', width: '80px' }}
                  alt="Tribble Assistant"
                />
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontWeight: 'light',
                  }}
                >
                  <Box
                    sx={{
                      position: 'relative',
                      maxWidth: 'lg',
                      bgcolor: 'white',
                      p: '24px',
                      fontSize: '20px',
                      borderRadius: '16px',
                      boxShadow:
                        '0 2px 8px rgba(192,192,192,0.3), 0 1px 2px rgba(192,192,192,0.2)',
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: '50%',
                        right: '100%',
                        transform: 'translateY(-50%)',
                        borderWidth: '8px',
                        borderStyle: 'solid',
                        borderColor: 'transparent',
                        borderRightColor: 'white',
                        filter:
                          'drop-shadow(-1px 0 1px rgba(128,128,128,0.15))',
                      },
                    }}
                  >
                    <Typography
                      sx={{
                        color: 'black',
                        fontSize: '16px',
                        fontWeight: 'bold',
                      }}
                    >
                      {recommendationText}
                    </Typography>
                    <Typography
                      sx={{
                        display: 'block',
                        color: 'rgb(75, 85, 99)',
                        fontSize: '16px',
                        fontWeight: 'normal',
                        mt: '8px',
                      }}
                    >
                      {subText}
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Box>

            {/* Project Info Card */}
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                mb: '32px',
              }}
            >
              <Box
                sx={{
                  bgcolor: 'white',
                  width: '100%',
                }}
              >
                <Stack direction="column" alignItems="start" gap={2}>
                  <Stack direction="column">
                    <Typography
                      variant="h2"
                      sx={{
                        fontSize: '24px',
                        fontWeight: 600,
                        color: 'rgb(17, 24, 39)',
                      }}
                    >
                      {rfp.details.project_name || rfp.details.customer_name}
                    </Typography>
                    <Typography sx={{ color: 'rgb(107, 114, 128)', mt: '4px' }}>
                      {rfp.details.customer_name}
                    </Typography>
                  </Stack>
                </Stack>

                <Box
                  sx={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    gap: '8px',
                    mt: '16px',
                  }}
                >
                  {rfp.details.files?.map((file) => (
                    <Box
                      key={file.name}
                      sx={{
                        display: 'inline-flex',
                        alignItems: 'center',
                        gap: '6px',
                        px: '12px',
                        py: '6px',
                        bgcolor: 'rgb(249, 250, 251)',
                        borderRadius: '9999px',
                        fontSize: '14px',
                        color: 'rgb(107, 114, 128)',
                      }}
                    >
                      <TextSnippetOutlined
                        sx={{ width: '12px', height: '12px' }}
                      />
                      <span>{file.name}</span>
                    </Box>
                  ))}
                </Box>
              </Box>

              <Box
                sx={{
                  mt: '16px',
                  px: '16px',
                  py: '8px',
                  borderRadius: '9999px',
                  bgcolor: recommendationBgColor,
                  color: recommendationTextColor,
                  fontSize: '14px',
                  fontWeight: 500,
                  width: 'fit-content',
                }}
              >
                {recommendationLabel}
              </Box>
            </Box>

            {numQuestions === 0 && (
              <Box
                sx={{
                  p: 2,
                  mb: 4,
                  borderRadius: '8px',
                  bgcolor: 'white',
                  border: `1px solid ${theme.palette.error.main}`,
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    mb: 0.5,
                  }}
                >
                  <Typography
                    sx={{
                      color: theme.palette.error.main,
                      fontWeight: 500,
                      fontSize: '0.875rem',
                    }}
                  >
                    No requirements were detected in this RFP: cannot proceed
                  </Typography>
                </Box>
              </Box>
            )}

            {/* KPI Boxes */}
            <Box
              sx={{
                border: '2px solid rgb(243, 244, 246)',
                borderRadius: '8px',
                display: 'flex',
                flexDirection: 'column',
                width: '100%',
                marginBottom: '40px',
                '& > div': {
                  width: '100%',
                  borderBottom: '2px solid rgb(243, 244, 246)',
                  '&:last-child': {
                    borderBottom: 'none',
                  },
                },
              }}
            >
              {getMetrics(rationaleSections, qualification).map(
                (metric, index) => (
                  <Box key={index}>
                    <Box
                      onClick={() =>
                        setExpandedSection(
                          expandedSection === metric.label
                            ? null
                            : metric.label,
                        )
                      }
                      sx={{
                        px: '16px',
                        py: '16px',
                        '&:hover': {
                          bgcolor: 'rgb(249, 250, 251)',
                          cursor: 'pointer',
                        },
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                      }}
                    >
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '8px',
                        }}
                      >
                        <Box
                          sx={{
                            width: '8px',
                            height: '8px',
                            borderRadius: '9999px',
                            bgcolor: metric.dotColor,
                            display: { xs: 'none', sm: 'block' },
                          }}
                        />
                        <PrimaryTooltip title={metric.tooltip} enterDelay={250}>
                          <Typography
                            sx={{
                              color: metric.textColor,
                              fontWeight: 600,
                              textTransform: 'uppercase',
                              fontSize: '16px',
                            }}
                          >
                            {metric.label}
                          </Typography>
                        </PrimaryTooltip>
                      </Box>
                      <Box
                        sx={{ display: 'flex', alignItems: 'center', gap: 2 }}
                      >
                        <Typography
                          sx={{
                            color: metric.color,
                            fontSize: '24px',
                            fontWeight: 600,
                            textAlign: 'center',
                          }}
                        >
                          {metric.value}
                        </Typography>
                        {expandedSection === metric.label ? (
                          <KeyboardArrowUpIcon
                            sx={{ color: 'text.secondary', fontSize: 18 }}
                          />
                        ) : (
                          <KeyboardArrowDownIcon
                            sx={{ color: 'text.secondary', fontSize: 18 }}
                          />
                        )}
                      </Box>
                    </Box>
                    <Collapse in={expandedSection === metric.label}>
                      <Box
                        sx={{
                          p: 1,
                          bgcolor: 'rgb(249, 250, 251)',
                          borderTop: '1px solid rgb(243, 244, 246)',
                        }}
                      >
                        <Stack spacing={1}>
                          {metric.details.map((item, idx) => (
                            <Box
                              key={idx}
                              sx={{
                                p: 2,
                                borderRadius: '8px',
                                bgcolor: 'white',
                                border: '1px solid rgb(243, 244, 246)',
                                cursor: 'pointer',
                                '&:hover': {
                                  borderColor: metric.color,
                                  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
                                },
                                transition: 'all 0.2s',
                              }}
                              onClick={() =>
                                setSelectedSection({
                                  name: item.sectionName,
                                  requirements: item.tooltip || '',
                                  violations: item.violations,
                                })
                              }
                            >
                              <Box
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'space-between',
                                  mb: 0.5,
                                }}
                              >
                                <Typography
                                  sx={{
                                    color: metric.color,
                                    fontWeight: 500,
                                    fontSize: '0.875rem',
                                  }}
                                >
                                  {item.sectionName}
                                </Typography>
                                {item.points && (
                                  <Typography
                                    sx={{
                                      px: 2,
                                      py: 0.5,
                                      fontSize: '0.75rem',
                                      fontWeight: 500,
                                      color: metric.color,
                                      bgcolor: `${metric.color}10`,
                                      borderRadius: '100px',
                                    }}
                                  >
                                    {item.points}
                                  </Typography>
                                )}
                              </Box>
                              <Stack direction="column" gap={2}>
                                <Typography
                                  sx={{
                                    fontSize: '0.75rem',
                                    color: 'text.secondary',
                                  }}
                                >
                                  {item.requirements}
                                </Typography>
                                <Typography
                                  sx={{
                                    color: 'primary.main',
                                    fontSize: '0.75rem',
                                  }}
                                >
                                  Click to view details
                                </Typography>
                              </Stack>
                            </Box>
                          ))}
                        </Stack>
                      </Box>
                    </Collapse>
                  </Box>
                ),
              )}
            </Box>
          </Box>

          {/* Bottom Progress Bar - Only show on larger screens */}
          {/* <Box
            sx={{
              position: 'sticky',
              bottom: 0,
              left: 0,
              right: 0,
              p: 3,
              bgcolor: 'white',
              borderTop: '1px solid rgba(0, 0, 0, 0.1)',
              display: { xs: 'none', md: 'flex' },
              alignItems: 'center',
              justifyContent: 'space-between',
              mt: 4,
            }}
          >
            <Stack
              direction="row"
              spacing={2}
              alignItems="center"
              sx={{ flex: 1 }}
            >
              <Box sx={{ flex: 1 }}>
                <Typography
                  variant="caption"
                  sx={{ color: 'text.secondary', mb: 0.5, display: 'block' }}
                >
                  Upload
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={100}
                  sx={{
                    height: 6,
                    borderRadius: 3,
                    bgcolor: 'rgba(0, 0, 0, 0.05)',
                    '& .MuiLinearProgress-bar': {
                      bgcolor: 'rgb(34, 197, 94)',
                      borderRadius: 3,
                    },
                  }}
                />
              </Box>
              <Box sx={{ flex: 1 }}>
                <Typography
                  variant="caption"
                  sx={{ color: 'text.secondary', mb: 0.5, display: 'block' }}
                >
                  Evaluate
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={100}
                  sx={{
                    height: 6,
                    borderRadius: 3,
                    bgcolor: 'rgba(0, 0, 0, 0.05)',
                    '& .MuiLinearProgress-bar': {
                      bgcolor: 'rgb(34, 197, 94)',
                      borderRadius: 3,
                    },
                  }}
                />
              </Box>
              <Box sx={{ flex: 1 }}>
                <Typography
                  variant="caption"
                  sx={{ color: 'text.secondary', mb: 0.5, display: 'block' }}
                >
                  Generate
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={0}
                  sx={{
                    height: 6,
                    borderRadius: 3,
                    bgcolor: 'rgba(0, 0, 0, 0.05)',
                    '& .MuiLinearProgress-bar': {
                      bgcolor: 'rgb(34, 197, 94)',
                      borderRadius: 3,
                    },
                  }}
                />
              </Box>
            </Stack>
          </Box> */}

          {/* Action Buttons - Show on all screens */}
          {/* <Box
            sx={{
              position: { xs: 'fixed', md: 'static' },
              bottom: { xs: '16px', md: 'auto' },
              left: { xs: '16px', md: 'auto' },
              right: { xs: '16px', md: 'auto' },
              display: 'flex',
              gap: '16px',
              zIndex: 1,
              bgcolor: { xs: 'white', md: 'transparent' },
              p: { xs: 2, md: 0 },
              borderRadius: { xs: '12px', md: 0 },
              boxShadow: {
                xs: '0 0 10px rgba(0,0,0,0.1), 0 0 20px rgba(0,0,0,0.1)',
                md: 'none',
              },
            }}
          > */}
          <Box
            sx={{
              position: 'fixed',
              bottom: '16px',
              left: '50%', // Center horizontally
              transform: 'translateX(-50%)', // Center horizontally
              width: 'calc(100% - 32px)', // Full width minus margins
              maxWidth: '600px', // Maximum width for better appearance
              display: 'flex',
              justifyContent: 'center',
              gap: '16px',
              zIndex: 1,
              bgcolor: 'white',
              p: 2,
              borderRadius: '12px',
              boxShadow: '0 0 10px rgba(0,0,0,0.1), 0 0 20px rgba(0,0,0,0.1)',
            }}
          >
            <Button
              onClick={() => setConfirmDelete(true)}
              disabled={processing || rfp.rfp_status === 'rejected'}
              variant="outlined"
              size="medium"
              color="error"
              fullWidth
              sx={{
                borderRadius: '100px',
                textTransform: 'none',
                fontWeight: 500,
                px: 4,
                '&:hover': {
                  bgcolor: 'rgba(0, 0, 0, 0.05)',
                },
              }}
            >
              {rfp.rfp_status === 'rejected' ? 'RFP was rejected' : 'Reject'}
            </Button>
            {rfp.rfp_status !== 'rejected' && (
              <Button
                onClick={onContinue}
                disabled={processing || (!processing && numQuestions === 0)}
                variant="contained"
                size="medium"
                color="primary"
                fullWidth
                sx={{
                  borderRadius: '100px',
                  textTransform: 'none',
                  fontWeight: 500,
                  px: 4,
                }}
              >
                Proceed
              </Button>
            )}
          </Box>
          {/* Add RequirementsModal */}
          {selectedSection && (
            <RequirementsModal
              isOpen={!!selectedSection}
              onClose={() => setSelectedSection(null)}
              sectionName={selectedSection.name}
              requirements={selectedSection.requirements}
              violations={selectedSection.violations}
            />
          )}
        </Box>
      )}
    </MoreDrawer>
  );
};
