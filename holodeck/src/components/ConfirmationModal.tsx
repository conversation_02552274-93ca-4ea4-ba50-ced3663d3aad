import * as React from 'react';

import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import Stack from '@mui/material/Stack';

import useTheme from '@mui/material/styles/useTheme';

import { Box, Typography } from '@mui/material';
import { ConfirmationModalBaseProps } from 'utils/model';

interface ConfirmationModalProps extends ConfirmationModalBaseProps {
  open: boolean;
  container?: HTMLDivElement | null;
  destructive?: boolean;
  fullWidth?: boolean;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  open,
  title,
  message,
  confirmText,
  cancelText,
  handleConfirm,
  handleCancel,
  container,
  destructive = false,
  children,
  fullWidth,
}) => {
  const theme = useTheme();

  return (
    <Dialog
      fullWidth={fullWidth == true}
      open={open}
      onClose={handleCancel}
      container={container ? container : document.body}
      aria-labelledby="responsive-dialog-title"
      sx={{ '& .MuiDialog-paper': { m: 2, width: 'calc(100% - 32px)' } }}
    >
      <DialogTitle
        id="responsive-dialog-title"
        sx={{ fontSize: '1.2rem', fontWeight: '600', pt: 2, pb: 3, px: 2 }}
      >
        {title}
      </DialogTitle>
      <DialogContent sx={{ p: 2 }}>
        <Typography variant="body2">{message}</Typography>
        {children}
      </DialogContent>

      <DialogActions sx={{ p: 2 }}>
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
          sx={{ width: '100%' }}
        >
          <Box sx={{ mr: 2 }}>
            <Button
              variant="outlined"
              autoFocus
              onClick={handleCancel}
              size="small"
            >
              {cancelText}
            </Button>
          </Box>
          <Button
            size="small"
            onClick={handleConfirm}
            autoFocus
            color={destructive ? 'error' : 'primary'}
            sx={{
              width: 'fit-content',
              '&:hover': {
                backgroundColor: destructive
                  ? theme.palette.error.main
                  : theme.palette.primary.main,
                color: '#fff',
              },
            }}
          >
            {confirmText}
          </Button>
        </Stack>
      </DialogActions>
    </Dialog>
  );
};

export default ConfirmationModal;
