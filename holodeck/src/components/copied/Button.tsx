import { But<PERSON>, <PERSON>ack, Typography, useTheme } from '@mui/material';
import React, { <PERSON><PERSON><PERSON>, PropsWithChildren, PropsWithRef } from 'react';

type ButtonProps = PropsWithChildren &
  PropsWithRef<{
    onClick: (e: MouseEvent<HTMLElement>) => void;
    disabled?: boolean;
    primary?: boolean;
    secondary?: boolean;
    red?: boolean;
    sx?: any;
    small?: boolean;
    large?: boolean;
    short?: boolean;
    blueBorder?: boolean;
  }>;

export const ButtonBase: React.FC<ButtonProps> = ({
  children,
  disabled,
  onClick,
  primary,
  secondary,
  red,
  sx,
  small,
  large,
  short,
  blueBorder,
}) => {
  const theme = useTheme();
  if (!sx) sx = {};
  if (secondary) {
    sx.backgroundColor = theme.palette.common.white;
    sx.border = `1px solid ${
      blueBorder ? `${theme.palette.primary.main}60` : theme.palette.grey.A200
    }`;
  }
  if (primary) {
    sx.backgroundColor = theme.palette.primary.main;
    sx.border = `1px solid ${theme.palette.grey.A200}`;
  }
  if (red) {
    sx.backgroundColor = theme.palette.error.main;
  }
  if (disabled) {
    sx.backgroundColor = theme.palette.grey.A100;
    sx.border = theme.palette.grey.A100;
  }

  return (
    <Button
      onClick={onClick}
      disabled={disabled}
      sx={{
        padding: small
          ? '4px 8px'
          : short
            ? '4px 20px 4px 20px'
            : large
              ? '12px 28px'
              : '10px 20px',
        borderRadius: '100px',
        alignItems: 'center',
        justifyContent: 'center',
        ...sx,
        '&:hover': {
          backgroundColor: sx.backgroundColor,
        },

        '&:hover p.MuiTypography-root': {
          color: sx.color,
        },
      }}
    >
      {children}
    </Button>
  );
};
const buttonTextStyle = {
  color: '#FFFFFFFF',
  fontSize: '14px',
  fontWeight: '600',
  textTransform: 'none',
};

type BasicButtonProps = ButtonProps & { label: string };
export const BasicButton: React.FC<BasicButtonProps> = ({
  onClick,
  label,
  small,
  large,
  short,
  primary,
  secondary,
  disabled,
  blueBorder,
  red,
}) => {
  const theme = useTheme();
  let textColor = theme.palette.common.white;
  if (secondary && !disabled) {
    textColor = theme.palette.primary.main;
  }
  return (
    <ButtonBase
      onClick={onClick}
      primary={primary}
      secondary={secondary}
      small={small}
      large={large}
      short={short}
      disabled={disabled}
      blueBorder={blueBorder}
      red={red}
    >
      <Typography
        color={'textColor'}
        sx={{ ...buttonTextStyle, color: textColor }}
      >
        {label}
      </Typography>
    </ButtonBase>
  );
};
