import {
  Chip,
  Link,
  Stack,
  SxProps,
  Theme,
  Typography,
  useTheme,
} from '@mui/material';
import React, {
  ChangeEvent,
  PropsWithChildren,
  PropsWithRef,
  useCallback,
  useState,
} from 'react';

type DropZoneProps = PropsWithRef<{
  handleFilesAdded: (files: File[]) => void;
  text: string;
  handleSelectFile: (event: ChangeEvent<HTMLInputElement>) => void;
  singleFile?: boolean;
  accept: string;
}>;
export const DropZone: React.FC<DropZoneProps> = ({
  handleFilesAdded,
  text,
  singleFile,
  handleSelectFile,
  accept,
}) => {
  const theme = useTheme();
  const [isDragging, setIsDragging] = useState(false);

  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(false);
  }, []);

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(false);

    if (event?.dataTransfer?.files?.length) {
      const droppedFiles = Array.from(event.dataTransfer.files);
      handleFilesAdded(droppedFiles);
    }
  };

  return (
    <Stack
      onClick={() => document.getElementById('file-upload-input')?.click()}
      sx={{
        display: 'flex',
        backgroundColor: isDragging
          ? theme.palette.grey[300]
          : theme.palette.grey[100],
        border: `1px dashed ${theme.palette.primary.main}60`,
        borderRadius: '8px',
        p: '32px',
        justifyContent: 'center',
        alignItems: 'center',
        cursor: 'pointer',
        minHeight: '80px',
      }}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      <input
        accept={accept}
        hidden
        id="file-upload-input"
        multiple={!singleFile}
        onChange={handleSelectFile}
        type="file"
      />
      <Typography
        textAlign={'center'}
        variant="body2"
        color={theme.palette.grey[600]}
      >
        {`${text} `}
        <Link
          underline="always"
          color="primary"
          onClick={(e) => {
            e.stopPropagation();
            document.getElementById('file-upload-input')?.click();
          }}
        >
          {`choose file${singleFile ? '' : 's'}`}
        </Link>
        {` to upload`}
      </Typography>
    </Stack>
  );
};

type RowProps = PropsWithChildren<{
  justifyContent?: 'flex-start' | 'center' | 'flex-end' | 'space-between';
  gap?: string | number;
  sx?: SxProps<Theme>;
  width?: string | number;
}>;
export const Row: React.FC<RowProps> = ({
  children,
  justifyContent,
  gap,
  width,
  sx,
}) => {
  return (
    <Stack
      sx={{
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent,
        gap,
        width,
        ...sx,
      }}
    >
      {children}
    </Stack>
  );
};

type StatusChipProps = PropsWithRef<{
  status:
    | 'Active'
    | 'Inactive'
    | 'Processing'
    | 'Error'
    | 'Archived'
    | 'Ready'
    | 'Pending'
    | 'Configure'
    | string;
  sx?: any;
  onClick?: (e) => void;
}>;

export const StatusChip: React.FC<StatusChipProps> = ({
  status,
  sx,
  onClick,
}) => {
  const theme = useTheme();
  status = status.charAt(0).toUpperCase() + status.slice(1);
  return (
    <Stack
      onClick={onClick}
      sx={{ cursor: onClick ? 'pointer' : 'default', ...sx }}
    >
      <Chip
        label={status}
        variant="filled"
        sx={{
          textTransform: 'none',
          backgroundColor:
            status === 'Processing' || status === 'Ready'
              ? theme.palette.secondary.light
              : status === 'Configure'
                ? theme.palette.error.light
                : status === 'Error'
                  ? theme.palette.error.light
                  : status === 'Inactive'
                    ? theme.palette.grey[300]
                    : theme.palette.primary.light,
          color:
            status === 'Processing' || status === 'Ready'
              ? theme.palette.secondary.main
              : status === 'Configure'
                ? theme.palette.warning.dark
                : status === 'Error'
                  ? theme.palette.grey.A100
                  : status === 'Inactive'
                    ? theme.palette.grey[600]
                    : theme.palette.primary.main,
          fontWeight: '600',
          width: 'fit-content',
        }}
      />
    </Stack>
  );
};

export const DocRowTitle: React.FC<{ title: string }> = ({ title }) => {
  return (
    <Typography variant="body2" sx={{ fontWeight: '500' }}>
      {title}
    </Typography>
  );
};
