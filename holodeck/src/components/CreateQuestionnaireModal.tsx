import React, { useState } from 'react';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import Modal from '@mui/material/Modal';
import OutlinedInput from '@mui/material/OutlinedInput';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';

import HelpOutlineIcon from '@mui/icons-material/HelpOutline';

import { Theme, useTheme } from '@mui/material/styles';

import { SecondaryTooltip } from './StyledTooltips';

interface CreateQuestionnaireModalProps {
  show: boolean;
  createQuestionnaire: (
    label: string,
    customerName: string,
    metadataFilter: any,
  ) => void;
  close: () => void;
  container?: HTMLDivElement | null;
  metadataFilters: any;
}

function getMenuItemStyles(
  mdfType: string,
  selectedMetadataFilterType: string[],
  theme: Theme,
) {
  return {
    fontWeight: selectedMetadataFilterType.includes(mdfType) ? '500' : '300',
    color: selectedMetadataFilterType.includes(mdfType)
      ? theme.palette.primary.main
      : '#000000de',
  };
}

const CreateQuestionnaireModal: React.FC<CreateQuestionnaireModalProps> = ({
  show,
  createQuestionnaire,
  close,
  container,
  metadataFilters,
}) => {
  const [errorMessage, setErrorMessage] = useState('');
  const [selectedMetadataFilterType, setSelectedMetadataFilterType] = useState<
    string[]
  >([]);
  const [selectedMetadataFilters, setSelectedMetadataFilters] = useState<
    string[]
  >([]);
  const [openMenuCoords, setOpenMenuCoords] = useState({ left: 0, top: 0 });

  const theme = useTheme();

  const inputRef = React.useRef<HTMLInputElement>(null);
  const inputCustomerRef = React.useRef<HTMLInputElement>(null);
  const inputSelectRef = React.useRef<HTMLSelectElement>(null);

  const metadataFilterTypes: string[] = metadataFilters.map(
    (mdf: any) => mdf.name,
  );
  const mapMetadataFilters: any = {};
  metadataFilters.forEach((mdf: any) => {
    mapMetadataFilters[mdf.name] = mdf;
  });

  function createNewQuestionnaire() {
    if (inputRef.current?.value !== undefined) {
      if (inputRef.current?.value != '') {
        if (
          selectedMetadataFilterType.length > 0 &&
          selectedMetadataFilters.length == 0
        ) {
          setErrorMessage('A filter type was selected, but no values selected');
        } else {
          const metadataFilter =
            selectedMetadataFilterType.length > 0 &&
            selectedMetadataFilters.length > 0
              ? {
                  type_id: mapMetadataFilters[selectedMetadataFilterType[0]].id,
                  type_name: selectedMetadataFilterType[0],
                  values: selectedMetadataFilters,
                }
              : {};

          createQuestionnaire(
            inputRef.current?.value,
            inputCustomerRef.current?.value || '',
            metadataFilter,
          );

          setErrorMessage('');
          setSelectedMetadataFilterType([]);
          setSelectedMetadataFilters([]);
        }
      } else {
        setErrorMessage('Please enter a label for the questionnaire');
      }
    }
  }

  const handleSelectTypeOpen = (
    event: React.MouseEvent<HTMLElement>,
    isTypeSelect: boolean,
  ) => {
    const eventCoords = event.currentTarget.getBoundingClientRect();
    if (container) {
      const parentCoords = container.getBoundingClientRect();

      // Weirdness we need to manually tweak
      let topOffset = 0;
      if (isTypeSelect) {
        if (selectedMetadataFilterType.length > 0) {
          topOffset = eventCoords.top - parentCoords.top - 20;
        } else {
          topOffset = eventCoords.top - parentCoords.top - 45;
        }
      } else {
        topOffset = eventCoords.top - parentCoords.top - 20;
      }

      setOpenMenuCoords({ left: 24, top: topOffset });
    }
  };

  const handleMdfTypeSelect = (event: SelectChangeEvent<string[]>) => {
    const {
      target: { value },
    } = event;

    setSelectedMetadataFilterType(
      typeof value === 'string' ? value.split(',') : value,
    );
  };

  const handleMdfTypeSelectItem = (el: HTMLLIElement) => {
    if (selectedMetadataFilterType.includes(el.innerText)) {
      // The above select event handler also gets called, so wait a little so not to interfere
      // (tried e.stopProgation, but that didn't work)
      setTimeout(() => {
        setSelectedMetadataFilterType([]);
        setSelectedMetadataFilters([]);
      }, 100);
    }
  };

  // Could combine these two w the ones above and just create another input param...but anyway...
  const handleMdfSelect = (event: SelectChangeEvent<string[]>) => {
    const {
      target: { value },
    } = event;

    setSelectedMetadataFilters(
      typeof value === 'string' ? value.split(',') : value,
    );
  };

  function closeModal() {
    setSelectedMetadataFilterType([]);
    setSelectedMetadataFilters([]);
    setErrorMessage('');
    close();
  }

  let modalMinHeight = '230px';
  if (show && metadataFilterTypes.length > 0) {
    if (selectedMetadataFilterType.length > 0) {
      modalMinHeight = '390px';
    } else {
      modalMinHeight = '340px';
    }
  }

  return (
    <Modal
      open={show}
      onClose={closeModal}
      container={container ? container : document.body}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <Box
        sx={{
          position: 'absolute' as const,
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: container ? 400 : 500,
          minHeight: modalMinHeight,
          maxHeight: '390px',
          bgcolor: 'background.paper',
          borderRadius: '4px',
          boxShadow: 24,
          p: 3,
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-between',
            height: modalMinHeight,
          }}
        >
          <Box sx={{ display: 'flex', flexDirection: 'column' }}>
            <Typography variant="h6">Create New Questionnaire</Typography>

            <Typography variant="body1" sx={{ mt: 2, mb: 1 }}>
              Please provide some information to get started:
            </Typography>

            <TextField
              id="inp_questionnaire_label"
              placeholder="Enter a label for this questionnaire..."
              label=""
              error={errorMessage != ''}
              multiline
              maxRows={1}
              inputRef={inputRef}
              required={true}
              sx={{
                mt: 1,
                mb: errorMessage != '' ? '4px' : 1,
                '& textarea': {
                  fontWeight: '300',
                },
                '& .MuiInputBase-multiline': {
                  padding: '4px 12px 8px 12px',
                },
                '& .notranslate': {
                  display: 'none',
                },
              }}
            />

            <TextField
              id="inp_customer_name"
              placeholder="Enter the customer this questionnaire is for..."
              label=""
              maxRows={1}
              inputRef={inputCustomerRef}
              sx={{
                mt: 1,
                mb: 2,
                '& textarea': {
                  fontWeight: '300',
                },
                '& .MuiInputBase-input': {
                  padding: '4px 12px 8px 12px',
                },
                '& .notranslate': {
                  display: 'none',
                },
              }}
            />

            {metadataFilterTypes.length > 0 && (
              <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'row',
                    mt: 2,
                    mb: 2,
                    alignItems: 'center',
                  }}
                >
                  <Typography
                    variant="body1"
                    sx={{
                      mr: 1,
                      color: theme.palette.grey[600],
                      textTransform: 'uppercase',
                    }}
                  >
                    Optional
                  </Typography>

                  <SecondaryTooltip
                    arrow
                    title="Filter source text to a specific filter (e.g. 'this questionnaire pertains to a specific product or deployment type')"
                  >
                    <HelpOutlineIcon
                      sx={{
                        height: '0.8em',
                        width: '0.8em',
                        color: theme.palette.grey[500],
                      }}
                    />
                  </SecondaryTooltip>
                </Box>

                <FormControl size="small" sx={{ width: '100%', mb: 2 }}>
                  <InputLabel id="mdfType-label">
                    This questionnaire is specific to...
                  </InputLabel>
                  <Select
                    labelId="mdfType-label"
                    id="selMdfType"
                    multiple
                    onOpen={(e: React.MouseEvent<HTMLElement>) =>
                      handleSelectTypeOpen(e, true)
                    }
                    onChange={handleMdfTypeSelect}
                    value={selectedMetadataFilterType}
                    label={'This questionnaire is specific to...'}
                    input={
                      <OutlinedInput
                        label={'This questionnaire is specific to...'}
                      />
                    }
                    inputRef={inputSelectRef}
                    MenuProps={
                      container
                        ? {
                            disablePortal: true,
                            container: container,
                            anchorReference: 'anchorPosition',
                            anchorPosition: {
                              left: openMenuCoords.left,
                              top: openMenuCoords.top,
                            },
                            transformOrigin: { horizontal: 0, vertical: 0 },
                          }
                        : {}
                    }
                    sx={{
                      width: '100%',
                      '& .notranslate': { display: 'none' },
                    }}
                  >
                    {metadataFilterTypes.map((mdf: string) => (
                      <MenuItem
                        key={mdf}
                        value={mdf}
                        style={getMenuItemStyles(
                          mdf,
                          selectedMetadataFilterType,
                          theme,
                        )}
                        onClick={(evt) => {
                          const el = evt.target as HTMLLIElement;
                          handleMdfTypeSelectItem(el);
                        }}
                        sx={{ fontSize: '0.85em' }}
                      >
                        {mdf}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                {selectedMetadataFilterType.length > 0 && (
                  <FormControl
                    size="small"
                    sx={{ width: '100%', mt: 1, mb: 2 }}
                  >
                    <InputLabel id="mdf-label">{`Select applicable "${selectedMetadataFilterType[0]}" values`}</InputLabel>
                    <Select
                      labelId="mdf-label"
                      id="selMdf"
                      multiple
                      onOpen={(e: React.MouseEvent<HTMLElement>) =>
                        handleSelectTypeOpen(e, false)
                      }
                      onChange={handleMdfSelect}
                      value={selectedMetadataFilters}
                      label={`Select applicable "${selectedMetadataFilterType[0]}" values`}
                      input={
                        <OutlinedInput
                          label={`Select applicable "${selectedMetadataFilterType[0]}" values`}
                        />
                      }
                      MenuProps={
                        container
                          ? {
                              disablePortal: true,
                              container: container,
                              anchorReference: 'anchorPosition',
                              anchorPosition: {
                                left: openMenuCoords.left,
                                top: openMenuCoords.top,
                              },
                              transformOrigin: { horizontal: 0, vertical: 0 },
                            }
                          : {}
                      }
                      sx={{
                        width: '100%',
                        '& .notranslate': { display: 'none' },
                      }}
                    >
                      {mapMetadataFilters[
                        selectedMetadataFilterType[0]
                      ].filter_values.map((mdf: any) => (
                        <MenuItem
                          key={mdf.value}
                          value={mdf.value}
                          style={getMenuItemStyles(
                            mdf.value,
                            selectedMetadataFilters,
                            theme,
                          )}
                          sx={{ fontSize: '0.85em' }}
                        >
                          {mdf.value}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              </Box>
            )}

            {errorMessage != '' && (
              <Typography
                variant="body1"
                sx={{ color: theme.palette.error.main }}
              >
                ERROR: {errorMessage}
              </Typography>
            )}
          </Box>

          <Box
            sx={{
              display: 'flex',
              flexDirection: 'row',
              width: '100%',
              justifyContent: 'flex-end',
              alignItems: 'center',
              mt: 3,
            }}
          >
            <Button
              variant="text"
              onClick={closeModal}
              sx={{ minWidth: '0', width: 'fit-content', mr: 3 }}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              onClick={createNewQuestionnaire}
              sx={{ minWidth: '0', width: 'fit-content' }}
            >
              Create
            </Button>
          </Box>
        </Box>
      </Box>
    </Modal>
  );
};

export default CreateQuestionnaireModal;
