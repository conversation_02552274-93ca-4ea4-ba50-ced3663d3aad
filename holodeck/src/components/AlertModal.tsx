import * as React from 'react';

import Dialog, { DialogProps } from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';

interface AlertModalProps extends DialogProps {
  title: string;
  message: string;
}

const AlertModal: React.FC<AlertModalProps> = ({
  title,
  message,
  ...props
}) => {
  return (
    <Dialog {...props}>
      <DialogTitle id="responsive-dialog-title">{title}</DialogTitle>
      <DialogContent>
        <DialogContentText>{message}</DialogContentText>
      </DialogContent>
    </Dialog>
  );
};

export default AlertModal;
