import * as React from 'react';
import { useState } from 'react';

import Box from '@mui/material/Box';
import Zoom from '@mui/material/Zoom';

interface LoadingCritterProps {
  isDOMMode?: boolean;
}

const LoadingCritter: React.FC<LoadingCritterProps> = ({ isDOMMode }) => {
  const [show, setShow] = useState(true);

  const critterURL = isDOMMode
    ? chrome.runtime.getURL('../assets/critter_small.png')
    : '../assets/critter_small.png';

  const triggerClose = () => {
    setTimeout(() => {
      setShow(false);
    }, 2500);
  };
  const triggerRestart = () => {
    setTimeout(() => {
      setShow(true);
    }, 500);
  };

  const CRITTER_SIZE = 20;

  return (
    <Box sx={{ display: 'flex', flexDirection: 'row' }}>
      <Zoom
        key={'LoadingCritter_0'}
        in={show}
        timeout={{ enter: 500, exit: 150 }}
        onEnter={triggerClose}
      >
        <img
          src={critterURL}
          style={{ height: CRITTER_SIZE, width: CRITTER_SIZE, marginRight: 16 }}
        />
      </Zoom>
      <Zoom
        key={'LoadingCritter_1'}
        in={show}
        timeout={{ enter: 500, exit: 150 }}
        style={{ transitionDelay: show ? '500ms' : '0ms' }}
      >
        <img
          src={critterURL}
          style={{ height: CRITTER_SIZE, width: CRITTER_SIZE, marginRight: 16 }}
        />
      </Zoom>
      <Zoom
        key={'LoadingCritter_2'}
        in={show}
        timeout={{ enter: 500, exit: 150 }}
        style={{ transitionDelay: show ? '1000ms' : '0ms' }}
      >
        <img
          src={critterURL}
          style={{ height: CRITTER_SIZE, width: CRITTER_SIZE, marginRight: 16 }}
        />
      </Zoom>
      <Zoom
        key={'LoadingCritter_3'}
        in={show}
        timeout={{ enter: 500, exit: 150 }}
        style={{ transitionDelay: show ? '1500ms' : '0ms' }}
      >
        <img
          src={critterURL}
          style={{ height: CRITTER_SIZE, width: CRITTER_SIZE, marginRight: 16 }}
        />
      </Zoom>
      <Zoom
        key={'LoadingCritter_4'}
        in={show}
        timeout={{ enter: 500, exit: 150 }}
        style={{ transitionDelay: show ? '2000ms' : '0ms' }}
        onExited={triggerRestart}
      >
        <img
          src={critterURL}
          style={{ height: CRITTER_SIZE, width: CRITTER_SIZE, marginRight: 16 }}
        />
      </Zoom>
    </Box>
  );
};

export default LoadingCritter;
