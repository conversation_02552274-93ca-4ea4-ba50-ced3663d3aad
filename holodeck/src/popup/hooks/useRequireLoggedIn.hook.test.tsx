test.todo(
  "Need to spend time and mock the lower level dependencies. Time we don't have right now.",
);

// import React from 'react';
// import { useRequireLoggedIn } from './useRequireLoggedIn.hook';
// import { useRootStore } from './useRootStore';
// import { RenderResult, render, act } from '@testing-library/react'

// const UseRequireLoggedInTestComponent: React.FC = () => {
//     const { navigate, setAuth, page, auth } = useRootStore(({navigate, setAuth, page, auth}) => ({navigate, setAuth, page, auth}));
//     useRequireLoggedIn();

//     const navigateHome = (e: React.SyntheticEvent) => {
//         e.preventDefault();
//         navigate('home');
//     }

//     const setSuccessfulAuth = (e: React.SyntheticEvent) => {
//         e.preventDefault();
//         setAuth({ status: 'success', data: { accessToken: 'foo-bar-baz', env: 'test' } });
//     }

//     return (
//         <div>
//             <div data-testid="page-indicator">{page}</div>
//             <div data-testid="auth-status-indicator">{auth.status}</div>
//             <button data-testid="set-user" onClick={setSuccessfulAuth}>Set User</button>
//             <button data-testid="navigate-home" onClick={navigateHome}>Navigate Home</button>
//         </div>
//     )
// }

// describe("useAuthGuard", () => {
//     let element: RenderResult;

//     beforeEach(() => {
//         element = render(<UseRequireLoggedInTestComponent />);
//     });

//     it('should render the login page by default', () => {
//         expect(element.getByTestId('page-indicator')).toHaveTextContent('login');
//     })

//     it('should return the user to the home page if they are not logged in', async () => {
//         expect(element.getByTestId('page-indicator')).toHaveTextContent('login');
//         act(() => element.getByTestId('navigate-home').click());
//         expect(element.getByTestId('page-indicator')).toHaveTextContent('login');
//     });

//     it('should let the user successfully navigate to the home page if they are logged in', async () => {
//         act(() => element.getByTestId('set-user').click());
//         expect(element.getByTestId('page-indicator')).toHaveTextContent('login');
//         act(() => element.getByTestId('navigate-home').click());
//         expect(element.getByTestId('page-indicator')).toHaveTextContent('home');
//     });
// });
