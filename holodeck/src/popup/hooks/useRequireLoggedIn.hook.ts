import { useEffect } from 'react';
import { checkConnection, clearAuthConfig } from '../../utils/auth0';
import { TokenRefreshManager } from '../../utils/tokenRefreshManager';
import { getEnv } from '../state/auth.storage';
import { useRootStore } from '../state/useRootStore';

/**
 * This hook is used to check if the user is logged in. It will check if the access token and env config are still valid.
 * It makes API calls to check if they are still valid. If they are not, it will clear the chrome storage and navigate to the login page.
 * It runs once on mount.
 */
export const useRequireLoggedIn = () => {
  const rootStore = useRootStore();

  useEffect(() => {
    (async () => {
      // ============================ Check if session is still valid ============================
      const savedEnv = await getEnv();
      const validToken = await TokenRefreshManager.getValidToken();
      // TODO: Migrate `checkConnection` to the new functions.
      const isConnected =
        savedEnv &&
        validToken &&
        (await checkConnection(validToken, rootStore));

      if (!isConnected) {
        // Access token no longer valid. Clear chrome and navigate to login.
        // TODO: migrate `clearAuthConfig` to the new functions.
        await clearAuthConfig();
        rootStore.setAuth({ status: 'error', error: 'Authorization failed' });
        rootStore.navigate({ name: 'login' });
        return;
      }

      // TODO: remove?
      // unsure if we need this anymore. in any case, we should not be storing access token and env config in store.
      // instead, we should be storing it in chrome storage and reading from there when we need it.

      if (process.env.NODE_ENV === 'production') {
        rootStore.setAuth({
          status: 'success',
          data: {
            accessToken: validToken,
            env: 'prod',
          },
        });
      } else {
        rootStore.setAuth({
          status: 'success',
          data: {
            accessToken: validToken,
            // TODO: Fix this any
            env: savedEnv as any,
          },
        });
      }
    })();
  }, []);
};
