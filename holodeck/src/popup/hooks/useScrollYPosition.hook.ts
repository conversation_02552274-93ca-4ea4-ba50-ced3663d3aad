import { useEffect, useState } from 'react';

/**
 * This hook continuously updates the scrollY position of the window.
 * It can be used to determine where the user is on the page.
 * @returns the current scrollY position of the window
 */
export const useScrollYPosition = () => {
  const [scrollYPosition, setScrollYPosition] = useState<number>(0);

  useEffect(() => {
    const onScroll = () => {
      setScrollYPosition(window.scrollY);
    };
    window.addEventListener('scroll', onScroll);
    return () => window.removeEventListener('scroll', onScroll);
  });

  return { scrollYPosition };
};
