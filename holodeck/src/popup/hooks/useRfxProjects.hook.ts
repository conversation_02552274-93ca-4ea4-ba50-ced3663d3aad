import { useEffect, useState } from 'react';
import { Rfp } from '../../components/EndToEndRfp';
import { QuestionnaireDto } from '../api/questionnaires.api';
import {
  getAllRfxProjects,
  mapRfxProjectToQuestionnaireDto,
  mapRfxProjectToRfpDto,
} from '../api/rfx_projects.api';

/**
 * Custom hook to fetch RFX projects and convert them to QuestionnaireDto and E2E Rfp formats
 * for backwards compatibility with the existing UI
 */
export const useRfxProjects = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | undefined>();
  const [questionnaires, setQuestionnaires] = useState<QuestionnaireDto[]>([]);
  const [e2eRfps, setE2eRfps] = useState<Rfp[]>([]);

  const fetchRfxProjects = async () => {
    setIsLoading(true);
    try {
      const result = await getAllRfxProjects();

      if (result.success && result.response) {
        const regularQuestionnaires: QuestionnaireDto[] = [];
        const e2eRfpRecords: Rfp[] = [];

        // Process each project and its content records
        result.response.forEach((project) => {
          (project.content_records || []).forEach((contentRecord) => {
            // Check if this is an E2E RFP (longForm and isE2E flags in the details)
            const isE2ERFP =
              contentRecord.details?.agentParams?.longForm === true &&
              !!contentRecord.details?.rfpId;

            if (isE2ERFP) {
              // For E2E RFPs, use the E2E RFP mapper
              const rfp = mapRfxProjectToRfpDto(project, contentRecord);
              e2eRfpRecords.push(rfp as Rfp);
            } else {
              // Regular questionnaires use the questionnaires mapper
              const questionnaireDto = mapRfxProjectToQuestionnaireDto(
                project,
                contentRecord,
              );
              regularQuestionnaires.push(questionnaireDto);
            }
          });
        });

        setQuestionnaires(regularQuestionnaires);
        setE2eRfps(e2eRfpRecords);
        setError(undefined);
      } else {
        setError('Failed to fetch RFX projects');
      }
    } catch (e: any) {
      console.error('[useRfxProjects] Error fetching RFX projects:', e);
      setError(e?.message || 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchRfxProjects();
  }, []);

  return {
    isLoading,
    error,
    questionnaires,
    e2eRfps,
    refetch: fetchRfxProjects,
  };
};
