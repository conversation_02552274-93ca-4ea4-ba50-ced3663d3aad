import { useEffect, useState } from 'react';
import { QuestionnaireDto } from '../api/questionnaires.api';
import {
  getRfxProjectByRfxContentId,
  mapRfxProjectToQuestionnaireDto,
} from '../api/rfx_projects.api';

/**
 * Custom hook to fetch a specific RFX project content and convert it to QuestionnaireDto format
 * for backwards compatibility with the existing UI
 */
export const useRfxProjectContent = (contentId: string | number) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | undefined>();
  const [questionnaire, setQuestionnaire] = useState<
    QuestionnaireDto | undefined
  >();

  const fetchRfxProjectContent = async () => {
    if (!contentId) {
      setError('No content ID provided');
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      // Convert contentId to number
      const numericContentId = Number(contentId);
      if (isNaN(numericContentId)) {
        throw new Error('Invalid content ID');
      }

      const result = await getRfxProjectByRfxContentId(numericContentId);

      if (result.success && result.response) {
        const project = result.response;

        // Find the content record that matches the contentId
        const contentRecord = project.content_records.find(
          (cr) =>
            cr.rfx_content_id === numericContentId ||
            cr.id === numericContentId,
        );

        if (contentRecord) {
          // Convert to questionnaire format
          const mappedQuestionnaire = mapRfxProjectToQuestionnaireDto(
            project,
            contentRecord,
          );
          setQuestionnaire(mappedQuestionnaire);
          setError(undefined);
        } else {
          setError('Content record not found in project');
        }
      } else {
        setError('Failed to fetch RFX project');
      }
    } catch (e: any) {
      console.error(
        '[useRfxProjectContent] Error fetching RFX project content:',
        e,
      );
      setError(e?.message || 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchRfxProjectContent();
  }, [contentId]);

  return {
    isLoading,
    error,
    questionnaire,
    refetch: fetchRfxProjectContent,
  };
};
