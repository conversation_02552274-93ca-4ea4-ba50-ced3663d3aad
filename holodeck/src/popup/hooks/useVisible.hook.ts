import { useEffect, useState } from 'react';

export function useIsVisible(ref: React.MutableRefObject<HTMLDivElement>) {
  const [isIntersecting, setIntersecting] = useState(false);

  useEffect(() => {
    let observer;

    const timer = setTimeout(() => {
      const observer = new IntersectionObserver(
        ([entry]) => setIntersecting(entry.isIntersecting),
        {
          threshold: 0.5,
        },
      );
      if (ref.current) {
        observer.observe(ref.current);
      }
    }, 200);

    return () => {
      if (observer) {
        observer.disconnect();
      }

      clearTimeout(timer);
    };
  }, [ref.current]);

  return isIntersecting;
}
