import {
  Alert,
  Al<PERSON><PERSON>itle,
  Click<PERSON>wayListener,
  Container,
  Snackbar,
  useTheme,
} from '@mui/material';
import React, { FC } from 'react';
import { HomePage } from './pages/HomePage';
import { LoginPage } from './pages/LoginPage';
import { useRootStore } from './state/useRootStore';

export const SidePanel: FC = () => {
  const { page, notification, clearNotification } = useRootStore();
  const theme = useTheme();

  const currentPage = page.name;
  const isLogin = currentPage == 'login';

  const Page = isLogin ? LoginPage : HomePage;

  return (
    <Container disableGutters={true} sx={{ height: '100%' }}>
      {notification && (
        <ClickAwayListener onClickAway={clearNotification}>
          <Snackbar
            ClickAwayListenerProps={{ onClickAway: clearNotification }}
            anchorOrigin={{ horizontal: 'center', vertical: 'top' }}
            open={notification != null}
          >
            <Alert
              variant="standard"
              severity={notification.type}
              onClose={() => clearNotification()}
              sx={{
                width: 300,
                border: `1px solid ${theme.palette[notification.type].main}`,
                borderRadius: '4px',
              }}
            >
              <AlertTitle sx={{ mt: '2px' }}>{notification.header}</AlertTitle>
              {notification.body}
            </Alert>
          </Snackbar>
        </ClickAwayListener>
      )}
      <Page />
    </Container>
  );
};
