import React from 'react';
import ReactDOM from 'react-dom/client';
import { SidePanel } from './SidePanel';

import ThemeProvider from '@mui/material/styles/ThemeProvider';
import { theme } from 'atomic-design/theme';

const Index = () => {
  return (
    <ThemeProvider theme={theme}>
      <SidePanel />
    </ThemeProvider>
  );
};

const root = ReactDOM.createRoot(document.getElementById('app')!);
root.render(<Index />);
