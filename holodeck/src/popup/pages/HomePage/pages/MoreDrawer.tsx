import React, { PropsWithChildren } from 'react';

import { Close } from '@mui/icons-material';
import { IconButton, Stack, useTheme } from '@mui/material';

import { BottomDrawer } from '../../../../atomic-design/atoms/BottomDrawer';
import { Title } from '../../../../atomic-design/atoms/Title';

type MoreDrawerProps = PropsWithChildren & {
  title: string;
  open: boolean;
  close: () => void;
};
export const MoreDrawer: React.FC<MoreDrawerProps> = ({
  title,
  open,
  children,
  close,
}) => {
  const theme = useTheme();

  return (
    <BottomDrawer open={open} close={close}>
      <Stack sx={{ my: '10px' }}>
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="flex-start"
          gap="2rem"
          sx={{ width: '100%', mr: '-16px' }}
        >
          <Title title={title} sx={{ mt: '10px', fontWeight: 600 }} />
          <IconButton
            onClick={close}
            sx={{ color: theme.palette.grey[400], mr: '-10px' }}
          >
            <Close />
          </IconButton>
        </Stack>

        <Stack
          sx={{
            gap: '8px',
            backgroundColor: 'white',
            maxHeight: '65vh',
            overflow: 'auto',
          }}
        >
          {children}
        </Stack>
      </Stack>
    </BottomDrawer>
  );
};
