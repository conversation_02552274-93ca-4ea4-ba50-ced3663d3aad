import { Rfp } from '../../../../components/EndToEndRfp';
import {
  convertTimestampToDate,
  removeAccents,
} from '../../../../utils/helpers';
import { QuestionnaireDto } from '../../../api/questionnaires.api';
import { UserDetailDto } from '../../../api/user.api';
import { QuestionnaireFilters } from '../useFilterStore';

type FilterCriteria = QuestionnaireFilters;

export const statusMapping = {
  all: null,
  active: [
    'processing',
    'new',
    'generating_outline',
    'in_review_outline',
    'generating_response',
    'in_review_response',
    'analyzing',
    'long_form_go_no_go',
    'spreadsheet_confirm_analysis',
    'generating',
    'in_review',
    'error',
    'rejected',
  ],
  finished: ['processed', 'finished'],
};

// Unfortunately this is different for E2E...
export const statusMappingRfp = {
  all: null,
  active: [
    'Pending',
    'Analyzing',
    'Active',
    'Gathering Answers',
    'Generating Response',
  ],
  finished: ['finished'],
};

export function applySort(
  items:
    | QuestionnaireDto[]
    | Rfp[]
    | (
        | (QuestionnaireDto & { recordType: 'questionnaire' | 'e2e' })
        | (Rfp & { recordType: 'questionnaire' | 'e2e' })
      )[],
  sortBy: string,
  sortDirection: string,
) {
  return items.sort((a, b) => {
    if (!sortBy || !sortDirection) return 0;

    let aValue: string | Date | number = '',
      bValue: string | Date | number = '';

    const sortAscending = sortDirection === 'asc';

    switch (sortBy) {
      case 'created_date':
        // created_date checking is a little wonky because created_date
        // and the fallback agentParams.thread are not always present.
        // So only compare if both are present, otherwise worst case
        // fall back to content_id check.
        if (a.created_date && b.created_date) {
          aValue = new Date(a.created_date).getTime();
          bValue = new Date(b.created_date).getTime();
        } else if (
          a.details?.agentParams?.thread &&
          b.details?.agentParams?.thread
        ) {
          aValue = convertTimestampToDate(
            Number(a.details?.agentParams?.thread),
          );
          bValue = convertTimestampToDate(
            Number(b.details?.agentParams?.thread),
          );
        } else if ((a.id || a.content_id) && (b.id || b.content_id)) {
          aValue = a.id
            ? Number(a.id)
            : a.content_id
              ? Number(a.content_id)
              : 0;
          bValue = b.id
            ? Number(b.id)
            : b.content_id
              ? Number(b.content_id)
              : 0;
        } else {
          // Easter Egg: Judgment Day = 1997-08-29, Day of First Contact = 2063-04-05
          aValue = sortAscending
            ? new Date('1997-08-29').getTime()
            : new Date('2063-04-05').getTime();
          bValue = sortAscending
            ? new Date('1997-08-29').getTime()
            : new Date('2063-04-05').getTime();
        }
        break;
      case 'due_date':
        aValue = a.due_date
          ? new Date(a.due_date).getTime()
          : sortAscending
            ? // Show nulls last
              new Date('2063-04-05').getTime()
            : new Date('1997-08-29').getTime();
        bValue = b.due_date
          ? new Date(b.due_date).getTime()
          : sortAscending
            ? // Show nulls last
              new Date('2063-04-05').getTime()
            : new Date('1997-08-29').getTime();
        break;
      case 'created_by_name':
        aValue = a.created_by_name;
        bValue = b.created_by_name;
        break;
      case 'label':
        aValue =
          a['recordType'] === 'questionnaire'
            ? a.label.toLocaleLowerCase()
            : (
                a.details?.project_name ??
                a.details?.fileName ??
                a.details?.customer_name ??
                ''
              ).toLocaleLowerCase();
        bValue =
          b['recordType'] === 'questionnaire'
            ? b.label.toLocaleLowerCase()
            : (
                b.details?.project_name ??
                b.details?.fileName ??
                a.details?.customer_name ??
                ''
              ).toLocaleLowerCase();
        break;
    }

    if (aValue == bValue) {
      aValue = a.id ? Number(a.id) : a.content_id ? Number(a.content_id) : 0;
      bValue = b.id ? Number(b.id) : b.content_id ? Number(b.content_id) : 0;
    }

    if (aValue < bValue) return sortAscending ? -1 : 1;
    if (aValue > bValue) return sortAscending ? 1 : -1;

    return 0;
  });
}

export function filterQuestionnaires(
  questionnaires: QuestionnaireDto[],
  filters: FilterCriteria,
  currentUser: UserDetailDto,
) {
  const filteredQuestionnaires = questionnaires.filter((item) => {
    // Status filter
    if (filters.status && filters.status.length) {
      // TODO: This is weird UX and needs to be fixed
      if (filters.status.includes('all')) {
        // don't filter, we want everything
      } else {
        // only filter if not "all"
        const matches = filters.status.some((status) =>
          statusMapping[status].includes(item.project_content_status),
        );
        if (!matches) {
          return false;
        }
      }
    }

    if (filters.ownerAssigneeFilter && filters.ownerAssigneeFilter != 'all') {
      if (filters.ownerAssigneeFilter == 'mine') {
        if (
          !item.assignees?.some(
            (assignee) => assignee.assignee_id == Number(currentUser?.id),
          )
        ) {
          return false;
        }
      } else if (
        filters.ownerAssigneeFilter == 'other' &&
        filters.ownerAssigneeFilterOtherId
      ) {
        if (
          !item.assignees?.some(
            (assignee) =>
              assignee.assignee_id ==
              Number(filters.ownerAssigneeFilterOtherId),
          )
        ) {
          return false;
        }
      }
    }

    // Reviewed filter
    if (filters.reviewed && filters.reviewed != 'all') {
      const reviewedMapping = {
        all: null,
        reviewed: 'reviewed',
        under_review: 'under review',
      };

      // TODO: This is weird UX and needs to be fixed
      if (filters.reviewed.includes('all')) {
        // don't filter, we want everything
      } else {
        // only filter if not "all"
        const num_questions_reviewed = Number(item.num_questions_accepted);
        const num_questions = Number(item.num_questions);
        const is_reviewed =
          num_questions > 0 && num_questions_reviewed == num_questions;

        const matches =
          (filters.reviewed == reviewedMapping.reviewed && is_reviewed) ||
          (filters.reviewed == reviewedMapping.under_review && !is_reviewed);
        if (!matches) {
          return false;
        }
      }
    }

    // Search query filter
    if (filters.searchQuery) {
      // Ignore accents.
      const query = removeAccents(filters.searchQuery?.toLowerCase());

      if (
        !removeAccents(item.file_name?.toLowerCase()).includes(query) &&
        !removeAccents(item.label?.toLowerCase()).includes(query) &&
        !removeAccents(item.created_by_name?.toLowerCase()).includes(query) &&
        (!item.details.customer_name ||
          !removeAccents(item.details.customer_name?.toLowerCase()).includes(
            query,
          ))
      ) {
        return false;
      }
    }

    // TODO: Implement other filters similarly...

    return true;
  });

  return filteredQuestionnaires;
}

export function filterE2ERfps(
  rfps: Rfp[],
  filters: FilterCriteria,
  currentUser: UserDetailDto,
) {
  const filteredE2ERfps = rfps.filter((item) => {
    // Status filter
    if (filters.status && filters.status.length) {
      // TODO: This is weird UX and needs to be fixed
      if (filters.status.includes('all')) {
        // don't filter, we want everything
      } else {
        // only filter if not "all"
        const statusField = item.status;
        const matches = filters.status.some((status) =>
          statusMapping[status].includes(statusField),
        );
        if (!matches || item.rfp_status === 'rejected') {
          return false;
        }
      }
    }

    if (filters.ownerAssigneeFilter && filters.ownerAssigneeFilter != 'all') {
      if (filters.ownerAssigneeFilter == 'mine') {
        if ((item.assignees?.length ?? 0) > 0) {
          if (
            !item.assignees?.some(
              (assignee) => assignee.assignee_id == Number(currentUser?.id),
            )
          ) {
            return false;
          }
        } else if (
          item.details.agentParams?.userId &&
          !(item.details.agentParams?.userId == Number(currentUser?.id))
        ) {
          return false;
        }
      } else if (
        filters.ownerAssigneeFilter == 'other' &&
        filters.ownerAssigneeFilterOtherId
      ) {
        if ((item.assignees?.length ?? 0) > 0) {
          if (
            !item.assignees?.some(
              (assignee) =>
                assignee.assignee_id ==
                Number(filters.ownerAssigneeFilterOtherId),
            )
          ) {
            return false;
          }
        } else if (
          item.details.agentParams?.userId &&
          !(
            item.details.agentParams?.userId ==
            Number(filters.ownerAssigneeFilterOtherId)
          )
        ) {
          return false;
        }
      }
    }

    // Search query filter
    if (filters.searchQuery) {
      // Ignore accents.
      const query = removeAccents(filters.searchQuery.toLowerCase());

      if (
        !removeAccents(
          item.details.customer_name?.toLowerCase() ?? '',
        ).includes(query) &&
        !removeAccents(item.details.project_name?.toLowerCase() ?? '').includes(
          query,
        )
      ) {
        return false;
      }
    }

    // Reviewed filter
    if (filters.reviewed && filters.reviewed != 'all') {
      const reviewedMapping = {
        all: null,
        reviewed: 'reviewed',
        under_review: 'under review',
      };

      if (filters.reviewed.includes('all')) {
        // don't filter, we want everything
      } else {
        // only filter if not "all"
        // For E2E, we want to simply hide everything when reviewed filter is set to "reviewed"
        const shouldRemove = filters.reviewed == reviewedMapping.reviewed;
        if (shouldRemove) {
          return false;
        }
      }
    }

    // TODO: Implement other filters similarly...

    return true;
  });

  return filteredE2ERfps;
}
