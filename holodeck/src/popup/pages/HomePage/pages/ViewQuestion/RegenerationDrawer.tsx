import {
  Box,
  Button,
  Checkbox,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import React, { FC, useState } from 'react';
import { BottomDrawer } from '../../../../../atomic-design/atoms/BottomDrawer';
import { QuestionDto } from '../../../../api/question.api';

interface RegenParams {
  question: QuestionDto;
  open: boolean;
  close: () => void;
}
export const RegenerationDrawer: FC<RegenParams> = (props) => {
  const [moreDetailed, setMoreDetailed] = useState(false);

  function toggleMoreDetailed() {
    setMoreDetailed(!moreDetailed);
  }

  const contents = (
    <Box>
      <List>
        <ListItem>
          <ListItemButton role={undefined} onClick={toggleMoreDetailed} dense>
            <ListItemIcon>
              <Checkbox
                edge="start"
                checked={moreDetailed}
                tabIndex={-1}
                disableRipple
              />
            </ListItemIcon>
            <ListItemText
              primary="Generate a more detailed answer"
              primaryTypographyProps={{
                variant: 'body2',
              }}
            />
          </ListItemButton>
        </ListItem>
      </List>
    </Box>
  );

  return (
    <BottomDrawer open={props.open} close={props.close}>
      <Box sx={{ display: 'flex', flexDirection: 'column' }}>
        {contents}
        <Button>Regenerate</Button>
      </Box>
    </BottomDrawer>
  );
};
