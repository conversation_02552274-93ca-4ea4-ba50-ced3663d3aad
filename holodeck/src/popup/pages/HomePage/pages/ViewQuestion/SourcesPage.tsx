import NotInterestedIcon from '@mui/icons-material/NotInterested';
import PrivacyTipIcon from '@mui/icons-material/PrivacyTip';
import { Box, Button, Chip, Stack, Typography, useTheme } from '@mui/material';
import React, { FC, useEffect, useState } from 'react';

import { <PERSON><PERSON> } from '../../../../../atomic-design/atoms/Chips/BaseChip';
import { PageHeaderBar } from '../../../../../atomic-design/atoms/PageHeaderBar';
import { PrimaryTooltip } from '../../../../../atomic-design/atoms/StyledTooltips';
import { CircularIconButton } from '../../../../../atomic-design/molecules/QuestionCard/CircularIconButton';
import { SourceCard } from '../../../../../atomic-design/molecules/SourceCard/SourceCard';
import { ContentSourceDoc, ContentSourceQA } from '../../../../../utils/model';
import { QuestionDto } from '../../../../api/question.api';
import { useRootStore } from '../../../../state/useRootStore';

import * as QA_UTILS from '../../../../utils/qa';
import {
  processAnswerCitations,
  reorderCitations,
} from '../ViewQuestionnaire/QuestionList/answerProcessing';

type SourcesProps = {
  question: QuestionDto;
  answerVersionIndex: number | null; // for multi version answers
  handleBack: () => void;
  handleRefresh: () => void;
  handleUseAsAnswer: (modified_text: string) => void;
};

export const SourcesPage: FC<SourcesProps> = (props) => {
  const theme = useTheme();
  const rootStore = useRootStore();

  const [usedDocuments, setUsedDocuments] = useState([]);

  const { question, answerVersionIndex } = props;
  const m = '1rem';

  const isModified = QA_UTILS.isModifiedAnswer(question);
  const answerVersions = question.output_original.answer_versions;
  const answer =
    answerVersionIndex != null
      ? answerVersions[answerVersionIndex]
      : question.output_original;
  const docSources = answer?.documents?.docs || [];
  const qaSources = answer?.documents?.qa || [];
  const numSources = docSources.length + qaSources.length;

  useEffect(() => {
    const loadData = async () => {
      const usedDocs = await QA_UTILS.getUsedSourceMetadata(answer);
      setUsedDocuments(usedDocs);
    };
    if (question) {
      loadData();
    }
  }, [props.question]);

  const buttonRow = (
    source: ContentSourceDoc | ContentSourceQA,
    isUsedSource,
    isPrivate,
  ) => {
    const ExcludeButton = (
      <Box>
        <PrimaryTooltip
          title={'Regenerate answer without this source'}
          enterDelay={250}
        >
          <CircularIconButton
            Icon={NotInterestedIcon}
            size="medium"
            onClick={() => {}}
          />
        </PrimaryTooltip>
      </Box>
    );

    const useAsAnswerButton = (modified_text: string) => {
      return (
        <PrimaryTooltip
          title={'Replace the generated answer with this text'}
          enterDelay={250}
        >
          <Button
            size="small"
            variant="outlined"
            onClick={() => props.handleUseAsAnswer(modified_text)}
            color={isUsedSource ? 'primary' : 'secondary'}
            sx={{ fontSize: '0.7rem' }}
          >
            Use as answer
          </Button>
        </PrimaryTooltip>
      );
    };

    return (
      <Stack
        direction="row"
        alignItems="center"
        justifyContent="space-between"
        gap={2}
        width={'100%'}
      >
        {isPrivate ? (
          <Chip
            variant="outlined"
            label="Private Source"
            color="error"
            icon={
              <PrivacyTipIcon
                sx={{ color: theme.palette.error.main + ' !important' }}
              />
            }
            sx={{
              color: theme.palette.error.main,
            }}
          />
        ) : (
          <div />
        )}
        {useAsAnswerButton(source.text)}
      </Stack>
    );
  };

  // Need to maintain the source order as that is what citations are based on.
  let combinedSources = Array(numSources).fill(null);
  docSources.forEach((source, i) => {
    combinedSources[source.context_index - 1] = source;
  });

  qaSources.forEach((source, i) => {
    combinedSources[source.context_index - 1] = source;
  });

  let usedSources = [];
  let unusedSources = [];

  // Reorder citations so they're sequential for the end user
  const removeCitations = false;
  let processedAnswer =
    processAnswerCitations(answer.answer, removeCitations) || '';

  const { usedCitationIndexes } = reorderCitations(processedAnswer);

  // Reposition "combinedSources" based on the reordered citations
  combinedSources.forEach((origSource, origSourceIdx) => {
    if (usedCitationIndexes.includes(origSourceIdx + 1)) {
      usedSources.push(origSource);
    } else {
      unusedSources.push(origSource);
    }
  });

  return (
    <Stack sx={{ minHeight: '100%', p: '1rem', pt: '0' }} direction={'column'}>
      <Box
        id="header-container"
        sx={{
          position: 'sticky',
          pt: '1rem',
          top: '0px',
          backgroundColor: '#FFF',
          zIndex: 1200,
          minHeight: '56.6px',
          mb: '1rem',
        }}
      >
        <PageHeaderBar onClickBack={props.handleBack}></PageHeaderBar>
      </Box>

      <Stack direction="row" alignItems="center" sx={{ mb: 1 }}>
        <Typography
          variant="h6"
          color="primary"
          sx={{ mr: 1, fontWeight: '600' }}
        >
          Sources
        </Typography>
        <Chip
          label={usedSources.length}
          size="small"
          color="primary"
          variant="filled"
          sx={{ fontWeight: '500', '& .MuiChip-label': { fontWeight: '600' } }}
        />
      </Stack>

      {isModified && usedSources.length > 0 && (
        <>
          <Typography
            variant="body2"
            color="primary"
            sx={{ fontWeight: '600' }}
          >
            Note: the original answer was modified.
          </Typography>
          <Typography variant="body2" color="primary" sx={{ mt: '0.5rem' }}>
            The following list reflects sources that were used in the original
            answer.
          </Typography>
        </>
      )}

      {!isModified && usedSources.length > 0 ? (
        <Typography variant="body2" color="primary">
          The following {usedSources.length > 1 ? 'sources were' : 'source was'}{' '}
          used by the Tribble Brain to generate the answer.
        </Typography>
      ) : (
        <Typography variant="body2" color="primary">
          No relevant sources were found for the given question / context.
        </Typography>
      )}

      {usedSources.length > 0 &&
        usedSources
          .filter((source) => source != null)
          .map((source, idx) => {
            const docMetadata = usedDocuments.find(
              (doc) => Number(doc.id) == source.document_id,
            );

            return (
              <Stack
                key={`source_${idx + 1}`}
                direction="column"
                gap="1rem"
                sx={{ mt: '2rem' }}
              >
                <Chippy
                  label={`Source ${idx + 1}`}
                  colorHex={theme.palette.primary.main}
                  sx={{ width: 'fit-content' }}
                />
                <SourceCard
                  source={source}
                  type={
                    source.type == 'qa' || source.type == 'RFP' ? 'RFP' : 'DOC'
                  }
                  key={`source_${idx + 1}`}
                  disableFade={true}
                />
                {buttonRow(source, true, docMetadata?.privacy === 'private')}
              </Stack>
            );
          })}

      <Box sx={{ minHeight: '1rem' }} />
    </Stack>
  );
};
