import { Box } from '@mui/material';
import React, { FC, useEffect, useRef, useState } from 'react';

import {
  ContentDetailState,
  QuestionDto,
  getSingleQuestion,
  patchQuestion,
  postModifyAnswer,
  postRevertAnswer,
  setQuestionStatus,
} from '../../../../api/question.api';
import { QuestionnaireDto } from '../../../../api/questionnaires.api';
import {
  processAnswerCitations,
  reorderCitations,
} from '../ViewQuestionnaire/QuestionList/answerProcessing';

import { QuestionCard } from 'atomic-design/molecules/QuestionCard/QuestionCard';
import { QuestionMultiCard } from 'atomic-design/molecules/QuestionCard/QuestionMultiCard';
import { RegenerationDrawer } from './RegenerationDrawer';
import { SourcesPage } from './SourcesPage';

import { useGroupings } from '../../../../api/groupings.swr';
import { useRootStore } from '../../../../state/useRootStore';

import * as QA_UTILS from '../../../../utils/qa';

type QuestionPageProps = {
  questionId: number;
  questionnaire: QuestionnaireDto;
};

export const QuestionPage: FC<QuestionPageProps> = (props) => {
  const { questionId, questionnaire } = props;

  const rootStore = useRootStore();
  const availableGroupings = useGroupings();

  const [question, setQuestion] = useState<QuestionDto>(); //Hacky until we figure out a state handler
  const questionState = useRef('initial');
  const [showSource, setShowSource] = useState(false);
  const [answerVersionIndex, setAnswerVersionIndex] = useState<number | null>(
    null,
  );
  const [drawerIsOpen, setDrawerOpen] = useState(false);

  const [mode, setMode] = useState<'edit' | 'view'>('view');

  const questionFetch = async () => {
    const questionResult = await getSingleQuestion(questionId);
    if (questionResult.success) {
      setQuestion(questionResult.response);
      questionState.current = questionResult.response.state;
    } else {
      rootStore.setNotification({
        type: 'error',
        body: 'Failed to fetch question detail',
        header: 'Oops',
      });
    }
  };

  useEffect(() => {
    const doFetch = async () => {
      await questionFetch();
    };

    doFetch();
  }, [questionId]);

  const handleRefresh = async () => {
    await questionFetch();
  };

  if (!question) return <></>;

  const removeCitations = false;

  const isModified = QA_UTILS.isModifiedAnswer(question);
  const isAccepted = QA_UTILS.isAcceptedAnswer(question);
  const multiVersionAnswer = QA_UTILS.isMultiVersionAnswer(question);

  const translatedText = question.output_original?.translation?.translation;

  const answerText = QA_UTILS.shouldUseTranslatedAnswer(question)
    ? translatedText
    : isModified
      ? question.output_modified!.text
      : question.output_original?.answer;

  let processedAnswer =
    processAnswerCitations(answerText!, removeCitations) || '';

  // Reorder citations so they're sequential for the end user
  if (!isModified) {
    const { usedCitationIndexes, renumberedProcessedAnswer } =
      reorderCitations(processedAnswer);
    processedAnswer = renumberedProcessedAnswer;
  }

  const handleShowSources = (answerIdx: number | null) => {
    setAnswerVersionIndex(answerIdx);
    setShowSource(true);
  };

  const closeSource = () => {
    setShowSource(false);
  };

  const toggleDrawer = () => {
    setDrawerOpen(!drawerIsOpen);
  };

  const markAsReviewed = async () => {
    const currentState = question.state;
    let newState: ContentDetailState = 'accepted';
    switch (currentState) {
      case 'initial':
        newState = 'accepted';
        break;
      case 'accepted':
        newState = 'initial';
        break;
      //Etc.
    }
    const result = await setQuestionStatus(question.id!, newState);
    if (result.success) {
      await handleRefresh();
    } else {
      rootStore.setNotification({
        body: 'Failed to update',
        header: 'Oops',
        type: 'error',
      });
    }
  };

  const modifyAnswer = async (newAnswer: string) => {
    const result = await postModifyAnswer(question.id!, newAnswer);
    if (result.success) {
      rootStore.setNotification({
        body: '',
        header: 'Updated',
        type: 'success',
      });
      setMode('view');
      await handleRefresh();
    } else {
      rootStore.setNotification({
        body: 'Failed to update',
        header: 'Oops',
        type: 'error',
      });
    }
  };

  const revertAnswer = async () => {
    const result = await postRevertAnswer(question.id!);
    if (result.success) {
      rootStore.setNotification({
        body: '',
        header: 'Answer reverted',
        type: 'success',
      });
      setMode('view');
      await handleRefresh();
    } else {
      rootStore.setNotification({
        body: 'Failed to update',
        header: 'Oops',
        type: 'error',
      });
    }
  };

  const handleUseAsAnswer = async (modifiedText: string) => {
    // If this was a "QUESTION: ... ANSWER: ..." (i.e. historic Q&A)
    // Just copy everything *after* "ANSWER: "
    if (
      modifiedText.indexOf('QUESTION:') > -1 &&
      modifiedText.indexOf('ANSWER:') > -1
    ) {
      const indexOfAnswerDelimited =
        modifiedText.indexOf('ANSWER: ') + 'ANSWER: '.length;
      modifiedText = modifiedText.substring(indexOfAnswerDelimited);
    }

    const result = await postModifyAnswer(question.id, modifiedText);
    if (result.success) {
      if (mode !== 'view') {
        setMode('view');
      }
      rootStore.setNotification({
        type: 'success',
        header: 'Answer updated',
        body: '',
      });
      await handleRefresh();
    } else {
      rootStore.setNotification({
        type: 'error',
        header: 'Oops',
        body: 'Failed to update answer',
      });
    }
  };

  const updateQuestion = async (groupingId: number, assigneeId: number) => {
    // Check if question requires regeneration:
    // - if grouping changed, where metadata filters are different
    let requiresRegeneration = QA_UTILS.requiresRegeneration(
      availableGroupings.data,
      props.questionnaire.metadata_filter,
      question.grouping_id,
      groupingId,
    );

    const result = await patchQuestion(
      Number(question.id!),
      // can't use question.state as it may not have updated during a
      // combined "modify text" + "change assignee/grouping" operation
      questionState.current,
      groupingId > 0 ? groupingId : null,
      assigneeId > 0 ? assigneeId : null,
      Number(props.questionnaire.content_id),
      requiresRegeneration,
    );
    if (result.success) {
      rootStore.setNotification({
        body: '',
        header: 'Question updated',
        type: 'success',
      });
      setMode('view');
      await handleRefresh();
    } else {
      rootStore.setNotification({
        body: 'Failed to update',
        header: 'Oops',
        type: 'error',
      });
    }
  };

  const page = showSource ? (
    <SourcesPage
      handleBack={closeSource}
      question={question}
      answerVersionIndex={answerVersionIndex}
      handleRefresh={handleRefresh}
      handleUseAsAnswer={handleUseAsAnswer}
    />
  ) : multiVersionAnswer ? (
    <QuestionMultiCard
      mode={mode}
      handleEditMode={() => {
        setMode('edit');
      }}
      handleStopEditing={() => {
        setMode('view');
      }}
      handleSaveEditedAnswer={modifyAnswer}
      handleRevert={revertAnswer}
      handleRegenButton={() => {
        setDrawerOpen(true);
      }}
      handleMarkAsReviewed={markAsReviewed}
      handleUpdateQuestion={updateQuestion}
      handleUseAsAnswer={handleUseAsAnswer}
      showSources={(answerIdx: number) => handleShowSources(answerIdx)}
      answerText={processedAnswer}
      isAccepted={isAccepted}
      question={question}
      questionnaire={questionnaire}
    />
  ) : (
    <QuestionCard
      mode={mode}
      handleEditMode={() => {
        setMode('edit');
      }}
      handleStopEditing={() => {
        setMode('view');
      }}
      handleSaveEditedAnswer={modifyAnswer}
      handleRevert={revertAnswer}
      handleRegenButton={() => {
        setDrawerOpen(true);
      }}
      handleMarkAsReviewed={markAsReviewed}
      handleUpdateQuestion={updateQuestion}
      handleUseAsAnswer={handleUseAsAnswer}
      showSources={() => handleShowSources(null)}
      answerText={processedAnswer}
      isAccepted={isAccepted}
      question={question}
      questionnaire={questionnaire}
    />
  );

  return (
    <Box sx={{ height: '100%' }}>
      {page}
      <RegenerationDrawer
        question={question}
        open={drawerIsOpen}
        close={toggleDrawer}
      />
    </Box>
  );
};
