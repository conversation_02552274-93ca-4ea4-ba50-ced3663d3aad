import React, { FC, useEffect, useRef, useState } from 'react';

import { Edit, Visibility } from '@mui/icons-material';
import {
  Avatar,
  Box,
  Button,
  Card,
  Chip,
  Divider,
  FormControl,
  InputLabel,
  ListItemText,
  MenuItem,
  Stack,
  TextField,
  Typography,
  useTheme,
} from '@mui/material';
import Select, { SelectChangeEvent } from '@mui/material/Select';

import { MenuDropdown } from '../../../../../atomic-design/atoms/MenuDropdown';
import { PageHeaderBar } from '../../../../../atomic-design/atoms/PageHeaderBar';
import {
  PrimaryTooltip,
  SecondaryTooltip,
} from '../../../../../atomic-design/atoms/StyledTooltips';
import { Title } from '../../../../../atomic-design/atoms/Title';
import { TribbleAvatar } from '../../../../../atomic-design/atoms/TribbleAvatar';
import { UtilityButton } from '../../../../../atomic-design/atoms/buttons/UtilityButton';
import { DueDate } from '../../../../../atomic-design/molecules/Date';
import { StatusChip } from '../../../../../atomic-design/molecules/QuestionnaireCard/StatusChip';
import ConfirmationModal from '../../../../../components/ConfirmationModal';
import { AssigneeAvatarGroup } from '../ViewQuestionnaire/QuestionnaireDetail';

import { updateE2ERfpStatus } from '../../../../../utils/api';
import { deleteRfp, patchE2ERfp } from '../../../../api/questionnaires.api';
import { UserDto } from '../../../../api/user.api';
import { useUsers } from '../../../../api/user.swr';
import { useRootStore } from '../../../../state/useRootStore';
import { markdownToHtml } from '../../../../utils';

import { OpportunityPicker } from '../../../../../atomic-design/molecules/OpportunityPicker';
import {
  SalesforceOpportunityDto,
  getSalesforceOpportunity,
} from '../../../../api/salesforce.api';
import { useSalesforceOpportunities } from '../../../../api/salesforce.swr';

import { Rfp } from '../../../../../components/EndToEndRfp';
import { TRIBBLE_APP_URL } from '../../../../../utils/constants_auth';
import { getEnv } from '../../../../state/auth.storage';

type ViewE2ELongFormProps = {
  rfp: Rfp;
};
export const ViewE2ELongForm: FC<ViewE2ELongFormProps> = ({ rfp }) => {
  const theme = useTheme();
  const rootStore = useRootStore();
  const users = useUsers();

  const [mode, setMode] = useState<'view' | 'edit'>('view');
  const [showDelete, setShowDelete] = useState(false);
  const [loadingE2ERfp, setLoadingE2ERfp] = useState<string | undefined>(
    undefined,
  );
  const [workingCopyRfp, setWorkingCopyRfp] = useState<Rfp>(rfp);
  const [availableUsers, setAvailableUsers] = useState<UserDto[]>([]);
  const [loadingUsers, setLoadingUsers] = useState(true);

  const rfpOwner = workingCopyRfp.assignees?.find((a) => a.is_owner);
  const rfpOwnerId =
    rfpOwner?.assignee_id ?? workingCopyRfp.details.agentParams?.userId ?? null;

  const projectTitleRef = useRef<String>(rfp.details.project_name || '');
  const customerNameRef = useRef<String>(rfp.details.customer_name || '');
  const updatedDueDateRef = useRef<Date | null>(rfp.due_date ?? new Date());
  const updatedOwnerId = useRef<number | null>(rfpOwnerId);

  const [origOpportunityId, setOrigOpportunityId] = useState('');
  const salesforceOpportunities = useSalesforceOpportunities(
    rootStore.connectedToSalesforce(),
  );

  const [allOpportunities, setAllOpportunities] = useState<any[]>([]);
  const [currentOpportunity, setCurrentOpportunity] =
    useState<SalesforceOpportunityDto>();
  const [newOpportunity, setNewOpportunity] =
    useState<SalesforceOpportunityDto>();
  const [loadingOpportunity, setLoadingOpportunity] = useState(true);

  const hasOpportunityId =
    rfp?.details?.opportunity_id &&
    rfp?.details?.opportunity_id !== 'undefined';

  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'instant' });
  }, []);

  useEffect(() => {
    if (users.data) {
      setAvailableUsers(users.data);
      setLoadingUsers(false);
    }
  }, [users.data]);

  useEffect(() => {
    if (
      salesforceOpportunities.data &&
      salesforceOpportunities.data.records?.length > 0
    ) {
      setAllOpportunities(salesforceOpportunities.data.records);
    }
  }, [salesforceOpportunities.data]);

  useEffect(() => {
    if (hasOpportunityId) {
      loadSalesforceOpportunity();
    } else {
      setCurrentOpportunity(undefined);
      setNewOpportunity(undefined);
    }
  }, [rfp?.details?.opportunity_id]);

  const loadSalesforceOpportunity = async () => {
    if (rootStore.connectedToSalesforce() && hasOpportunityId) {
      const oppty = await getSalesforceOpportunity(
        rfp?.details?.opportunity_id,
      );
      setOrigOpportunityId(rfp?.details?.opportunity_id);
      setCurrentOpportunity(oppty);
      setNewOpportunity(oppty);
    }
    setLoadingOpportunity(false);
  };

  const isProcessed =
    rfp.status === 'in_review_response' || rfp.status === 'finished';

  const processedWithDetails =
    isProcessed && !!rfp.details.agentParams.finalReview;

  const handleSave = async () => {
    const newWorkingCopy = {
      ...workingCopyRfp,
      due_date: updatedDueDateRef.current,
      assignees: updatedOwnerId
        ? [
            {
              assignee_id: updatedOwnerId.current,
              user_name: availableUsers.find(
                (u) => u.id === updatedOwnerId.current,
              )?.name,
              is_owner: true,
            },
          ]
        : [],
      details: {
        ...workingCopyRfp.details,
        project_name: String(projectTitleRef.current),
        customer_name: String(customerNameRef.current),
        opportunity_id: newOpportunity?.Id,
      },
    };
    const result = await patchE2ERfp(newWorkingCopy);

    if (result.success) {
      rootStore.setNotification({
        header: 'Updates successfully saved',
        body: '',
        type: 'success',
      });

      setWorkingCopyRfp(newWorkingCopy);
      setCurrentOpportunity(newOpportunity);
      setMode('view');
    } else {
      rootStore.setNotification({
        header: 'Oops',
        body: 'Could not save updates',
        type: 'error',
      });
    }
  };

  const handleDelete = async () => {
    const result = await deleteRfp(rfp);
    if (result.success) {
      rootStore.setNotification({
        header: 'RFP deleted',
        body: '',
        type: 'success',
      });
      rootStore.navigate({ name: 'default' });
    } else {
      rootStore.setNotification({
        header: 'Oops',
        body: 'Something went wrong',
        type: 'error',
      });
    }
  };

  const editButton = () => {
    const editHandler = () => {
      setMode('edit');
      window.scrollTo({
        top: 0,
        behavior: 'instant',
      });
    };

    return <UtilityButton label={'Edit'} onClick={editHandler} Icon={Edit} />;
  };

  const getHeader = () => {
    if (mode == 'view') {
      return (
        <PageHeaderBar
          onClickBack={() => {
            rootStore.navigate({ name: 'default' });
          }}
        >
          <Stack direction="row" justifyContent="flex-end">
            <Box>{editButton()}</Box>
          </Stack>
        </PageHeaderBar>
      );
    } else {
      return (
        <PageHeaderBar onClickBack={() => {}} disableBackButton={true}>
          <Stack
            direction="row"
            alignItems="center"
            justifyContent="space-between"
            width="100%"
          >
            <Button
              size="small"
              variant="outlined"
              onClick={() => {
                setMode('view');
              }}
            >
              Cancel
            </Button>
            <Button onClick={handleSave} variant="contained" size="small">
              Update Details
            </Button>
          </Stack>
        </PageHeaderBar>
      );
    }
  };

  const getTitle = () => {
    return mode == 'edit' ? (
      <TextField
        fullWidth={true}
        placeholder="Project Title"
        label="Project Title"
        defaultValue={workingCopyRfp.details.project_name}
        onChange={(evt) => (projectTitleRef.current = evt.target.value)}
        sx={{ mt: '8px', mb: '16px' }}
      />
    ) : (
      <Title
        title={
          workingCopyRfp.details.project_name ||
          workingCopyRfp.details.customer_name
        }
      ></Title>
    );
  };

  const getSubtitle = () => {
    return mode == 'edit' ? (
      <TextField
        fullWidth={true}
        placeholder="Customer Name"
        label="Customer Name"
        defaultValue={workingCopyRfp.details.customer_name}
        onChange={(evt) => (customerNameRef.current = evt.target.value)}
        sx={{ mb: '4px' }}
      />
    ) : workingCopyRfp.details.project_name ? (
      <Typography variant="body2">
        {workingCopyRfp.details.customer_name}
      </Typography>
    ) : null;
  };

  const OwnerEdit = () => {
    return (
      <FormControl fullWidth>
        <InputLabel id="owner-select-small-label">Owner</InputLabel>
        <Select
          labelId="owner-select-small-label"
          label="Owner"
          defaultValue={String(rfpOwnerId)}
          onChange={(e: SelectChangeEvent) =>
            (updatedOwnerId.current = Number(e.target.value))
          }
          renderValue={(selected) => {
            const selectedUser = availableUsers.find(
              (user) => String(user.id) == selected,
            );
            return selectedUser ? selectedUser.name : '';
          }}
          MenuProps={{
            sx: {
              maxHeight: '400px',
            },
            anchorOrigin: {
              vertical: 'bottom',
              horizontal: 'left',
            },
          }}
        >
          {availableUsers
            .sort((a, b) =>
              a.name.toLowerCase() < b.name.toLowerCase() ? -1 : 1,
            )
            .map((user) => (
              <MenuItem value={user.id} key={user.id}>
                <div style={{ marginRight: '1rem' }}>
                  {user.picture && user.picture != '' && (
                    <Avatar src={user.picture} sx={{ height: 24, width: 24 }} />
                  )}
                  {(!user.picture || user.picture == '') && (
                    <TribbleAvatar
                      username={user.name}
                      height="24px"
                      width="24px"
                    />
                  )}
                </div>
                <ListItemText
                  primary={user.name}
                  sx={{ '& .MuiTypography-root': { fontSize: '0.85rem' } }}
                />
              </MenuItem>
            ))}
        </Select>
      </FormControl>
    );
  };

  const OpportunityEdit = () => {
    return (
      <div
        key={`oppty_picker_${currentOpportunity?.Id}`}
        style={{ width: '100%' }}
      >
        <OpportunityPicker
          opportunities={allOpportunities}
          selected={currentOpportunity}
          onSelect={(value) => setNewOpportunity(value)}
        />
      </div>
    );
  };

  let tooltipOpportunityAmount,
    tooltipOpportunityLineItems =
      '• No products are associated with this opportunity.',
    opportunityLink,
    opportunityLineItems;
  if (currentOpportunity) {
    tooltipOpportunityAmount = `• It has a value of $${Number(
      currentOpportunity.Amount,
    ).toLocaleString()}`;
    opportunityLink =
      rootStore.salesforceUser.instance_url + '/' + currentOpportunity.Id;

    // This was a special field we added in the backend query
    if (
      currentOpportunity['__LineItems'] &&
      currentOpportunity['__LineItems'].length > 0
    ) {
      opportunityLineItems = currentOpportunity['__LineItems'].map(
        (lineItem) => lineItem.Name,
      );

      tooltipOpportunityLineItems = `• It is for the following product${
        opportunityLineItems.length == 1 ? '' : 's'
      }:\n- ${opportunityLineItems?.join('\n- ')}`;
    }
  }

  return (
    <Stack
      minHeight="100%"
      aria-label="ViewQuestionContainer"
      sx={{
        backgroundColor: theme.palette.grey[200],
        boxSizing: 'border-box',
      }}
    >
      <Box
        id="header-container"
        sx={{
          position: 'sticky',
          top: '0px',
          px: '1rem',
          pt: '1rem',
          backgroundColor: '#FFF',
          zIndex: 1200,
        }}
      >
        {getHeader()}
      </Box>
      <Box
        sx={{
          width: '100%',
          p: '1rem',
          boxSizing: 'border-box',
          bgcolor: '#fff',
        }}
      >
        <Stack
          direction={'column'}
          justifyContent={'space-between'}
          alignItems={'flex-start'}
          gap={'1rem'}
        >
          <Stack
            direction={'row'}
            justifyContent={'space-between'}
            alignItems={'center'}
            width={'100%'}
          >
            {mode === 'view' ? (
              <MenuDropdown
                trigger={
                  <StatusChip
                    jobStatus={workingCopyRfp.status}
                    showToggle={true}
                  />
                }
                disableTrigger={!processedWithDetails}
                preventDefault={true}
              >
                {(handleClose) => {
                  const menuItems = [
                    <MenuItem
                      disabled={true}
                      key="status_empty"
                      sx={{
                        minHeight: '24px',
                        p: '4px 12px',
                        fontSize: '0.75rem',
                        textTransform: 'uppercase',
                        color: theme.palette.grey[900],
                      }}
                    >
                      Change Status
                    </MenuItem>,
                    <MenuItem
                      onClick={async (event: React.MouseEvent<HTMLElement>) => {
                        try {
                          event.stopPropagation();
                          event.preventDefault();
                        } catch (err) {
                          // no-op
                        }
                        await updateE2ERfpStatus(rfp.id, 'finished');
                        setWorkingCopyRfp({
                          ...workingCopyRfp,
                          status: 'finished',
                        });
                        handleClose();
                      }}
                      autoFocus={false}
                      key={`status_finished`}
                      sx={{
                        minHeight: '24px',
                        p: '4px 12px',
                        fontSize: '0.85rem',
                        '&:hover': { fontWeight: 500 },
                      }}
                    >
                      Mark as Finished
                    </MenuItem>,
                  ];

                  return menuItems;
                }}
              </MenuDropdown>
            ) : (
              <StatusChip
                mode={mode}
                onChange={(newStatus) => {
                  console.warn(
                    '[ViewE2ELongform] This should no longer be used. Doing nothing.',
                  );
                  // // This unfortunate mapping...
                  // setWorkingCopyRfp({
                  //   ...workingCopyRfp,
                  //   status:
                  //     newStatus === 'processed' ? 'finished' : 'processed',
                  // });
                }}
                jobStatus={workingCopyRfp.status}
              />
            )}
          </Stack>

          <Stack
            direction={'column'}
            justifyContent={'space-between'}
            alignItems={'flex-start'}
            gap="4px"
            sx={{ width: '100%' }}
          >
            <Box sx={{ width: '100%' }}>{getTitle()}</Box>
            {getSubtitle()}
          </Stack>

          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            sx={{ width: '100%' }}
          >
            <DueDate
              date={workingCopyRfp.due_date}
              mode={mode}
              onChange={(newDueDate: Date | null) =>
                (updatedDueDateRef.current = newDueDate)
              }
            />
            {!loadingUsers && rfpOwnerId && mode === 'view' && (
              <AssigneeAvatarGroup
                assigneeIds={[]}
                ownerId={Number(rfpOwnerId)}
                availableUsers={availableUsers}
              />
            )}
          </Stack>

          {mode === 'edit' && !loadingUsers && (
            <Box sx={{ mt: '4px', width: '100%' }}>{OwnerEdit()}</Box>
          )}

          {mode !== 'edit' && currentOpportunity && (
            <Stack
              direction="row"
              gap="0.5rem"
              sx={{ flexWrap: 'wrap', maxWidth: '100%' }}
            >
              <SecondaryTooltip
                title={`This project is mapped to the [${currentOpportunity.Name}] opportunity.\n\n${tooltipOpportunityAmount}\n${tooltipOpportunityLineItems}\n\nClick to view the record in Salesforce.`}
              >
                <Chip
                  label={`Opportunity: ${currentOpportunity.Name}`}
                  size="small"
                  variant="outlined"
                  color="secondary"
                  sx={{
                    color: theme.palette.secondary.main,
                    pointer: 'cursor',
                  }}
                  onClick={() => window.open(opportunityLink, '_blank')}
                />
              </SecondaryTooltip>
            </Stack>
          )}
          {/* Corner case where there's an oppty attached, but user is not connected to SFDC */}
          {mode !== 'edit' &&
            !currentOpportunity &&
            hasOpportunityId &&
            !loadingOpportunity && (
              <Typography variant="body2" color="secondary">
                ⓘ This project is mapped to an opportunity but you're not
                connected to Salesforce (in order to view its details). Go to
                the home screen and Settings to connect.
              </Typography>
            )}

          {mode === 'edit' && allOpportunities?.length > 0 && OpportunityEdit()}

          <Stack direction="column" gap="8px" sx={{ mb: '4px' }}>
            <Typography variant="body2">Generated using</Typography>
            {workingCopyRfp.details.files?.map((f, i) => (
              <Typography
                key={f + i.toString()}
                variant="body2"
                sx={{ fontWeight: 300 }}
              >{`${i + 1}. ${f.name}`}</Typography>
            ))}
          </Stack>
        </Stack>

        {mode == 'edit' && (
          <Stack
            direction="row"
            justifyContent="center"
            sx={{
              width: '100%',
              pb: '1rem',
              pt: '2.25rem',
              backgroundColor: '#fff',
            }}
          >
            <Button
              variant="outlined"
              color="error"
              onClick={() => setShowDelete(true)}
              sx={{ width: '250px' }}
            >
              Delete RFP
            </Button>
          </Stack>
        )}
      </Box>

      <Divider />

      <Stack
        direction={'column'}
        width={'100%'}
        sx={{ bgcolor: 'grey.200', p: '1rem', boxSizing: 'border-box' }}
        gap={2}
      >
        {isProcessed && (
          <Stack
            direction="row"
            justifyContent="flex-end"
            sx={{ width: '100%' }}
          >
            <PrimaryTooltip title="Preview RFP in a new browser window">
              <Button
                size="small"
                variant="outlined"
                disabled={loadingE2ERfp === rfp.id + 'pdf'}
                onClick={async () => {
                  // open in new tab
                  const env = await getEnv();
                  const appUrl = TRIBBLE_APP_URL[env ?? 'localhost'];
                  const url = `${appUrl}/projects/${rfp.id}`;
                  window.open(url, '_blank');
                }}
                startIcon={<Visibility />}
              >
                Open RFP in Tribble Editor
              </Button>
            </PrimaryTooltip>
            {/* Just link to web app now...where they can download from there */}
            {/* <PrimaryTooltip title="Download completed RFP as a docx file">
              <Button
                size="small"
                variant="contained"
                disabled={loadingE2ERfp === rfp.id}
                onClick={async () => {
                  if (rfp.details.rfpId === undefined) return;
                  setLoadingE2ERfp(rfp.id);
                  await downloadRfpInBrowser(
                    rfp.details.rfpId,
                    'docx',
                    rfp.details.project_name ?? rfp.details.customer_name,
                  );
                  setLoadingE2ERfp(undefined);
                }}
                startIcon={<Download />}
              >
                Download RFP
              </Button>
            </PrimaryTooltip> */}
          </Stack>
        )}

        <Card
          variant="outlined"
          sx={{
            p: '14px',
            pt: '14px',
            border: `1px solid ${theme.palette.grey[400]}60`,
          }}
        >
          <Typography
            variant="body1"
            sx={{ fontWeight: '600', textTransform: 'uppercase', mb: 1 }}
            color="primary"
          >
            Strengths
          </Typography>

          <Typography
            variant="body2"
            sx={{
              whiteSpace: 'pre-line',
              fontWeight: 300,
              lineHeight: 1.3,
              fontSize: '0.85rem',
            }}
            dangerouslySetInnerHTML={{
              __html: markdownToHtml(
                rfp.details.agentParams.finalReview?.strengths ??
                  '(none generated)',
              ),
            }}
          ></Typography>
        </Card>

        <Card
          variant="outlined"
          sx={{
            p: '14px',
            pt: '14px',
            border: `1px solid ${theme.palette.grey[400]}60`,
          }}
        >
          <Typography
            variant="body1"
            sx={{ fontWeight: '600', textTransform: 'uppercase', mb: 1 }}
            color="primary"
          >
            Areas to improve
          </Typography>
          <Typography
            variant="body2"
            sx={{
              whiteSpace: 'pre-line',
              fontWeight: 300,
              lineHeight: 1.3,
              fontSize: '0.85rem',
            }}
            dangerouslySetInnerHTML={{
              __html: markdownToHtml(
                rfp.details.agentParams.finalReview?.areasToImprove ??
                  '(none generated)',
              ),
            }}
          />
        </Card>
        <Card
          variant="outlined"
          sx={{
            p: '14px',
            pt: '14px',
            border: `1px solid ${theme.palette.grey[400]}60`,
          }}
        >
          <Typography
            variant="body1"
            sx={{
              fontWeight: '600',
              textTransform: 'uppercase',
              whiteSpace: 'pre-line',
              mb: 1,
            }}
            color="primary"
          >
            Overall
          </Typography>
          <Typography
            variant="body2"
            sx={{
              whiteSpace: 'pre-line',
              fontWeight: 300,
              lineHeight: 1.3,
              fontSize: '0.85rem',
            }}
            dangerouslySetInnerHTML={{
              __html: markdownToHtml(
                rfp.details.agentParams.finalReview?.overall ??
                  '(none generated)',
              ),
            }}
          />
        </Card>
      </Stack>

      {showDelete && (
        <ConfirmationModal
          cancelText="No, Cancel"
          confirmText="Yes, Delete"
          handleCancel={() => {
            setShowDelete(false);
          }}
          handleConfirm={handleDelete}
          message="This will delete this RFP. Are you sure?"
          open={showDelete}
          destructive={true}
          title="Delete"
        />
      )}
    </Stack>
  );
};
