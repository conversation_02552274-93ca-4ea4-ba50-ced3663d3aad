import {
  <PERSON><PERSON>,
  <PERSON>ack,
  ToggleButton,
  Typography,
  useTheme,
} from '@mui/material';
import React, { FC, useEffect, useState } from 'react';

import { BottomDrawer } from '../../../../atomic-design/atoms/BottomDrawer';
import { StyledToggleButtonGroup } from '../../../../atomic-design/atoms/StyledToggleButtonGroup';

import { USER_SETTING_NOTIFICATION_PREFERENCES } from '../../../../utils/constants';
import {
  disconnectSalesforce,
  getSalesforceLoginUrl,
} from '../../../api/salesforce.api';
import { getUserSetting, updateUserSetting } from '../../../api/user.api';
import { useRootStore } from '../../../state/useRootStore';

interface DefaultSettingsParams {
  open: boolean;
  close: () => void;
  onSave: (settings: any[]) => void;
}

export const DefaultSettingsDrawer: FC<DefaultSettingsParams> = (props) => {
  const rootStore = useRootStore();
  const theme = useTheme();

  const [connectingSalesforce, setConnectingSalesforce] = useState(false);
  const [disconnectingSalesforce, setDisconnectingSalesforce] = useState(false);

  const [origNotificationSetting, setOrigNotificationSetting] = useState<
    '' | 'EMAIL' | 'SLACK'
  >('');
  const [notificationSetting, setNotificationSetting] = useState<
    '' | 'EMAIL' | 'SLACK'
  >('');

  useEffect(() => {
    const loadSettings = async () => {
      // Where are we putting constants now?
      const userNotificationSetting = await getUserSetting(
        USER_SETTING_NOTIFICATION_PREFERENCES,
      );

      setNotificationSetting(userNotificationSetting.value ?? '');
      setOrigNotificationSetting(userNotificationSetting.value ?? '');
    };

    loadSettings();
  }, []);

  const handleClose = () => {
    setNotificationSetting(origNotificationSetting);
    props.close();
  };

  const handleNotificationChange = (
    e: React.MouseEvent<HTMLElement, MouseEvent>,
    notification: '' | 'EMAIL',
  ) => {
    e.preventDefault();

    // Do more in the future
    setNotificationSetting(notification);
  };

  const saveSettings = async () => {
    const success = await updateUserSetting(
      USER_SETTING_NOTIFICATION_PREFERENCES,
      notificationSetting,
    );

    if (success) {
      rootStore.setNotification(
        { header: 'Settings updated', body: null, type: 'success' },
        2000,
      );
      setOrigNotificationSetting(notificationSetting);
      props.close();
      props.onSave([
        {
          name: USER_SETTING_NOTIFICATION_PREFERENCES,
          value: notificationSetting,
        },
      ]);
    } else {
      rootStore.setNotification({
        header: 'Could not update settings',
        body: null,
        type: 'error',
      });
    }
  };

  const handleSalesforceConnect = async () => {
    setConnectingSalesforce(true);
    const url = await getSalesforceLoginUrl();
    window.open(url);

    // Todo: poll for connection
  };

  const handleSalesforceDisconnect = async () => {
    setDisconnectingSalesforce(true);

    try {
      await disconnectSalesforce();
      rootStore.setSalesforceUser(null);
      rootStore.setNotification(
        { header: 'Disconnected from Salesforce', body: null, type: 'success' },
        2000,
      );
    } catch (err) {
      rootStore.setNotification({
        header: 'Could not disconnect from Salesforce',
        body: null,
        type: 'error',
      });
    }

    setDisconnectingSalesforce(false);
  };

  const notificationSettingsButtons = [
    {
      value: '',
      label: "Don't Notify",
    },
    {
      value: 'EMAIL',
      label: 'Email',
    },
  ];

  const connectedToSalesforce = rootStore.connectedToSalesforce();
  const extensionVersion = chrome.runtime.getManifest().version;

  return (
    <BottomDrawer open={props.open} close={handleClose}>
      <Stack direction="column">
        <Stack direction="column" sx={{ pt: '1rem', pb: '2rem' }}>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            sx={{ width: '100%', mb: '2rem' }}
          >
            <Typography variant="h6" sx={{ fontWeight: '600' }}>
              Settings
            </Typography>
            {extensionVersion && (
              <Typography
                variant="subtitle2"
                sx={{ color: theme.palette.grey[500] }}
              >
                (Version {extensionVersion})
              </Typography>
            )}
          </Stack>
          <Stack direction="column">
            <Typography
              variant="body2"
              color="primary"
              sx={{
                textTransform: 'uppercase',
                fontWeight: '500',
                pb: '0.5rem',
              }}
            >
              Notifications
            </Typography>
            <Typography variant="body2" sx={{ pb: '1rem' }}>
              When answers are generated, how should Tribble notify you?
            </Typography>
            <StyledToggleButtonGroup
              exclusive={true}
              value={notificationSetting}
              onChange={handleNotificationChange}
            >
              {notificationSettingsButtons.map(({ value, label }) => (
                <ToggleButton key={value} value={value}>
                  {label}
                </ToggleButton>
              ))}
            </StyledToggleButtonGroup>
          </Stack>
          <Stack direction="column" sx={{ mt: 3 }}>
            <Typography
              variant="body2"
              color="primary"
              sx={{
                textTransform: 'uppercase',
                fontWeight: '500',
                pb: '0.5rem',
              }}
            >
              Salesforce Integration
            </Typography>
            {connectedToSalesforce ? (
              <>
                <Typography
                  variant="body2"
                  color="secondary"
                  sx={{ pb: '1rem', fontWeight: 500 }}
                >
                  Connected to Salesforce ✓
                </Typography>
                <Button
                  onClick={handleSalesforceDisconnect}
                  size="small"
                  variant="outlined"
                  color="error"
                  sx={{ width: 'fit-content' }}
                  disabled={disconnectingSalesforce}
                >
                  {disconnectingSalesforce ? 'Disconnecting...' : 'Disconnect'}
                </Button>
              </>
            ) : (
              <>
                <Typography variant="body2" sx={{ pb: '1rem' }}>
                  Connect to Salesfore to link projects to opportunities.
                </Typography>
                <Button
                  onClick={handleSalesforceConnect}
                  size="small"
                  variant="outlined"
                  color="secondary"
                  sx={{ width: 'fit-content' }}
                  disabled={connectingSalesforce}
                >
                  {connectingSalesforce
                    ? 'Connecting...'
                    : 'Connect to Salesforce'}
                </Button>
              </>
            )}
          </Stack>
        </Stack>
        <Stack direction="row" justifyContent="center" sx={{ width: '100%' }}>
          <Button onClick={saveSettings}>Save Settings</Button>
        </Stack>
      </Stack>
    </BottomDrawer>
  );
};
