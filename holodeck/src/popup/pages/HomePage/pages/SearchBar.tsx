import { OutlinedInputProps } from '@mui/material';
import React, { FC } from 'react';

import { SearchField } from '../../../../atomic-design/molecules/SearchField';
import { saveUserPreferences } from '../../../../utils/helpers';
import { useFilterStore } from '../useFilterStore';

type SearchBarProps = {
  extraProps?: OutlinedInputProps;
};
export const SearchBar: FC<SearchBarProps> = ({ extraProps }) => {
  const filterStore = useFilterStore();

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    e.preventDefault();
    const newValue = { searchQuery: e.target.value };
    filterStore.setFilters(newValue);
    saveUserPreferences({ ...filterStore.filters, ...newValue });
  };

  const handleClearSearch = (
    e: React.MouseEvent<HTMLButtonElement, MouseEvent>,
  ) => {
    e.preventDefault();
    const newValue = { searchQuery: '' };
    filterStore.setFilters(newValue);
    saveUserPreferences({ ...filterStore.filters, ...newValue });
  };

  const { searchQuery } = filterStore.filters;

  return (
    <SearchField
      handleChange={handleChange}
      handleClearSearch={handleClearSearch}
      searchQuery={searchQuery}
      extraProps={extraProps || {}}
    />
  );
};
