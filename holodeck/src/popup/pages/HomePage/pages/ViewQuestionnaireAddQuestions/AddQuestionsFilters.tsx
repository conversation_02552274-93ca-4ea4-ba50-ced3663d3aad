import React, { FC } from 'react';
import { SearchField } from '../../../../../atomic-design/molecules/SearchField';

export type AddQuestionsFiltersProps = {
  searchQuery: string;
  onChangeSearchQuery: (searchQuery: string) => void;
};

export const AddQuestionsFilters: FC<AddQuestionsFiltersProps> = ({
  searchQuery,
  onChangeSearchQuery,
}) => {
  return (
    <SearchField
      handleChange={(e) => onChangeSearchQuery(e.target.value)}
      handleClearSearch={() => onChangeSearchQuery('')}
      searchQuery={searchQuery}
      extraProps={{
        id: 'search-filter',
        sx: { borderRadius: 200, fontSize: '0.9rem' },
      }}
    />
  );
};
