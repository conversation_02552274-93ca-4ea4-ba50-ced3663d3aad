import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ield, Typography, useTheme } from '@mui/material';
import React, { FC, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

import { MetadataFilterPicker } from '../../../../../../atomic-design/molecules/MetadataFilterPicker';

import {
  MetadataFilterValueDto,
  formatMDFValuesForDatabase,
  getMappedMetdataFilterValues,
} from '../../../../../api/metadata.api';
import { useMetadataFilters } from '../../../../../api/metadata.swr';
import {
  QuestionnaireDto,
  getQuestionnaireById,
} from '../../../../../api/questionnaires.api';

import { createGrouping } from '../../../../../api/groupings.api';
import { useGroupings } from '../../../../../api/groupings.swr';
import { useRootStore } from '../../../../../state/useRootStore';
import { errorHelper } from '../../../../../utils/form.utils';

type CreateGroupingsFormProps = {
  onSubmit: (name: string) => void;
  onCancel: () => void;
  contentId: number;
};
export const CreateGroupingsForm: FC<CreateGroupingsFormProps> = ({
  onSubmit,
  onCancel,
  contentId,
}) => {
  const rootStore = useRootStore();
  const metadataFilters = useMetadataFilters();
  const theme = useTheme();

  const [questionnaire, setQuestionnaire] = useState<QuestionnaireDto | null>(
    null,
  );
  const [selectedTags, setSelectedTags] = useState<MetadataFilterValueDto[]>(
    [],
  );
  const [defaultSelectedTags, setDefaultSelectedTags] = useState<
    MetadataFilterValueDto[]
  >([]);
  const [useQuestionnaireContext, setUseQuestionnaireContext] =
    useState<boolean>(true);
  const [groupingHasNoContext, setGroupingHasNoContext] =
    useState<boolean>(false);

  useEffect(() => {
    getQuestionnaireById(contentId).then((questionnaire) => {
      if (questionnaire) {
        setQuestionnaire(questionnaire);
      }
    });
  }, []);

  useEffect(() => {
    if (questionnaire && metadataFilters.data.metadata_filters.length > 0) {
      // Check if questionnaire has MDF defined, and if so, set the selected tags
      // to them as a default
      const questionnaireMDF = questionnaire?.metadata_filter
        ? questionnaire.metadata_filter.multiple_types || [
            questionnaire.metadata_filter,
          ]
        : [];

      if (questionnaireMDF.length > 0) {
        const mdfValues = [];

        // For each questionnaire context MDF...
        questionnaireMDF.forEach((mdf) => {
          // Find the corresponding MDF Type...
          const mdfType = metadataFilters.data.metadata_filters.find((mdft) => {
            return Number(mdft.id) === Number(mdf.type_id);
          });
          if (mdfType) {
            // Then find the corresponding MDF Value records
            const questionnaireMDFV = mdfType.filter_values.filter((mdfv) =>
              mdf.values.includes(mdfv.value),
            );
            mdfValues.push(...questionnaireMDFV);
          }
        });
        setSelectedTags(mdfValues);
        setDefaultSelectedTags(mdfValues);
      }
    }
  }, [questionnaire, metadataFilters.data]);

  const availableMetadataFilters = metadataFilters.data.metadata_filters;
  const availableMetadataFiltersMap = availableMetadataFilters.reduce(
    (acc, mdf) => {
      return { ...acc, [mdf.id]: mdf };
    },
    {},
  );

  const mapMetadataFilterValuesById = getMappedMetdataFilterValues(
    availableMetadataFilters,
  );

  type CreateGroupingInputs = {
    name: string;
  };
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<CreateGroupingInputs>({
    defaultValues: {
      name: '',
    },
  });

  const groupingsSwr = useGroupings();

  const handleSelectTag = (
    selection: number[],
    initialLoad: boolean = false,
  ) => {
    const mdf_values = selection.map((mdfId) => {
      return mapMetadataFilterValuesById[mdfId];
    });
    setSelectedTags(mdf_values);
  };

  const handleCreateGrouping = async ({ name }) => {
    try {
      // This shouldn't happen, but just in case
      if (name == '') {
        rootStore.setNotification({
          header: 'Error',
          body: 'Please enter a name for this grouping.',
          type: 'error',
        });
        return;
      }

      // Check selected tags (context) and compare against questionnaire context.
      // If the same, write as the special '{}' value. If no contexts, then write
      // as the even more special 'all' value.
      const groupingHasNoContext = selectedTags.length === 0;
      const sameAsQuestionnaireContext =
        selectedTags.every((mdfv) =>
          defaultSelectedTags.find((defMdfv) => mdfv.id == defMdfv.id),
        ) &&
        defaultSelectedTags.every((defMdfv) =>
          selectedTags.find((mdfv) => mdfv.id == defMdfv.id),
        );

      // Need special handling for "use questionnaire context" = false
      // AND no tags selected: { '0': ['all'] }
      // Answer RFP will read this special case and not apply any MDF filters
      const groupingContext = sameAsQuestionnaireContext
        ? {}
        : groupingHasNoContext
          ? {
              type_id: 0,
              type_name: '',
              values: ['all'],
            } // special
          : {
              multiple_types: formatMDFValuesForDatabase(selectedTags),
            };

      await createGrouping(name, contentId, groupingContext);
      groupingsSwr.mutate();
      onSubmit(name);
    } catch (e) {
      rootStore.setNotification({
        header: 'Error',
        body: 'Failed to create grouping.',
        type: 'error',
      });
      return;
    }
  };

  // Count the # of answer versions that will be generated, based on selected tags
  const variantTypeNames = [];

  const numAnswerVersions = Object.values(
    selectedTags.reduce(
      (acc, mdfv) => {
        // Only include in the numAnswerVersions count if the MDF type has distinct_answer=true
        if (availableMetadataFiltersMap[mdfv.type_id].distinct_answer) {
          if (!acc[mdfv.type_name]) {
            acc[mdfv.type_name] = [];
            variantTypeNames.push(mdfv.type_name);
          }
          acc[mdfv.type_name].push(mdfv.value);
        }
        return acc;
      },
      {} as Record<string, any[]>,
    ),
  ).reduce((acc, arrTypeValues) => acc * arrTypeValues.length, 1);

  const questionnaireMDF = questionnaire?.metadata_filter
    ? questionnaire.metadata_filter.multiple_types || [
        questionnaire.metadata_filter,
      ]
    : [];

  return (
    <form
      onSubmit={handleSubmit(handleCreateGrouping)}
      style={{ height: '100%' }}
    >
      <Stack direction="column" gap="1rem">
        <Stack direction="column">
          <Typography
            variant="body1"
            color="primary"
            sx={{ mb: '0.5rem', fontWeight: 500 }}
          >
            Name
          </Typography>
          <TextField
            size="small"
            placeholder="Enter a grouping name"
            sx={{ mb: 2 }}
            {...register('name', { required: true })}
            {...errorHelper('name', errors)}
          />
        </Stack>

        {availableMetadataFilters.length > 0 && (
          <Stack direction="column" sx={{ mb: 5 }}>
            <Typography
              variant="body1"
              color="primary"
              sx={{ mb: '0.5rem', fontWeight: 500 }}
            >
              Tags
            </Typography>

            <Typography variant="body2" sx={{ mb: '1.5rem' }}>
              Groupings can be created with or without tags. Tags can help
              Tribble generate more accurate answers.{' '}
              {questionnaireMDF.length > 0
                ? "(Defaulting to the questionnaire's configured tags.)"
                : ''}
            </Typography>
            <MetadataFilterPicker
              size="small"
              onSelect={handleSelectTag}
              selections={selectedTags}
              hideInactive={true}
              hideVerbatim={true}
            />
            {numAnswerVersions > 1 && !groupingHasNoContext && (
              <Typography
                variant="body1"
                color="error"
                sx={{ mt: '1rem', fontWeight: 500 }}
              >
                {variantTypeNames.length == 1 &&
                  `⚠️ Each question in this grouping will generate ${numAnswerVersions} answer versions. One for each ${variantTypeNames[0]} selected.`}
                {variantTypeNames.length > 1 &&
                  `⚠️ Each question in this grouping will generate ${numAnswerVersions} answer versions. One for each combination of values from ${variantTypeNames.join(
                    ', ',
                  )}.`}
              </Typography>
            )}
          </Stack>
        )}

        <div style={{ height: '16px' }}></div>

        <Stack
          direction="row"
          justifyContent="center"
          sx={{
            width: 'calc(100% - 24px)',
            position: 'fixed',
            bottom: '12px',
            left: '12px',
            backgroundColor: '#fff',
            zIndex: 100,
          }}
        >
          <Button type="submit">Create Grouping</Button>
        </Stack>
      </Stack>
    </form>
  );
};
