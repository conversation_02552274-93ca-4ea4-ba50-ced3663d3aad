import { InfoOutlined } from '@mui/icons-material';
import {
  Button,
  FormControl,
  FormControlLabel,
  FormLabel,
  Radio,
  RadioGroup,
  Stack,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import React, { FC } from 'react';
import { useForm } from 'react-hook-form';
import { P, match } from 'ts-pattern';

import { useContentGroupings } from '../../../../../api/groupings.swr';
import {
  formatMDFValuesForUI,
  getMappedMetdataFilterValues,
  usesAllMDF,
} from '../../../../../api/metadata.api';
import { useMetadataFilters } from '../../../../../api/metadata.swr';
import { useRootStore } from '../../../../../state/useRootStore';

import { GreyTooltip } from '../../../../../../atomic-design/atoms/StyledTooltips';

type SelectGroupingsFormProps = {
  onSubmit: (selectedGroupingId: number) => void;
  onCancel: () => void;
  onSwitchToCreate: () => void;
  contentId: number;
};
export const SelectGroupingsForm: FC<SelectGroupingsFormProps> = ({
  onSubmit,
  onCancel,
  onSwitchToCreate,
  contentId,
}) => {
  const groupingsSwr = useContentGroupings(contentId);
  const metadataFilters = useMetadataFilters();
  const theme = useTheme();

  type SelectGroupingInputs = {
    groupingId: number;
  };
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<SelectGroupingInputs>({
    defaultValues: {
      groupingId: undefined,
    },
  });

  // temp
  const allValues = watch();
  const hasSelectedGrouping =
    allValues['groupingId'] && allValues['groupingId'] !== null;

  const availableMetadataFilterTypes = metadataFilters.data.metadata_filters;
  const mappedMetadataFilters = getMappedMetdataFilterValues(
    availableMetadataFilterTypes,
  );

  const matchesWide = useMediaQuery('(min-width:450px)');

  const performOnSubmit = (results: { groupingId: string }) => {
    let result: number;
    try {
      const groupingId = parseInt(results.groupingId);
      result = groupingId;
    } catch (e) {
      console.error(
        `[TRIBBLE] performOnSubmit failed to parse results`,
        results,
      );
      useRootStore().setNotification({
        header: 'Error',
        body: 'Failed to parse selected grouping.',
        type: 'error',
      });
      return;
    }
    onSubmit(result);
  };

  return match(groupingsSwr)
    .with({ isValidating: true }, () => <div>Loading...</div>)
    .with({ error: P.not(P.nullish) }, () => <div>Error</div>)
    .otherwise(({ data: availableGroupings }) => {
      const sortedGroupings = [...availableGroupings].sort((a, b) => {
        return a.grouping_name.localeCompare(b.grouping_name);
      });

      return (
        <form onSubmit={handleSubmit(performOnSubmit)}>
          <Stack direction="column">
            <FormControl
              sx={{ maxHeight: '30vh', overflowY: 'auto', mb: '2rem' }}
            >
              <FormLabel sx={{ mb: 1 }}>Select a grouping:</FormLabel>

              <RadioGroup>
                {sortedGroupings.map((grouping) => {
                  const hasMDF =
                    Object.keys(grouping.grouping_metadata_filter).length > 0;

                  // Remember the special { '0': ['all'] } case (see CreateGroupingForm.tsx)
                  const isAllMDF =
                    hasMDF && usesAllMDF(grouping.grouping_metadata_filter);

                  let mappedMdfString = '';

                  if (!isAllMDF) {
                    const mappedMdf = formatMDFValuesForUI(
                      grouping.grouping_metadata_filter,
                    );

                    Object.keys(mappedMdf).forEach((key, idx) => {
                      if (idx > 0) {
                        mappedMdfString += '\n\n';
                      }
                      mappedMdfString += `${key}:\n- ${mappedMdf[key].join('\n- ')}`;
                    });
                  }

                  const tooltipText =
                    hasMDF && !isAllMDF
                      ? `Any questions in this grouping will be answered using the following context(s), which supercede any questionnaire-level contexts:\n\n${mappedMdfString}`
                      : hasMDF && isAllMDF
                        ? `No context tags will be applied to questions in this grouping`
                        : `Questions in this grouping will be answered using the questionnaire-level context(s)`;

                  return (
                    <Stack
                      direction="row"
                      alignItems="center"
                      key={grouping.grouping_id}
                    >
                      <FormControlLabel
                        control={<Radio />}
                        label={grouping.grouping_name}
                        value={grouping.grouping_id}
                        {...register('groupingId', { required: true })}
                      />
                      <GreyTooltip title={tooltipText}>
                        <InfoOutlined
                          fontSize="small"
                          sx={{ color: theme.palette.grey[500] }}
                        />
                      </GreyTooltip>
                    </Stack>
                  );
                })}
              </RadioGroup>
            </FormControl>
            {matchesWide && (
              <Stack
                direction="row"
                gap="1rem"
                justifyContent="space-between"
                alignItems="center"
              >
                <Button type="submit" disabled={!hasSelectedGrouping}>
                  Apply Grouping
                </Button>
                <Button
                  variant="outlined"
                  type="reset"
                  onClick={onSwitchToCreate}
                >
                  Create New Grouping
                </Button>
              </Stack>
            )}
            {!matchesWide && (
              <Stack direction="column" gap="1rem">
                <Button type="submit" disabled={!hasSelectedGrouping}>
                  Apply Grouping
                </Button>

                <Button
                  variant="outlined"
                  type="reset"
                  onClick={onSwitchToCreate}
                >
                  Create New Grouping
                </Button>
              </Stack>
            )}
          </Stack>
        </form>
      );
    });
};
