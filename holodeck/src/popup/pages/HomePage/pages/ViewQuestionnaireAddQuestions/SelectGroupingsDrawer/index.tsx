import { HelpOutline } from '@mui/icons-material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import CloseIcon from '@mui/icons-material/Close';
import { IconButton, Stack, Typography } from '@mui/material';
import React, { FC, useState } from 'react';
import { P, match } from 'ts-pattern';

import { BottomDrawer } from '../../../../../../atomic-design/atoms/BottomDrawer';
import { SecondaryTooltip } from '../../../../../../atomic-design/atoms/StyledTooltips';
import { useContentGroupings } from '../../../../../api/groupings.swr';

import { CreateGroupingsForm } from './CreateGroupingForm';
import { SelectGroupingsForm } from './SelectGroupingForm';

type SelectGroupingsDrawerProps = {
  contentId: number;
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (selectedGroupingIds: number) => void;
};
export const SelectGroupingsDrawer: FC<SelectGroupingsDrawerProps> = ({
  contentId,
  isOpen,
  onClose,
  onSubmit,
}) => {
  const [preferredMode, setPreferredMode] = useState<'create' | 'select'>(
    'select',
  );
  const contentGroupingsSwr = useContentGroupings(contentId);

  const handleSetGrouping = (selectedGroupingId: number) => {
    onSubmit(selectedGroupingId);
  };

  const handleClose = () => {
    setPreferredMode('select');
    onClose();
  };

  const handleGroupingCreated = async (name: string) => {
    setPreferredMode('select');
    const updatedContentGroupSwr = await contentGroupingsSwr.mutate();

    // Wait for mutate to finish then automatically set the grouping for the questions
    const newGrouping = updatedContentGroupSwr?.find(
      (grouping) => grouping.grouping_name === name,
    );

    if (newGrouping) {
      handleSetGrouping(newGrouping.grouping_id);
    }
  };

  return match(contentGroupingsSwr)
    .with({ isValidating: true }, () => <div>Loading...</div>)
    .with({ error: P.not(P.nullish) }, () => <div>Error</div>)
    .otherwise(({ data: availableGroupings }) => {
      return (
        <BottomDrawer open={isOpen} close={handleClose}>
          <Stack direction="column" sx={{ pt: '1rem' }}>
            <Stack
              direction="row"
              justifyContent="space-between"
              alignItems="center"
              sx={{ width: '100%', mb: '2rem' }}
            >
              <Stack direction="row" alignItems="center">
                {preferredMode == 'create' && (
                  <IconButton
                    aria-label="back"
                    onClick={() => setPreferredMode('select')}
                    sx={{
                      color: (theme) => theme.palette.grey[500],
                      mr: 1,
                      ml: '-12px',
                    }}
                  >
                    <ArrowBackIcon />
                  </IconButton>
                )}
                <Typography variant="h6" sx={{ fontWeight: '600' }}>
                  {preferredMode == 'create' ? 'Create' : 'Apply'} Grouping
                </Typography>
              </Stack>

              <IconButton
                aria-label="close"
                onClick={handleClose}
                sx={{
                  color: (theme) => theme.palette.grey[500],
                  mr: '-8px',
                }}
              >
                <CloseIcon />
              </IconButton>
            </Stack>
            {match({ availableGroupings, preferredMode })
              .with({ availableGroupings: [] }, () => (
                <>
                  <Stack
                    direction="row"
                    justifyContent="space-between"
                    alignItems="center"
                    sx={{ width: '100%', mb: '0.5rem' }}
                  >
                    <Typography
                      variant="body1"
                      sx={{ fontWeight: 500 }}
                      color="secondary"
                    >
                      No groupings created yet
                    </Typography>
                    <SecondaryTooltip
                      title={`Groupings are tied to questionnaires; if you create a grouping here, it will only be available for questions in this questionnaire.\n\nNote: in previous versions of the Chrome Extension, groupings created in one questionnaire were visible in other questionnaires.`}
                    >
                      <HelpOutline fontSize="small" color="secondary" />
                    </SecondaryTooltip>
                  </Stack>
                  <Typography
                    variant="body2"
                    sx={{ mb: '2rem' }}
                    color="secondary"
                  >
                    Groupings are useful to categorize questions, for example
                    you may have "Legal", "Finance", or "Security" groupings.
                  </Typography>
                  <Typography
                    variant="body1"
                    sx={{ mb: '1.5rem', fontWeight: 500 }}
                  >
                    Create a new grouping
                  </Typography>
                  <CreateGroupingsForm
                    onSubmit={handleGroupingCreated}
                    onCancel={handleClose}
                    contentId={contentId}
                  />
                </>
              ))
              .with({ preferredMode: 'create' }, () => (
                <CreateGroupingsForm
                  onSubmit={handleGroupingCreated}
                  onCancel={() => setPreferredMode('select')}
                  contentId={contentId}
                />
              ))
              .otherwise(() => (
                <SelectGroupingsForm
                  onSubmit={handleSetGrouping}
                  onCancel={handleClose}
                  onSwitchToCreate={() => setPreferredMode('create')}
                  contentId={contentId}
                />
              ))}
          </Stack>
        </BottomDrawer>
      );
    });
};
