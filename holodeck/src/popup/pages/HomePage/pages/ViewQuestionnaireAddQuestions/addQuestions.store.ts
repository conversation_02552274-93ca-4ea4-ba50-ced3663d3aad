import { v4 } from 'uuid';
import { buildStore } from '../../../storeContextBuilder';

type AddQuestionsStoreShape = {
  _questions: QuestionHolder[];
  _selections: string[];
  getQuestions: () => QuestionHolder[];
  getSelectedQuestions: () => QuestionHolder[];
  editQuestion: (id: string, value: string) => void;
  bulkAddQuestions: (questions: string[]) => number;
  addAtIndex: (index: number, question: string) => void;
  deleteAtIndex: (index: number) => void;
  deleteById: (id: string) => void;
  isEmpty: () => boolean;
  hasSelections: () => boolean;
  selectQuestion: (id: string) => void;
  deselectQuestion: (id: string) => void;
  selectQuestionByIndex: (index: number) => void;
  clearSelections: () => void;
  selectAll: () => void;
  isSelected: (id: string) => boolean;
  getSelectionSparsity: () => 'all' | 'none' | 'some';
  deleteAllSelected: () => void;
  bulkAddGroupingToSelectedQuestions: (groupingIdToBeApplied: number) => number;
  removeQuestionGrouping: (questionId: string) => void;
};

const mapQuestion = (question: string): QuestionHolder => ({
  id: v4(),
  question,
  groupingId: null,
});

export const AddQuestionsStore = buildStore<AddQuestionsStoreShape>(
  (set, get) => {
    return {
      _questions: [mapQuestion('')],
      _selections: [],

      getQuestions: () => {
        return [...get()._questions];
      },
      getSelectedQuestions: () => {
        return get()._questions.filter((q) => get()._selections.includes(q.id));
      },
      editQuestion: (id: string, value: string) => {
        set((state) => {
          const questions = [...state._questions];
          const questionIndex = questions.findIndex((q) => q.id === id);
          questions[questionIndex].question = value;
          return {
            ...state,
            _questions: questions,
          };
        });
      },
      addAtIndex: (index: number, question: string) => {
        // if adding at the top of an empty list, there will be a blank question already there. Replace it.
        set((state) => {
          if (get().isEmpty() && index === 0) {
            return {
              ...state,
              _questions: [mapQuestion(question)],
            };
          }

          // otherwise, insert the question at the index
          const questions = [...state._questions];
          questions.splice(index, 0, mapQuestion(question));
          return {
            ...state,
            _questions: questions,
          };
        });
      },
      deleteAtIndex: (index: number) => {
        if (index > get()._questions.length - 1) return;

        const question = get()._questions[index];
        if (!question) return;

        set((state) => {
          const questions = [...state._questions];
          questions.splice(index, 1);

          // remove from selections
          const selections = state._selections.filter((s) => s !== question.id);

          return {
            ...state,
            _questions: questions,
            _selections: selections,
          };
        });
      },
      deleteById: (id: string) => {
        set((state) => {
          const remainingQuestions = state._questions.filter(
            (q) => q.id !== id,
          );

          // if questions is empty, add a blank question
          if (remainingQuestions.length === 0) {
            remainingQuestions.push(mapQuestion(''));
          }

          // remove from selections
          const selections = state._selections.filter((s) => s !== id);

          return {
            ...state,
            _questions: remainingQuestions,
            _selections: selections,
          };
        });
      },
      bulkAddQuestions: (addedQuestions: string[]) => {
        let addedFromIdx = 0;
        set((state) => {
          // if the only question is blank, remove the blank question
          if (state.isEmpty()) {
            return {
              ...state,
              _questions: [...addedQuestions.map(mapQuestion)],
            };
          }

          addedFromIdx = state._questions.length;

          return {
            ...state,
            _questions: [
              ...state._questions,
              ...addedQuestions.map(mapQuestion),
            ],
          };
        });

        return addedFromIdx;
      },
      isEmpty: () => {
        return (
          get()._questions.length === 1 && get()._questions[0].question === ''
        );
      },
      selectQuestion: (id: string) => {
        set((state) => {
          return {
            ...state,
            _selections: [...state._selections, id],
          };
        });
      },
      selectQuestionByIndex: (index: number) => {
        if (index > get()._questions.length - 1) return;

        const question = get()._questions[index];
        if (!question || get()._selections.includes(question.id)) return;

        set((state) => {
          return {
            ...state,
            _selections: [...state._selections, question.id],
          };
        });
      },
      deselectQuestion: (id: string) => {
        set((state) => {
          return {
            ...state,
            _selections: state._selections.filter((s) => s !== id),
          };
        });
      },
      clearSelections: () => {
        set((state) => {
          return {
            ...state,
            _selections: [],
          };
        });
      },
      isSelected: (id: string) => {
        return get()._selections.includes(id);
      },
      // returns true, false... or if there are SOME selections, but not all, returns 'some'
      getSelectionSparsity: (): 'all' | 'none' | 'some' => {
        const questionLength = get()._questions.length;
        const selectionLength = get()._selections.length;
        const isEmpty = get().isEmpty();

        if (isEmpty || selectionLength === 0) {
          return 'none';
        }

        if (questionLength === selectionLength) {
          return 'all';
        }

        return 'some';
      },
      selectAll: () => {
        if (get().isEmpty()) {
          return;
        }

        set((state) => {
          return {
            ...state,
            _selections: state._questions.map((q) => q.id),
          };
        });
      },
      hasSelections: () => {
        return get()._selections.length > 0;
      },
      deleteAllSelected: () => {
        set((state) => {
          let newQuestions = state._questions.filter(
            (q) => !state._selections.includes(q.id),
          );
          if (newQuestions.length === 0) {
            newQuestions.push(mapQuestion(''));
          }
          return {
            ...state,
            _questions: newQuestions,
            _selections: [],
          };
        });
      },
      bulkAddGroupingToSelectedQuestions: (groupingIdToBeApplied: number) => {
        let counter = 0;
        get()._selections.forEach((selectionId) => {
          get()._questions.forEach((question) => {
            const isQuestionSelected = question.id === selectionId;
            if (isQuestionSelected) {
              question.groupingId = groupingIdToBeApplied;
              counter++;
            }
          });
        });
        return counter;
      },
      removeQuestionGrouping: (questionId: string) => {
        set((state) => {
          const questions = [...state._questions];
          const questionIndex = questions.findIndex((q) => q.id === questionId);
          questions[questionIndex].groupingId = null;
          return {
            ...state,
            _questions: questions,
          };
        });
      },
    };
  },
);

export type QuestionHolder = {
  id: string;
  question: string;
  groupingId: number | null;
};
