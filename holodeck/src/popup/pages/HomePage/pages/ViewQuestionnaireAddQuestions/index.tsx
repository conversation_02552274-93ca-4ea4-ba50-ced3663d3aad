import DeleteForeverIcon from '@mui/icons-material/DeleteForever';
import DeselectIcon from '@mui/icons-material/Deselect';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import WorkspacesIcon from '@mui/icons-material/Workspaces';
import {
  Box,
  Button,
  Checkbox,
  Chip,
  Fab,
  IconButton,
  Stack,
  TextareaAutosize,
  Typography,
  useTheme,
} from '@mui/material';
import React, { FC, SyntheticEvent, useEffect, useState } from 'react';
import { useDebouncedCallback } from 'use-debounce';

import { PageContainer } from '../../../../../atomic-design/atoms/PageContainer';
import { PageHeaderBar } from '../../../../../atomic-design/atoms/PageHeaderBar';
import { useContentGroupings } from '../../../../api/groupings.swr';
import { addQuestionsToQuestionnaire } from '../../../../api/question.api';
import { getQuestionnaireById } from '../../../../api/questionnaires.api';
import { useRootStore } from '../../../../state/useRootStore';
import { ActionDivider } from './ActionDivider';
import { AddQuestionsFilters } from './AddQuestionsFilters';
import { SelectGroupingsDrawer } from './SelectGroupingsDrawer';
import { AddQuestionsStore } from './addQuestions.store';
import {
  PastedQuestionsResultSuccess,
  getAnyQuestionsOnClipboard,
} from './clipboard.service';
import { StyledTextArea } from './styledComponents';

type ViewQuestionnaireAdQuestionsProps = {
  id: string;
  rfxContentId: string;
};
export const ViewQuestionnaireAddQuestions: FC<
  ViewQuestionnaireAdQuestionsProps
> = ({ id, rfxContentId }) => {
  const rootStore = useRootStore();
  const theme = useTheme();
  const { data: groupings } = useContentGroupings(Number(id));
  const questionStore = AddQuestionsStore.useLocalStore();
  const questions = questionStore.getQuestions();
  const selectedQuestions = questionStore.getSelectedQuestions();
  const isPastedQuestionsEmpty = questionStore.isEmpty();
  const hasAnySelections =
    questionStore.getSelectionSparsity() === 'all' ||
    questionStore.getSelectionSparsity() === 'some';

  const [jobId, setJobId] = useState<number>();
  const [isGroupingDrawerOpen, setIsGroupingDrawerOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [submitting, setSubmitting] = useState(false);

  // reimplement the Fab here as scrolling within the questions div didn't seem to work
  const [showFab, setShowFab] = useState(false);

  const scrollingRef = React.useRef<HTMLDivElement>(null);

  const filteredQuestions = questions.filter((q) => {
    return searchQuery.length
      ? q.question.toLowerCase().includes(searchQuery.toLowerCase())
      : true;
  });

  const findGroupingLabelById = (groupingId: number): string => {
    const match = groupings.find(
      (grouping) => grouping.grouping_id === groupingId,
    );
    if (match) {
      return match.grouping_name;
    } else {
      // unknown grouping
      return `Grouping [id=${groupingId}]`;
    }
  };

  const DEBOUNCE_TIME_MS = 1000;

  const handlePaste = useDebouncedCallback(
    async (e: {
      target: ClipboardEvent['target'] | React.ClipboardEvent['target'];
    }) => {
      // If drawer is open, don't do the question copy/paste
      if (isGroupingDrawerOpen) {
        return;
      }

      // if the user is pasting into an element that has the attribute "tribble-disable-paste", then we don't want to do anything
      const targetHasAttribute = (e.target as Element).hasAttribute?.(
        'tribble-disable-paste',
      );
      const isTrue =
        (e.target as Element).getAttribute?.('tribble-disable-paste') ===
        'true';

      const targetIndex = (e.target as Element).getAttribute?.(
        'tribble-question-index',
      );

      const isSearchFilter = (e.target as Element).id === 'search-filter';

      const result = await getAnyQuestionsOnClipboard();

      if (result.success === false) {
        if (result.errorCode === 'clipboard-empty') {
          rootStore.setNotification({
            header: 'Clipboard Empty',
            body: 'There are no questions on the clipboard.',
            type: 'error',
          });
        } else if (result.errorCode === 'unexpected-error') {
          if (isSearchFilter) {
            // Just paste whatever is on the clipboard into the search filter
            setSearchQuery(result.clipboardContents ?? '');
          } else if (
            targetHasAttribute &&
            isTrue &&
            !isNaN(Number(targetIndex))
          ) {
            // Just paste whatever is on the clipboard into the search filter
            questionStore.addAtIndex(
              Number(targetIndex),
              result.clipboardContents ?? '',
            );
            // When you're pasting into a text box, the addAtIndex will create
            // a blank row at the end. So just remove it after the above pasting.
            questionStore.deleteAtIndex(Number(targetIndex) + 1);
          } else {
            rootStore.setNotification({
              header: 'Unexpected Error',
              body: 'Unable to paste clipboard contents into questionnaire.',
              type: 'error',
            });
          }
        }
        return;
      }

      const questionsToPaste =
        (result as PastedQuestionsResultSuccess).questions || [];

      if (questionsToPaste.length === 0) {
        return;
      }

      const lastOldQuestionIndex = filteredQuestions.length - 1;
      const isEmpty = questionStore.isEmpty();

      // If it looks like they're just pasting one question into the search box...
      // It may just be that. But if we detect multiple questions, more than likely
      // they meant to paste as new questions
      if (isSearchFilter && questionsToPaste.length === 1) {
        setSearchQuery(questionsToPaste[0]);
        return;
      }

      const autoSelectAfterAdding = true;

      if (targetHasAttribute && isTrue && !isNaN(Number(targetIndex))) {
        questionStore.clearSelections();

        questionsToPaste.forEach((question, pasteIdx) => {
          questionStore.addAtIndex(Number(targetIndex) + pasteIdx, question);

          // After pasting, auto select these pasted questions for further action.
          // E.g. applying a grouping to all of them.
          questionStore.selectQuestionByIndex(Number(targetIndex) + pasteIdx);
        });
        // When you're pasting into a text box, the addAtIndex will create
        // a blank row at the end. So just remove it after the above pasting.
        questionStore.deleteAtIndex(
          Number(targetIndex) + questionsToPaste.length,
        );

        return;
      }

      const addedFromIdx = questionStore.bulkAddQuestions(result.questions);

      // after adding, auto select
      if (autoSelectAfterAdding) {
        result.questions.forEach((q, idx) => {
          questionStore.selectQuestionByIndex(addedFromIdx + idx);
        });
      }

      let message = '';
      if (result.questions.length === 1) {
        message = '1 question was added to the list below.';
      } else {
        message = `${result.questions.length} questions were added to the list below.`;
      }

      rootStore.setNotification({
        header: 'Import successful',
        body: message,
        type: 'success',
      });

      // scroll to the bottom of the list of questions, so that the user can see the new questions
      if (!isEmpty) {
        setTimeout(() => {
          const possibleFirstNewQuestionIndex = lastOldQuestionIndex + 1;
          const oldQuestionElement = document.querySelector(
            `[tribble-scroll-index="${lastOldQuestionIndex}"]`,
          );
          const newQuestionElement = document.querySelector(
            `[tribble-scroll-index="${possibleFirstNewQuestionIndex}"]`,
          );
          const indexToScrollTo: number | null = newQuestionElement
            ? possibleFirstNewQuestionIndex
            : oldQuestionElement
              ? lastOldQuestionIndex
              : null;

          if (indexToScrollTo !== null) {
            document
              .querySelector(`[tribble-scroll-index="${indexToScrollTo}"]`)
              ?.scrollIntoView({ behavior: 'smooth' });
            // @ts-expect-error - this is a hack to blur the element that was pasted into
            document.activeElement?.blur();
          }
        }, 100);
      }
    },
    DEBOUNCE_TIME_MS,
    { leading: true, trailing: false },
  );

  useEffect(() => {
    getQuestionnaireById(id).then((result) => {
      setJobId(result.job_id);
    });
  }, []);

  useEffect(() => {
    document.addEventListener('paste', handlePaste);
    return () => {
      document.removeEventListener('paste', handlePaste);
    };
  }, []);

  const handleCreateQuestions = async () => {
    if (
      questionStore.isEmpty() ||
      questionStore.getQuestions().filter((q) => q.question.trim() !== '')
        .length == 0
    ) {
      rootStore.setNotification({
        header: 'No Questions',
        body: 'There are no questions to add to the questionnaire. Try adding a few and try again.',
        type: 'error',
      });
      return;
    }

    setSubmitting(true);

    try {
      const result = await addQuestionsToQuestionnaire({
        content_id: id,
        job_id: jobId,
        questions: questionStore
          .getQuestions()
          .filter((q) => q.question.trim() !== '')
          .map((q) => ({
            assignee_id: null,
            grouping_id: q.groupingId || null,
            query_string: q.question,
          })),
      });

      if (result.success) {
        rootStore.setNotification({
          header: 'Questions Added',
          body: 'The questions have been added to the questionnaire.',
          type: 'success',
        });
        rootStore.navigate({
          name: 'viewQuestionnaire',
          id: rfxContentId,
          questionsAdded: true,
        });
      } else {
        rootStore.setNotification({
          header: 'Error Adding Questions',
          body: 'Unable to add questions to questionnaire.',
          type: 'error',
        });
        return;
      }
    } catch (e) {
      console.error(e);
      rootStore.setNotification({
        header: 'Unexpected Error',
        body: 'Unable to add questions to questionnaire.',
        type: 'error',
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteQuestion = (id: string, e: SyntheticEvent) => {
    // TODO: IMPLEMENT

    // We dont want to trigger the click on the parent stack
    e.stopPropagation();
    e.preventDefault();
    questionStore.deleteById(id);
  };

  const addEmptyQuestion = (index: number, e: SyntheticEvent) => {
    e.stopPropagation();
    e.preventDefault();
    questionStore.addAtIndex(index, '');
  };

  const navigateBack = () =>
    rootStore.navigate({
      name: 'viewQuestionnaire',
      id: rfxContentId,
    });

  const handleBulkSelections = (selectedGroupingId: number) => {
    const numQuestions =
      questionStore.bulkAddGroupingToSelectedQuestions(selectedGroupingId);

    // Keep selected in case user wants to undo
    // questionStore.clearSelections();
    setIsGroupingDrawerOpen(false);
    rootStore.setNotification(
      {
        header: 'Grouping Applied',
        body: `The grouping applied to ${numQuestions} questions`,
        type: 'success',
      },
      3000,
    );
  };

  const handleScroll = (e) => {
    setShowFab(scrollingRef?.current.scrollTop > 300);
  };

  const scrollToTop = () => {
    scrollingRef?.current.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <PageContainer
      ref={scrollingRef}
      onScroll={handleScroll}
      sx={{
        gap: 0,
        height: '100%',
        width: '100%',
        backgroundColor: '#fff',
        padding: 0,
        overflowY: 'scroll',
        overflowX: 'hidden',
      }}
    >
      <Box sx={{ m: '1rem', mb: 0 }}>
        <PageHeaderBar onClickBack={navigateBack} />
      </Box>
      <Stack
        sx={(theme) => ({
          padding: '1rem',
          gap: '0.5rem',
          backgroundColor: theme.palette.grey[200],
        })}
      >
        <Stack direction="row" alignItems="center" gap="0.5rem">
          <Typography variant="h6" sx={{ fontWeight: '600' }}>
            Add Questions
          </Typography>
          <Chip
            label={questions.length}
            size="small"
            variant="filled"
            color={questions.length ? 'primary' : 'default'}
          />
        </Stack>
        <Typography variant="body2" sx={{ color: 'grey.500' }}>
          You can paste questions from the clipboard using Ctrl+V or Cmd+V. Edit
          an existing question by clicking on it.
        </Typography>
        <Stack
          sx={{
            justifyContent: 'space-between',
            flexDirection: 'column',
            width: '100%',
            gap: '1rem',
            mt: '0.5rem',
          }}
        >
          <AddQuestionsFilters
            onChangeSearchQuery={(val) => setSearchQuery(val)}
            searchQuery={searchQuery}
          />
          <Stack
            direction="row"
            alignItems="center"
            justifyContent="space-between"
            sx={{ mt: '0.5rem', mb: '1rem', mr: '-2px' }}
          >
            <Stack direction="row" gap="0.5rem" alignItems="center">
              <Typography variant="body2" sx={{ color: 'grey.500' }}>
                Selected
              </Typography>
              <Chip
                label={selectedQuestions.length}
                size="small"
                variant="filled"
                sx={{ ml: '4px' }}
                color={selectedQuestions.length ? 'primary' : 'default'}
              />
            </Stack>
            <Stack direction="row" alignItems={'center'} gap="0.5rem">
              <Typography variant="body2">All</Typography>
              <Checkbox
                sx={{ padding: 0 }}
                checked={questionStore.getSelectionSparsity() === 'all'}
                indeterminate={questionStore.getSelectionSparsity() === 'some'}
                disabled={questionStore.isEmpty()}
                onClick={(e) => {
                  const areAllSelected = questionStore.getSelectionSparsity();

                  if (areAllSelected === 'all') {
                    questionStore.clearSelections();
                  } else {
                    questionStore.selectAll();
                  }
                }}
              />
            </Stack>
          </Stack>
        </Stack>
      </Stack>

      <Stack height="100%">
        <Stack paddingBottom="10rem">
          <Box position="relative">
            <ActionDivider onClick={(e) => addEmptyQuestion(0, e)} />
          </Box>

          {filteredQuestions.map((question, i) => {
            const isSelected = questionStore.isSelected(question.id);

            return (
              <Stack
                tribble-scroll-index={`${i}`}
                position="relative"
                direction="column"
                key={question.id}
                sx={{
                  ':hover': { backgroundColor: 'grey.300' },
                  paddingY: '2rem',
                }}
              >
                <Stack
                  direction="row"
                  justifyContent={'space-between'}
                  alignItems={'flex-start'}
                >
                  <Stack
                    justifyContent="start"
                    alignItems="center"
                    height="100%"
                    sx={{ pl: '1rem', pr: '0.5rem' }}
                  >
                    <Typography
                      variant="body2"
                      sx={{
                        color: 'grey.400',
                        paddingTop: '0.5rem',
                        minWidth: '16px',
                      }}
                    >
                      {i + 1}
                    </Typography>
                  </Stack>
                  <Stack
                    direction="column"
                    sx={{ flexGrow: '1' }}
                    tribble-disable-paste="false"
                    gap="0.5rem"
                  >
                    <StyledTextArea
                      tribble-disable-paste="true"
                      tribble-question-index={i}
                      placeholder="Add a question..."
                      value={question.question}
                      minRows={1}
                      maxRows={20}
                      onChange={(e) => {
                        questionStore.editQuestion(question.id, e.target.value);
                      }}
                      sx={{
                        padding: '4px 8px',
                        border: '2px solid #ffffff00',
                        borderRadius: '4px',
                        overflowY: 'initial !important',
                        '&:focus': {
                          border:
                            '2px solid ' + theme.palette.primary.main + '50',
                        },
                      }}
                    />
                    {question.groupingId && (
                      <Chip
                        label={findGroupingLabelById(question.groupingId)}
                        onDelete={() =>
                          questionStore.removeQuestionGrouping(question.id)
                        }
                        variant="outlined"
                        size="small"
                        sx={{ width: 'fit-content', ml: '0.6rem' }}
                      />
                    )}
                  </Stack>
                  <Stack direction="column" sx={{ mr: '0.5rem' }}>
                    <Checkbox
                      checked={isSelected}
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();

                        if (isSelected) {
                          questionStore.deselectQuestion(question.id);
                        } else {
                          questionStore.selectQuestion(question.id);
                        }
                      }}
                      disabled={isPastedQuestionsEmpty}
                      sx={{
                        strokeWidth: 1,
                        p: '4px 6px',
                      }}
                    />
                  </Stack>
                </Stack>
                <ActionDivider onClick={(e) => addEmptyQuestion(i + 1, e)} />
              </Stack>
            );
          })}
          {/* This is diabolical fix for copy paste not working after clicking on the empty space at the bottom of the list. 
                        For some reason, when I click on the empty space at the bottom of a short list of questions, the browser thinks 
                        it's focused on the textarea, and so it pastes the clipboard contents into the textarea. However, since the last 
                        textarea has the attribute "tribble-disable-paste", it doesn't paste the clipboard contents into the textarea. 
                        This results in nothing happening. So, I added this empty textarea at the bottom of the list, so that when the
                        user clicks on the empty space at the bottom of the list, it focuses on the empty textarea, and then when the
                        user pastes the clipboard contents, it pastes it gets caught by the handler above. No actual content is pasted
                        into this textarea.
                    */}
          <TextareaAutosize
            style={{ opacity: 0, pointerEvents: 'none', resize: 'none' }}
          />
        </Stack>
        {showFab && (
          <Fab
            size="large"
            color="primary"
            aria-label="scroll-up"
            style={{
              position: 'fixed',
              bottom: selectedQuestions.length > 0 ? '155px' : '105px',
              right: '16px',
              zIndex: 1199,
              backgroundColor: theme.palette.primary.main,
            }}
            onClick={scrollToTop}
          >
            <KeyboardArrowUpIcon />
          </Fab>
        )}
      </Stack>
      <Stack
        sx={(theme) => ({
          position: 'fixed',
          bottom: '0',
          width: '100%',
          backgroundColor: 'white',
          borderTopWidth: '1px',
          borderTopColor: theme.palette.grey[300],
          borderTopStyle: 'solid',
          zIndex: 1200,
          alignItems: 'center',
        })}
      >
        <Stack
          direction="column"
          sx={{
            p: '1rem',
            alignItems: 'flex-start',
            maxWidth: '500px',
          }}
        >
          {selectedQuestions.length > 0 && (
            <>
              <Typography
                variant="subtitle2"
                sx={{ color: 'grey.500', fontWeight: 500 }}
              >
                Selected Question Actions ({selectedQuestions.length}):
              </Typography>
              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
                sx={{ width: '100%', mt: '4px', mb: '1rem' }}
              >
                <Button
                  key="add-questions-grouping"
                  variant="outlined"
                  size="small"
                  color="secondary"
                  sx={{
                    width: 'fit-content',
                    minWidth: '100px',
                    lineHeight: 1.25,
                    fontWeight: 500,
                    textTransform: 'none',
                    minHeight: '28px',
                  }}
                  disabled={!hasAnySelections}
                  onClick={() => setIsGroupingDrawerOpen(true)}
                  startIcon={<WorkspacesIcon />}
                >
                  Apply Grouping
                </Button>
                <Button
                  key="add-questions-deselect"
                  variant="outlined"
                  size="small"
                  color="secondary"
                  sx={{
                    width: 'fit-content',
                    minWidth: '100px',
                    lineHeight: 1.25,
                    fontWeight: 500,
                    textTransform: 'none',
                    minHeight: '28px',
                  }}
                  disabled={!hasAnySelections}
                  onClick={(e) => {
                    questionStore.clearSelections();
                  }}
                  startIcon={<DeselectIcon />}
                >
                  Deselect Items
                </Button>
                <IconButton
                  size="small"
                  color="error"
                  disabled={!hasAnySelections}
                  onClick={(e) => {
                    questionStore.deleteAllSelected();
                  }}
                >
                  <DeleteForeverIcon />
                </IconButton>
              </Stack>
            </>
          )}
          <Button
            key="add-questions-submit"
            color="primary"
            onClick={handleCreateQuestions}
            disabled={isPastedQuestionsEmpty || submitting}
            sx={{ width: 'fit-content', minWidth: '330px' }}
          >
            {submitting ? 'Submitting...' : 'Generate Answers'}
          </Button>
        </Stack>
      </Stack>

      <SelectGroupingsDrawer
        contentId={Number(id)}
        isOpen={isGroupingDrawerOpen}
        onClose={() => setIsGroupingDrawerOpen(false)}
        onSubmit={handleBulkSelections}
      />
    </PageContainer>
  );
};
