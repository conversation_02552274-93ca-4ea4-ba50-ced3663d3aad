import { Stack, TextareaAutosize, styled } from '@mui/material';

export const StyledTextArea = styled(TextareaAutosize)(({ theme }) => ({
  fontFamily: 'Inter',
  fontSize: '0.875rem',
  lineHeight: 1.5,
  fontWeight: '400',
  outline: 'none',
  border: 'none',
  backgroundColor: 'transparent',
  resize: 'none',
  '::placeholder': {
    color: theme.palette.grey[400],
  },
}));

export const SelectionBar = styled(Stack)(({ theme }) => ({
  // a narrow, wide bar that says "Selected (0)                           All [ ]"
  // the selection is the number of questions that have been selected
  // the "All" checkbox is used to select all questions
  backgroundColor: theme.palette.grey[200],
  borderBottomWidth: '1px',
  borderBottomStyle: 'solid',
  borderBottomColor: theme.palette.grey[300],
  justifyContent: 'space-between',
  flexDirection: 'column',
  width: '100%',
  padding: '0 1rem',
}));
