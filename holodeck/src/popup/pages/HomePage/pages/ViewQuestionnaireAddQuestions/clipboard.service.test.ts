import { getAnyQuestionsOnClipboard } from './clipboard.service';

describe('clipboard.service', () => {
  const mockClipboard = {
    contents: undefined as string | undefined,
  };
  let originalExecCommand: typeof document.execCommand;

  beforeEach(() => {
    // clear the mock
    mockClipboard.contents = undefined;

    // replace the original function with a mock. save the original for later.
    originalExecCommand = global.document.execCommand;
    global.document.execCommand = () => {
      const isFocusedElementInput =
        document.activeElement?.tagName === 'TEXTAREA';
      if (mockClipboard.contents === undefined || !isFocusedElementInput) {
        // either the clipboard is empty or the focused element is not an input
        return true;
      }

      (document.activeElement as HTMLInputElement).value =
        mockClipboard.contents;
      return true;
    };
  });

  afterEach(() => {
    // restore the original function
    global.document.execCommand = originalExecCommand;
  });

  it('returns Clipboard is Empty failure object if the clipboard is empty', async () => {
    mockClipboard.contents = undefined;
    const result = await getAnyQuestionsOnClipboard();
    expect(result).toMatchInlineSnapshot(`
            {
              "errorCode": "clipboard-empty",
              "success": false,
            }
        `);
  });

  it('returns Clipboard is Empty failure object if the clipboard has empty string', async () => {
    mockClipboard.contents = '     ';
    const result = await getAnyQuestionsOnClipboard();
    expect(result).toMatchInlineSnapshot(`
            {
              "errorCode": "clipboard-empty",
              "success": false,
            }
        `);
  });

  it('can get content from clipboard.', async () => {
    mockClipboard.contents = 'Hello, World!';
    const result = await getAnyQuestionsOnClipboard();
    expect(result).toMatchInlineSnapshot(`
            {
              "questions": [
                "Hello, World!",
              ],
              "success": true,
            }
        `);
  });

  it('trims the content on the clipboard.', async () => {
    mockClipboard.contents = '  Hello, World!  ';
    const result = await getAnyQuestionsOnClipboard();
    expect(result).toMatchInlineSnapshot(`
            {
              "questions": [
                "Hello, World!",
              ],
              "success": true,
            }
        `);
  });

  it('removes newlines.', async () => {
    mockClipboard.contents = '"Hello, \nWorld!\n "';
    const result = await getAnyQuestionsOnClipboard();
    expect(result.success && result.questions.length).toBe(1);
    expect(result.success && result.questions[0]).toBe('Hello, \nWorld!');
  });

  it('removes internal quotes.', async () => {
    mockClipboard.contents = `"Hello, world"

Lorem ipsum
"The quick brown fox

Jumped over the lazy dog."
"Space, the final frontier.

These are the voyages of the Starship, Enterprise.

To boldly go where no one has gone before."
"Hipster ipsum!

Sustainable blue bottle gastropub, irony mustache four dollar toast tousled vexillologist XOXO waistcoat heirloom enamel pin unicorn pug tumblr. Aesthetic marxism messenger bag jawn, enamel pin man braid godard ascot. Biodiesel enamel pin quinoa Brooklyn adaptogen grailed bruh. Tonx godard tacos, franzen paleo lumbersexual forage shaman typewriter marfa keytar. Keffiyeh blackbird spyplane tousled, migas marxism bruh occupy vinyl. Thundercats brunch pitchfork forage before they sold out shoreditch knausgaard roof party pop-up slow-carb.

""Oh no, it's a quote in a paragraph!"""`;
    const result = await getAnyQuestionsOnClipboard();
    expect(result.success && result.questions[0]).toBe('Hello, world');
    expect(result.success && result.questions[1]).toBe('Lorem ipsum');
    expect(result.success && result.questions[2]).toBe(
      'The quick brown fox\n\nJumped over the lazy dog.',
    );
    expect(result.success && result.questions[3]).toBe(
      'Space, the final frontier.\n\nThese are the voyages of the Starship, Enterprise.\n\nTo boldly go where no one has gone before.',
    );
    expect(result.success && result.questions[4]).toBe(
      'Hipster ipsum!\n\nSustainable blue bottle gastropub, irony mustache four dollar toast tousled vexillologist XOXO waistcoat heirloom enamel pin unicorn pug tumblr. Aesthetic marxism messenger bag jawn, enamel pin man braid godard ascot. Biodiesel enamel pin quinoa Brooklyn adaptogen grailed bruh. Tonx godard tacos, franzen paleo lumbersexual forage shaman typewriter marfa keytar. Keffiyeh blackbird spyplane tousled, migas marxism bruh occupy vinyl. Thundercats brunch pitchfork forage before they sold out shoreditch knausgaard roof party pop-up slow-carb.\n\n"Oh no, it\'s a quote in a paragraph!"',
    );
    expect(result.success && result.questions.length).toBe(5);
  });

  it('returns an error if something unexpected happens', async () => {
    global.document.execCommand = () => {
      throw new Error('Something unexpected happened');
    };
    const result = await getAnyQuestionsOnClipboard();
    expect(result).toMatchInlineSnapshot(`
            {
              "errorCode": "unexpected-error",
              "success": false,
            }
        `);
  });
});
