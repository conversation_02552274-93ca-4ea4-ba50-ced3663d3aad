import { Button, Stack, TextareaAutosize } from '@mui/material';
import React, { FC, SyntheticEvent, useState } from 'react';

type AddQuestionFormCardProps = {
  onSubmit: (val: string) => void;
  onCancel: (e?: SyntheticEvent) => void;
  initialValue?: string;
};
export const AddQuestionFormCard: FC<AddQuestionFormCardProps> = ({
  onSubmit,
  onCancel,
  initialValue = '',
}) => {
  const [value, setValue] = useState<string>(initialValue);

  const handleSubmit = (e: SyntheticEvent) => {
    e.stopPropagation();
    e.preventDefault();
    if (!value) return;
    onSubmit(value);
  };

  const handleCancel = (e: SyntheticEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onCancel(e);
  };

  return (
    <Stack>
      <TextareaAutosize
        maxRows={4}
        aria-label="Enter your question here"
        placeholder="Enter your question here"
        onChange={(e) => setValue(e.target.value)}
        value={value}
      />
      <Button disabled={!value} onClick={(e) => handleSubmit(e)}>
        Submit
      </Button>
      <Button onClick={(e) => handleCancel(e)}>Cancel</Button>
    </Stack>
  );
};
