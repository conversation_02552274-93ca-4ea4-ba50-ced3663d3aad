export type PastedQuestionsResultSuccess = {
  success: true;
  questions: string[];
};

type PastedQuestionsResultFailure = {
  success: false;
  errorCode: 'clipboard-empty' | 'unexpected-error';
  clipboardContents?: string;
};

type PastedQuestionsResult =
  | PastedQuestionsResultSuccess
  | PastedQuestionsResultFailure;

// // This is an adapted version of the old code as found in Popup.bak.tsx
// // Keeping this here for reference in case we want to go back to it.
// function splitCsvString(clipboard: string): string[] {
//     if (!clipboard.trim()) {
//         return [];
//     }

//     // Quotes within the cell...
//     const clipboardText = clipboard.trim().replace(/["]{2}/g, '~~!~~');

//     // Cells may have multiple paragraphs, which should be enclosed by quotes
//     // (at least that's what Google Sheets does)
//     const clipboardQuestions = clipboardText.match(/"[^"]*"|.+/g) || [];

//     if (!clipboardQuestions.length) {
//         return [];
//     }

//     return (clipboardQuestions
//         .map((q) => {
//             if (q.startsWith('"') && q.endsWith('"')) {
//                 return q.substring(1, q.length - 1).trim().replace(/~~!~~/g, '"')
//             } else {
//                 return q.trim().replace(/~~!~~/g, '"')
//             }
//         }))
// }

function splitCsvString(input: string): string[] {
  if (!input) {
    return [];
  }

  const hasQuotesInOneLine =
    input.indexOf('"') > -1 &&
    input.indexOf('""') == -1 &&
    input.indexOf('\n') == -1;
  const hasInnerQuotesAndMultiline =
    (input.indexOf('"') > -1 && input.indexOf('""') > -1) ||
    (input.indexOf('"') > -1 &&
      input.indexOf('""') == -1 &&
      input.indexOf('\n') > -1);

  if (hasQuotesInOneLine) {
    input = input.replace(/["]/g, '~~!~~');
  } else {
    input = input.replace(/["]{2}/g, '~~!~~');
  }

  // Another special case to handle. See the test case spreadsheet
  if (hasInnerQuotesAndMultiline) {
    const innerQuotes = /[^\n]("[^"\n]*")[^\n]/g;
    let matchInnerQuotes;
    while ((matchInnerQuotes = innerQuotes.exec(input)) !== null) {
      // For each, just replace the "(text)" with `(text)`.
      // Then we'll swap the ` for " at the end. I don't think anyone will mind.
      const matchedInnerQuote = matchInnerQuotes[1];

      // A little inefficient, but, I assume this won't happen much
      input =
        input.substring(0, matchInnerQuotes.index) +
        ' `' +
        matchedInnerQuote.replace(/["]/g, '') +
        '`' +
        input.substring(
          matchInnerQuotes.index + matchInnerQuotes[1].length + 1,
        );
    }
  }

  const regex = /"(?:[^"\\]|\\.)*"|[^"\n]+/g;
  const results = [];
  let match;

  while ((match = regex.exec(input)) !== null) {
    const value = match[0];
    if (value.startsWith('"') && value.endsWith('"')) {
      results.push(
        value
          .substring(1, value.length - 1)
          .trim()
          .replace(/~~!~~/g, '"')
          .replace(/[`]/g, '"'),
      ); // Remove surrounding quotes and unescape inner quotes
    } else {
      results.push(value.trim().replace(/~~!~~/g, '"').replace(/[`]/g, '"'));
    }
  }

  return results
    .map((str) => str.trim())
    .filter((datum) => datum !== undefined && datum !== null && datum !== '');
}

// We will want to move to using clipbarod API instead of execCommand soon, since execCommand is deprecated
// I've moved my old WIP code with clipboard API into the following gist.
// This code is only semi working, but it's a good start: https://gist.github.com/monarch-tribble/c60e328604264d67a0e8f2bd19adf2c0
export const getAnyQuestionsOnClipboard =
  async (): Promise<PastedQuestionsResult> => {
    let clipboardContents;
    try {
      // create a temporary input element that is invisible
      const input = document.createElement('textarea');
      input.style.height = '1px';
      input.style.width = '1px';
      input.style.border = 'none';
      document.body.appendChild(input);
      // focus on the input element so that we can paste into it
      input.focus();
      // execute the paste command
      document.execCommand('paste');
      // get the value of the input element
      clipboardContents = input.value;
      // remove the input element from the DOM
      input.remove();

      const data = splitCsvString(clipboardContents);

      if (data.length === 0) {
        return {
          success: false,
          errorCode: 'clipboard-empty',
        };
      }

      return {
        success: true,
        questions: data
          .filter(
            (datum) =>
              datum !== undefined &&
              datum !== null &&
              datum !== '' &&
              datum.trim() !== '',
          )
          .map((str) => str.trim()),
      };
    } catch (err) {
      console.error('TRIBBLE: Failed to read clipboard content:', err);
      return {
        success: false,
        errorCode: 'unexpected-error',
        clipboardContents,
      };
    }
  };
