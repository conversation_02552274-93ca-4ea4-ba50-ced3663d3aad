import AddIcon from '@mui/icons-material/Add';
import { Box, Fab, Stack } from '@mui/material';
import React, { FC, SyntheticEvent } from 'react';
import { PrimaryTooltip } from '../../../../../atomic-design/atoms/StyledTooltips';

export type ActionDividerProps = {
  onClick: (e: SyntheticEvent) => void;
};

export const ActionDivider: FC<ActionDividerProps> = ({ onClick }) => {
  return (
    <Stack
      position={'absolute'}
      sx={{
        backgroundColor: 'grey.300',
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
        height: '1px',
        '&:hover': {
          backgroundColor: 'primary.main',
          '.addQuestionButtonCollapsed': {
            borderWidth: '2px',
            borderColor: 'primary.main',
            color: 'primary.main',
          },
        },
      }}
    >
      <Box
        sx={(theme) => ({
          fontFamily: theme.typography.fontFamily,
          width: '100%',
          textAlign: 'center',
          fontSize: '1rem',
          cursor: 'pointer',
        })}
        onClick={(e) => {
          onClick(e);
        }}
      >
        <PrimaryTooltip title={'Add a question here'} enterDelay={250}>
          <Fab
            color="inherit"
            size="small"
            disableRipple
            sx={{
              width: 'fit',
              boxShadow: 'none',
              color: 'grey.400',
              borderColor: 'grey.300',
              transition: 'none',
              '&:active': {
                boxShadow: 'none',
              },
            }}
            className="addQuestionButtonCollapsed"
          >
            <AddIcon />
          </Fab>
        </PrimaryTooltip>
      </Box>
    </Stack>
  );
};
