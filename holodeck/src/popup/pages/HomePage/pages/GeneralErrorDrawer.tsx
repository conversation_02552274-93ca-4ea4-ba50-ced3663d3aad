import React, { useState } from 'react';

import { <PERSON><PERSON>, <PERSON>ack, Typography } from '@mui/material';

import { Row } from '../../../../components/copied';
import { post } from '../../../api/apiUtils';
import { MoreDrawer } from './MoreDrawer';

import { Rfp } from '../../../../components/EndToEndRfp';
import {
  QuestionnaireDto,
  deleteQuestionnaire,
} from '../../../api/questionnaires.api';

type GeneralErrorDrawerProps = {
  open: boolean;
  close: () => void;
  questionnaire?: QuestionnaireDto;
  rfp?: Rfp;
  error: string;
};
export const GeneralErrorDrawer: React.FC<GeneralErrorDrawerProps> = ({
  open,
  close,
  questionnaire,
  rfp,
  error,
}) => {
  const [confirmDelete, setConfirmDelete] = useState(false);

  const onDelete = async () => {
    if (rfp) {
      await post('/delete-rfp', {
        contentId: rfp.id,
      });
    } else {
      await deleteQuestionnaire(Number(questionnaire.content_id));
    }

    window.location.reload();
  };

  const label =
    (rfp
      ? rfp.details.project_name ?? rfp.details.customer_name
      : questionnaire.label) || '';
  const customerName = rfp
    ? rfp.details.customer_name ?? ''
    : questionnaire.details.customer_name ?? '';

  return (
    <MoreDrawer
      title={`${rfp ? 'RFP' : 'Questionnaire'}: ${label || customerName}`}
      open={open}
      close={close}
    >
      {confirmDelete ? (
        <Typography
          variant="body1"
          sx={{ fontWeight: '600', textTransform: 'uppercase', mt: 3 }}
          color="error"
        >
          Are you sure you want to delete this {rfp ? 'RFP' : 'questionnaire'}?
        </Typography>
      ) : (
        <>
          <Typography variant="body1" sx={{ mt: 3 }}>
            Looks like something may have gone wrong...
          </Typography>
          <Typography
            variant="body1"
            color="error"
            sx={{ mt: 1, fontWeight: '600' }}
          >
            {error}
          </Typography>
        </>
      )}
      <Row gap={'1rem'} sx={{ mt: 4, justifyContent: 'center' }}>
        {confirmDelete ? (
          <>
            <Button
              onClick={() => setConfirmDelete(false)}
              color="primary"
              variant="outlined"
            >
              No, Cancel
            </Button>
            <Button onClick={onDelete} color="error" variant="contained">
              Yes, Delete
            </Button>
          </>
        ) : (
          <Stack direction="row" justifyContent="center" sx={{ width: '100%' }}>
            <Button
              onClick={() => setConfirmDelete(true)}
              color="error"
              variant="outlined"
            >
              Delete {rfp ? 'RFP' : 'questionnaire'}
            </Button>
          </Stack>
        )}
      </Row>
    </MoreDrawer>
  );
};
