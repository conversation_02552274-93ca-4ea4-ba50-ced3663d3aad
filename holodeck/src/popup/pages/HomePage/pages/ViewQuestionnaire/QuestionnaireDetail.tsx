import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import {
  Avatar,
  AvatarGroup,
  Badge,
  Box,
  Chip,
  CircularProgress,
  FormControl,
  InputLabel,
  ListItemText,
  MenuItem,
  Stack,
  Typography,
  useTheme,
} from '@mui/material';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import React, { FC, useEffect, useState } from 'react';

import { MenuDropdown } from '../../../../../atomic-design/atoms/MenuDropdown';
import {
  PrimaryTooltip,
  SecondaryTooltip,
} from '../../../../../atomic-design/atoms/StyledTooltips';
import { Title } from '../../../../../atomic-design/atoms/Title';
import { TribbleAvatar } from '../../../../../atomic-design/atoms/TribbleAvatar';
import { StatusChip } from '../../../../../atomic-design/molecules/QuestionnaireCard/StatusChip';
import { MetadataFilterTypeDto } from '../../../../api/metadata.api';
import { GroupingChip } from './GroupingChip';
import { MetadataChip } from './MetadataChip';

import { OpportunityPicker } from '../../../../../atomic-design/molecules/OpportunityPicker';
import { useContentGroupings } from '../../../../api/groupings.swr';
import { QuestionDto } from '../../../../api/question.api';
import {
  PatchQuestionnaireDto,
  RfpStatusValue,
} from '../../../../api/questionnaires.api';
import {
  SalesforceOpportunityDto,
  getSalesforceOpportunity,
} from '../../../../api/salesforce.api';
import { useSalesforceOpportunities } from '../../../../api/salesforce.swr';
import { UserDto } from '../../../../api/user.api';
import { useUsers } from '../../../../api/user.swr';
import { useRootStore } from '../../../../state/useRootStore';

import { getSortedAssigneeIdsFromQuestions } from './util';

interface QuestionnaireDetailProps {
  questionnaire: PatchQuestionnaireDto;
  questions: QuestionDto[];
  mode: 'edit' | 'view';
  onSave: (questionDetail?: PatchQuestionnaireDto) => void;
  onUpdate: (questionDetail: PatchQuestionnaireDto) => void;
  allMDF: MetadataFilterTypeDto[];
  resetDetails: number;
  openGroupingsDrawer: () => void;
}

export const QuestionnaireDetail: FC<QuestionnaireDetailProps> = ({
  questionnaire,
  questions,
  mode,
  onSave,
  onUpdate,
  allMDF,
  resetDetails,
  openGroupingsDrawer,
}) => {
  const users = useUsers();
  const theme = useTheme();
  const rootStore = useRootStore();

  // I hate that some things (content_id) are passed back as strings...
  const contentGroupings = useContentGroupings(
    Number(questionnaire.content_id),
  );

  const [loadingUsers, setLoadingUsers] = useState(true);
  const [availableUsers, setAvailableUsers] = useState<UserDto[]>([]);
  const [origOwnerId, setOrigOwnerId] = useState(''); // convert to Number before saving
  const [newOwnerId, setNewOwnerId] = useState('');
  const [origCustomerName, setOrigCustomerName] = useState('');

  const [origOpportunityId, setOrigOpportunityId] = useState('');
  const salesforceOpportunities = useSalesforceOpportunities(
    rootStore.connectedToSalesforce(),
  );

  const [allOpportunities, setAllOpportunities] = useState<any[]>([]);
  const [currentOpportunity, setCurrentOpportunity] =
    useState<SalesforceOpportunityDto>();
  const [newOpportunity, setNewOpportunity] =
    useState<SalesforceOpportunityDto>();
  const [loadingOpportunity, setLoadingOpportunity] = useState(true);

  const hasOpportunityId =
    questionnaire?.opportunity_id &&
    questionnaire?.opportunity_id !== 'undefined';

  useEffect(() => {
    const owner = questionnaire.assignees?.find(
      (assignee) => assignee.is_owner,
    );
    if (owner) {
      setOrigOwnerId(String(owner.assignee_id));
      setNewOwnerId(String(owner.assignee_id));
    }
    setOrigCustomerName(questionnaire?.details?.customer_name);
  }, []);

  useEffect(() => {
    if (users.data) {
      setAvailableUsers(users.data);
      setLoadingUsers(false);
    }
  }, [users.data]);

  useEffect(() => {
    if (
      salesforceOpportunities.data &&
      salesforceOpportunities.data.records?.length > 0
    ) {
      setAllOpportunities(salesforceOpportunities.data.records);
    }
  }, [salesforceOpportunities.data]);

  useEffect(() => {
    if (hasOpportunityId) {
      loadSalesforceOpportunity();
    } else {
      setCurrentOpportunity(undefined);
      setNewOpportunity(undefined);
    }
  }, [questionnaire?.opportunity_id]);

  useEffect(() => {
    // These need to be manually reset
    if (resetDetails > 0) {
      setNewOwnerId(origOwnerId);
      setNewOpportunity(currentOpportunity);
      // handleCustomerUpdate(origCustomerName);
    }
  }, [resetDetails]);

  const loadSalesforceOpportunity = async () => {
    if (rootStore.connectedToSalesforce() && hasOpportunityId) {
      const oppty = await getSalesforceOpportunity(
        questionnaire?.opportunity_id,
      );
      setOrigOpportunityId(questionnaire?.opportunity_id);
      setCurrentOpportunity(oppty);
      setNewOpportunity(oppty);
    }
    setLoadingOpportunity(false);
  };

  const isEditMode = mode == 'edit';
  const totalQuestions = questions.length;
  const reviewed = questions.filter((q) => q.state == 'accepted').length;

  const assignees = getSortedAssigneeIdsFromQuestions(questions);

  const metadata_filters = questionnaire.metadata_filter
    ? questionnaire.metadata_filter.multiple_types || [
        questionnaire.metadata_filter,
      ]
    : [];
  const hasMetadata =
    metadata_filters.length > 0 &&
    !(
      metadata_filters.length == 1 &&
      Object.keys(metadata_filters[0]).length == 0
    );

  const hasGroupings =
    contentGroupings.data && contentGroupings.data.length > 0;

  const isActive = ['in_review'].includes(questionnaire.status);

  // const handleLabelUpdate = (newLabel: string) => {
  //   const newDetail = { ...questionnaire };
  //   newDetail.label = newLabel;
  //   onUpdate(newDetail);

  //   return newDetail;
  // };

  // const handleDescriptionUpdate = (newDescription: string) => {
  //   const newDetail = { ...questionnaire };
  //   newDetail.projectDescription = newDescription;
  //   newDetail.details = {
  //     ...newDetail.details,
  //     project_description: newDescription,
  //   };
  //   onUpdate(newDetail);

  //   return newDetail;
  // };

  // const handleCustomerUpdate = (newCustomer: string) => {
  //   const newDetail = { ...questionnaire };
  //   if (!newDetail.details) newDetail.details = {};

  //   newDetail.details.customer_name = newCustomer;
  //   onUpdate(newDetail);

  //   return newDetail;
  // };

  // const handleDueDateUpdate = (newDueDate: Date | null) => {
  //   const newDetail = { ...questionnaire };
  //   newDetail.due_date = newDueDate;
  //   onUpdate(newDetail);

  //   return newDetail;
  // };

  const handleStatusUpdate = (newStatus: RfpStatusValue) => {
    const newDetail = { ...questionnaire, status: newStatus };
    onUpdate(newDetail);

    return newDetail;
  };

  const handleOwnerUpdate = (updatedOwnerId: string) => {
    if (updatedOwnerId == origOwnerId) {
      return;
    }

    const newOwner = users.data?.find(
      (user) => user.id == Number(updatedOwnerId),
    );
    const newDetail = { ...questionnaire, owner: newOwner };

    let newOwnerFound = false;
    newDetail.assignees = (questionnaire.assignees || [])
      .filter((user) =>
        assignees.some((assigneeId) => assigneeId == user.assignee_id),
      )
      .map((user) => {
        if (user.assignee_id == Number(origOwnerId)) {
          user.is_owner = false;
        } else if (user.assignee_id == Number(updatedOwnerId)) {
          user.is_owner = true;
          newOwnerFound = true;
        }
        return user;
      });

    if (!newOwnerFound) {
      newDetail.assignees.push({
        assignee_id: Number(updatedOwnerId),
        is_owner: true,
      });
    }

    onUpdate(newDetail);
    setNewOwnerId(updatedOwnerId);

    return newDetail;
  };

  const handleOpportunityUpdate = (
    updatedOpportunity: SalesforceOpportunityDto,
  ) => {
    if (updatedOpportunity?.Id === newOpportunity?.Id) {
      return;
    }
    const newDetail = {
      ...questionnaire,
      status: questionnaire.status,
      opportunity_id: updatedOpportunity?.Id,
    };

    onUpdate(newDetail);
    setNewOpportunity(updatedOpportunity);

    return newDetail;
  };

  const title = (
    // isEditMode ? (
    //   <TextField
    //     fullWidth={true}
    //     placeholder="Project Title"
    //     label="Project Title"
    //     value={questionnaire.label}
    //     onChange={(e) => {
    //       handleLabelUpdate(e.target.value);
    //     }}
    //     sx={{ mb: '12px' }}
    //   />
    // ) : (
    <Title title={questionnaire.label}></Title>
  );
  // );

  const description = (
    // isEditMode ? (
    //   <TextField
    //     fullWidth={true}
    //     multiline={true}
    //     maxRows={2}
    //     placeholder="Project Description"
    //     label="Project Description"
    //     value={questionnaire.projectDescription ?? ''}
    //     onChange={(e) => {
    //       handleDescriptionUpdate(e.target.value);
    //     }}
    //     sx={{ mb: '12px' }}
    //   />
    // ) : questionnaire.projectDescription ? (
    <Typography
      variant="subtitle2"
      sx={{
        color: theme.palette.grey[600],
        display: '-webkit-box',
        WebkitLineClamp: 2,
        WebkitBoxOrient: 'vertical',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        fontWeight: 400,
        mb: '12px',
      }}
    >
      {questionnaire.projectDescription}
    </Typography>
  );
  // ) : null;

  function renderCustomerName() {
    let customerName = questionnaire.details.customer_name || '';
    // return isEditMode ? (
    //   <TextField
    //     fullWidth={true}
    //     placeholder="Customer Name"
    //     label="Customer Name"
    //     value={customerName}
    //     onChange={(e) => {
    //       handleCustomerUpdate(e.target.value);
    //     }}
    //   />
    // ) : (
    return (
      <Typography variant="body2" sx={{ textTransform: 'uppercase' }}>
        {customerName}
      </Typography>
    );
    // );
  }

  const OwnerEdit = () => {
    return (
      <FormControl fullWidth>
        <InputLabel id="owner-select-small-label">Owner</InputLabel>
        <Select
          labelId="owner-select-small-label"
          label="Owner"
          value={String(newOwnerId)}
          onChange={(e: SelectChangeEvent) => handleOwnerUpdate(e.target.value)}
          renderValue={(selected) => {
            const selectedUser = availableUsers.find(
              (user) => String(user.id) == selected,
            );
            return selectedUser ? selectedUser.name : '';
          }}
          MenuProps={{
            sx: {
              maxHeight: '400px',
            },
            anchorOrigin: {
              vertical: 'bottom',
              horizontal: 'left',
            },
          }}
        >
          {availableUsers
            .sort((a, b) =>
              a.name.toLowerCase() < b.name.toLowerCase() ? -1 : 1,
            )
            .map((user) => (
              <MenuItem value={user.id} key={user.id}>
                <div style={{ marginRight: '1rem' }}>
                  {user.picture && user.picture != '' && (
                    <Avatar src={user.picture} sx={{ height: 24, width: 24 }} />
                  )}
                  {(!user.picture || user.picture == '') && (
                    <TribbleAvatar
                      username={user.name}
                      height="24px"
                      width="24px"
                    />
                  )}
                </div>
                <ListItemText
                  primary={user.name}
                  sx={{ '& .MuiTypography-root': { fontSize: '0.85rem' } }}
                />
              </MenuItem>
            ))}
        </Select>
      </FormControl>
    );
  };

  const OpportunityEdit = () => {
    return (
      <div
        key={`oppty_picker_${currentOpportunity?.Id}`}
        style={{ width: '100%' }}
      >
        <OpportunityPicker
          opportunities={allOpportunities}
          selected={currentOpportunity}
          onSelect={(value) => handleOpportunityUpdate(value)}
        />
      </div>
    );
  };

  const isTerse = questionnaire.details?.terse ?? false;
  const verbosityWords = questionnaire.details?.verbosity_words ?? 100;
  const targetLanguage = questionnaire.details?.target_language;

  let tooltipOpportunityAmount,
    tooltipOpportunityLineItems =
      '• No products are associated with this opportunity.',
    opportunityLink,
    opportunityLineItems;
  if (currentOpportunity) {
    tooltipOpportunityAmount = `• It has a value of $${Number(
      currentOpportunity.Amount,
    ).toLocaleString()}`;
    opportunityLink =
      rootStore.salesforceUser.instance_url + '/' + currentOpportunity.Id;

    // This was a special field we added in the backend query
    if (
      currentOpportunity['__LineItems'] &&
      currentOpportunity['__LineItems'].length > 0
    ) {
      opportunityLineItems = currentOpportunity['__LineItems'].map(
        (lineItem) => lineItem.Name,
      );

      tooltipOpportunityLineItems = `• It is for the following product${
        opportunityLineItems.length == 1 ? '' : 's'
      }:\n- ${opportunityLineItems?.join('\n- ')}`;
    }
  }

  return (
    <Stack
      direction={'column'}
      justifyContent={'space-between'}
      alignItems={'flex-start'}
      gap={'1rem'}
    >
      <Stack
        direction={'row'}
        justifyContent={'space-between'}
        alignItems={'center'}
        width={'100%'}
        sx={{
          mt: mode == 'view' ? 0 : '0.5rem',
          mb: mode == 'view' ? 0 : '1rem',
        }}
      >
        {isActive && mode == 'view' ? (
          <MenuDropdown
            trigger={
              <StatusChip
                mode={mode}
                onChange={handleStatusUpdate}
                jobStatus={questionnaire.status}
                showToggle={true}
              />
            }
            disableTrigger={!isActive}
          >
            {(handleClose) => {
              const onClose = () => {
                handleClose();
              };

              const menuItems = [
                <MenuItem
                  disabled={true}
                  key="status_empty"
                  sx={{
                    minHeight: '24px',
                    p: '4px 12px',
                    fontSize: '0.75rem',
                    textTransform: 'uppercase',
                    color: theme.palette.grey[900],
                  }}
                >
                  Change Status
                </MenuItem>,
                <MenuItem
                  onClick={async () => {
                    const updatedQuestionnaire = handleStatusUpdate('finished');
                    await onSave(updatedQuestionnaire);
                    onClose();
                  }}
                  autoFocus={false}
                  key={`status_finished`}
                  sx={{
                    minHeight: '24px',
                    p: '4px 12px',
                    fontSize: '0.85rem',
                    '&:hover': { fontWeight: 500 },
                  }}
                >
                  Mark as Finished
                </MenuItem>,
              ];

              return menuItems;
            }}
          </MenuDropdown>
        ) : (
          <StatusChip
            mode={mode}
            onChange={handleStatusUpdate}
            jobStatus={questionnaire.status}
          />
        )}
        {mode == 'view' &&
          questionnaireProgress(reviewed, totalQuestions, theme)}
      </Stack>

      <Stack
        direction={'column'}
        justifyContent={'space-between'}
        alignItems={'flex-start'}
        gap="4px"
        sx={{ width: '100%' }}
      >
        <Box sx={{ width: '100%' }}>{title}</Box>
        {questionnaire.projectDescription && (
          <Box sx={{ width: '100%' }}>{description}</Box>
        )}
        <Box sx={{ width: '100%' }}>{renderCustomerName()}</Box>
      </Stack>

      {!isEditMode &&
        (hasMetadata || hasGroupings || isTerse || verbosityWords != 100) && (
          <Stack
            direction="row"
            gap="0.5rem"
            sx={{ flexWrap: 'wrap', maxWidth: '100%' }}
          >
            {isTerse && (
              <SecondaryTooltip title="Terse Answers: answer with 'Yes' or 'No' only, where applicable.">
                <Chip
                  label="Terse"
                  size="small"
                  variant="outlined"
                  color="secondary"
                  sx={{ color: theme.palette.secondary.main }}
                />
              </SecondaryTooltip>
            )}
            {verbosityWords != 100 && (
              <SecondaryTooltip
                title={`Tribble will aim to provide answers with about ${verbosityWords} words.`}
              >
                <Chip
                  label={`Verbose: ${verbosityWords}`}
                  size="small"
                  variant="outlined"
                  color="secondary"
                  sx={{ color: theme.palette.secondary.main }}
                />
              </SecondaryTooltip>
            )}
            {hasMetadata && (
              <MetadataChip
                allMDF={allMDF}
                metadata_filters={metadata_filters}
              />
            )}
            {hasGroupings && (
              <GroupingChip
                groupings={contentGroupings.data}
                showGroupingsDrawer={openGroupingsDrawer}
              />
            )}
          </Stack>
        )}

      {!isEditMode && currentOpportunity && (
        <Stack
          direction="row"
          gap="0.5rem"
          sx={{ flexWrap: 'wrap', maxWidth: '100%' }}
        >
          <SecondaryTooltip
            title={`This project is mapped to the [${currentOpportunity.Name}] opportunity.\n\n${tooltipOpportunityAmount}\n${tooltipOpportunityLineItems}\n\nClick to view the record in Salesforce.`}
          >
            <Chip
              label={`Opportunity: ${currentOpportunity.Name}`}
              size="small"
              variant="outlined"
              color="secondary"
              sx={{ color: theme.palette.secondary.main, pointer: 'cursor' }}
              onClick={() => window.open(opportunityLink, '_blank')}
            />
          </SecondaryTooltip>
        </Stack>
      )}
      {/* Corner case where there's an oppty attached, but user is not connected to SFDC */}
      {!isEditMode &&
        !currentOpportunity &&
        hasOpportunityId &&
        !loadingOpportunity && (
          <Typography variant="body2" color="secondary">
            ⓘ This project is mapped to an opportunity but you're not connected
            to Salesforce (in order to view its details). Go to the home screen
            and Settings to connect.
          </Typography>
        )}

      {/* <Box sx={{ width: '100%' }}>
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
          spacing={2}
          width={'100%'}
        >
          <Stack direction="row" gap={2} sx={{ width: '100%' }}>
            <DueDate
              date={questionnaire.due_date}
              mode={mode}
              onChange={handleDueDateUpdate}
            />
            {targetLanguage && !isEditMode && (
              <SecondaryTooltip
                title={`Answer Target Language: ${targetLanguage}`}
              >
                <LanguageIcon color="secondary" fontSize="small" />
              </SecondaryTooltip>
            )}
          </Stack>
          {!isEditMode && !loadingUsers && (
            <AssigneeAvatarGroup
              assigneeIds={assignees}
              ownerId={Number(newOwnerId)}
              availableUsers={availableUsers}
            />
          )}
          {!isEditMode && loadingUsers && <CircularProgress size="small" />}
        </Stack>
      </Box> */}

      {isEditMode && !loadingUsers && OwnerEdit()}
      {isEditMode && allOpportunities?.length > 0 && OpportunityEdit()}
      {isEditMode && questionnaire.details?.custom_instructions && (
        <Stack
          gap={1}
          sx={{
            border: `1px solid ${theme.palette.primary.main}`,
            borderRadius: '8px',
            p: '12px',
            width: '100%',
          }}
        >
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            gap={1}
          >
            <Typography
              color="primary"
              variant="subtitle2"
              sx={{ fontWeight: '600', textTransform: 'uppercase' }}
            >
              Custom Instructions used when answering questions
            </Typography>
            <PrimaryTooltip title="These instructions were set when the project was created.">
              <InfoOutlinedIcon color="primary" fontSize="small" />
            </PrimaryTooltip>
          </Stack>
          <Typography variant="subtitle2" sx={{ fontWeight: '600' }}>
            {questionnaire.details?.custom_instructions}
          </Typography>
        </Stack>
      )}
    </Stack>
  );
};

//Group of assignee avatars.
//TODO add pictures
export const AssigneeAvatarGroup: FC<{
  assigneeIds: number[];
  ownerId: number;
  availableUsers: UserDto[];
}> = (props) => {
  const { assigneeIds, ownerId, availableUsers } = props;
  const theme = useTheme();

  if (!availableUsers.length) {
    return;
  }

  let avatars = [];
  let avatarOwner;

  // OK if ownerId exists in list -- it'll just overwrite avatarOwner var w same data
  [...assigneeIds, ownerId].forEach((assignee) => {
    const assigneeUser = availableUsers.find((user) => user.id == assignee);
    if (!assigneeUser) {
      return;
    }

    // Need to wrap TribbleAvatar in divs so the tooltip works
    const assigneeAvatar =
      assigneeUser.picture && assigneeUser.picture != '' ? (
        <Avatar src={assigneeUser.picture} sx={{ height: 24, width: 24 }} />
      ) : (
        <div>
          <TribbleAvatar
            username={assigneeUser.name}
            height="24px"
            width="24px"
          />
        </div>
      );

    if (assignee == ownerId) {
      avatarOwner = (
        <PrimaryTooltip
          title={`Project Owner: ${assigneeUser.name}`}
          enterDelay={250}
        >
          <Badge color="success" overlap="circular" variant="dot">
            {assigneeAvatar}
          </Badge>
        </PrimaryTooltip>
      );
    } else {
      avatars.push(
        <PrimaryTooltip
          title={`Assignee: ${assigneeUser.name}`}
          enterDelay={250}
        >
          {assigneeAvatar}
        </PrimaryTooltip>,
      );
    }
  });

  return (
    <Stack direction="row" gap="0.5rem" alignItems="center">
      <AvatarGroup
        max={4}
        spacing={6}
        renderSurplus={(surplus) => (
          <PrimaryTooltip
            title={
              surplus > 1
                ? `${surplus} more users are assignees`
                : `${surplus} more user is an assignee`
            }
            enterDelay={250}
          >
            <Typography
              sx={{
                fontFamily: 'Inter',
                fontWeight: '500',
                fontSize: surplus > 9 ? '0.7rem' : '0.8rem',
                color: theme.palette.grey[600],
              }}
            >
              +{surplus}
            </Typography>
          </PrimaryTooltip>
        )}
        sx={{ '& .MuiAvatar-root': { height: 24, width: 24 } }}
      >
        {...avatars}
      </AvatarGroup>
      {avatarOwner}
    </Stack>
  );
};

//Circular progress thingy for how many questions have been accepted.
const questionnaireProgress = (
  reviewed: number,
  totalQuestions: number,
  theme,
) => {
  const is_reviewed = totalQuestions > 0 && reviewed == totalQuestions;
  const value = Math.ceil((reviewed / totalQuestions) * 100);
  const progressColor = is_reviewed ? 'success' : 'primary';

  return (
    <Stack
      direction={'row'}
      justifyContent={'space-between'}
      alignItems={'center'}
      gap="0.5rem"
    >
      <Typography
        variant="body2"
        sx={{
          color: is_reviewed
            ? theme.palette.success.main
            : theme.palette.grey[500],
        }}
      >
        {is_reviewed
          ? 'Reviewed'
          : totalQuestions == 0
            ? 'Not Started'
            : 'Reviewed'}
      </Typography>
      {totalQuestions > 0 && (
        <Stack direction="row" alignItems="center" marginLeft="0px !important">
          <Typography
            variant="body2"
            sx={{
              mr: reviewed > 0 ? '8px' : 0,
              ml: 0,
              fontWeight: '600',
              color: is_reviewed
                ? theme.palette.success.main
                : theme.palette.common.black,
            }}
          >
            {reviewed} / {totalQuestions}
          </Typography>

          {reviewed > 0 && (
            <CircularProgress
              color={progressColor}
              size={'0.9rem'}
              variant="determinate"
              value={value}
            />
          )}
        </Stack>
      )}
    </Stack>
  );
};
