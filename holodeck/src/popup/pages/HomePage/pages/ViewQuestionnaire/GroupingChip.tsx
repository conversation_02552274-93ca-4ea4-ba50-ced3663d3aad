import { Chip, useTheme } from '@mui/material';
import React, { FC } from 'react';

import { GreyTooltip } from '../../../../../atomic-design/atoms/StyledTooltips';
import { ContentGroupingDto } from '../../../../api/groupings.api';

interface MDProps {
  groupings: ContentGroupingDto[];
  showGroupingsDrawer: () => void;
}

export const GroupingChip: FC<MDProps> = ({
  groupings,
  showGroupingsDrawer,
}) => {
  const theme = useTheme();

  const oneGrouping = groupings.length == 1;
  let tooltipText = `There ${oneGrouping ? 'is' : 'are'} ${groupings.length} ${
    oneGrouping ? 'grouping' : 'groupings'
  } in this questionnaire.\n\n`;

  let groupingsWithMDF = false;
  const sortedGroupings = [...groupings].sort((a, b) => {
    return a.grouping_name.localeCompare(b.grouping_name);
  });

  sortedGroupings.map((grouping, groupIdx) => {
    const hasMDF = Object.keys(grouping.grouping_metadata_filter).length > 0;
    if (hasMDF) {
      groupingsWithMDF = true;
    }

    if (groupIdx > 0) {
      tooltipText += '\n\n';
    }

    if (!oneGrouping) {
      tooltipText += `${groupIdx + 1}. `;
    }

    tooltipText += `${grouping.grouping_name}`;
  });

  if (groupingsWithMDF) {
    if (oneGrouping) {
      tooltipText += `\n\nThis grouping has its own context.`;
    } else {
      tooltipText += `\n\nSome groupings have their own context.`;
    }
    tooltipText += ' Click on the chip to see the grouping details.';
  }

  return (
    <GreyTooltip title={tooltipText}>
      <Chip
        variant="outlined"
        size="small"
        label={`Groupings: ${groupings.length}`}
        sx={{
          color: theme.palette.grey[600],
          fontSize: '0.8rem',
          cursor: groupingsWithMDF ? 'pointer' : 'default',
        }}
        onClick={() => {
          if (groupingsWithMDF) {
            showGroupingsDrawer();
          }
        }}
      />
    </GreyTooltip>
  );
};
