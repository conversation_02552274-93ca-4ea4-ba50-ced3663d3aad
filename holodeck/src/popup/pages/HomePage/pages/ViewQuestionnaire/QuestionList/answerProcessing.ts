import * as QA_UTILS from '../../../../../../popup/utils/qa';
import { QuestionDto } from '../../../../../api/question.api';

export const getGeneratedAnswer = (question: QuestionDto | any): string => {
  return question.output_original?.answer_versions !== undefined
    ? question.output_original?.answer_versions
        .reduce((agg, answer, idx) => {
          const isMultiTypeMDF = answer?.metadata_filter?.multiple_types;
          const mdfMultiTypeLabel = isMultiTypeMDF
            ? ' (' +
              answer?.metadata_filter?.multiple_types.reduce(
                (acc, mdf_type, idx) => {
                  return (
                    acc +
                    `${idx > 0 ? ', ' : ''}${mdf_type.type_name}: ${
                      mdf_type.value
                    }`
                  );
                },
                '',
              ) +
              ')'
            : '';
          const answerLabel = isMultiTypeMDF
            ? `Answer Version ${idx + 1}${mdfMultiTypeLabel}:\n`
            : `${answer?.metadata_filter.value}: `;

          return agg + `${answerLabel}${answer.answer || ''}\n\n`;
        }, '')
        .trim()
    : question.output_original!.answer || '';
};

const getAnswerConfidence = (question: QuestionDto | any): string => {
  const getConfidenceLabel = (
    confidenceScore: number,
    couldNotAnswer: boolean,
  ) => {
    return couldNotAnswer
      ? 'Could not answer'
      : [
          'Low Confidence',
          'Low Confidence',
          'Medium Confidence',
          'High Confidence',
        ][confidenceScore];
  };

  if (question.output_original?.answer_versions !== undefined) {
    return question.output_original?.answer_versions
      .reduce((agg, answer, idx) => {
        let confidenceScore = answer.confidence_score;
        confidenceScore =
          confidenceScore > 3 ? 3 : confidenceScore < 0 ? 0 : confidenceScore;
        const confidenceLabel = getConfidenceLabel(
          confidenceScore,
          answer.review_message?.includes(
            'Could not generate an answer to the question',
          ),
        );

        return agg + `Answer ${idx + 1}: ${confidenceLabel}\n\n`;
      }, '')
      .trim();
  } else {
    let confidenceScore = question.output_original.confidence_score;
    confidenceScore =
      confidenceScore > 3 ? 3 : confidenceScore < 0 ? 0 : confidenceScore;
    return getConfidenceLabel(
      confidenceScore,
      QA_UTILS.couldNotGenerateAnswer(question),
    );
  }
};

export const getQuestionsAndAnswers = (questions: QuestionDto[]) => {
  const hasGroupings = questions.some(
    (question) => question.grouping_id !== null,
  );

  return questions.map((question: any) => {
    const isRegenerated =
      (question.input?.regenerated_query_string !== undefined &&
        question.input?.regenerated_query_string != '' &&
        question.input?.regenerated_query_string !=
          question.input.query_string) ||
      (question.input?.regenerated_extra_context !== undefined &&
        question.input?.regenerated_extra_context != '');

    const originalQuestionText = QA_UTILS.questionText(question);

    const processedQuestion = isRegenerated
      ? question.input?.regenerated_query_string || originalQuestionText
      : originalQuestionText;

    const generatedAnswer = getGeneratedAnswer(question);

    const translatedText = question.output_original?.translation?.translation;

    const processedAnswer = QA_UTILS.shouldUseTranslatedAnswer(question)
      ? processAnswerCitations(translatedText, true)
      : QA_UTILS.isModifiedAnswer(question)
        ? question.output_modified!.text
        : processAnswerCitations(generatedAnswer, true);

    // Just replace all quotes with single quotation marks so CSV parsers can handle.
    let details = {
      question_number: String(question.index + 1),
    };

    if (hasGroupings) {
      details['grouping'] = question.grouping_name || '';
    }

    return {
      ...details,
      question: processedQuestion.replace(/"/g, "'"),
      answer: processedAnswer.replace(/"/g, "'"),
      confidence: QA_UTILS.isModifiedAnswer(question)
        ? 'Modified Answer'
        : getAnswerConfidence(question),
    };
  });
};

export const handleDownloadAsCSV = (
  questions: QuestionDto[],
  fileName: string,
) => {
  const qaList = getQuestionsAndAnswers(questions);

  const keys = Object.keys(qaList[0]);
  const csvContents =
    `${keys.map((key) => key.replace(/[_]/g, ' ')).join(',')}\n` +
    qaList
      .map((qa: any) => {
        return keys
          .map((k) => {
            let cell = qa[k];
            if (!cell) return '';
            cell.replace(/"/g, '""');
            if (cell.search(/("|,|\n)/g) >= 0) {
              cell = `"${cell}"`;
            }
            return cell;
          })
          .join(',');
      })
      .join('\n');

  const csvData = new Blob([csvContents], { type: 'text/csv' });
  const csvUrl = URL.createObjectURL(csvData);
  chrome.downloads.download({
    filename: fileName.replace(/\s+/g, '_') + '.csv',
    url: csvUrl,
  });
};

export function processAnswerCitations(
  answer: string,
  removeCitations = false,
) {
  if (!answer) return '';

  const processAnswer = (answer: string, removeCitations: boolean) => {
    // GPT either does citations like ^1 or ^1^ (yeah, it's weird)
    // Need to find out which one. Then change the citation to [1] square brackets style
    if (answer.indexOf('^') > -1) {
      if (removeCitations) {
        // If just removing citations, then easy: replace all '^1' patterns, and then remove any leftover '^'.
        // Also, sometimes citations separated by commas because...why not.
        return answer
          .replace(/[\^]\d{1,2}/g, '')
          .replace(/[^\.]\d{1,2}[\^]/g, '')
          .replace(/[\^]/g, '')
          .replace(/[,]+\./g, '.');
      } else {
        // Check if ^1 style. Assume there's no '^' in the actual answer, which feels reasonable.
        // If you replace all the instances of ^(digit) and there's still a ^ left, then it's ^1^ style.
        const replaceCitation = answer.replace(/[\^]\d{1,2}/g, '');

        if (
          replaceCitation.length < answer.length &&
          replaceCitation.indexOf('^') > -1
        ) {
          // ^1^ style
          // Watch out for commas separating multi-context citation (^1^,^2^)
          const matches = answer.match(/[\^]\d{1,2}[\^],*/g);

          // Sanity check
          if (matches != null) {
            matches.forEach((citation: string) => {
              // TODO: replace each citation instance with a <span> with tooltip
              // How to do this without dangerouslyInsertHTML-like construct?
              answer = answer.replace(
                citation,
                ' ' +
                  citation.replace('^', '[').replace('^', ']').replace(',', ''),
              );
            });
          }
        } else {
          // ^1 style
          // Watch out for commas separating multi-context citation (^1,^2)
          const matchesPrefix = answer.match(/[\^]\d{1,2},*/g);

          // Look for 1^ style
          const matchesSuffix = answer.match(/\d{1,2}[\^],*/g);

          // Sanity check
          if (matchesPrefix != null) {
            matchesPrefix.forEach((citation: string) => {
              answer = answer.replace(
                citation,
                ' ' + citation.replace('^', '[').replace(',', '') + ']',
              );
            });
          } else if (matchesSuffix != null) {
            matchesSuffix.forEach((citation: string) => {
              answer = answer.replace(
                citation,
                '[' + citation.replace('^', ']').replace(',', ''),
              );
            });
          }
        }

        // Corner-case: GPT seems to SOMETIMES answer with BOTH ^1 and ^1^ formats, across different sentences.
        // The above can lead to straglers like "...[1]2. Blah blah..."
        // Look for "](digits)." Regex and surround those (digits) with [] too. Sigh.
        const cornerCaseRegex = /](\d+)[\.]/g;
        let match;
        while ((match = cornerCaseRegex.exec(answer))) {
          let fixedCitation = match[0].replace(']', '][').replace('.', '].');
          answer = answer.replace(match[0], fixedCitation);
        }

        // Another corner-case whack a mole: "...[1]2 [3][4]..."
        const cornerCase2Regex = /](\d+)\s\[/g;
        let match2;
        while ((match2 = cornerCase2Regex.exec(answer))) {
          let fixedCitation = match2[0].replace(']', '][').replace(' [', '][');
          answer = answer.replace(match2[0], fixedCitation);
        }

        // Replace any "[1] [2]" --> "[1][2]"
        answer = answer.replace(/\]\s\[/g, '][');
      }
    }
    return answer;
  };

  let processedAnswer = processAnswer(answer, removeCitations);

  // Seeing sometimes [1]^2 after the above is run...so...run the thing again...
  // Note: seems to be caused by citations like this: ^1^,^2  ...because...why the heck GPT
  if (processedAnswer.indexOf('^') > -1) {
    processedAnswer = processAnswer(processedAnswer, removeCitations);
  }

  // With the new Agentic Answer RFP, processed citations may look like:
  // "Lorem ipsum . Hello world ." (notice the spaces between period)
  if (removeCitations) {
    processedAnswer = processedAnswer
      .replaceAll(' . ', '. ')
      .replace(/ \.$/, '.')
      .replace(/ \.\n/g, '.\n');
  }

  return processedAnswer;
}

// Citations simply map to indexes to contexts as they were presented to LLM.
// Reorder so that instead of say [1], [4], [5] we have [1], [2], and [3] instead.
// And in the sources page, we show [1], [4], [5] first and then can separately present
// the other contexts that weren't used. We'll just need to juggle accordingly,
// especially if we regenerate.
export const reorderCitations = (processedAnswer: string) => {
  const regexExtractCitationNumbers = /(\[\d+\])/g;

  let allCitationIndexes = {};
  let match;
  while ((match = regexExtractCitationNumbers.exec(processedAnswer))) {
    allCitationIndexes[match[0].replace('[', '').replace(']', '')] = null;
  }

  let usedCitationIndexes = Object.keys(allCitationIndexes)
    .map((index) => parseInt(index))
    .sort((a, b) => a - b);

  // Renumber the citations based on their order in the above sorted array.
  // E.g. [1], [2], [5], [7] --> [1], [2], [3], [4]
  let renumberedProcessedAnswer = processedAnswer;
  usedCitationIndexes.forEach((origIndex, idx) => {
    const replaceRegex = new RegExp('\\[' + origIndex + '\\]', 'g');
    renumberedProcessedAnswer = renumberedProcessedAnswer.replace(
      replaceRegex,
      '[' + (idx + 1) + ']',
    );
  });

  return {
    usedCitationIndexes,
    renumberedProcessedAnswer,
  };
};
