import { Stack, useTheme } from '@mui/material';
import React, { FC, useEffect, useState } from 'react';

import { LittleQuestionCard } from '../../../../../../atomic-design/molecules/LittleQuestionCard/LittleQuestionCard';
import {
  ContentDetailState,
  QuestionDto,
} from '../../../../../api/question.api';
import { useUsers } from '../../../../../api/user.swr';
import { useIsVisible } from '../../../../../hooks/useVisible.hook';

interface QLProps {
  questions: QuestionDto[];
  scrollToId?: string | undefined;
  justUpdatedId?: string | undefined;
  navigateToQuestion: (questionId: number) => void;
  updateQuestionStatus: (
    questionId: number,
    newState: ContentDetailState,
  ) => void;
  hasRendered: () => void;
  selectMode: boolean;
  selectedItems: number[];
  toggleSelectedItem: (questionId: number) => void;
}
export const QuestionList: FC<QLProps> = (props) => {
  const theme = useTheme();
  const users = useUsers();
  const { questions } = props;

  const [didScroll, setDidScroll] = useState(false);
  const scrollToRef = React.useRef<HTMLDivElement>(null);
  const isVisible = useIsVisible(scrollToRef);

  const availableUsers = users.data || [];

  // For very long lists + if the post inline question update scrollTo needs to fire
  useEffect(() => {
    props.hasRendered();
  }, []);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (!didScroll && scrollToRef.current) {
        setDidScroll(true);

        //Only scroll if the thing isn't already visible.
        if (!isVisible) {
          const toolbarOffset = parseInt(String(theme.mixins.toolbar.height));
          const rect = scrollToRef.current.getBoundingClientRect();
          window.scrollTo({
            top: rect.top - toolbarOffset * 2 - 12,
            behavior: 'smooth',
          });
        }
      }
    }, 250);
    return () => {
      clearTimeout(timer);
    };
  }, [isVisible, didScroll]);

  const littleQuestionCards = questions.map((question, i) => {
    if (question.assignee_id) {
      const assignedUser = availableUsers.find(
        (user) => Number(user.id) == Number(question.assignee_id),
      );

      if (assignedUser) {
        question.assignee_picture = assignedUser.picture;
      }
    }

    const questionSelected = props.selectedItems.includes(question.id!);

    if (question!.id!.toString() == props.scrollToId) {
      return (
        <div ref={scrollToRef} key={`lqc-${i}`}>
          <LittleQuestionCard
            question={question}
            selected={questionSelected}
            selectMode={props.selectMode}
            toggleSelected={(questionId: number) =>
              props.toggleSelectedItem(questionId)
            }
            justUpdatedId={props.justUpdatedId}
            navigateToQuestion={props.navigateToQuestion}
            updateQuestionStatus={props.updateQuestionStatus}
          />
        </div>
      );
    } else {
      return (
        <LittleQuestionCard
          question={question}
          selected={questionSelected}
          selectMode={props.selectMode}
          toggleSelected={(questionId: number) =>
            props.toggleSelectedItem(questionId)
          }
          key={`lqc-${i}`}
          justUpdatedId={props.justUpdatedId}
          navigateToQuestion={props.navigateToQuestion}
          updateQuestionStatus={props.updateQuestionStatus}
        />
      );
    }
  });
  return (
    <Stack direction={'column'} gap={2}>
      {littleQuestionCards}
    </Stack>
  );
};
