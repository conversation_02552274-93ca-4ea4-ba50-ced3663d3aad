import { ToggleButton, useTheme } from '@mui/material';
import React, { FC } from 'react';

import { StyledToggleButtonGroup } from '../../../../../../atomic-design/atoms/StyledToggleButtonGroup';
import { GenericFilterStore } from '../../../useFilterStore';
import { QuestionFilters } from '../util';

type QuestionStatusFilterProps = {
  store: GenericFilterStore<QuestionFilters>;
};
export const QuestionStatusFilterGroup: FC<QuestionStatusFilterProps> = (
  props,
) => {
  const store = props.store;
  const theme = useTheme();

  const handleChange = (
    e: React.MouseEvent<HTMLElement, MouseEvent>,
    status: QuestionFilters['status'],
  ) => {
    e.preventDefault();
    // Don't allow no selection (this happens when user toggles the currently selected)
    if (status) {
      store.setFilters({ status });
    }
  };

  const buttons: { value: QuestionFilters['status']; label: string }[] = [
    { value: 'all', label: 'All' },
    { value: 'notReviewed', label: 'Not Reviewed' },
    { value: 'reviewed', label: 'Reviewed' },
  ];

  return (
    <StyledToggleButtonGroup
      exclusive={true}
      value={store.filters.status}
      onChange={handleChange}
    >
      {buttons.map(({ value, label }) => {
        return (
          <ToggleButton key={label} value={value}>
            {label}
          </ToggleButton>
        );
      })}
    </StyledToggleButtonGroup>
  );
};
