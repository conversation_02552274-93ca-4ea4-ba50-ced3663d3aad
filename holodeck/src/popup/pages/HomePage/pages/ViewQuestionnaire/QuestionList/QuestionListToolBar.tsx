import { Add, Checklist, Download, Refresh, Tune } from '@mui/icons-material/';
import {
  Badge,
  Box,
  Button,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Stack,
  Typography,
  useTheme,
} from '@mui/material';
import React, { FC } from 'react';
import { CircularIconButton } from '../../../../../../atomic-design/molecules/QuestionCard/CircularIconButton';
import { QuestionDto } from '../../../../../api/question.api';

import { PrimaryTooltip } from '../../../../../../atomic-design/atoms/StyledTooltips';
import { SearchField } from '../../../../../../atomic-design/molecules/SearchField';
import { downloadRfpInBrowser } from '../../../../../../components/EndToEndRfp';
import { useRootStore } from '../../../../../state/useRootStore';
import { useSharedStore } from '../util';
import { QuestionStatusFilterGroup } from './StatusFilterBar';
import {
  getQuestionsAndAnswers,
  handleDownloadAsCSV,
} from './answerProcessing';

interface QLTProps {
  questions: QuestionDto[];
  projectLabel: string;
  questionnaireId: string;
  reloadQuestions: () => void;
  openFiltersDraw: () => void;
  toggleSelectMode: () => void;
  selectMode: boolean;
  excel: boolean;
  customerName?: string;
  rfxContentId: string;
  hasE2eWorkbook: boolean;
}

export const QuestionListToolbar: FC<QLTProps> = (props) => {
  const {
    questions,
    projectLabel,
    openFiltersDraw,
    rfxContentId,
    excel,
    customerName,
    hasE2eWorkbook,
  } = props;
  const hasQuestions = questions.length;
  const theme = useTheme();
  const rootStore = useRootStore();
  const filterStore = useSharedStore();
  const { filters, setFilters } = filterStore;

  // For E2E Spreadsheet, can download as a completed XLSX or CSV of just Q&A
  const [e2eAnchorEl, setE2eAnchorEl] = React.useState<null | HTMLElement>(
    null,
  );
  const closeE2EMenu = () => {
    setE2eAnchorEl(null);
  };

  const addHandler = () => {
    rootStore.navigate({
      name: 'viewQuestionnaire/addQuestions',
      id: props.questionnaireId,
      rfxContentId: props.rfxContentId,
    });
  };

  const ButtonBar = (
    <Stack
      direction={'row'}
      alignItems={'center'}
      justifyContent={'space-between'}
    >
      <Stack direction="row" alignItems="center" gap="8px">
        <Typography
          variant="body2"
          sx={{ color: theme.palette.common.black, fontWeight: '500' }}
        >
          Questions
        </Typography>
        <Chip
          label={hasQuestions}
          size="small"
          variant="filled"
          sx={{ '& .MuiChip-label': { fontWeight: 600 } }}
        />
        <PrimaryTooltip title="Add Questions">
          <Button
            color="primary"
            size="small"
            variant="outlined"
            onClick={addHandler}
            startIcon={<Add />}
            sx={{
              ml: '12px',
              padding: '1px 8px',
              fontSize: '12px',
              '& .MuiButton-startIcon': { mr: '2px' },
            }}
          >
            Add
          </Button>
        </PrimaryTooltip>
      </Stack>

      <Stack
        direction={'row'}
        alignItems={'center'}
        justifyContent={'flex-end'}
        gap="6px"
      >
        <PrimaryTooltip
          title="Toggle Select Mode"
          placement={`${rfxContentId && excel ? 'top' : 'bottom'}`}
        >
          {props.selectMode ? (
            <Badge variant="dot" overlap="circular" color="primary">
              <CircularIconButton
                Icon={Checklist}
                size="small"
                disableBorder={true}
                onClick={props.toggleSelectMode}
                color={props.selectMode ? 'primary' : 'default'}
              />
            </Badge>
          ) : (
            <Box>
              <CircularIconButton
                Icon={Checklist}
                size="small"
                disableBorder={true}
                onClick={props.toggleSelectMode}
                color={props.selectMode ? 'primary' : 'default'}
              />
            </Box>
          )}
        </PrimaryTooltip>

        <PrimaryTooltip
          title="Refresh Questions List"
          placement={`${rfxContentId && excel ? 'top' : 'bottom'}`}
        >
          <Box>
            <CircularIconButton
              Icon={Refresh}
              size="small"
              disabled={!hasQuestions}
              disableBorder={true}
              onClick={() => {
                props.reloadQuestions();
              }}
            />
          </Box>
        </PrimaryTooltip>

        <PrimaryTooltip
          title={`Download as a ${
            rfxContentId && excel ? 'XLSX or CSV' : 'CSV'
          } file`}
          placement={`${rfxContentId && excel ? 'top' : 'bottom'}`}
        >
          <Box>
            <CircularIconButton
              Icon={Download}
              size="small"
              disabled={!hasQuestions}
              disableBorder={true}
              onClick={async (event: React.MouseEvent<HTMLButtonElement>) => {
                if (rfxContentId && excel) {
                  setE2eAnchorEl(event.currentTarget);
                } else {
                  handleDownloadAsCSV(questions, projectLabel);
                }
              }}
            />
          </Box>
        </PrimaryTooltip>
        <Menu
          id="e2e-download-menu"
          anchorEl={e2eAnchorEl}
          open={Boolean(e2eAnchorEl)}
          onClose={closeE2EMenu}
        >
          <MenuItem
            onClick={async () => {
              await downloadRfpInBrowser(
                Number(rfxContentId),
                'xlsx',
                projectLabel ?? customerName,
                hasE2eWorkbook,
              );
              closeE2EMenu();
            }}
          >
            {hasE2eWorkbook
              ? 'Download as filled in XLSX'
              : 'Download the original XLSX file'}
          </MenuItem>
          <MenuItem
            onClick={() => {
              handleDownloadAsCSV(questions, projectLabel);
              closeE2EMenu();
            }}
          >
            Download just Q&A as a CSV
          </MenuItem>
        </Menu>
      </Stack>
    </Stack>
  );

  const hasFilters =
    filters.groupingIds.length > 0 ||
    filters.assigneeIds.length > 0 ||
    filters.confidence.length > 0;

  const SearchBar = () => {
    return (
      <Stack
        direction={'row'}
        alignItems={'center'}
        justifyContent={'space-between'}
        gap={1}
      >
        <SearchField
          extraProps={{
            sx: {
              backgroundColor: 'grey.300',
              borderRadius: '100px',
              fontSize: '0.9rem',
            },
          }}
          handleChange={(e) => {
            setFilters({ searchQuery: e.target.value });
          }}
          handleClearSearch={() => {
            setFilters({ searchQuery: '' });
          }}
          searchQuery={filters.searchQuery}
        />
        {hasFilters && (
          <IconButton onClick={openFiltersDraw}>
            <Badge
              variant="dot"
              color="error"
              anchorOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
            >
              <Tune />
            </Badge>
          </IconButton>
        )}
        {!hasFilters && (
          <IconButton onClick={openFiltersDraw}>
            <Tune />
          </IconButton>
        )}
      </Stack>
    );
  };

  const FilterBar = () => {
    return <QuestionStatusFilterGroup store={filterStore} />;
  };

  return (
    <Stack direction={'column'} gap={1} justifyContent={'flex-start'}>
      {ButtonBar}
      {SearchBar()}
      {FilterBar()}
    </Stack>
  );
};

const copyAll = (questions: QuestionDto[]): string => {
  const qaList = getQuestionsAndAnswers(questions);
  const listOfStrings = qaList.map((qa) => {
    return `'${qa.question}','${qa.answer}'`;
  });
  listOfStrings.unshift(`'question','answer`);
  return listOfStrings.join('\n');
};
