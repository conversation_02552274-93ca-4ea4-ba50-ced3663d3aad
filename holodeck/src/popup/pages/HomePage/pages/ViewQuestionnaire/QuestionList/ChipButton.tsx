import { Chip, SxProps } from '@mui/material';
import React, { FC } from 'react';

interface CBProps {
  handleToggle: () => void;
  isActive: boolean;
  colorHex: string;
  value: string;
  sx?: SxProps;
}

export const ChipButton: FC<CBProps> = (props) => {
  const variant = props.isActive ? undefined : 'outlined';
  const chipStyle = {
    color: props.colorHex,
    borderColor: props.isActive ? props.colorHex + '50' : props.colorHex,
    backgroundColor: props.isActive ? props.colorHex + '50' : '#fff',
    fontWeight: props.isActive ? '600' : '500',
    ...props.sx,
  };
  return (
    <Chip
      variant={variant}
      onClick={props.handleToggle}
      sx={chipStyle}
      label={props.value}
    />
  );
};
