import CloseIcon from '@mui/icons-material/Close';
import { Box, IconButton, Stack, Typography } from '@mui/material';
import React, { FC } from 'react';

import { BottomDrawer } from '../../../../../atomic-design/atoms/BottomDrawer';

import { ContentGroupingDto } from '../../../../api/groupings.api';
import {
  MetadataFilterTypeDto,
  formatMDFValuesForUI,
  usesAllMDF,
} from '../../../../api/metadata.api';

/**
 * This component is used whenever a grouping has MDF.
 * Trying to show the user Grouping MDF details via a Chip looked a bit ugly.
 */

interface GroupingsDrawerParams {
  groupings: ContentGroupingDto[];
  allMDF: MetadataFilterTypeDto[];
  open: boolean;
  close: () => void;
}
export const GroupingsDrawer: FC<GroupingsDrawerParams> = (props) => {
  const oneGrouping = props.groupings.length == 1;
  const groupingsWithMDF = props.groupings.some((grouping) => {
    return Object.keys(grouping.grouping_metadata_filter).length > 0;
  });

  let subtitleText = `There ${oneGrouping ? 'is' : 'are'} ${props.groupings.length} ${
    oneGrouping ? 'grouping' : 'groupings'
  } in this questionnaire.`;

  if (groupingsWithMDF) {
    subtitleText += ` Some groupings have their own context, which supercedes any context set at the questionnaire level.`;
  }

  const sortedGroupings = [...props.groupings].sort((a, b) => {
    return a.grouping_name.localeCompare(b.grouping_name);
  });

  const contents = (
    <Stack direction="column" sx={{ py: '1rem', width: '100%' }}>
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        sx={{ width: '100%', mb: '0.5rem' }}
      >
        <Typography variant="h6" sx={{ fontWeight: '600' }}>
          Grouping Details
        </Typography>

        <IconButton
          aria-label="close"
          onClick={props.close}
          sx={{
            color: (theme) => theme.palette.grey[500],
            mr: '-8px',
          }}
        >
          <CloseIcon />
        </IconButton>
      </Stack>

      <Typography variant="body2" sx={{ mt: '0.5rem' }}>
        {subtitleText}
      </Typography>

      {sortedGroupings.map((grouping, idx) => {
        const hasMDF =
          Object.keys(grouping.grouping_metadata_filter).length > 0;

        // Remember the special { '0': ['all'] } case (see CreateGroupingForm.tsx)
        const isAllMDF =
          hasMDF && usesAllMDF(grouping.grouping_metadata_filter);

        let mdfText = '';
        let mappedMdf = {};

        if (hasMDF && !isAllMDF) {
          mappedMdf = formatMDFValuesForUI(grouping.grouping_metadata_filter);
          Object.keys(mappedMdf).forEach((key, idx) => {
            if (idx > 0) {
              mdfText += '\n\n';
            }
            mdfText += `${key}:\n- ${mappedMdf[key].join('\n- ')}`;
          });
        }

        return (
          <Box key={`grouping_${idx}`}>
            <Typography
              variant="body2"
              color="primary"
              sx={{ textTransform: 'uppercase', fontWeight: 500, mt: '1.5rem' }}
            >
              {oneGrouping ? '' : `${idx + 1}. `}
              {grouping.grouping_name}
            </Typography>
            <Typography
              variant="body2"
              sx={{ whiteSpace: 'pre-line', pt: '0.5rem', ml: '0.5rem' }}
            >
              {hasMDF && !isAllMDF && mdfText}
              {hasMDF && isAllMDF && `(no context tags will be applied)`}
              {!hasMDF && `(uses the questionnaire-level context)`}
            </Typography>
          </Box>
        );
      })}
    </Stack>
  );

  return (
    <BottomDrawer open={props.open} close={props.close}>
      <Box sx={{ display: 'flex', flexDirection: 'column' }}>{contents}</Box>
    </BottomDrawer>
  );
};
