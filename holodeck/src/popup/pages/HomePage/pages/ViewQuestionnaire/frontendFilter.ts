import * as QA_UTILS from '../../../../../popup/utils/qa';
import { removeAccents } from '../../../../../utils/helpers';
import { ContentGroupingDto } from '../../../../api/groupings.api';
import { ContentDetailState, QuestionDto } from '../../../../api/question.api';
import { Assignee } from '../../../../api/questionnaires.api';
import { QuestionFilters, confidenceScoreMap } from './util';

const FILTER_MAPPING: Record<QuestionFilters['status'], ContentDetailState[]> =
  {
    all: ['initial', 'accepted', 'modified', 'rejected', 'processing'],
    notReviewed: ['initial', 'modified'],
    reviewed: ['accepted'],
  };
const isSelectedByStatus = (
  question: QuestionDto,
  statusFilter: QuestionFilters['status'],
): boolean => {
  return FILTER_MAPPING[statusFilter]?.includes(question.state);
};

export function filterQuestions(
  questions: QuestionDto[],
  filters: QuestionFilters,
  groupings: ContentGroupingDto[],
  assignees: Assignee[],
) {
  return questions.filter((item) => {
    if (!filters) {
      return true;
    }
    // If 'all', just continue to next filter, otherwise check if it matches
    if (!isSelectedByStatus(item, filters.status)) {
      return false;
    }

    if (filters.confidence.length > 0) {
      const confidenceScoresToShow = filters.confidence.map(
        (conf) => confidenceScoreMap[conf],
      );
      const confidenceFiltersIncludesNoAnswer =
        filters.confidence.includes('no_answer');
      const isModified = QA_UTILS.isModifiedAnswer(item);

      // Modified is a special state - in theory high confidence?
      if (isModified) {
        return false;
      }

      const couldNotAnswer = QA_UTILS.couldNotGenerateAnswer(item);

      // Handle multi-version answers too
      if (item.output_original?.answer_versions?.length > 0) {
        // Do any of the any versions have a confidence score that maps to a selected confidence level?
        if (
          !item.output_original.answer_versions.some((answerVersion) => {
            const answerConfidence = answerVersion.confidence_score || 0;
            const confidenceScoreScaled =
              answerConfidence > 3
                ? 3
                : answerConfidence < 0
                  ? 0
                  : answerConfidence;
            return confidenceScoresToShow.includes(confidenceScoreScaled);
          })
        ) {
          if (!confidenceFiltersIncludesNoAnswer || !couldNotAnswer) {
            return false;
          }
        }
      } else {
        const answerConfidence = item.output_original.confidence_score || 0;
        const confidenceScoreScaled =
          answerConfidence > 3
            ? 3
            : answerConfidence < 0
              ? 0
              : answerConfidence;

        // Does the answer confidence score map to a selected confidence level?
        if (
          !confidenceScoresToShow.includes(confidenceScoreScaled) &&
          (!confidenceFiltersIncludesNoAnswer || !couldNotAnswer)
        ) {
          return false;
        }
      }
    }

    if (filters.groupingIds.length > 0) {
      if (
        !filters.groupingIds.some((groupingId) =>
          groupingId == -1
            ? item.grouping_id == null
            : groupingId == item.grouping_id,
        )
      ) {
        return false;
      }
    }

    if (filters.assigneeIds.length > 0) {
      if (
        !filters.assigneeIds.some((assigneeId) =>
          assigneeId == -1
            ? item.assignee_id == null
            : assigneeId == item.assignee_id,
        )
      ) {
        return false;
      }
    }

    // Search query filter
    if (filters.searchQuery) {
      const query = removeAccents(filters.searchQuery.toLowerCase());
      if (
        !removeAccents(item.input?.query_string?.toLowerCase()).includes(
          query,
        ) &&
        !removeAccents(
          item.input?.regenerated_query_string?.toLowerCase(),
        ).includes(query) &&
        !removeAccents(item.output_original?.answer?.toLowerCase()).includes(
          query,
        ) &&
        !removeAccents(item.grouping_name?.toLowerCase()).includes(query) &&
        !removeAccents(item.assignee_name?.toLowerCase()).includes(query)
      ) {
        return false;
      }
    }

    return true;
  });
}
