import {
  Avatar,
  Button,
  Chip,
  FormControl,
  ListItemText,
  MenuItem,
  Stack,
  ToggleButton,
  Typography,
  useTheme,
} from '@mui/material';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import React, { FC, useEffect, useState } from 'react';

import { GroupingDto } from '../../../../api/groupings.api';
import { useUsers } from '../../../../api/user.swr';
import { useFilterStore as useHomeFilterStore } from '../../useFilterStore';
import { confidenceFilterOptions, useSharedStore } from './util';

import { BottomDrawer } from '../../../../../atomic-design/atoms/BottomDrawer';
import { StyledToggleButtonGroup } from '../../../../../atomic-design/atoms/StyledToggleButtonGroup';
import { TribbleAvatar } from '../../../../../atomic-design/atoms/TribbleAvatar';

interface QuestionFiltersProps {
  open: boolean;
  close: () => void;
  groupings: GroupingDto[];
  assignees: number[];
  confidence: string[];
  currentUserId: number;
}
export const QuestionFiltersDrawer: FC<QuestionFiltersProps> = (props) => {
  const { filters, setFilters, clearAllFilters } = useSharedStore();
  const theme = useTheme();
  const allUsers = useUsers();

  // Owner / Assignee filter from the home screen is passed by default to the questionnaire.
  // (Assuming the filter user id is an assignee id.)
  // If that user id is removed from this filter drawer, then cancel out the "home screen" filter.
  // Assumption is that if the end user removes in one view, that should apply to any other
  // questionnaire detail view.
  const homeFilters = useHomeFilterStore();

  const { groupings, assignees, confidence, currentUserId } = props;

  // Cast to number[] before saving
  const [selectedGroupingIds, setSelectedGroupingIds] = useState<number[]>(
    filters.groupingIds || [],
  );
  const [selectedAssigneeIds, setSelectedAssigneeIds] = useState<number[]>(
    filters.assigneeIds || [],
  );

  const [selectedConfidence, setSelectedConfidence] = useState<string[]>(
    filters.confidence || [],
  );

  useEffect(() => {
    setSelectedGroupingIds(filters.groupingIds || []);
    setSelectedAssigneeIds(filters.assigneeIds || []);
  }, [props.open]);

  const handleChangeGroupingsFilter = (e: SelectChangeEvent<number[]>) => {
    e.preventDefault();

    if (
      typeof e.target.value == 'number' ||
      typeof e.target.value == 'string'
    ) {
      setSelectedGroupingIds([Number(e.target.value)]);
    } else {
      setSelectedGroupingIds(e.target.value.map((val) => Number(val)));
    }
  };

  const handleRemoveGroupingFilter = (idToRemove: number) => {
    setSelectedGroupingIds(
      selectedGroupingIds.filter((groupingId) => groupingId != idToRemove),
    );
  };

  const handleChangeAssigneesFilter = (e: SelectChangeEvent<number[]>) => {
    e.preventDefault();

    if (
      typeof e.target.value == 'number' ||
      typeof e.target.value == 'string'
    ) {
      setSelectedAssigneeIds([Number(e.target.value)]);
    } else {
      setSelectedAssigneeIds(e.target.value);
    }
  };

  const handleRemoveAssigneeFilter = (idToRemove: number) => {
    setSelectedAssigneeIds(
      selectedAssigneeIds.filter((assigneeId) => assigneeId != idToRemove),
    );
  };

  const handleToggleConfidence = (
    event: React.MouseEvent,
    confidenceLevels: string[],
  ) => {
    setSelectedConfidence(confidenceLevels);
  };

  const resetFilterDrawer = () => {
    homeFilters.setFilters({ ownerAssigneeFilter: 'all', assignedToIds: [] });

    setSelectedAssigneeIds([]);
    setSelectedGroupingIds([]);
    setSelectedConfidence([]);
  };

  const applyFilters = () => {
    // Check if home screen filters are set, and if so, update if necessary
    if (homeFilters.filters.ownerAssigneeFilter === 'mine') {
      // The current user's filter got removed -- remove the "global" filter
      if (
        filters.assigneeIds.includes(currentUserId) &&
        !selectedAssigneeIds.includes(currentUserId)
      ) {
        homeFilters.setFilters({
          ownerAssigneeFilter: 'all',
          assignedToIds: [],
        });
      }
    } else if (homeFilters.filters.ownerAssigneeFilter === 'other') {
      const otherUserId = Number(
        homeFilters.filters.ownerAssigneeFilterOtherId,
      );
      if (!selectedAssigneeIds.includes(otherUserId)) {
        homeFilters.setFilters({
          ownerAssigneeFilter: 'all',
          assignedToIds: [],
        });
      }
    }

    setFilters({
      groupingIds: selectedGroupingIds,
      assigneeIds: selectedAssigneeIds,
      confidence: selectedConfidence,
    });
    props.close();
  };

  const resetFilters = () => {
    setFilters({ groupingIds: [], assigneeIds: [], confidence: [] });
    resetFilterDrawer();
    props.close();
  };

  const groupingPicker = () => {
    return (
      <FormControl fullWidth size="small">
        <Select
          multiple
          value={selectedGroupingIds}
          onChange={handleChangeGroupingsFilter}
          renderValue={(selected) => {
            if (groupings.length > 0 && selectedGroupingIds.length > 0) {
              return selectedGroupingIds.map((groupingId) => {
                if (Number(groupingId) == -1) {
                  return (
                    <Chip
                      key={`selected_no_grouping`}
                      label="(no grouping)"
                      size="small"
                      color="primary"
                      variant="outlined"
                      sx={{ mr: '0.5rem' }}
                      onDelete={(e) => handleRemoveGroupingFilter(groupingId)}
                      onMouseDown={(e) => e.stopPropagation()}
                    />
                  );
                } else {
                  const grouping = groupings.find(
                    (g) => g.id == Number(groupingId),
                  );
                  return (
                    <Chip
                      key={`selected_grouping${grouping?.id}`}
                      label={grouping?.name}
                      size="small"
                      color="primary"
                      variant="outlined"
                      sx={{ mr: '0.5rem' }}
                      onDelete={(e) => handleRemoveGroupingFilter(grouping?.id)}
                      onMouseDown={(e) => e.stopPropagation()}
                    />
                  );
                }
              });
            } else {
              return <></>;
            }
          }}
          MenuProps={{
            sx: {
              maxHeight: '400px',
            },
            anchorOrigin: {
              vertical: 'bottom',
              horizontal: 'left',
            },
          }}
        >
          {groupings &&
            [{ id: -1, name: '(no grouping)' }, ...groupings].map(
              (grouping) => {
                return (
                  <MenuItem value={grouping.id} key={grouping.id}>
                    {grouping.name}
                  </MenuItem>
                );
              },
            )}
        </Select>
      </FormControl>
    );
  };

  const assigneePicker = () => {
    return (
      <FormControl fullWidth size="small">
        <Select
          multiple
          value={selectedAssigneeIds}
          onChange={handleChangeAssigneesFilter}
          renderValue={(selected) => {
            if (allUsers?.data.length > 0 && selectedAssigneeIds.length > 0) {
              return selectedAssigneeIds.map((assigneeId) => {
                if (Number(assigneeId) == -1) {
                  return (
                    <Chip
                      key={`selected_unassigned`}
                      label="(unassigned)"
                      size="small"
                      color="primary"
                      variant="outlined"
                      sx={{ mr: '0.5rem' }}
                      onDelete={(e) => handleRemoveAssigneeFilter(assigneeId)}
                      onMouseDown={(e) => e.stopPropagation()}
                    />
                  );
                } else {
                  const user = allUsers.data.find(
                    (u) => u.id == Number(assigneeId),
                  );
                  return (
                    <Chip
                      key={`selected_assignee${user?.id}`}
                      label={user?.name}
                      size="small"
                      color="primary"
                      variant="outlined"
                      sx={{ mr: '0.5rem' }}
                      onDelete={(e) => handleRemoveAssigneeFilter(user?.id)}
                      onMouseDown={(e) => e.stopPropagation()}
                    />
                  );
                }
              });
            } else {
              return <></>;
            }
          }}
          MenuProps={{
            sx: {
              maxHeight: '400px',
            },
            anchorOrigin: {
              vertical: 'bottom',
              horizontal: 'left',
            },
          }}
        >
          <MenuItem value={-1} key={`assignee_unassigned`}>
            <ListItemText
              primary="(unassigned)"
              sx={{ '& .MuiTypography-root': { fontSize: '0.85rem' } }}
            />
          </MenuItem>
          {allUsers?.data?.length > 0 &&
            assignees &&
            assignees
              .filter((assigneeId) => {
                const user = allUsers.data.find((u) => u.id == assigneeId);
                return user != null;
              })
              .map((assigneeId) =>
                allUsers.data.find((u) => u.id == assigneeId),
              )
              .sort((a, b) =>
                a.name.toLowerCase() < b.name.toLowerCase() ? -1 : 1,
              )
              .map((user) => {
                return (
                  <MenuItem value={user.id} key={user.id}>
                    <div style={{ marginRight: '1rem' }}>
                      {user.picture && user.picture != '' && (
                        <Avatar
                          src={user.picture}
                          sx={{ height: 24, width: 24 }}
                        >
                          {user.name}
                        </Avatar>
                      )}
                      {(!user.picture || user.picture == '') && (
                        <TribbleAvatar
                          username={user.name}
                          height="24px"
                          width="24px"
                        />
                      )}
                    </div>
                    <ListItemText
                      primary={user.name}
                      sx={{ '& .MuiTypography-root': { fontSize: '0.85rem' } }}
                    />
                  </MenuItem>
                );
              })}
        </Select>
      </FormControl>
    );
  };

  // Hack until we can implement custom function
  // const hasFilters = filterStore.hasAdvancedFiltersSet();
  const hasFilters =
    filters.groupingIds.length > 0 ||
    filters.assigneeIds.length > 0 ||
    filters.confidence.length > 0;

  const contents = (
    <Stack direction="column" sx={{ py: '2rem', width: '100%' }}>
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        sx={{ width: '100%' }}
      >
        <Typography variant="h6" sx={{ fontWeight: '600' }}>
          More Filters
        </Typography>
        {hasFilters && (
          <Button
            size="small"
            variant="outlined"
            color="warning"
            onClick={resetFilters}
            sx={{ width: 'fit-content' }}
          >
            Reset Filters
          </Button>
        )}
        {!hasFilters && <></>}
      </Stack>

      <Typography
        variant="body2"
        sx={{ color: theme.palette.grey[500], mt: '0.5rem' }}
      >
        Use the options below to filter the list of questions.
      </Typography>

      <Typography
        variant="body2"
        color="primary"
        sx={{
          textTransform: 'uppercase',
          fontWeight: '500',
          pb: '0.25rem',
          mt: '2rem',
        }}
      >
        Confidence Level
      </Typography>
      <Typography
        variant="body2"
        sx={{ color: theme.palette.grey[500], pb: '0.75rem' }}
      >
        Filter questions by one or more confidence levels
      </Typography>
      <StyledToggleButtonGroup
        value={selectedConfidence}
        onChange={handleToggleConfidence}
      >
        {confidenceFilterOptions.map(({ value, label }) => (
          <ToggleButton key={`owner_${value}`} value={value}>
            {label}
          </ToggleButton>
        ))}
      </StyledToggleButtonGroup>

      <Typography
        variant="body2"
        color="primary"
        sx={{
          textTransform: 'uppercase',
          fontWeight: '500',
          pb: '0.75rem',
          mt: '2rem',
        }}
      >
        Groupings
      </Typography>
      {groupingPicker()}

      <Typography
        variant="body2"
        color="primary"
        sx={{
          textTransform: 'uppercase',
          fontWeight: '500',
          pb: '0.75rem',
          mt: '2rem',
        }}
      >
        Assignees
      </Typography>
      {assigneePicker()}
    </Stack>
  );

  return (
    <BottomDrawer open={props.open} close={props.close}>
      <Stack direction="column" sx={{ alignItems: 'center' }}>
        {contents}
        <Button onClick={applyFilters}>Apply Filters</Button>
      </Stack>
    </BottomDrawer>
  );
};
