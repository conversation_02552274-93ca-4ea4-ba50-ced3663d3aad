import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import FactCheckIcon from '@mui/icons-material/FactCheck';
import LanguageIcon from '@mui/icons-material/Language';
import PeopleIcon from '@mui/icons-material/People';

import {
  Box,
  Button,
  Checkbox,
  CircularProgress,
  Divider,
  FormControlLabel,
  LinearProgress,
  Stack,
  Typography,
  styled,
  useTheme,
} from '@mui/material';
import React, { FC, useEffect, useRef, useState } from 'react';

import critter_glasses from '../../../../../assets/critter_glasses.png';
import { PageHeaderBar } from '../../../../../atomic-design/atoms/PageHeaderBar';
import {
  ErrorTooltip,
  SecondaryTooltip,
} from '../../../../../atomic-design/atoms/StyledTooltips';
import { UtilityButton } from '../../../../../atomic-design/atoms/buttons/UtilityButton';
import { FabScrollUp } from '../../../../../atomic-design/molecules/FabScrollUp';
import { CircularIconButton } from '../../../../../atomic-design/molecules/QuestionCard/CircularIconButton';
import ConfirmationModal from '../../../../../components/ConfirmationModal';
import { GroupingDto } from '../../../../api/groupings.api';
import { useContentGroupings } from '../../../../api/groupings.swr';
import {
  MetadataFilterTypeDto,
  getAvailableMetadataFilters,
} from '../../../../api/metadata.api';
import {
  ContentDetailState,
  QuestionDto,
  bulkUpdateQuestions,
  getQuestions,
  setQuestionStatus,
} from '../../../../api/question.api';
import {
  Assignee,
  PatchQuestionnaireDto,
  QuestionnaireDto,
  deleteQuestionnaire,
  getQuestionnaireJobStatus,
  patchQuestionnaire,
} from '../../../../api/questionnaires.api';
import { useE2EWorkbook } from '../../../../api/rfx_projects.swr';
import { useCurrentUser } from '../../../../api/user.swr';
import { useRfxProjectContent } from '../../../../hooks/useRfxProjectContent.hook';
import { useRootStore } from '../../../../state/useRootStore';
import { useFilterStore as useHomeFilterStore } from '../../useFilterStore';
import { BulkActionDrawer } from './BulkActionDrawer';
import { GroupingsDrawer } from './GroupingsDrawer';
import { QuestionFiltersDrawer } from './QuestionFiltersDrawer';
import { QuestionList } from './QuestionList/QuestionList';
import { QuestionListToolbar } from './QuestionList/QuestionListToolBar';
import { QuestionnaireDetail } from './QuestionnaireDetail';
import { QuestionnaireSummaryScrollUp } from './QuestionnaireSummaryScrollUp';
import { filterQuestions } from './frontendFilter';
import {
  getInProgressJobs,
  getSortedAssigneeIdsFromQuestions,
  useSharedStore,
} from './util';

const questionFetcher = (questionnaireId: string) =>
  getQuestions(questionnaireId).then((result) =>
    result.success && result.response
      ? result.response
      : Promise.reject(result),
  );

export const NoQuestionsTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  fontSize: '1.5rem',
}));

export const NoQuestionsBody = styled(Typography)(({ theme }) => ({
  fontWeight: 500,
  fontSize: '1rem',
  lineHeight: '1.4',
  maxWidth: '30rem',
  color: theme.palette.grey[600],
  textAlign: 'center',
}));

type ViewQuestionnaireProps = {
  id: string;
  scrollToId?: string;
  questionsAdded?: boolean;
};
export const ViewQuestionnaire: FC<ViewQuestionnaireProps> = ({
  id: rfxContentId,
  scrollToId,
  questionsAdded,
}) => {
  const theme = useTheme();
  const rootStore = useRootStore();
  const { filters, clearAllFilters, setFilters } = useSharedStore();
  const currentUserSwr = useCurrentUser();

  const {
    questionnaire: rfxQuestionnaire,
    isLoading: rfxLoading,
    error: rfxError,
    refetch: refetchRfx,
  } = useRfxProjectContent(rfxContentId);

  const contentGroupings = useContentGroupings(
    Number(rfxQuestionnaire?.content_id),
  );

  // Used to control whether to show "Download as filled in XLSX" option:
  // For older XLSX files, there was no E2E Workbook yet, so best we can
  // do is allow download of the original XLSX file.
  const e2eWorkbook = useE2EWorkbook(Number(rfxQuestionnaire?.content_id));

  const [showDelete, setShowDelete] = useState(false);
  const [loadingQuestions, setLoadingQuestions] = useState(true);
  const [filterDrawerOpen, setFilterDrawerOpen] = useState(false);
  const [groupingsDrawerOpen, setGroupingsDrawerOpen] = useState(false);
  const [fabShowing, setFabShowing] = useState(false);

  // This is used if a question status was updated inline
  const [postUpdateScrollToY, setPostUpdateScrollToY] = useState(-1);
  const preUpdateScrollToY = useRef(0);
  const [justUpdatedQuestionId, setJustUpdatedQuestionId] = useState<
    string | undefined
  >(undefined);

  const [questionnaire, setQuestionnaire] = useState<QuestionnaireDto>();
  const [questions, setQuestions] = useState<QuestionDto[]>([]);

  // Used to track any active answer questionnaire jobs in progress
  // (Note: does this need to use useState or can we use useRef??)
  const [jobLogData, setJobLogData] = useState<any[]>([]);

  // When the first page loads post-answering questions, check a little faster
  const jobLogCheckCounter = useRef(0);
  const jobLogTimeoutRef = useRef(null);

  // Need to detect these transitions to see whether a processing job
  // was actually found (i.e. job_log.length > 0) and then later
  // completed (job_log.length == 0). This is different from the
  // "there was never anything done" situation (which is most of the time)
  const jobLogNoneToPresentDetected = useRef(false);
  const jobLogPresentToNoneDetected = useRef(false);

  // For the very end. Otherwise, when the process finishes and we set
  // everything to default, if questionsAdded is true, we'll get a spinner
  const jobLogCompletionDetected = useRef(false);

  // Keep track of previous check's num answered: if changed, refresh the question list
  const jobLogLastNumAnswered = useRef(0);

  const [origQuestionnaireAssignees, setOrigQuestionnaireAssignees] = useState<
    Assignee[]
  >([]);

  const [allMDF, setAllMDF] = useState<MetadataFilterTypeDto[]>([]); //All customer metadata filters, because the mdf in the QuestionDTO only has ids, not the strings.
  const [mode, setMode] = useState<'edit' | 'view'>('view');
  const [resetTrigger, setResetTrigger] = useState(0);

  // For select mode
  const [selectMode, setSelectMode] = useState<boolean>(false);
  const [selectedItems, setSelectedItems] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState<boolean>(false);
  const [bulkActionDrawerOpen, setBulkActionDrawerOpen] = useState(false);
  const [bulkAction, setBulkAction] = useState<string>('');
  const selectedQuestions = questions.filter((q) =>
    selectedItems.includes(q.id),
  );

  const [localQuestionnaireDetail, setLocalQuestionnaireDetail] =
    useState<PatchQuestionnaireDto>(); //This is kept as 'working copy' that can get dirty and be used to update the db. Todo: Replace with swr :)

  // If filter-where-(someone)-is-owner-or-assignee filteres were set on the home page (e.g. Default),
  // a) if the (someone) is the owner of the current questionnaire, then proceed as normal
  // b) if the (someone) is not the owner, then default the questionnaire filter with assignee = (someone)
  const homeFilters = useHomeFilterStore();

  const filteredQuestions = filterQuestions(
    questions,
    filters,
    contentGroupings.data,
    questionnaire?.assignees,
  );

  useEffect(() => {
    if (rfxQuestionnaire) {
      setQuestionnaire(rfxQuestionnaire);
      setOrigQuestionnaireAssignees(
        JSON.parse(JSON.stringify(rfxQuestionnaire.assignees || [])),
      );
    }

    getAvailableMetadataFilters().then((result) => {
      if (result.success) {
        setAllMDF(result.response.metadata_filters);
      }
    });
  }, [rfxQuestionnaire]);

  useEffect(() => {
    // Fetch questions after groupings have been loaded, so grouping_name can be applied
    questionnaire?.content_id &&
      questionFetcher(questionnaire?.content_id).then((result) => {
        if (result.length) {
          setQuestions(
            result.map((question) => {
              if (question.grouping_id) {
                const grouping = contentGroupings.data.find(
                  (g) => g.grouping_id == question.grouping_id,
                );
                if (grouping) {
                  return { ...question, grouping_name: grouping.grouping_name };
                } else {
                  return question;
                }
              } else {
                return question;
              }
            }),
          );
        }

        setLoadingQuestions(false);
      });
  }, [contentGroupings.data, questionnaire?.content_id]);

  //Update questionDetail when questionnaire is updated
  useEffect(() => {
    putQuestionnaireDetailsToLocal();

    if (questionnaire) {
      // (Copying comment from above [see homeFilter declaration] for clarity...)

      // If filter-where-(someone)-is-owner-or-assignee filteres were set on the home page (e.g. Default),
      // a) if the (someone) is the owner of the current questionnaire, then proceed as normal
      // b) if the (someone) is not the owner, then default the questionnaire filter with assignee = (someone)
      const questionnaireOwner = questionnaire?.assignees?.find(
        (assignee) => assignee.is_owner,
      );
      if (
        homeFilters.filters.ownerAssigneeFilter == 'mine' &&
        (!questionnaireOwner ||
          questionnaireOwner?.assignee_id !== Number(currentUserSwr.data.id))
      ) {
        setFilters({ assigneeIds: [Number(currentUserSwr.data.id)] });
      } else if (
        homeFilters.filters.ownerAssigneeFilter == 'other' &&
        homeFilters.filters.ownerAssigneeFilterOtherId != '' &&
        (!questionnaireOwner ||
          questionnaireOwner?.assignee_id !==
            Number(homeFilters.filters.ownerAssigneeFilterOtherId))
      ) {
        setFilters({
          assigneeIds: [Number(homeFilters.filters.ownerAssigneeFilterOtherId)],
        });
      }

      // Periodically check for job status records.
      // If questionsAdded was passed in, then check sooner.
      let timeout = questionsAdded ? 1000 : 5000;
      if (questionnaire.job_log_data) {
        const inProgressJobs = getInProgressJobs(questionnaire.job_log_data);
        if (inProgressJobs.length > 0) {
          setJobLogData(questionnaire.job_log_data);
          timeout = 5000;
        }
      }

      // Note: if a user added questions but then went to another page and back,
      // the questionsAdded won't get set.
      jobLogTimeoutRef.current = setTimeout(checkInProgressJobStatus, timeout);

      return () => {
        clearTimeout(jobLogTimeoutRef.current);
      };
    }
  }, [questionnaire]);

  const checkInProgressJobStatus = async () => {
    const refreshedJobLogData = await getQuestionnaireJobStatus(
      questionnaire.job_id,
    );
    setJobLogData(refreshedJobLogData);

    const inProgressJobs = getInProgressJobs(refreshedJobLogData);

    // In progress job so keep checking
    if (inProgressJobs.length > 0) {
      jobLogNoneToPresentDetected.current = true;
      jobLogCheckCounter.current = 0;
      jobLogTimeoutRef.current = setTimeout(checkInProgressJobStatus, 10000);
    } else {
      if (jobLogNoneToPresentDetected.current) {
        // Questions have been processed.
        // Show toast and reload question list.
        rootStore.setNotification({
          body: 'All questions submitted have been processed.',
          header: 'Questions Processed',
          type: 'success',
        });

        // Needed since if questionsAdded = true, the spinner will
        // show up again once processing is done.
        jobLogCompletionDetected.current = true;
        jobLogPresentToNoneDetected.current = true;

        handleReloadQuestions();
      } else if (jobLogCheckCounter.current < 3) {
        jobLogTimeoutRef.current = setTimeout(
          checkInProgressJobStatus,
          questionsAdded ? 2000 : 5000,
        );

        // If we know questions were added, don't increment
        // this sanity counter, and just keep checking
        if (!questionsAdded) {
          jobLogCheckCounter.current++;
        }
      } else if (jobLogCheckCounter.current >= 3) {
        jobLogTimeoutRef.current = null;
      }
    }
  };

  const handleReloadQuestions = () => {
    if (!questionnaire?.content_id) {
      console.warn(
        'Cannot reload questions: questionnaire.content_id is undefined',
      );
      return;
    }

    setLoadingQuestions(true);
    setPostUpdateScrollToY(window.scrollY);

    questionFetcher(questionnaire.content_id).then((result) => {
      setQuestions(result);
      setLoadingQuestions(false);
    });
  };

  // This is called by the QuestionList component after it has rendered
  const handlePostQuestionListRender = () => {
    // Bit tricky the logic, but:
    // scrollToY IF...
    // a) there's even an offset (ScrollToY >=0)
    // b) if the question status was just updated inline...OR
    // c) scrollY has changed since last refresh...OR
    // d) we've transitioned from processing --> complete
    if (
      postUpdateScrollToY >= 0 &&
      (justUpdatedQuestionId ||
        preUpdateScrollToY.current != postUpdateScrollToY ||
        jobLogPresentToNoneDetected.current)
    ) {
      setTimeout(() => {
        window.scrollTo({
          top: postUpdateScrollToY,
          behavior: 'instant',
        });
      }, 200);

      preUpdateScrollToY.current = postUpdateScrollToY;

      // This will flash a linearprogress indicator
      // inside LittleQuestionCard for a brief moment
      if (justUpdatedQuestionId) {
        setTimeout(() => {
          setJustUpdatedQuestionId(undefined);
        }, 600);
      }
    }

    // After refreshing question list, remember the scrollY
    // (for the logic above to work)
    setTimeout(() => {
      preUpdateScrollToY.current = window.scrollY;
    }, 500);

    // We must have just finished some processing. Reset to default state.
    if (jobLogPresentToNoneDetected.current) {
      jobLogNoneToPresentDetected.current = false;
      jobLogPresentToNoneDetected.current = false;

      jobLogTimeoutRef.current = null;
      jobLogCheckCounter.current = 0;
    }
  };

  const handleUpdateDetails = (questionDetails: PatchQuestionnaireDto) => {
    const newQuestionnaire = {
      ...localQuestionnaireDetail,
      ...questionDetails,
    };
    setLocalQuestionnaireDetail(newQuestionnaire);
  };

  const handleUpdateQuestionStatus = async (
    questionId: number,
    newState: ContentDetailState,
  ) => {
    const result = await setQuestionStatus(questionId, newState);
    if (result.success) {
      setJustUpdatedQuestionId(String(questionId));
      handleReloadQuestions();
    } else {
      rootStore.setNotification({
        body: 'Failed to update status',
        header: 'Oops',
        type: 'error',
      });
    }
  };

  const handleNavigateToQuestion = (questionId: number) => {
    rootStore.navigate({
      name: 'viewQuestion',
      questionId,
      questionnaire,
    });
  };

  const putQuestionnaireDetailsToLocal = () => {
    if (questionnaire) {
      const details: PatchQuestionnaireDto = {
        content_id: questionnaire.content_id,
        document_id: questionnaire.document_id,
        job_id: questionnaire.job_id,
        label: questionnaire.label || questionnaire.file_name,
        projectDescription: questionnaire.details?.project_description,
        customInstructions: questionnaire.details?.custom_instructions,
        status: questionnaire.project_content_status,
        details: questionnaire.details,
        due_date: questionnaire.due_date,
        assignees: questionnaire.assignees,
        metadata_filter: questionnaire.metadata_filter,
        opportunity_id: questionnaire.opportunity_id,
      };
      setLocalQuestionnaireDetail(details);
    }
  };

  const doDelete = async () => {
    setShowDelete(false);
    const result = await deleteQuestionnaire(Number(questionnaire.content_id));
    if (result.success) {
      rootStore.setNotification({
        header: 'Questionnaire deleted',
        body: '',
        type: 'success',
      });
      rootStore.navigate({ name: 'default' });
    } else {
      rootStore.setNotification({
        header: 'Oops',
        body: 'Something went wrong',
        type: 'error',
      });
    }
  };

  const updateQuestionnaire = async (
    questionDetail?: PatchQuestionnaireDto,
  ) => {
    const result = await patchQuestionnaire(
      questionDetail ?? localQuestionnaireDetail,
      questionnaire.rfx_content_id,
    );

    if (result.success) {
      refetchRfx();
      rootStore.setNotification({
        type: 'success',
        header: 'Ok',
        body: 'Questionnaire updated.',
      });
    } else {
      rootStore.setNotification({
        type: 'error',
        header: 'Oops',
        body: 'Something went wrong.',
      });
    }
  };

  const doSave = async (questionDetail?: PatchQuestionnaireDto) => {
    await updateQuestionnaire(questionDetail);
  };

  const cancelEditButton = (
    <Button
      size="small"
      variant="outlined"
      onClick={() => {
        putQuestionnaireDetailsToLocal();
        setResetTrigger(new Date().getTime()); // Hacky...but need a way to trigger QuestionnaireDetail
        setMode('view');
      }}
    >
      Cancel
    </Button>
  );

  const DeleteConfirmation = (
    <ConfirmationModal
      cancelText="No, Cancel"
      confirmText="Yes, Delete"
      handleCancel={() => {
        setShowDelete(false);
      }}
      handleConfirm={doDelete}
      message="This will delete the questionnaire and all questions and answers. Are you sure?"
      open={showDelete}
      destructive={true}
      title="Delete"
    />
  );

  const saveButton = () => {
    const saveHandler = async () => {
      await doSave();
      setMode('view');
    };
    return (
      <Button onClick={saveHandler} variant="contained" size="small">
        Update Details
      </Button>
    );
  };

  const editButton = () => {
    const editHandler = () => {
      setMode('edit');
      window.scrollTo({
        top: 0,
        behavior: 'instant',
      });
    };

    return (
      <UtilityButton label={'Edit'} onClick={editHandler} Icon={EditIcon} />
    );
  };

  const getHeader = () => {
    if (mode == 'view') {
      return (
        <PageHeaderBar
          onClickBack={() => {
            clearAllFilters();
            rootStore.navigate({ name: 'default' });
          }}
        >
          <Stack direction="row" justifyContent="flex-end">
            <Box>{editButton()}</Box>
          </Stack>
        </PageHeaderBar>
      );
    } else {
      return (
        <PageHeaderBar onClickBack={() => {}} disableBackButton={true}>
          <Stack
            direction="row"
            alignItems="center"
            justifyContent="space-between"
            width="100%"
          >
            {cancelEditButton}
            {saveButton()}
          </Stack>
        </PageHeaderBar>
      );
    }
  };

  // check for any answering jobs in progress
  const inProgressJobs = getInProgressJobs(jobLogData);
  let inProgressJobsTotalQuestions = 0;
  let inProgressJobsAnsweredQuestions = 0;
  let jobLogMessage = [];
  inProgressJobs.forEach((job) => {
    inProgressJobsTotalQuestions += job.details.num_questions;
    inProgressJobsAnsweredQuestions += job.details.num_answered;
    jobLogMessage = job.messages ?? []; // there should just be one job record; but if multiple, get latest
  });

  if (inProgressJobsAnsweredQuestions != jobLogLastNumAnswered.current) {
    jobLogLastNumAnswered.current = inProgressJobsAnsweredQuestions;
    handleReloadQuestions();
  }

  const jobLogStatus = () => {
    // There are multiple possible states while questions are being answered.
    let state = 'default';

    if (inProgressJobs.length > 0) {
      if (
        inProgressJobsAnsweredQuestions == inProgressJobsTotalQuestions &&
        inProgressJobsTotalQuestions > 0
      ) {
        // We should be done, but the Present-to-None transition hasn't been detected yet.
        state = 'processed';
      } else {
        state = 'processing';
      }
    } else {
      if (
        jobLogNoneToPresentDetected.current &&
        jobLogPresentToNoneDetected.current
      ) {
        state = 'processed';
      } else if (questionsAdded && !jobLogCompletionDetected.current) {
        // Special state: questions were submitted, but the Durable Function
        // hasn't kicked off yet.
        state = 'queued';
      }
    }

    if (state == 'default' || state == 'processed') {
      return <></>;
    } else {
      return (
        <Stack
          direction="column"
          gap="0.25rem"
          sx={{ width: '100%', mt: '1.5rem' }}
        >
          <LinearProgress />
          {state == 'processing' && (
            <Typography
              variant="body2"
              color="disabled"
              sx={{ fontSize: '0.8rem', whiteSpace: 'pre-line' }}
            >
              {jobLogMessage.length ? `(${jobLogMessage.join('\n')})\n\n` : ''}
              Processed {inProgressJobsAnsweredQuestions} out of{' '}
              {inProgressJobsTotalQuestions} questions
            </Typography>
          )}
          {state == 'queued' && (
            <Typography
              variant="body2"
              color="disabled"
              sx={{ fontSize: '0.8rem' }}
            >
              Questions submitted. Initializing process...
            </Typography>
          )}
        </Stack>
      );
    }
  };

  const handleOpenGroupingsDrawer = () => {
    setGroupingsDrawerOpen(true);
  };

  const noQuestionsYet = questionnaire != null && questions.length == 0;
  const allAssigneeIds = getSortedAssigneeIdsFromQuestions(questions);

  // TODO: switch over to contentGrouping
  // Need to dedupe
  const groupingsMap = {};
  const questionGroupings: GroupingDto[] = questions
    .filter((q) => q.grouping_name)
    .map((q) => {
      return { id: q.grouping_id, name: q.grouping_name };
    })
    .reduce((acc, q) => {
      if (!groupingsMap[q.id]) {
        groupingsMap[q.id] = true;
        return acc.concat(q);
      } else {
        return acc;
      }
    }, [])
    .sort((a, b) => (a.name.toLowerCase() < b.name.toLowerCase() ? -1 : 1));

  const toggleSelectedItem = (id: number) => {
    if (selectedItems.includes(id)) {
      setSelectedItems(selectedItems.filter((i) => i != id));
    } else {
      setSelectedItems([...selectedItems, id]);
    }
  };

  const openBulkActionDrawer = (action: string) => {
    setBulkAction(action);
    setBulkActionDrawerOpen(true);
  };

  const handleBulkAction = async (payload: any) => {
    let successMessage = '';
    if (bulkAction == 'assign') {
      const result = await bulkUpdateQuestions('assign', {
        question_ids: selectedItems,
        questionnaire_id: questionnaire.content_id,
        assignee_id: payload.selectedUser,
      });
      if (result.success) {
        successMessage = 'Questions assigned';
      }
    } else if (bulkAction == 'status') {
      const result = await bulkUpdateQuestions('status', {
        question_ids: selectedItems,
        status: payload.status,
      });
      if (result.success) {
        successMessage = 'Questions updated';
      }
    } else if (bulkAction == 'translate') {
      const result = await bulkUpdateQuestions('translate', {
        question_ids: selectedItems,
        target_language: payload.targetLanguage,
      });
      if (result.success) {
        successMessage = 'Translate job has been started';
      }
    } else if (bulkAction == 'delete') {
      const result = await bulkUpdateQuestions('delete', {
        question_ids: selectedItems,
      });
      if (result.success) {
        successMessage = 'Questions deleted';
      }
    }

    if (!successMessage) {
      rootStore.setNotification({
        header: 'Oops',
        body: 'Something went wrong',
        type: 'error',
      });
    } else {
      // Don't reset completely in case user made an error
      // setSelectMode(false);
      // setSelectedItems([]);
      // setSelectAll(false);
      setBulkActionDrawerOpen(false);
      handleReloadQuestions();

      if (bulkAction === 'translate') {
        setTimeout(() => {
          checkInProgressJobStatus();
        }, 3000);
      }

      rootStore.setNotification({
        header: successMessage,
        body: '',
        type: 'success',
      });
    }
  };

  return (
    <Stack
      minHeight="100%"
      aria-label="ViewQuestionContainer"
      sx={{
        backgroundColor: theme.palette.grey[200],
        boxSizing: 'border-box',
      }}
    >
      <Box
        id="header-container"
        sx={{
          position: 'sticky',
          top: '0px',
          px: '1rem',
          pt: '1rem',
          backgroundColor: '#FFF',
          zIndex: 1200,
        }}
      >
        {getHeader()}
      </Box>
      <Box
        sx={{
          width: '100%',
          p: '1rem',
          boxSizing: 'border-box',
          bgcolor: '#fff',
        }}
      >
        {/*Body */}
        <div>
          {localQuestionnaireDetail && (
            <QuestionnaireDetail
              allMDF={allMDF}
              mode={mode}
              questions={questions}
              questionnaire={localQuestionnaireDetail}
              onSave={doSave}
              onUpdate={handleUpdateDetails}
              resetDetails={resetTrigger}
              openGroupingsDrawer={handleOpenGroupingsDrawer}
            />
          )}
        </div>

        {mode != 'edit' && jobLogStatus()}
      </Box>

      {mode == 'edit' && (
        <Stack
          direction="row"
          justifyContent="center"
          sx={{
            width: '100%',
            pt: '1rem',
            pb: '2.25rem',
            backgroundColor: '#fff',
          }}
        >
          <Button
            variant="outlined"
            color="error"
            onClick={() => setShowDelete(true)}
            sx={{ width: 'fit-content' }}
          >
            Delete Questionnaire
          </Button>
        </Stack>
      )}

      {mode == 'view' && <Divider />}

      {/*Question Section */}
      {loadingQuestions && (
        <Stack
          direction="column"
          alignItems="center"
          justifyContent="center"
          textAlign="center"
          height="100%"
          paddingX="2rem"
          paddingTop="4rem"
          gap="1.2rem"
        >
          <CircularProgress />

          <Typography
            variant="body2"
            sx={{
              fontSize: '0.8rem',
              color: theme.palette.grey[500],
            }}
          >
            Loading questions...
          </Typography>
        </Stack>
      )}

      {!loadingQuestions &&
        questionnaire &&
        (noQuestionsYet ? (
          <Stack
            alignItems={'center'}
            justifyContent={'center'}
            textAlign={'center'}
            height="100%"
            paddingX="2rem"
            paddingY="4rem"
            gap="1.2rem"
          >
            <img
              src={critter_glasses}
              alt="Tribble's Autonomous Agent"
              height="100px"
            />
            <Stack
              alignItems="center"
              justifyContent="center"
              textAlign="center"
              gap="1rem"
              sx={{ mb: 2 }}
            >
              <NoQuestionsTitle>Let's go.</NoQuestionsTitle>
              <NoQuestionsBody>
                Simply add questions to this project and Tribble will start
                filling them out for you.
              </NoQuestionsBody>
            </Stack>
            <Button
              variant="contained"
              fullWidth={false}
              onClick={(e) =>
                rootStore.navigate({
                  name: 'viewQuestionnaire/addQuestions',
                  id: questionnaire.content_id,
                  rfxContentId: questionnaire.rfx_content_id,
                })
              }
              sx={{ width: 'fit-content', pl: 3, pr: 4 }}
            >
              <AddIcon />
              Add questions
            </Button>
          </Stack>
        ) : (
          <Stack
            direction={'column'}
            width={'100%'}
            sx={{ bgcolor: 'grey.200', p: '1rem', boxSizing: 'border-box' }}
            gap={2}
          >
            <QuestionListToolbar
              questionnaireId={questionnaire.content_id}
              rfxContentId={questionnaire.rfx_content_id}
              projectLabel={questionnaire.label}
              questions={filteredQuestions}
              reloadQuestions={handleReloadQuestions}
              openFiltersDraw={() => setFilterDrawerOpen(true)}
              toggleSelectMode={() => setSelectMode(!selectMode)}
              selectMode={selectMode}
              excel={
                questionnaire.details?.fileName &&
                questionnaire.details?.fileName.includes('.xlsx')
              }
              customerName={questionnaire.details?.customer_name}
              hasE2eWorkbook={!!e2eWorkbook.data}
            />
            {filteredQuestions.length < questions.length && (
              <Stack
                direction="row"
                sx={{ width: '100%', justifyContent: 'flex-end' }}
              >
                <Typography
                  variant="body2"
                  sx={{
                    fontSize: '0.8rem',
                    fontStyle: 'italic',
                    color: theme.palette.grey[500],
                    mr: '0.5rem',
                  }}
                >
                  Filters applied: showing {filteredQuestions.length} out of{' '}
                  {questions.length}
                </Typography>
              </Stack>
            )}
            <Stack direction={'column'} gap={2} sx={{ mb: 1 }}>
              {loadingQuestions && <LinearProgress />}
              {!loadingQuestions && (
                <>
                  {selectMode &&
                    filteredQuestions.length > 0 &&
                    !fabShowing && (
                      <>
                        <Typography
                          variant="body2"
                          color="primary"
                          sx={{
                            fontSize: '0.75rem',
                            fontWeight: 600,
                            ml: '1rem',
                          }}
                        >
                          Select Mode ON ({selectedItems.length} question
                          {selectedItems.length == 1 ? ' ' : 's '} selected)
                          <br />
                          <span style={{ fontWeight: 400 }}>
                            Select one or more questions and perform bulk
                            actions (e.g. assign to user, update status, delete)
                          </span>
                        </Typography>
                      </>
                    )}
                  {selectMode &&
                    filteredQuestions.length > 0 &&
                    !fabShowing && (
                      <Stack
                        direction="row"
                        justifyContent="space-between"
                        alignItems="center"
                        sx={{ height: '28px' }}
                      >
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={selectAll}
                              color="secondary"
                              onChange={(
                                event: React.ChangeEvent<HTMLInputElement>,
                              ) => {
                                if (!selectAll) {
                                  setSelectedItems(
                                    filteredQuestions.map((q) => q.id),
                                  );
                                } else {
                                  setSelectedItems([]);
                                }
                                setSelectAll(!selectAll);
                              }}
                            />
                          }
                          label={
                            <Typography
                              variant="body2"
                              color="secondary"
                              sx={{ fontWeight: selectAll ? '600' : '400' }}
                            >
                              Select All
                            </Typography>
                          }
                          sx={{
                            ml: '4px',
                            mt: '-12px',
                            mb: '-14px',
                          }}
                        />
                        {selectedItems.length > 0 ? (
                          <Stack direction="row" alignItems="center" gap={1}>
                            <SecondaryTooltip title="Assign Questions">
                              <Box>
                                <CircularIconButton
                                  Icon={PeopleIcon}
                                  size="small"
                                  disableBorder={true}
                                  onClick={() => openBulkActionDrawer('assign')}
                                  color="secondary"
                                />
                              </Box>
                            </SecondaryTooltip>
                            <SecondaryTooltip title="Update Reviewed Status">
                              <Box>
                                <CircularIconButton
                                  Icon={FactCheckIcon}
                                  size="small"
                                  disableBorder={true}
                                  onClick={() => openBulkActionDrawer('status')}
                                  color="secondary"
                                />
                              </Box>
                            </SecondaryTooltip>
                            <SecondaryTooltip title="Translate">
                              <Box>
                                <CircularIconButton
                                  Icon={LanguageIcon}
                                  size="small"
                                  disableBorder={true}
                                  onClick={() =>
                                    openBulkActionDrawer('translate')
                                  }
                                  color="secondary"
                                />
                              </Box>
                            </SecondaryTooltip>
                            <ErrorTooltip title="Delete">
                              <Box>
                                <CircularIconButton
                                  Icon={DeleteIcon}
                                  size="small"
                                  disableBorder={true}
                                  onClick={() => openBulkActionDrawer('delete')}
                                  color="error"
                                />
                              </Box>
                            </ErrorTooltip>
                          </Stack>
                        ) : (
                          <div />
                        )}
                      </Stack>
                    )}
                  <QuestionList
                    questions={filteredQuestions}
                    scrollToId={postUpdateScrollToY == -1 ? scrollToId : null}
                    justUpdatedId={justUpdatedQuestionId}
                    navigateToQuestion={handleNavigateToQuestion}
                    updateQuestionStatus={handleUpdateQuestionStatus}
                    hasRendered={handlePostQuestionListRender}
                    selectMode={selectMode}
                    selectedItems={selectedItems}
                    toggleSelectedItem={(questionId: number) =>
                      toggleSelectedItem(questionId)
                    }
                  />
                </>
              )}
            </Stack>
            {/* For the FAB */}
            {filteredQuestions.length > 2 && <Box sx={{ minHeight: '45px' }} />}
          </Stack>
        ))}

      {DeleteConfirmation}

      <QuestionFiltersDrawer
        open={filterDrawerOpen}
        close={() => setFilterDrawerOpen(false)}
        groupings={questionGroupings}
        assignees={allAssigneeIds}
        confidence={filters.confidence}
        currentUserId={Number(currentUserSwr?.data?.id)}
      />

      <GroupingsDrawer
        open={groupingsDrawerOpen}
        close={() => setGroupingsDrawerOpen(false)}
        groupings={contentGroupings.data}
        allMDF={allMDF}
      />

      <BulkActionDrawer
        open={bulkActionDrawerOpen}
        close={() => setBulkActionDrawerOpen(false)}
        handleSubmit={handleBulkAction}
        questions={selectedQuestions}
        action={bulkAction}
      />

      <FabScrollUp onToggle={(showing) => setFabShowing(showing)} />

      <QuestionnaireSummaryScrollUp
        questionnaire={questionnaire}
        questions={filteredQuestions}
        selectMode={selectMode}
        toggleSelectMode={() => setSelectMode(!selectMode)}
        selectedItems={selectedItems}
        openBulkActionDrawer={openBulkActionDrawer}
        setSelectedItems={setSelectedItems}
      />
    </Stack>
  );
};
