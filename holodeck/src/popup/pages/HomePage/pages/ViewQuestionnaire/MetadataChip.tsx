import { Chip, useTheme } from '@mui/material';
import React, { FC } from 'react';

import { GreyTooltip } from '../../../../../atomic-design/atoms/StyledTooltips';
import { MetadataFilterTypeDto } from '../../../../api/metadata.api';

interface MDProps {
  metadata_filters:
    | {
        values?: string[];
        type_id?: number;
        type_name?: string;
      }[]
    | undefined
    | null;
  allMDF: MetadataFilterTypeDto[];
}

export const MetadataChip: FC<MDProps> = ({ allMDF, metadata_filters }) => {
  const theme = useTheme();

  if (
    !metadata_filters ||
    !metadata_filters.length ||
    !metadata_filters[0]?.values?.length
  )
    return <></>;

  let tooltipText = 'This questionnaire is specific to:\n\n';
  if (metadata_filters.length == 1) {
    if (metadata_filters[0].values.length == 1) {
      tooltipText += `${metadata_filters[0].type_name}:\n- ${metadata_filters[0].values[0]}`;
    } else {
      tooltipText += `${metadata_filters[0].type_name}:\n- `;
      tooltipText += metadata_filters[0].values.join('\n- ');
    }
  } else {
    // Each mdf_type is { type_id, type_name, values: [] }
    metadata_filters.forEach((mdf_type, idx) => {
      if (idx > 0) tooltipText += '\n\n';

      if (mdf_type.values.length == 1) {
        tooltipText += `${idx + 1}. ${mdf_type.type_name}:\n- ${
          mdf_type.values[0]
        }`;
      } else {
        tooltipText += `${idx + 1}. ${mdf_type.type_name}:\n- `;
        tooltipText += mdf_type.values.join('\n- ');
      }
    });
  }

  const chipLabel =
    metadata_filters.length == 1
      ? metadata_filters[0].values.length == 1
        ? // If just one type, and one value, just show the value
          `${metadata_filters[0].values[0]}`
        : `${metadata_filters[0].type_name}: ${metadata_filters[0].values.length}`
      : `Context Types: ${metadata_filters.length}`;

  return (
    <GreyTooltip title={tooltipText}>
      <Chip
        variant="outlined"
        size="small"
        label={chipLabel}
        sx={{ color: theme.palette.grey[600], fontSize: '0.8rem' }}
      />
    </GreyTooltip>
  );
};
