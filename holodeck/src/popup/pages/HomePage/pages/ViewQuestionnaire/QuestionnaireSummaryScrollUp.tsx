import {
  Delete,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  MoreVert,
  People,
} from '@mui/icons-material/';
import {
  Box,
  IconButton,
  MenuItem,
  Stack,
  Typography,
  useTheme,
} from '@mui/material';
import React, { FC, useEffect, useState } from 'react';

import { MenuDropdown } from '../../../../../atomic-design/atoms/MenuDropdown';
import {
  ErrorTooltip,
  SecondaryTooltip,
} from '../../../../../atomic-design/atoms/StyledTooltips';
import { Title } from '../../../../../atomic-design/atoms/Title';
import { CircularIconButton } from '../../../../../atomic-design/molecules/QuestionCard/CircularIconButton';
import { SearchField } from '../../../../../atomic-design/molecules/SearchField';
import { QuestionDto } from '../../../../api/question.api';
import { QuestionnaireDto } from '../../../../api/questionnaires.api';
import { useRootStore } from '../../../../state/useRootStore';
import { useSharedStore } from './util';

interface QuestionnaireSummaryScrollUpProps {
  questionnaire: QuestionnaireDto;
  questions: QuestionDto[];
  toggleSelectMode: () => void;
  selectMode: boolean;
  selectedItems: number[];
  setSelectedItems: (items: number[]) => void;
  openBulkActionDrawer: (
    action: 'assign' | 'status' | 'translate' | 'delete',
  ) => void;
}
export const QuestionnaireSummaryScrollUp: FC<
  QuestionnaireSummaryScrollUpProps
> = (props) => {
  const [showSummary, setShowSummary] = useState(false);
  const theme = useTheme();
  const filterStore = useSharedStore();
  const rootStore = useRootStore();
  const { filters, setFilters } = filterStore;

  const handleScroll = () => {
    const currentScrollPos = window.scrollY;
    if (currentScrollPos > 300) {
      setShowSummary(true);
    } else {
      setShowSummary(false);
    }
  };

  useEffect(() => {
    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return showSummary ? (
    <Stack
      sx={{
        position: 'fixed',
        top: '64px',
        width: '100%',
        height: props.selectMode ? '215px' : '135px',
        backgroundColor: 'white',
        borderBottom: `2px solid ${theme.palette.primary.main}`,
        p: 2,
      }}
      gap="1rem"
    >
      <Stack
        direction={'column'}
        justifyContent={'space-between'}
        alignItems={'flex-start'}
        gap="4px"
      >
        <Title
          title={props.questionnaire?.label}
          sx={{
            mt: '1px',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            maxWidth: '100%',
          }}
        ></Title>
        <Typography variant="body2">
          {props.questionnaire?.details?.customer_name}
        </Typography>
      </Stack>

      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        gap="4px"
      >
        <SearchField
          extraProps={{
            sx: {
              backgroundColor: 'grey.300',
              borderRadius: '100px',
              fontSize: '0.9rem',
            },
          }}
          handleChange={(e) => {
            setFilters({ searchQuery: e.target.value });
          }}
          handleClearSearch={() => {
            setFilters({ searchQuery: '' });
          }}
          searchQuery={filters.searchQuery}
        />

        <MenuDropdown
          trigger={
            <IconButton>
              <MoreVert />
            </IconButton>
          }
        >
          {(handleClose) => [
            <MenuItem
              key={'toggle-select-mode'}
              onClick={() => {
                props.toggleSelectMode();
                handleClose();
              }}
              disableRipple
            >
              Toggle Select Mode
            </MenuItem>,
            <MenuItem
              key={'add-questions'}
              onClick={() => {
                rootStore.navigate({
                  name: 'viewQuestionnaire/addQuestions',
                  id: props.questionnaire.content_id,
                  rfxContentId: props.questionnaire.rfx_content_id,
                });
                handleClose();
              }}
              disableRipple
            >
              Add Questions
            </MenuItem>,
          ]}
        </MenuDropdown>
      </Stack>

      <Stack
        direction="column"
        justifyContent="flex-start"
        alignItems="flex-start"
      >
        <Stack
          direction="row"
          justifyContent="flex-start"
          alignItems="flex-start"
          gap="6px"
        >
          {props.selectMode ? (
            <Stack direction="column">
              <Typography
                variant="body2"
                color="primary"
                sx={{
                  fontSize: '0.75rem',
                  fontWeight: 600,
                }}
              >
                Select Mode ON ({props.selectedItems.length} question
                {props.selectedItems.length == 1 ? ' ' : 's '} selected)
              </Typography>
              <Stack direction="row" gap={1}>
                <Typography
                  variant="subtitle2"
                  color="primary"
                  sx={{
                    fontSize: '0.75rem',
                    fontWeight: 300,
                    cursor: 'pointer',
                  }}
                  onClick={() => {
                    props.setSelectedItems(props.questions.map((q) => q.id));
                  }}
                >
                  Select All
                </Typography>
                <Typography
                  variant="subtitle2"
                  sx={{
                    fontSize: '0.75rem',
                    fontWeight: 300,
                    color: theme.palette.grey[500],
                  }}
                >
                  |
                </Typography>
                <Typography
                  variant="subtitle2"
                  color="primary"
                  sx={{
                    fontSize: '0.75rem',
                    fontWeight: 300,
                    cursor: 'pointer',
                  }}
                  onClick={() => {
                    props.setSelectedItems([]);
                  }}
                >
                  Deselect All
                </Typography>
                <Typography
                  variant="subtitle2"
                  sx={{
                    fontSize: '0.75rem',
                    fontWeight: 300,
                    color: theme.palette.grey[500],
                  }}
                >
                  |
                </Typography>
                <Typography
                  variant="subtitle2"
                  color="primary"
                  sx={{
                    fontSize: '0.75rem',
                    fontWeight: 300,
                    cursor: 'pointer',
                  }}
                  onClick={props.toggleSelectMode}
                >
                  Turn OFF Select Mode
                </Typography>
              </Stack>
            </Stack>
          ) : null}
        </Stack>
        {props.selectMode && (
          <Stack
            direction="row"
            justifyContent="flex-start"
            alignItems="center"
            gap="1rem"
            sx={{ mt: 1, height: '28px', width: '100%' }}
          >
            <Stack direction="row" alignItems="center" gap="1rem">
              <Typography
                variant="body2"
                color="secondary"
                sx={{
                  fontSize: '0.75rem',
                  fontWeight: 600,
                }}
              >
                Actions:
              </Typography>
              {props.selectedItems.length > 0 ? (
                <>
                  <SecondaryTooltip title="Assign Questions">
                    <Box>
                      <CircularIconButton
                        Icon={People}
                        size="small"
                        disableBorder={true}
                        onClick={() => props.openBulkActionDrawer('assign')}
                        color="secondary"
                      />
                    </Box>
                  </SecondaryTooltip>
                  <SecondaryTooltip title="Update Reviewed Status">
                    <Box>
                      <CircularIconButton
                        Icon={FactCheck}
                        size="small"
                        disableBorder={true}
                        onClick={() => props.openBulkActionDrawer('status')}
                        color="secondary"
                      />
                    </Box>
                  </SecondaryTooltip>
                  <SecondaryTooltip title="Translate">
                    <Box>
                      <CircularIconButton
                        Icon={Language}
                        size="small"
                        disableBorder={true}
                        onClick={() => props.openBulkActionDrawer('translate')}
                        color="secondary"
                      />
                    </Box>
                  </SecondaryTooltip>
                  <ErrorTooltip title="Delete">
                    <Box>
                      <CircularIconButton
                        Icon={Delete}
                        size="small"
                        disableBorder={true}
                        onClick={() => props.openBulkActionDrawer('delete')}
                        color="error"
                      />
                    </Box>
                  </ErrorTooltip>
                </>
              ) : (
                <Typography
                  variant="body2"
                  color="secondary"
                  sx={{
                    fontSize: '0.75rem',
                    fontWeight: 300,
                  }}
                >
                  (select some questions first)
                </Typography>
              )}
            </Stack>
          </Stack>
        )}
      </Stack>
    </Stack>
  ) : (
    <div />
  );
};
