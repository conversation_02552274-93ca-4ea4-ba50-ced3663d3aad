import CloseIcon from '@mui/icons-material/Close';
import {
  Box,
  Button,
  IconButton,
  Stack,
  ToggleButton,
  Typography,
} from '@mui/material';
import React, { FC, useEffect, useState } from 'react';

import { BottomDrawer } from '../../../../../atomic-design/atoms/BottomDrawer';
import { StyledToggleButtonGroup } from '../../../../../atomic-design/atoms/StyledToggleButtonGroup';
import { LanguagePicker } from '../../../../../atomic-design/molecules/LanguagePicker';
import { UserPickList } from '../../../../../atomic-design/molecules/UserPickList';

import { QuestionDto } from '../../../../api/question.api';
import { UserDto } from '../../../../api/user.api';
import { useUsers } from '../../../../api/user.swr';

/**
 * This component is used for performing bulk actions on a set of questions.
 */

interface BulkActionDrawerParams {
  questions: QuestionDto[];
  action: string;
  open: boolean;
  close: () => void;
  handleSubmit: (payload: any) => void;
}

export const BulkActionDrawer: FC<BulkActionDrawerParams> = (props) => {
  const users = useUsers();
  const [availableUsers, setAvailableUsers] = useState<UserDto[]>([]);
  const [assignedUser, setAssignedUser] = useState<string>('');
  const [reviewStatus, setReviewStatus] = useState<string>('');
  const [targetLanguage, setTargetLanguage] = useState<string>('');

  const labelQuestionSuffix = `${props.questions?.length} question${
    props.questions?.length > 1 ? 's' : ''
  }`;

  const actionMap = {
    assign: {
      title: 'Assign User',
      description: 'Assign a user to the selected questions.',
      buttonLabel: `${
        Number(assignedUser) == -1 ? 'Unassign' : 'Assign'
      } ${labelQuestionSuffix}`,
      buttonColor: 'primary',
    },
    status: {
      title: 'Update Review Status',
      description: 'Update the review status for selected questions.',
      buttonLabel: `Update ${labelQuestionSuffix}`,
      buttonColor: 'primary',
    },
    translate: {
      title: 'Translate Answers',
      description: 'Translate multiple answers into another language.',
      buttonLabel: `Update ${labelQuestionSuffix}`,
      buttonColor: 'primary',
    },
    delete: {
      title: 'Delete Questions',
      description:
        'Delete multiple questions from the questionnaire. Careful: this operation cannot be undone!',
      buttonLabel: `Delete ${labelQuestionSuffix}`,
      buttonColor: 'error',
    },
  };

  useEffect(() => {
    if (users.data) {
      setAvailableUsers(users.data);
    }
  }, [users.data]);

  const reviewButtons = [
    { value: 'reviewed', label: 'Reviewed' },
    { value: 'notReviewed', label: 'Not Reviewed' },
  ];

  const enableSubmit =
    (props.action == 'assign' && assignedUser !== '') ||
    (props.action == 'status' && reviewStatus !== '') ||
    (props.action == 'translate' && targetLanguage !== '') ||
    props.action == 'delete';

  return (
    <BottomDrawer open={props.open} close={props.close}>
      <Box sx={{ display: 'flex', flexDirection: 'column' }}>
        <Stack direction="column" sx={{ py: '1rem', width: '100%' }}>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            sx={{ width: '100%', mb: '0.5rem' }}
          >
            <Typography variant="h6" sx={{ fontWeight: '600' }}>
              Bulk Actions: {actionMap[props.action]?.title}
            </Typography>

            <IconButton
              aria-label="close"
              onClick={props.close}
              sx={{
                color: (theme) => theme.palette.grey[500],
                mr: '-8px',
              }}
            >
              <CloseIcon />
            </IconButton>
          </Stack>

          <Typography variant="body2">
            {actionMap[props.action]?.description}
          </Typography>

          <Box sx={{ mt: 1 }}>
            {props.action === 'assign' && (
              <UserPickList
                availableUsers={availableUsers}
                multiple={false}
                selectedUser={assignedUser}
                updateSelectedUsers={(id: string) =>
                  setAssignedUser(id == assignedUser ? '' : id)
                }
                includeUnassign={true}
              />
            )}
            {props.action === 'status' && (
              <StyledToggleButtonGroup
                exclusive={true}
                value={reviewStatus}
                onChange={(
                  e: React.MouseEvent<HTMLElement, MouseEvent>,
                  status: string,
                ) => {
                  e.preventDefault();
                  setReviewStatus(status);
                }}
                sx={{ mt: 2, mb: 3 }}
              >
                {reviewButtons.map(({ value, label }) => {
                  return (
                    <ToggleButton key={label} value={value} sx={{ px: 2 }}>
                      {label}
                    </ToggleButton>
                  );
                })}
              </StyledToggleButtonGroup>
            )}
            {props.action === 'translate' && (
              <LanguagePicker
                selected={targetLanguage}
                onSelect={setTargetLanguage}
                size="small"
                sx={{ mb: 3 }}
              />
            )}
            {props.action === 'delete' && (
              <Box sx={{ minHeight: 3 }}>&nbsp;</Box>
            )}
          </Box>

          <Stack direction="row" justifyContent="flex-end" sx={{ mt: 2 }}>
            <Button
              sx={{ width: 'fit-content', px: 2 }}
              variant="contained"
              size="small"
              disabled={!enableSubmit}
              onClick={() => {
                const payload = {};
                if (props.action == 'assign') {
                  payload['selectedUser'] =
                    Number(assignedUser) == -1 ? null : assignedUser;
                } else if (props.action == 'status') {
                  payload['status'] = reviewStatus;
                } else if (props.action == 'translate') {
                  payload['targetLanguage'] = targetLanguage;
                }

                props.handleSubmit(payload);

                setAssignedUser('');
                setReviewStatus('');
                setTargetLanguage('');
              }}
              color={actionMap[props.action]?.buttonColor}
            >
              {actionMap[props.action]?.buttonLabel}
            </Button>
          </Stack>
        </Stack>
      </Box>
    </BottomDrawer>
  );
};
