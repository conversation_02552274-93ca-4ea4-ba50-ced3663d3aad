import { QuestionDto } from '../../../../api/question.api';
import { buildFilterStore } from '../../useFilterStore';

export type QuestionFilters = {
  status: 'all' | 'notReviewed' | 'reviewed';
  searchQuery: string | undefined;
  groupingIds: number[];
  assigneeIds: number[];
  confidence: string[];
};

const getDefaultQuestionState = (): QuestionFilters => ({
  status: 'all',
  searchQuery: undefined,
  groupingIds: [],
  assigneeIds: [],
  confidence: [],
});

export const confidenceFilterOptions = [
  {
    value: 'high',
    label: 'High',
  },
  {
    value: 'medium',
    label: 'Medium',
  },
  {
    value: 'low',
    label: 'Low',
  },
  {
    value: 'no_answer',
    label: 'No Answer',
  },
];

export const confidenceScoreMap = {
  no_answer: -1,
  low: 1,
  medium: 2,
  high: 3,
};

export const { useLocalStore, useSharedStore } =
  buildFilterStore<QuestionFilters>(getDefaultQuestionState);

export const getSortedAssigneeIdsFromQuestions = (
  questions: QuestionDto[],
): number[] => {
  const assigneeIdNameMap: Record<string, string> = {};
  let assignees = questions
    .filter((q) => q.assignee_id != null)
    .map((q) => {
      assigneeIdNameMap[String(q.assignee_id)] = q.assignee_name || '';
      return q.assignee_id;
    });

  // Filter out dupes
  return Array.from(new Set(assignees)).sort((a, b) => {
    const aName = assigneeIdNameMap[String(a)].toLowerCase();
    const bName = assigneeIdNameMap[String(b)].toLowerCase();
    return aName < bName ? -1 : 1;
  });
};

export const getInProgressJobs = (job_log_data) => {
  if (!job_log_data) {
    return [];
  }

  // Look in the job_log_data field for any records where:
  // state = 'processing' AND duration.start >= 6 hours ago
  // (do the start comparison as chance a job failed and didn't clean up)
  return job_log_data?.filter((log) => {
    const sixHoursAgo = new Date();
    sixHoursAgo.setHours(sixHoursAgo.getHours() - 24);

    // start was written by Python time.time() which is in seconds
    // (but just check anyway to be safe)
    const multiplier = Number(log.duration.start) > 10000000000 ? 1 : 1000;
    const jobLogStartTs = Number(log.duration.start) * multiplier;

    return log.state === 'processing' && jobLogStartTs >= sixHoursAgo.getTime();
  });
};
