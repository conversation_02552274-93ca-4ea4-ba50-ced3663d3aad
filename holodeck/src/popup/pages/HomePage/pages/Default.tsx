import CheckIcon from '@mui/icons-material/Check';
import PersonIcon from '@mui/icons-material/Person';
import TuneIcon from '@mui/icons-material/Tune';

import {
  Avatar,
  Badge,
  Box,
  Button,
  Card,
  Chip,
  CircularProgress,
  IconButton,
  MenuItem,
  Stack,
  Typography,
  styled,
  useTheme,
} from '@mui/material';
import React, { FC, useEffect, useRef, useState } from 'react';

import { clearAuthConfig } from 'utils/auth0';

import { AccountMenuToggle } from 'atomic-design/molecules/AccountMenuToggle';
import { useRequireLoggedIn } from 'popup/hooks/useRequireLoggedIn.hook';
import { useRootStore } from 'popup/state/useRootStore';
import { StatusFilterGroup } from '../StatusFilterGroup';
import {
  OWNER_ASSIGNEE_FILTER_DEFAULT,
  QuestionnaireFilters,
  REVIEWED_FILTER_DEFAULT,
  useFilterStore,
} from '../useFilterStore';

import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import { QuestionnaireDto } from 'popup/api/questionnaires.api';
import { useRfxProjects } from 'popup/hooks/useRfxProjects.hook';
import { MenuDropdown } from '../../../../atomic-design/atoms/MenuDropdown';
import { GreyTooltip } from '../../../../atomic-design/atoms/StyledTooltips';
import { FabScrollUp } from '../../../../atomic-design/molecules/FabScrollUp';
import {
  QuestionnaireCard,
  isQuestionnaireAnalyzing,
  questionnaireHasError,
} from '../../../../atomic-design/molecules/QuestionnaireCard/QuestionnaireCard';
import {
  QualificationDrawer,
  RFPCard,
  Rfp,
} from '../../../../components/EndToEndRfp';
import { DefaultFiltersDrawer } from './DefaultFiltersDrawer';
import { DefaultSettingsDrawer } from './DefaultSettingsDrawer';
import { GeneralErrorDrawer } from './GeneralErrorDrawer';
import { SearchBar } from './SearchBar';
import {
  applySort,
  filterE2ERfps,
  filterQuestionnaires,
  statusMapping,
  statusMappingRfp,
} from './frontendFilter';

import critter_glasses_png from 'assets/critter_glasses.png';
import { Row } from '../../../../components/copied';
import { USER_SETTING_E2E_RFP_EXTENSION_ENABLED } from '../../../../utils/constants';
import {
  getUserPreferences,
  saveUserPreferences,
} from '../../../../utils/helpers';
import { useSalesforceUser } from '../../../api/salesforce.swr';
import { useSlackSetting } from '../../../api/slack.swr';
import { getClientSetting } from '../../../api/user.api';
import { useCurrentUser } from '../../../api/user.swr';

const PageHeader = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  fontSize: '1.5rem',
  lineHeight: '1.75rem',
  textAlign: 'center',
  marginBottom: '1rem',
}));

const PageBodyText = styled(Typography)(({ theme }) => ({
  fontWeight: 400,
  fontSize: '1rem',
  lineHeight: '1.25rem',
  maxWidth: '300px',
  color: theme.palette.grey[600],
  textAlign: 'center',
}));

const MAX_ITEMS_TO_SHOW = 100;

export const DefaultPage: FC = () => {
  const store = useRootStore();
  const filterStore = useFilterStore();
  const theme = useTheme();

  useRequireLoggedIn();
  const {
    questionnaires: rfxQuestionnaires,
    e2eRfps: rfxE2ERfps,
    isLoading: rfxLoading,
    error: rfxError,
    refetch: refetchRfx,
  } = useRfxProjects();
  const [questionnaires, setQuestionnaires] = useState<QuestionnaireDto[]>([]);
  const [E2ERfps, setE2ERfps] = useState<Rfp[]>([]);

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>();
  const [filterDrawerOpen, setFilterDrawerOpen] = useState(false);
  const [settingsDrawerOpen, setSettingsDrawerOpen] = useState(false);

  const [showQualificationE2ERfp, setShowQualificationE2ERfp] = useState<
    Rfp | undefined
  >();
  const [generalE2ERfpError, setGeneralE2ERfpError] = useState<{
    questionnaire?: QuestionnaireDto;
    rfp?: Rfp;
    error: string;
  }>(undefined);
  const loadedAtTs = useRef(new Date().getTime());

  const currentUserSwr = useCurrentUser();
  const slackSetting = useSlackSetting();
  const salesforceUser = useSalesforceUser();

  useEffect(() => {
    (async () => {
      try {
        if (store.auth.status !== 'success') {
          return;
        }

        // Set filters to cached user preferences
        const userPreferences = await getUserPreferences();
        if (userPreferences) {
          filterStore.setFilters(userPreferences);
        }

        // Load E2E setting only once on initial load
        const e2eRfpExtensionEnabled = await getClientSetting(
          USER_SETTING_E2E_RFP_EXTENSION_ENABLED,
        );
        store.setE2EEnabled(Boolean(e2eRfpExtensionEnabled.value));
      } catch (e: any) {
        console.error(
          `[loadUserPreferences] error, store state is ${store.auth.status}`,
          e,
        );
      }
    })();
  }, [store.auth.status]);

  // Separate effect to update questionnaires and E2ERfps when they change
  useEffect(() => {
    try {
      // Update questionnaires from rfxQuestionnaires
      setQuestionnaires(rfxQuestionnaires || []);

      // Update E2ERfps from rfxE2ERfps
      setE2ERfps(rfxE2ERfps || []);

      // Clear errors and set loading state
      setError(undefined);
      setLoading(false);
    } catch (e: any) {
      console.error('[updateQuestionnaires] error:', e);
      setError(e?.message || 'Unknown error');
      setQuestionnaires([]);
      setE2ERfps([]);
    }
  }, [rfxQuestionnaires, rfxE2ERfps]);

  useEffect(() => {
    if (currentUserSwr.data !== undefined) {
      store.setUser(currentUserSwr.data);
    }
  }, [currentUserSwr.data]);

  useEffect(() => {
    if (slackSetting.data) {
      store.setSlackSettings({
        collaboration_channel: slackSetting.data,
      });
    }
  }, [slackSetting.data]);

  useEffect(() => {
    if (salesforceUser.data) {
      store.setSalesforceUser(salesforceUser.data);
    }
  }, [salesforceUser.data]);

  const onLogout = async () => {
    await chrome.identity.clearAllCachedAuthTokens();
    await clearAuthConfig();
    store.setAuth({
      status: 'idle',
    });
    window.location.reload();
  };

  const adjustFilters = () => {
    setFilterDrawerOpen(true);
  };

  const showSettingsDrawer = () => {
    setSettingsDrawerOpen(true);
  };

  const handleSetSortBy = (value) => {
    const newValue = { sortBy: value };
    filterStore.setFilters(newValue);

    // Save as user preferences
    saveUserPreferences({ ...filterStore.filters, ...newValue });
  };

  const handleSetSortDirection = (value) => {
    const newValue = { sortDirection: value };
    filterStore.setFilters(newValue);

    // Save as user preferences
    saveUserPreferences({ ...filterStore.filters, ...newValue });
  };

  const handleQuestionnaireStatusUpdated = async (
    updatedQuestionnaire: QuestionnaireDto,
  ) => {
    setQuestionnaires(
      questionnaires.map((q) => {
        if (q.content_id === updatedQuestionnaire.content_id) {
          return updatedQuestionnaire;
        }
        return q;
      }),
    );
  };

  const handleSettingsChange = (settings: any[]) => {
    // No-op for now.
  };

  const filteredQuestionnaires = filterQuestionnaires(
    questionnaires,
    filterStore.filters,
    store.user,
  );

  const filteredE2ERfps = filterE2ERfps(
    E2ERfps,
    filterStore.filters,
    store.user,
  );

  // For the "Filters applied: showing [x] out of [y]" message:
  // the "filteredQuestionnaires" array is the one to use to display [y]
  const currentStatusViewQuestionnaires = questionnaires.filter((q) => {
    if (
      filterStore.filters.status.includes('all') ||
      filterStore.filters.status.some((status) =>
        statusMapping[status].includes(q.project_content_status),
      )
    ) {
      return true;
    } else {
      return false;
    }
  });

  const currentStatusViewE2ERfps = E2ERfps.filter((r) => {
    if (
      filterStore.filters.status.includes('all') ||
      filterStore.filters.status.some((status) =>
        statusMappingRfp[status].includes(r.status),
      )
    ) {
      return true;
    } else {
      return false;
    }
  });

  type SortByOptions = Record<
    Exclude<QuestionnaireFilters['sortBy'], undefined>,
    string
  >;
  const sortByOptions: SortByOptions = {
    created_date: 'Created Date',
    due_date: 'Due Date',
    created_by_name: 'Creator',
    label: 'Title',
  };

  const currentSortBy = sortByOptions[filterStore.filters['sortBy']];

  type SortDirectionOptions = Record<
    Exclude<QuestionnaireFilters['sortDirection'], undefined>,
    string
  >;
  const sortDirectionOptions: SortDirectionOptions = {
    asc: 'Sort ↑',
    desc: 'Sort ↓',
  };

  const currentSortDirection =
    sortDirectionOptions[filterStore.filters['sortDirection']];

  const currentUserName: string | null = currentUserSwr.data?.name || null;

  // Hack until we can implement custom function
  // const hasFilters = filterStore.hasAdvancedFiltersSet();
  const hasFilters =
    filterStore.filters.reviewed !== REVIEWED_FILTER_DEFAULT ||
    filterStore.filters.ownerAssigneeFilter !== OWNER_ASSIGNEE_FILTER_DEFAULT;

  // As of PR 949, a user's default filter will show their own stuff (vs. all)
  const viewingOwnData =
    filterStore.filters.ownerAssigneeFilter === OWNER_ASSIGNEE_FILTER_DEFAULT;

  const noProjectsFoundMessage =
    questionnaires.length > 0 || E2ERfps.length > 0
      ? 'Please adjust your filters or search term.'
      : 'Create one to start answering questions!';

  const combinedList: (
    | (QuestionnaireDto & { recordType: 'questionnaire' | 'e2e' })
    | (Rfp & { recordType: 'questionnaire' | 'e2e' })
  )[] = [
    ...filteredQuestionnaires.map((q) => {
      q['recordType'] = 'questionnaire';
      return q as QuestionnaireDto & { recordType: 'questionnaire' | 'e2e' };
    }),
    ...filteredE2ERfps.map((e) => {
      e['recordType'] = 'e2e';
      return e as Rfp & { recordType: 'questionnaire' | 'e2e' };
    }),
  ];

  const combinedListSorted = applySort(
    combinedList,
    filterStore.filters.sortBy,
    filterStore.filters.sortDirection,
  );

  // Check for pending E2E RFPs
  const pendingE2ERfps = filteredE2ERfps.filter(
    (rfp) =>
      (rfp.status === 'in_review_outline' ||
        rfp.status === 'in_review_response') &&
      !(rfp.details?.agentParams?.error?.length > 0),
  );

  const pendingQuestionnaires = filteredQuestionnaires.filter(
    (questionnaire) =>
      isQuestionnaireAnalyzing(questionnaire) &&
      !questionnaireHasError(questionnaire),
  );

  // There shouldn't be any E2E that last longer than 30 mins.
  // And if there is, good chance the user isn't staring at the Chrome Ext.
  const MAX_REFRESH_CHECK = 1000 * 60 * 30;
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Clear previous timeout if it exists
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
      refreshTimeoutRef.current = null;
    }

    // If there are pending questionnaires or E2E RFPs, set up a new timeout
    if (
      (pendingQuestionnaires.length > 0 || pendingE2ERfps.length > 0) &&
      new Date().getTime() - loadedAtTs.current < MAX_REFRESH_CHECK
    ) {
      refreshTimeoutRef.current = setTimeout(() => {
        refetchRfx();
      }, 10000);
    }

    // Clean up
    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, [pendingQuestionnaires, pendingE2ERfps, refetchRfx]);

  return (
    <Box
      sx={{
        backgroundColor: 'grey.200',
        width: '100%',
        padding: '1rem',
        boxSizing: 'border-box',
        minHeight: '100%',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: '1rem',
          width: '100%',
        }}
      >
        <Stack
          direction="row"
          gap="1rem"
          justifyContent={'space-between'}
          alignItems={'center'}
        >
          <SearchBar
            extraProps={{ sx: { borderRadius: 200, fontSize: '0.9rem' } }}
          />
          <AccountMenuToggle
            username={currentUserName || ''}
            onLogout={onLogout}
            onReload={() => window.location.reload()}
            showSettings={showSettingsDrawer}
          />
        </Stack>
        <Row gap="6px">
          <Button
            sx={{ fontWeight: '600', maxWidth: '100%' }}
            onClick={() => store.navigate({ name: 'addQuestionnaire' })}
          >
            + Create Project
          </Button>
        </Row>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            gap: '1rem',
          }}
        >
          <StatusFilterGroup />
          {hasFilters && (
            <IconButton onClick={() => setFilterDrawerOpen(true)}>
              <Badge
                variant="dot"
                color="error"
                anchorOrigin={{
                  vertical: 'top',
                  horizontal: 'right',
                }}
              >
                <TuneIcon />
              </Badge>
            </IconButton>
          )}
          {!hasFilters && (
            <IconButton onClick={() => setFilterDrawerOpen(true)}>
              <TuneIcon />
            </IconButton>
          )}
        </Box>
      </Box>

      <Box
        sx={{
          display: 'flex',
          gap: '1rem',
          flexDirection: 'column',
          mt: '1rem',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Stack direction="row" alignItems={'center'} sx={{ ml: '4px' }}>
            <Typography
              variant="body2"
              sx={{
                mr: 1,
                color: theme.palette.common.black,
                fontWeight: '500',
              }}
            >
              Projects
            </Typography>
            <Chip
              label={filteredQuestionnaires.length + filteredE2ERfps.length}
              size="small"
              variant="filled"
            />
          </Stack>
          <Box
            sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}
          >
            <MenuDropdown
              trigger={
                <Button
                  size="small"
                  variant="text"
                  sx={{
                    color: theme.palette.grey[700],
                    fontSize: '0.75rem',
                    fontWeight: '400',
                    textTransform: 'none',
                    minWidth: 'fit-content',
                    p: '4px',
                  }}
                >
                  {currentSortDirection}
                </Button>
              }
            >
              {(handleClose) =>
                Object.entries(sortDirectionOptions).map((keyValue) => {
                  const value = keyValue[0] as keyof SortDirectionOptions;
                  const label =
                    value == 'asc' ? 'Sort Ascending ↑' : 'Sort Descending ↓';
                  const selected =
                    filterStore.filters['sortDirection'] === value;

                  const onClose = () => {
                    handleClose();
                    handleSetSortDirection(value);
                  };

                  return (
                    <MenuItem
                      onClick={() => onClose()}
                      key={value}
                      sx={{
                        fontWeight: selected ? 600 : 400,
                        color: selected
                          ? theme.palette.primary.main
                          : theme.palette.grey[700],
                      }}
                    >
                      {label}
                    </MenuItem>
                  );
                })
              }
            </MenuDropdown>

            <MenuDropdown
              trigger={
                <Button
                  size="small"
                  variant="text"
                  sx={{
                    color: theme.palette.common.black,
                    fontSize: '0.8rem',
                    fontWeight: '500',
                    textTransform: 'none',
                  }}
                >
                  {currentSortBy} <ArrowDropDownIcon />
                </Button>
              }
            >
              {(handleClose) =>
                Object.entries(sortByOptions).map((keyValue) => {
                  const value = keyValue[0] as keyof SortByOptions;
                  const label = keyValue[1];
                  const selected = filterStore.filters['sortBy'] === value;

                  const onClose = () => {
                    handleClose();
                    handleSetSortBy(value);
                  };

                  return (
                    <MenuItem
                      onClick={() => onClose()}
                      key={value}
                      sx={{
                        fontWeight: selected ? 600 : 400,
                        color: selected
                          ? theme.palette.primary.main
                          : theme.palette.grey[700],
                      }}
                    >
                      {label}
                      {selected && (
                        <CheckIcon
                          sx={{
                            ml: '1rem',
                            mt: '-4px',
                            color: `${theme.palette.primary.main} !important`,
                          }}
                        />
                      )}
                    </MenuItem>
                  );
                })
              }
            </MenuDropdown>
          </Box>
        </Box>

        {viewingOwnData ||
        filteredQuestionnaires.length <
          currentStatusViewQuestionnaires.length ||
        filteredE2ERfps.length < currentStatusViewE2ERfps.length ? (
          <Stack
            direction="row"
            alignItems="center"
            justifyContent="flex-end"
            gap={1}
            sx={{ mr: '0.5rem' }}
          >
            {viewingOwnData ||
            filteredQuestionnaires.length <
              currentStatusViewQuestionnaires.length ||
            filteredE2ERfps.length < currentStatusViewE2ERfps.length ? (
              <Typography
                variant="body2"
                sx={{
                  fontSize: '0.75rem',
                  fontStyle: 'italic',
                  color: theme.palette.grey[500],
                }}
              >
                Filters applied: showing{' '}
                {combinedListSorted.length > MAX_ITEMS_TO_SHOW
                  ? `first ${MAX_ITEMS_TO_SHOW}`
                  : filteredQuestionnaires.length + filteredE2ERfps.length}{' '}
                out of{' '}
                {currentStatusViewQuestionnaires.length +
                  currentStatusViewE2ERfps.length}
              </Typography>
            ) : combinedListSorted.length > MAX_ITEMS_TO_SHOW ? (
              <Typography
                variant="body2"
                sx={{
                  fontSize: '0.75rem',
                  fontStyle: 'italic',
                  color: theme.palette.grey[500],
                }}
              >
                Showing first {MAX_ITEMS_TO_SHOW} out of{' '}
                {combinedListSorted.length}
              </Typography>
            ) : (
              <div />
            )}
            {viewingOwnData ? (
              <GreyTooltip title="Showing questionnaires where you are the owner, or, where you have questions assigned to you.">
                <Avatar
                  sx={{
                    border: `2px solid ${theme.palette.grey[400]}`,
                    backgroundColor: '#ffffff00',
                    width: '28px',
                    height: '28px',
                    ml: '4px',
                  }}
                >
                  <PersonIcon
                    sx={{ fill: theme.palette.grey[400] }}
                    fontSize="small"
                  />
                </Avatar>
              </GreyTooltip>
            ) : (
              <div />
            )}
          </Stack>
        ) : combinedListSorted.length > MAX_ITEMS_TO_SHOW ? (
          <Stack
            direction="row"
            alignItems="center"
            justifyContent="flex-end"
            gap={1}
            sx={{ mr: '0.5rem' }}
          >
            <Typography
              variant="body2"
              sx={{
                fontSize: '0.75rem',
                fontStyle: 'italic',
                color: theme.palette.grey[500],
              }}
            >
              Showing first {MAX_ITEMS_TO_SHOW} out of{' '}
              {combinedListSorted.length}
            </Typography>
          </Stack>
        ) : null}

        {!loading && combinedListSorted.length > 0 && (
          <>
            {combinedListSorted.slice(0, MAX_ITEMS_TO_SHOW).map((rfp) => {
              if (rfp.recordType === 'e2e') {
                const e2eRfp = rfp as Rfp;
                return (
                  <RFPCard
                    rfp={e2eRfp}
                    key={e2eRfp.id + 'card'}
                    toggleViewQualification={(rfp) =>
                      setShowQualificationE2ERfp(rfp)
                    }
                    toggleViewError={(rfp, error) =>
                      setGeneralE2ERfpError({ rfp, error })
                    }
                    onUpdate={refetchRfx}
                  />
                );
              } else {
                const questionnaire: QuestionnaireDto = rfp as QuestionnaireDto;
                return (
                  <QuestionnaireCard
                    questionnaire={questionnaire}
                    key={questionnaire.content_id}
                    onUpdate={handleQuestionnaireStatusUpdated}
                    toggleViewError={(questionnaire, error) =>
                      setGeneralE2ERfpError({ questionnaire, error })
                    }
                  />
                );
              }
            })}
            {combinedList.length > 5 && <Box sx={{ minHeight: '55px' }} />}
          </>
        )}
        {filteredQuestionnaires.length == 0 && filteredE2ERfps.length == 0 && (
          <Card
            variant="outlined"
            sx={{ maxWidth: '100%', backgroundColor: '#fff' }}
          >
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                minHeight: '400px',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              {loading && <CircularProgress />}
              {!loading && (
                <>
                  <img src={critter_glasses_png} style={{ height: '100px' }} />
                  <Stack
                    direction="column"
                    alignItems="center"
                    justifyContent="center"
                    sx={{ p: '1rem', mb: '2rem' }}
                  >
                    <PageHeader>No Projects Found</PageHeader>
                    <PageBodyText>{noProjectsFoundMessage}</PageBodyText>
                    {(questionnaires.length > 0 || E2ERfps.length > 0) &&
                      hasFilters && (
                        <Button
                          variant="outlined"
                          color="warning"
                          onClick={adjustFilters}
                          sx={{ mt: '2rem', width: 'fit-content' }}
                        >
                          Adjust Filters
                        </Button>
                      )}
                  </Stack>
                </>
              )}
            </Box>
          </Card>
        )}

        <DefaultFiltersDrawer
          open={filterDrawerOpen}
          close={() => setFilterDrawerOpen(false)}
        />
        <DefaultSettingsDrawer
          open={settingsDrawerOpen}
          close={() => setSettingsDrawerOpen(false)}
          onSave={handleSettingsChange}
        />
        {showQualificationE2ERfp !== undefined && (
          <QualificationDrawer
            open={showQualificationE2ERfp !== undefined}
            close={() => setShowQualificationE2ERfp(undefined)}
            rfp={showQualificationE2ERfp}
            handleProceed={() => {
              setLoading(true);
              setTimeout(() => {
                window.location.reload();
              }, 250);
            }}
          />
        )}
        {generalE2ERfpError !== undefined && (
          <GeneralErrorDrawer
            open={generalE2ERfpError !== undefined}
            close={() => setGeneralE2ERfpError(undefined)}
            rfp={generalE2ERfpError['rfp']}
            questionnaire={generalE2ERfpError['questionnaire']}
            error={generalE2ERfpError['error']}
          />
        )}
      </Box>
      <FabScrollUp />
    </Box>
  );
};
