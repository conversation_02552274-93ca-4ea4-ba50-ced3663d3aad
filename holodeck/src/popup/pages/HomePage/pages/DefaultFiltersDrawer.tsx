import {
  Avatar,
  Button,
  Chip,
  FormControl,
  ListItemText,
  MenuItem,
  Stack,
  ToggleButton,
  Typography,
} from '@mui/material';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import React, { FC, useEffect, useState } from 'react';

import { saveUserPreferences } from '../../../../utils/helpers';
import { useUsers } from '../../../api/user.swr';
import { QuestionnaireFilters, useFilterStore } from '../useFilterStore';

import { BottomDrawer } from '../../../../atomic-design/atoms/BottomDrawer';
import { StyledToggleButtonGroup } from '../../../../atomic-design/atoms/StyledToggleButtonGroup';
import { TribbleAvatar } from '../../../../atomic-design/atoms/TribbleAvatar';

interface DefaultFilterParams {
  open: boolean;
  close: () => void;
}
export const DefaultFiltersDrawer: FC<DefaultFilterParams> = (props) => {
  const store = useFilterStore();
  const allUsers = useUsers();

  const [reviewedFilter, setReviewedFilter] = useState<
    'reviewed' | 'all' | 'under review'
  >(store.filters.reviewed || 'all');

  const [ownerAssigneeFilter, setOwnerAssigneeFilter] = useState<
    'all' | 'mine' | 'other'
  >(store.filters.ownerAssigneeFilter || 'all');

  const [selectedUserId, setSelectedUserId] = useState(
    store.filters.ownerAssigneeFilterOtherId || '',
  );

  useEffect(() => {
    setOwnerAssigneeFilter(store.filters.ownerAssigneeFilter || 'all');
    setSelectedUserId(store.filters.ownerAssigneeFilterOtherId || '');
    setReviewedFilter(store.filters.reviewed || 'all');
  }, [store.filters]);

  const handleChangeOwnerFilter = (
    e: React.MouseEvent<HTMLElement, MouseEvent>,
    ownerFilter: QuestionnaireFilters['ownerAssigneeFilter'],
  ) => {
    e.preventDefault();
    setOwnerAssigneeFilter(ownerFilter);
  };

  const handleChangeReviewedFilter = (
    e: React.MouseEvent<HTMLElement, MouseEvent>,
    reviewed: QuestionnaireFilters['reviewed'],
  ) => {
    e.preventDefault();
    setReviewedFilter(reviewed);
  };

  const resetFilterDrawer = () => {
    setReviewedFilter('all');
    setOwnerAssigneeFilter('all');
    setSelectedUserId('');

    const newFilterValues = {
      reviewed: 'all',
      ownerAssigneeFilter: 'all',
      ownerAssigneeFilterOtherId: '',
    } as QuestionnaireFilters;

    store.setFilters(newFilterValues);
    saveUserPreferences({ ...store.filters, ...newFilterValues });
  };

  const applyFilters = () => {
    const hasSelectedUserId = !!selectedUserId && selectedUserId != '';

    // Set to all if no user was selected w other
    if (ownerAssigneeFilter == 'other' && !hasSelectedUserId) {
      setOwnerAssigneeFilter('all');
    }

    const newFilterValues = {
      reviewed: reviewedFilter,
      ownerAssigneeFilter:
        ownerAssigneeFilter == 'other' && !hasSelectedUserId
          ? 'all'
          : ownerAssigneeFilter,
      ownerAssigneeFilterOtherId:
        ownerAssigneeFilter === 'other' && hasSelectedUserId
          ? selectedUserId
          : undefined,
    };

    store.setFilters(newFilterValues);
    saveUserPreferences({ ...store.filters, ...newFilterValues });

    props.close();
  };

  const resetFilters = () => {
    store.setFilters({
      reviewed: 'all',
      ownerAssigneeFilter: 'all',
      ownerAssigneeFilterOtherId: undefined,
    });
    resetFilterDrawer();
    props.close();
  };

  const ownerButtons = [
    {
      value: 'mine',
      label: "I'm Owner / Assignee",
    },
    {
      value: 'all',
      label: 'All',
    },
    {
      value: 'other',
      label: 'Other',
    },
  ];

  const reviewedButtons = [
    {
      value: 'all',
      label: 'All',
    },
    {
      value: 'reviewed',
      label: 'Reviewed',
    },
    {
      value: 'under review',
      label: 'Under Review',
    },
  ];

  const handleChangeUserFilter = (e: SelectChangeEvent<string>) => {
    e.preventDefault();
    setSelectedUserId(e.target.value);
  };

  const userPicker = () => {
    if (allUsers?.data?.length > 0) {
      const selectedUser = allUsers.data.find(
        (u) => u.id == Number(selectedUserId),
      );

      return (
        <FormControl fullWidth size="small">
          <Select
            value={selectedUser ? selectedUserId : ''}
            onChange={handleChangeUserFilter}
            renderValue={(selected) => {
              if (selectedUser) {
                return (
                  <Chip
                    key={`selected_assignee${selectedUser.id}`}
                    label={selectedUser.name}
                    size="small"
                    color="primary"
                    variant="outlined"
                    sx={{ mr: '0.5rem' }}
                    onDelete={() => setSelectedUserId('')}
                    onMouseDown={(e) => e.stopPropagation()}
                  />
                );
              } else {
                return <></>;
              }
            }}
            MenuProps={{
              sx: {
                maxHeight: '400px',
              },
              anchorOrigin: {
                vertical: 'bottom',
                horizontal: 'left',
              },
            }}
          >
            {allUsers.data
              .sort((a, b) =>
                a.name.toLowerCase() < b.name.toLowerCase() ? -1 : 1,
              )
              .map((user) => {
                return (
                  <MenuItem value={user.id} key={`user_${user.id}`}>
                    <div style={{ marginRight: '1rem' }}>
                      {user.picture && user.picture != '' && (
                        <Avatar
                          src={user.picture}
                          sx={{ height: 24, width: 24 }}
                        >
                          {user.name}
                        </Avatar>
                      )}
                      {(!user.picture || user.picture == '') && (
                        <TribbleAvatar
                          username={user.name}
                          height="24px"
                          width="24px"
                        />
                      )}
                    </div>
                    <ListItemText
                      primary={user.name}
                      sx={{ '& .MuiTypography-root': { fontSize: '0.85rem' } }}
                    />
                  </MenuItem>
                );
              })}
          </Select>
        </FormControl>
      );
    } else {
      return <></>;
    }
  };

  // Hack until we can implement custom function
  // const hasFilters = filterStore.hasAdvancedFiltersSet();
  const hasFilters =
    store.filters.reviewed !== 'all' ||
    store.filters.ownerAssigneeFilter != 'all';

  const contents = (
    <Stack direction="column" sx={{ py: '2rem' }}>
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        sx={{ width: '100%' }}
      >
        <Typography variant="h6" sx={{ fontWeight: '600' }}>
          More Filters
        </Typography>
        {hasFilters && (
          <Button
            size="small"
            variant="outlined"
            color="warning"
            onClick={resetFilters}
            sx={{ width: 'fit-content' }}
          >
            Reset Filters
          </Button>
        )}
        {!hasFilters && <></>}
      </Stack>
      <Typography
        variant="body2"
        color="primary"
        sx={{
          textTransform: 'uppercase',
          fontWeight: '500',
          pb: '0.25rem',
          mt: '2rem',
        }}
      >
        Project to Show
      </Typography>
      <Typography variant="body2" sx={{ pb: '0.75rem' }}>
        Note: the owner / assignee filter will get applied by default when you
        click into a project.
      </Typography>
      <StyledToggleButtonGroup
        exclusive={true}
        value={ownerAssigneeFilter}
        onChange={handleChangeOwnerFilter}
      >
        {ownerButtons.map(({ value, label }) => (
          <ToggleButton key={`owner_${value}`} value={value}>
            {label}
          </ToggleButton>
        ))}
      </StyledToggleButtonGroup>
      {ownerAssigneeFilter == 'other' && (
        <Stack direction="column" sx={{ pl: '1rem', mt: '1.5rem', mb: '1rem' }}>
          <Typography variant="body2" sx={{ mb: '0.5rem' }}>
            Where the owner or assignee is:
          </Typography>
          {userPicker()}
        </Stack>
      )}
      <Typography
        variant="body2"
        color="primary"
        sx={{
          textTransform: 'uppercase',
          fontWeight: '500',
          pb: '0.75rem',
          mt: '2rem',
        }}
      >
        Review Status
      </Typography>
      <StyledToggleButtonGroup
        exclusive={true}
        value={reviewedFilter}
        onChange={handleChangeReviewedFilter}
      >
        {reviewedButtons.map(({ value, label }) => (
          <ToggleButton key={`review_${value}`} value={value}>
            {label}
          </ToggleButton>
        ))}
      </StyledToggleButtonGroup>
    </Stack>
  );

  return (
    <BottomDrawer open={props.open} close={props.close}>
      <Stack direction="column">
        {contents}
        <Stack direction="row" justifyContent="center" sx={{ width: '100%' }}>
          <Button onClick={applyFilters}>Apply Filters</Button>
        </Stack>
      </Stack>
    </BottomDrawer>
  );
};
