import { Box, SxProps } from '@mui/material';
import Typography, { TypographyTypeMap } from '@mui/material/Typography';
import React, { FC } from 'react';

type SectionHeaderProps = {
  children: React.ReactNode;
  required?: boolean;
  explainer?: string;
  variant?: TypographyTypeMap['props']['variant'];
  headerColor?: TypographyTypeMap['props']['color'];
  sx?: SxProps;
  hideOptionalText?: boolean;
};
export const SectionHeader: FC<SectionHeaderProps> = ({
  children,
  required,
  explainer,
  variant,
  headerColor,
  sx,
  hideOptionalText,
}) => {
  return (
    <Box
      sx={{ ...sx, display: 'flex', flexDirection: 'column', gap: '0.25rem' }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          gap: '0.5rem',
          alignItems: 'center',
        }}
      >
        <Typography
          variant="h6"
          fontWeight="bold"
          color={headerColor ?? ''}
          sx={{ display: 'flex', flexDirection: 'row', gap: '0' }}
        >
          {children}
        </Typography>
        {required ? (
          <Typography
            variant={variant ?? 'h6'}
            sx={{ color: 'red', fontWeight: 'bold' }}
          >
            *
          </Typography>
        ) : hideOptionalText ? null : (
          <Typography variant="body2" sx={{ color: 'grey.600', mt: '1px' }}>
            (Optional)
          </Typography>
        )}
      </Box>
      {explainer && (
        <Typography variant="body2" sx={{ color: 'grey.600', mb: '4px' }}>
          {explainer}
        </Typography>
      )}
    </Box>
  );
};
