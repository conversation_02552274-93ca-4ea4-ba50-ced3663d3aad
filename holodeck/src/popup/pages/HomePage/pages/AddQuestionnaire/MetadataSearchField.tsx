import { Stack, Typography } from '@mui/material';
import React, { FC } from 'react';
import {
  TreeSearchField,
  TreeSearchFieldProps,
} from '../../../../../atomic-design/molecules/TreeSearchField/TreeSearchField';
import { MetadataFilterTypeDto } from '../../../../api/metadata.api';

type MetadataSearchFieldProps = {
  onChange: (newValue: string[]) => void;
  selectedValues: string[];
  availableMetadataFilters: MetadataFilterTypeDto[];
};
export const MetadataSearchField: FC<MetadataSearchFieldProps> = ({
  selectedValues,
  onChange,
  availableMetadataFilters,
}) => {
  const availableMetadataFiltersMap = availableMetadataFilters.reduce(
    (acc, mdf) => {
      return { ...acc, [mdf.id]: mdf };
    },
    {},
  );

  let selectedTypeIds = {};
  availableMetadataFilters.forEach((mdf_type) => {
    mdf_type.filter_values
      .filter((filter) => filter.is_active)
      .forEach((filter) => {
        selectedValues.forEach((val) => {
          if (Number(val) == filter.id) {
            selectedTypeIds[mdf_type.id] = selectedTypeIds[mdf_type.id]
              ? selectedTypeIds[mdf_type.id].concat(val)
              : [val];
          }
        });
      });
  });

  // map the selected metadata ids to the interface that the TreeSearchField expects
  let mappedMetadata: TreeSearchFieldProps['options'] = availableMetadataFilters
    .filter((group) => {
      // Only show a group if there are active filters in it
      return group.filter_values.filter((mdf) => mdf.is_active).length > 0;
    })
    .map((group) => ({
      id: '' + group.id,
      label: group.name,
      type: 'group',
      children: group.filter_values
        .filter((filter) => filter.is_active)
        .map((filter) => {
          return {
            id: '' + filter.id,
            label: filter.value,
            type: 'option',
          };
        }),
    }));

  let permutations = 1;
  const variantTypeNames = [];
  const exampleVariant = []; // For the warning message

  Object.keys(selectedTypeIds).forEach((type_id) => {
    const values = selectedTypeIds[type_id];

    // With the distinct_answer flag introduced, we only create variants
    // if that flag is true
    if (availableMetadataFiltersMap[type_id].distinct_answer) {
      permutations *= values.length;
      if (values.length >= 1) {
        variantTypeNames.push(availableMetadataFiltersMap[type_id].name);
        exampleVariant.push(
          availableMetadataFiltersMap[type_id].filter_values.find(
            (f) => f.id == values[0],
          )?.value,
        );
        return true;
      }
    }
    return false;
  });

  // If > 1 answer version is gonna be generated per question,
  // give a warning to the user: either it's multiple values within the same MDF type,
  // or, it's multiple versions across multiple MDF types
  if (permutations > 1) {
    return (
      <Stack direction="column" gap="1rem">
        {variantTypeNames.length == 1 && (
          <Typography variant="body1" color="error" sx={{ fontWeight: 400 }}>
            ⚠️ Each question will generate{' '}
            <span style={{ fontWeight: 600 }}>{permutations}</span> answer
            versions: one for each{' '}
            <span style={{ fontWeight: 600 }}>{variantTypeNames[0]}</span>{' '}
            selected.
          </Typography>
        )}
        {variantTypeNames.length > 1 && (
          <Typography variant="body1" color="error" sx={{ fontWeight: 400 }}>
            ⚠️ Each question will generate{' '}
            <span style={{ fontWeight: 600 }}>{permutations}</span> answer
            versions: one for each combination of values from{' '}
            <span style={{ fontWeight: 600 }}>
              {variantTypeNames.join(', ')}
            </span>
            .
            <br />
            <br />
            For example, an answer version will be generated for{' '}
            <span style={{ fontWeight: 600 }}>
              {exampleVariant.join(', ')}.
            </span>
          </Typography>
        )}
        <TreeSearchField
          selectedValues={selectedValues}
          onChange={onChange}
          options={mappedMetadata}
          hideSearchBar={true}
        />
      </Stack>
    );
  } else {
    return (
      <TreeSearchField
        selectedValues={selectedValues}
        onChange={onChange}
        options={mappedMetadata}
        hideSearchBar={true}
      />
    );
  }
};
