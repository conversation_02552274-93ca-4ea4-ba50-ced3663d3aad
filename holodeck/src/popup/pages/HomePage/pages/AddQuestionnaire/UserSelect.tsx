import { Autocomplete, TextField } from '@mui/material';
import React, { FC } from 'react';
import { UserDto } from '../../../../api/user.api';

type UserSelectProps = {
  availableUsers: UserDto[];
  selectedUserId: number | null | undefined;
  onChange: (value: number | null) => void;
};
export const UserSelect: FC<UserSelectProps> = ({
  availableUsers,
  selectedUserId,
  onChange,
}) => {
  const getUserById = (userId: number) =>
    availableUsers.find((user) => user.id === userId);

  const getUsernameFromId = (userId: number): string => {
    if (!userId) {
      return 'Error: Unknown User';
    }

    if (!availableUsers) {
      return 'User id: ' + userId;
    }

    const match = getUserById(userId);

    if (match?.name) {
      return match.name;
    }

    return 'User id: ' + userId;
  };

  return (
    <Autocomplete<number>
      id="ownerId"
      options={availableUsers.map((user) => user.id)}
      getOptionLabel={(userId) => getUsernameFromId(userId)}
      // renderOption={(props, userId) => (getUsernameFromId(userId))}
      // renderInput={(params) => <TextField {...params} label="Owner" />}
      renderInput={(params) => (
        <TextField {...params} label="Select Owner" variant="standard" />
      )}
      onChange={(e, v) => onChange(v)}
      value={selectedUserId}
    />
  );
};
