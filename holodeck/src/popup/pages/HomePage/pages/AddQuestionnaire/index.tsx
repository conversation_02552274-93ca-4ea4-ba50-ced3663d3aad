import {
  Alert,
  Avatar,
  Box,
  Button,
  Checkbox,
  Chip,
  CircularProgress,
  FormControl,
  FormHelperText,
  ListItemText,
  MenuItem,
  Stack,
  TextField,
  Typography,
  useTheme,
} from '@mui/material';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import React, { ChangeEvent, useEffect, useState } from 'react';

import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import dayJs, { Dayjs } from 'dayjs';

import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import { v4 as uuidv4 } from 'uuid';
import { PageContainer } from '../../../../../atomic-design/atoms/PageContainer';
import { PageHeaderBar } from '../../../../../atomic-design/atoms/PageHeaderBar';
import {
  PrimaryTooltip,
  SecondaryTooltip,
} from '../../../../../atomic-design/atoms/StyledTooltips';
import { TribbleAvatar } from '../../../../../atomic-design/atoms/TribbleAvatar';
import { LanguagePicker } from '../../../../../atomic-design/molecules/LanguagePicker';
import { MetadataFilterPicker } from '../../../../../atomic-design/molecules/MetadataFilterPicker';
import { OpportunityPicker } from '../../../../../atomic-design/molecules/OpportunityPicker';
import { get } from '../../../../api/apiUtils';
import {
  MetadataFilterValueDB,
  MetadataFilterValueDto,
  getMappedMetdataFilterValues,
} from '../../../../api/metadata.api';
import { createQuestionnaire } from '../../../../api/questionnaires.api';
import { SalesforceOpportunityDto } from '../../../../api/salesforce.api';
import { useSalesforceOpportunities } from '../../../../api/salesforce.swr';
import { UserDto } from '../../../../api/user.api';
import { formatDate } from '../../../../utils/dateTime';
import { errorHelper } from '../../../../utils/form.utils';
import { SectionHeader } from './SectionHeader';

import { DropZone } from '../../../../../components/copied';
import { UploadFileParams, uploadFiles } from '../../../../../utils/api';
import { MIME_TYPE } from '../../../../../utils/constants';
import { useMetadataFilters } from '../../../../api/metadata.swr';
import { useUsers } from '../../../../api/user.swr';
import { useRootStore } from '../../../../state/useRootStore';

export type NewQuestionnaireFormInputs = {
  title: string;
  projectDescription?: string;
  clientName: string;
  // metadata: string[];
  dueDate: Date;
  ownerId: number;
  assignedTo: number;
  verbosityWords: string;
  opportunityId?: string;
  customInstructions?: string;
};

type RfxCriteria = {
  id: number;
  name: string;
  description: string;
  tags: number[];
};

export const AddQuestionnaire = () => {
  const rootStore = useRootStore();
  const users = useUsers();
  const metadataFilters = useMetadataFilters();
  const { navigate } = useRootStore();
  const theme = useTheme();

  const {
    register,
    handleSubmit,
    watch,
    control,
    formState: { errors },
    getValues,
  } = useForm<NewQuestionnaireFormInputs>();

  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);

  const [availableUsers, setAvailableUsers] = useState<UserDto[]>([]);
  const [selectedTags, setSelectedTags] = useState<MetadataFilterValueDto[]>(
    [],
  );
  const [beTerse, setBeTerse] = useState(false);
  const verbosityWords = watch('verbosityWords', '100');

  const [files, setFiles] = useState<File[]>([]);
  const [view, setView] = useState<'dropzone' | 'non-e2e' | 'files'>(
    rootStore.e2eEnabled ? 'dropzone' : 'non-e2e',
  );
  const [fileWarn, setFileWarn] = useState<string | undefined>(undefined);
  const [criteria, setCriteria] = useState<RfxCriteria[]>([]);
  const [customCriteria, setCustomCriteria] = useState<string>('');
  const [criteriaTags, setCriteriaTags] = useState<
    {
      id: number;
      mdf_id: number;
      rfx_criteria_id: number;
    }[]
  >([]);

  const [targetLanguage, setTargetLanguage] = useState<string>('');

  const currentUser = rootStore.user;
  const currentLocalDate = new Date(
    new Date().getFullYear(),
    new Date().getMonth(),
    new Date().getDate(),
  );

  const isE2ESpreadsheet = files.some((f) => f.type === MIME_TYPE.xlsx);
  const isE2ELongForm = files.length > 0 && !isE2ESpreadsheet;

  const salesforceOpportunities = useSalesforceOpportunities(
    rootStore.connectedToSalesforce(),
  );
  const [allOpportunities, setAllOpportunities] = useState<
    SalesforceOpportunityDto[]
  >([]);
  const [selectedOpportunity, setSelectedOpportunity] =
    useState<SalesforceOpportunityDto>(undefined);

  useEffect(() => {
    const getCriteriaXrefTags = async () => {
      const response = (await get('rfx/criteria/tag')) as {
        success: boolean;
        criteria: { id: number; mdf_id: number; rfx_criteria_id: number }[];
      };
      if (response.success) {
        setCriteriaTags(response.criteria);
      }
    };

    const getCriteria = async () => {
      const response = (await get('rfx/criteria')) as {
        success: boolean;
        criteria: RfxCriteria[];
      };
      if (response.success) {
        const newCriteria = response.criteria;
        setCriteria(newCriteria);
      }
    };

    getCriteriaXrefTags();
    getCriteria();
  }, []);

  useEffect(() => {
    if (users.data) {
      setAvailableUsers(users.data);
      setLoading(false);
    }
  }, [users.data]);

  useEffect(() => {
    if (
      metadataFilters.data &&
      metadataFilters.data.metadata_filters?.length > 0
    ) {
      const defaultMdfValues: MetadataFilterValueDto[] = [];
      metadataFilters.data.metadata_filters.forEach((mdfType) => {
        if (mdfType && mdfType.filter_values?.length > 0) {
          mdfType.filter_values.forEach((mdf) => {
            if (mdf.rfp_default === true) {
              defaultMdfValues.push(mdf);
            }
          });
        }
      });
      setSelectedTags(defaultMdfValues);
    }
  }, [metadataFilters.data]);

  useEffect(() => {
    if (
      salesforceOpportunities.data &&
      salesforceOpportunities.data.records?.length > 0
    ) {
      setAllOpportunities(salesforceOpportunities.data.records);
    }
  }, [salesforceOpportunities.data]);

  const availableMetadataFilters = metadataFilters.data.metadata_filters;
  const availableMetadataFiltersMap = availableMetadataFilters.reduce(
    (acc, mdf) => {
      return { ...acc, [mdf.id]: mdf };
    },
    {},
  );
  const availableMetadataFiltersWithActiveMdf = availableMetadataFilters.filter(
    (group) => {
      // Only show a group if there are active filters in it
      return group.filter_values.filter((mdf) => mdf.is_active).length > 0;
    },
  );
  const mapMetadataFilterValuesById = getMappedMetdataFilterValues(
    availableMetadataFilters,
  );

  const submitToApi: SubmitHandler<NewQuestionnaireFormInputs> = async (
    values,
  ) => {
    // values.metadata come in as an array of string ids
    // Need to map to the group id, as well as to the metadata "labels" (i.e. not ids)
    let metadataFilter: MetadataFilterValueDB = {} as MetadataFilterValueDB;
    if (selectedTags.length > 0) {
      const num_selected_values = selectedTags.map((tag) => Number(tag.id));

      // Could be selecting MDF values from multiple types.
      // If so, write to a "multiple_types" key in the metadata object.
      // Note: will need to change MDF schema to be {} | {}[]
      const metadata_types = availableMetadataFilters.filter((mdf_type) => {
        return mdf_type.filter_values.some((filter) =>
          num_selected_values.includes(filter.id),
        );
      });

      if (metadata_types.length == 1) {
        const value_labels = selectedTags
          .map((tag) => {
            // This shouldn't be null
            if (tag.value && tag.is_active) {
              return tag.value;
            }
            return null;
          })
          .filter((label) => label != null);

        metadataFilter = {
          type_id: Number(metadata_types[0].id),
          type_name: metadata_types[0].name,
          type_distinct_answer: metadata_types[0].distinct_answer,
          values: value_labels,
        };
      } else if (metadata_types.length > 1) {
        // Each key will the mdf_type_id~!~mdf_type_name and the value will be the array of the user selected mdf values
        const mdf_type_values_map = {};
        const ID_NAME_SEPARATE = '~!~';

        // Find the mdf_type for each value
        selectedTags.forEach((tag) => {
          const mdf_type = metadata_types.find(
            (mdf_type) => Number(mdf_type.id) === Number(tag.type_id),
          );
          if (mdf_type) {
            const key = mdf_type.id + ID_NAME_SEPARATE + mdf_type.name;
            mdf_type_values_map[key] = (mdf_type_values_map[key] ?? []).concat(
              tag.value,
            );
          }
        });

        if (Object.keys(mdf_type_values_map).length > 1) {
          metadataFilter = {
            multiple_types: Object.keys(mdf_type_values_map).map((key) => {
              const [type_id, type_name] = key.split(ID_NAME_SEPARATE);
              return {
                type_id: Number(type_id),
                type_name,
                type_distinct_answer:
                  availableMetadataFiltersMap[type_id].distinct_answer,
                values: mdf_type_values_map[key],
              };
            }),
          };
        } else if (Object.keys(mdf_type_values_map).length == 1) {
          const key = Object.keys(mdf_type_values_map)[0];
          const [type_id, type_name] = key.split(ID_NAME_SEPARATE);
          metadataFilter = {
            type_id: Number(type_id),
            type_name,
            type_distinct_answer:
              availableMetadataFiltersMap[type_id].distinct_answer,
            values: mdf_type_values_map[key],
          };
        }
      }
    }

    try {
      if (files.length == 0) {
        const response = await createQuestionnaire({
          source: 'EXTENSION',
          due_date: values.dueDate,
          assignee_list: [
            { assignee_id: Number(values.ownerId), is_owner: true },
          ],
          metadata: {
            type: 'RFP', //Hard coded questionnaire type. TODO: something else
            customerName: values.clientName,
            label: values.title,
            projectDescription: values.projectDescription,
            terse: beTerse,
            verbosityWords: Number(values.verbosityWords),
            privacy: 'public',
            uuid: uuidv4(),
            metadataFilter,
            opportunityId: selectedOpportunity?.Id,
            targetLanguage,
            customInstructions: values.customInstructions,
          },
        });

        rootStore.setNotification({
          type: 'success',
          header: 'Success!',
          body: 'Questionnaire created.',
        });

        navigate({
          name: 'viewQuestionnaire',
          id: String(response.rfxContentId),
        });
      } else {
        setSubmitting(true);

        const subProjectsData: UploadFileParams['subProjects'] = [
          {
            mainFile: files[0].name,
            supportingFiles: [],
            outputType: isE2ESpreadsheet ? 'spreadsheet' : 'longform',
            verbosityWords,
            beTerse,
            targetLanguage,
            customInstructions: values.customInstructions,
            excludedPlatforms: ['gong', 'slack'], // By default exclude gong and slack
          },
        ];

        await uploadFiles({
          files,
          projectName: values.title,
          projectDescription: values.projectDescription,
          customerName: values.clientName,
          dueDate: dayJs(values.dueDate).toDate().toISOString(),
          tags: metadataFilter,
          criteria: customCriteria,
          criteriaIds: criteriaForSelectedTags.map((c) => c.id),
          ownerId: values.ownerId,
          opportunityId: selectedOpportunity?.Id,
          subProjects: subProjectsData,
        });

        rootStore.navigate({ name: 'default' });
      }
    } catch (e: any) {
      console.error(e);
      rootStore.setNotification(
        {
          type: 'error',
          header: 'Error, could not create questionnaire.',
          body: 'The questionnaire could not be created successfully. Please try again.',
        },
        10000,
      );
    }
  };

  const registerHelper = (
    field: keyof NewQuestionnaireFormInputs,
    required: boolean = true,
  ) => {
    return {
      ...register(field, { required }),
      ...errorHelper(field, errors),
    };
  };

  const handleSelectTag = (
    selection: number[],
    initialLoad: boolean = false,
  ) => {
    const mdf_values = selection.map((mdfId) => {
      return mapMetadataFilterValuesById[mdfId];
    });
    setSelectedTags(mdf_values);
  };

  const handleFilesAdded = (newFiles: File[]) => {
    let filesToSet: File[] = [];

    if (
      newFiles.length > 1 &&
      !!newFiles.find((f) => f.type === MIME_TYPE.xlsx)
    ) {
      const firstFile = newFiles[0];

      if (firstFile.type === MIME_TYPE.xlsx) {
        filesToSet = [firstFile];
        setFileWarn('I can only accept one XLSX file at a time.');
      } else {
        filesToSet = newFiles.filter((f) => f.type !== MIME_TYPE.xlsx);
        setFileWarn(
          'I can only accept exclusively document files (DOCX, PDF), or, exclusively spreadsheet files (XLSX)',
        );
      }
    } else {
      filesToSet = newFiles;
      setFileWarn(undefined);
    }
    if (filesToSet.length) {
      setFiles(filesToSet);
      setView('files');
    }
  };
  const handleSelectFile = (event: ChangeEvent<HTMLInputElement>) => {
    if (event?.target.files?.length) {
      const selectedFiles = Array.from(event.target.files);
      handleFilesAdded(selectedFiles);
    }
  };

  // Handle teleported user
  const currentUserApplicable = availableUsers.find(
    (user) => String(user.id) == currentUser.id,
  );

  const criteriaForSelectedTags = Array.from(
    new Set(
      selectedTags
        .flatMap((t) => criteriaTags.filter((c) => c.mdf_id === t.id))
        .flatMap((c) => criteria.filter((cr) => cr.id === c?.rfx_criteria_id)),
    ),
  );

  return (
    <PageContainer
      sx={{ backgroundColor: '#fff', overflowY: 'hidden', p: '16px', pr: 0 }}
    >
      {/* Header */}
      <PageHeaderBar
        onClickBack={() => rootStore.navigate({ name: 'default' })}
        title=""
      />
      {/* Form */}

      <form style={{ height: '100%', overflowY: 'auto' }}>
        {/* Wrapper */}
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-between',
            mr: '16px',
          }}
        >
          {/* Main vertical tray that contains the form */}
          {errors?.root && (
            <Alert variant="filled" severity="error">
              {errors.root.message || 'An unknown error occurred.'}
            </Alert>
          )}
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
            {view === 'dropzone' && (
              <Stack direction="column" gap="1rem">
                <Typography
                  variant="body2"
                  color="primary"
                  sx={{ fontWeight: 600 }}
                >
                  Option 1: Upload files to get started
                </Typography>

                <DropZone
                  accept=".pdf,.docx,.xlsx"
                  handleFilesAdded={handleFilesAdded}
                  handleSelectFile={handleSelectFile}
                  text="Drag & Drop RFP files (either .docx & .pdf files, or, an .xlsx file), or, "
                />
              </Stack>
            )}
            {files.length > 0 && (
              <Stack
                direction="column"
                gap="1rem"
                sx={{
                  p: 2,
                  border: `1px solid ${theme.palette.grey[400]}`,
                  backgroundColor: theme.palette.grey[200],
                  borderRadius: 1,
                }}
              >
                <Typography
                  variant="body2"
                  sx={{ color: theme.palette.grey[600] }}
                >
                  {isE2ESpreadsheet
                    ? 'Tribble will extract questions from the below file and automatically answer them:'
                    : 'The Tribble Agent will use the following files to generate a RFP response:'}
                </Typography>
                {files.map((f, i) => (
                  <PrimaryTooltip key={f.name + i} title={f.name}>
                    <Chip
                      label={f.name}
                      key={f.name + i}
                      variant="outlined"
                      size="small"
                      color="primary"
                      sx={{ width: 'fit-content' }}
                    />
                  </PrimaryTooltip>
                ))}

                <Stack
                  direction="row"
                  justifyContent="flex-end"
                  sx={{ mt: 1, mr: '-8px' }}
                >
                  <Button
                    variant="text"
                    color="error"
                    size="small"
                    onClick={() => {
                      setFiles([]);
                      setView('dropzone');
                    }}
                  >
                    Reset
                  </Button>
                </Stack>
              </Stack>
            )}

            <Typography
              variant="body2"
              color="primary"
              sx={{ fontWeight: 600 }}
            >
              {view === 'dropzone'
                ? 'Option 2: Fill out the following information and then add RFx questions'
                : files.length > 0
                  ? 'Provide additional details for the project'
                  : 'Fill out the following information to get started'}
            </Typography>

            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: '1rem',
                mt: '-16px',
              }}
            >
              <SectionHeader required>General Details</SectionHeader>
              <TextField
                type="text"
                placeholder="Project Title"
                {...registerHelper('title')}
                inputProps={{
                  sx: {
                    '&::placeholder': {
                      color: theme.palette.grey[500],
                      opacity: 1,
                    },
                  },
                }}
              />
              <TextField
                type="text"
                placeholder="Project Description"
                {...registerHelper('projectDescription', false)}
                inputProps={{
                  sx: {
                    '&::placeholder': {
                      color: theme.palette.grey[500],
                      opacity: 1,
                    },
                  },
                }}
              />
              <TextField
                type="text"
                placeholder="Customer Name"
                {...registerHelper('clientName')}
                inputProps={{
                  sx: {
                    '&::placeholder': {
                      color: theme.palette.grey[500],
                      opacity: 1,
                    },
                  },
                }}
              />
              {allOpportunities.length > 0 && (
                <div
                  key={`oppty_picker_${selectedOpportunity?.Id}`}
                  style={{ width: '100%' }}
                >
                  <OpportunityPicker
                    opportunities={allOpportunities}
                    selected={selectedOpportunity}
                    onSelect={(value) => setSelectedOpportunity(value)}
                  />
                </div>
              )}
            </Box>

            <Box sx={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
              <SectionHeader required explainer="">
                Due Date
              </SectionHeader>
              <LocalizationProvider dateAdapter={AdapterDayjs}>
                <Controller
                  name="dueDate"
                  control={control}
                  defaultValue={currentLocalDate}
                  render={({ field }) => {
                    return (
                      <DatePicker
                        value={dayJs(field.value)}
                        onChange={(newValue: Dayjs) =>
                          field.onChange(formatDate(newValue.toDate()))
                        }
                      />
                    );
                  }}
                />
              </LocalizationProvider>
            </Box>

            {availableUsers.length > 1 && (
              <>
                <Box
                  sx={{ display: 'flex', flexDirection: 'column', gap: '12px' }}
                >
                  <SectionHeader
                    required
                    explainer="Set an owner of the project, who is in charge of
                    managing its completion."
                  >
                    Owner
                  </SectionHeader>

                  <FormControl fullWidth>
                    <Select
                      defaultValue={currentUserApplicable ? currentUser.id : ''}
                      {...(registerHelper('ownerId') as any)}
                      renderValue={(selected) => {
                        const currSelectedUser = availableUsers.find(
                          (user) => String(user.id) == selected,
                        );
                        return currSelectedUser?.name || '';
                      }}
                      MenuProps={{
                        sx: {
                          maxHeight: '300px',
                        },
                        anchorOrigin: {
                          vertical: 'bottom',
                          horizontal: 'left',
                        },
                      }}
                    >
                      {availableUsers
                        .sort((a, b) =>
                          a.name.toLowerCase() < b.name.toLowerCase() ? -1 : 1,
                        )
                        .map((user) => (
                          <MenuItem value={user.id} key={user.id}>
                            <div style={{ marginRight: '1rem' }}>
                              {user.picture && user.picture != '' && (
                                <Avatar
                                  src={user.picture}
                                  sx={{ height: 24, width: 24 }}
                                />
                              )}
                              {(!user.picture || user.picture == '') && (
                                <TribbleAvatar
                                  username={user.name}
                                  height="24px"
                                  width="24px"
                                />
                              )}
                            </div>
                            <ListItemText
                              primary={user.name}
                              sx={{
                                '& .MuiTypography-root': {
                                  fontSize: '0.85rem',
                                },
                              }}
                            />
                          </MenuItem>
                        ))}
                    </Select>
                    {errors?.ownerId && (
                      <FormHelperText error>
                        {errorHelper('ownerId', errors).helperText}
                      </FormHelperText>
                    )}
                  </FormControl>
                </Box>
              </>
            )}

            {((availableMetadataFilters.length > 0 &&
              Object.keys(availableMetadataFiltersWithActiveMdf).length > 0) ||
              files.length === 0 ||
              isE2ESpreadsheet) && (
              <SectionHeader
                headerColor="secondary"
                explainer=""
                hideOptionalText={true}
                sx={{ mb: '-16px' }}
              >
                Optional Settings
              </SectionHeader>
            )}

            {availableMetadataFilters.length > 0 &&
              Object.keys(availableMetadataFiltersWithActiveMdf).length > 0 && (
                <Box
                  sx={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}
                >
                  <SectionHeader
                    explainer="Select tags to help generate accurate answers. You can also customize this at the grouping
                    level."
                  >
                    Context
                  </SectionHeader>

                  <MetadataFilterPicker
                    onSelect={handleSelectTag}
                    selections={selectedTags}
                    hideInactive={true}
                    hideVerbatim={true}
                  />
                  {isE2ELongForm &&
                    !!criteriaForSelectedTags &&
                    criteriaForSelectedTags.length > 0 && (
                      <>
                        <Typography
                          variant="body2"
                          color="secondary"
                          sx={{
                            mt: 1,
                            fontWeight: 500,
                          }}
                        >
                          These tags pertain to the following criteria that will
                          be used in determining RFP recommendations:
                        </Typography>
                        <Stack direction="row" sx={{ flexWrap: 'wrap' }}>
                          {criteriaForSelectedTags.map((c, i) => (
                            <SecondaryTooltip
                              key={c?.id + i}
                              title={c?.description}
                            >
                              <Chip
                                variant="outlined"
                                color="secondary"
                                key={c?.id + i}
                                label={c?.name}
                                sx={{
                                  mr: 1,
                                  mb: 1,
                                  color: theme.palette.secondary.main,
                                }}
                              />
                            </SecondaryTooltip>
                          ))}
                        </Stack>
                      </>
                    )}
                  {isE2ELongForm && (
                    <Stack direction="column" sx={{ mt: 1 }} gap="12px">
                      <SectionHeader
                        explainer="Are there any specific criteria that should
                          be considered before generating the response?"
                      >
                        RFP Custom Criteria
                      </SectionHeader>
                      <TextField
                        value={customCriteria}
                        size="small"
                        multiline
                        placeholder="Example: RFP must be at least $100K; deals in EMEA cannot include product [x]"
                        onChange={(e) => setCustomCriteria(e.target.value)}
                        minRows={2}
                        maxRows={5}
                        InputProps={{
                          sx: {
                            '& .MuiInputBase-input': {
                              maxHeight: '120px',
                              overflowY: 'auto',
                            },
                          },
                        }}
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            display: 'flex',
                            alignItems: 'flex-start',
                          },
                        }}
                      />
                    </Stack>
                  )}
                </Box>
              )}

            {files.length === 0 || isE2ESpreadsheet ? (
              <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                <SectionHeader explainer="Control how verbose Tribble's answers should be">
                  Verbose Setting
                </SectionHeader>

                <FormControl fullWidth sx={{ mt: '16px', mb: '20px' }}>
                  <Typography
                    variant="body2"
                    sx={{
                      fontWeight: 500,
                      color: theme.palette.grey[700],
                      mb: '8px',
                    }}
                  >
                    Target Answer Length
                  </Typography>
                  <Select
                    size="small"
                    value={verbosityWords}
                    {...(registerHelper('verbosityWords') as any)}
                    MenuProps={{
                      sx: {
                        maxHeight: '300px',
                      },
                      anchorOrigin: {
                        vertical: 'bottom',
                        horizontal: 'left',
                      },
                    }}
                  >
                    <MenuItem value="100" key="verbosityWords_100">
                      Default (AI decides suitable answer length)
                    </MenuItem>
                    <MenuItem value="50" key="verbosityWords_50">
                      Shorter (~50 words)
                    </MenuItem>
                    <MenuItem value="150" key="verbosityWords_150">
                      Longer (~150 words)
                    </MenuItem>
                  </Select>
                </FormControl>

                <Stack direction="column">
                  <Typography
                    variant="body2"
                    sx={{ fontWeight: 500, color: theme.palette.grey[700] }}
                  >
                    Terse Yes/No Answers
                  </Typography>
                  <Stack
                    direction="row"
                    alignItems="flex-start"
                    sx={{ mt: '-2px' }}
                  >
                    <Checkbox
                      checked={beTerse}
                      onChange={(
                        event: React.ChangeEvent<HTMLInputElement>,
                      ) => {
                        setBeTerse(event.target.checked);
                      }}
                    />
                    <Stack direction="column" sx={{ mt: '13px' }}>
                      <Typography variant="body2" sx={{ color: 'grey.700' }}>
                        When answering Yes/No-type questions, answer with "Yes"
                        or "No" only.
                      </Typography>
                    </Stack>
                  </Stack>
                </Stack>

                <Stack direction="column" sx={{ mt: 3 }} gap="12px">
                  <SectionHeader explainer="Translate answers into a different language. Note: only set this if you want the generated answer to be different than the input (question) language.">
                    Target Language
                  </SectionHeader>

                  <LanguagePicker
                    selected={targetLanguage}
                    onSelect={setTargetLanguage}
                    size="small"
                  />
                </Stack>

                <Stack direction="column" sx={{ mt: 3 }} gap="12px">
                  <SectionHeader explainer="Provide custom instructions for Tribble to follow when answering questions">
                    Custom Instructions
                  </SectionHeader>

                  <TextField
                    type="text"
                    size="small"
                    multiline={true}
                    maxRows={2}
                    placeholder="Enter Custom instructions"
                    {...registerHelper('customInstructions', false)}
                    inputProps={{
                      sx: {
                        '&::placeholder': {
                          color: theme.palette.grey[500],
                          opacity: 1,
                        },
                      },
                    }}
                  />
                </Stack>
              </Box>
            ) : null}

            {false /* Hide for now */ && (
              <>
                <SectionHeader
                  required
                  explainer="Select individuals who will be tasked with answering questions"
                >
                  Assigned to
                </SectionHeader>
                <FormControl fullWidth>
                  <Select
                    defaultValue={currentUser.id}
                    {...(registerHelper('assignedTo') as any)}
                    placeholder="Select Assignee"
                  >
                    {availableUsers.map((user) => (
                      <MenuItem value={user.id} key={user.id}>
                        {user.name}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors?.assignedTo && (
                    <FormHelperText error>
                      {errorHelper('assignedTo', errors).helperText}
                    </FormHelperText>
                  )}
                </FormControl>
              </>
            )}

            {/* Spacer */}
            <Box sx={{ minHeight: '36px' }} />

            <Stack
              sx={(theme) => ({
                position: 'fixed',
                bottom: 0,
                left: 0,
                width: '100%',
                alignItems: 'center',
                backgroundColor: 'white',
                borderTopWidth: '1px',
                borderTopColor: theme.palette.grey[300],
                borderTopStyle: 'solid',
                zIndex: 1200,
              })}
            >
              <Stack
                direction="row"
                sx={{
                  p: '1.5rem 1rem',
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '95%',
                }}
              >
                <Button
                  key="create-questionnaire"
                  color="primary"
                  onClick={handleSubmit(submitToApi)}
                  disabled={submitting}
                  sx={{ maxWidth: '320px' }}
                >
                  {submitting ? 'Creating...' : 'Create Project'}
                </Button>
              </Stack>
            </Stack>
          </Box>

          {/* Controls at the bottom */}

          {loading && (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'row',
                width: '100%',
                justifyContent: 'center',
              }}
            >
              <CircularProgress />
            </Box>
          )}

          <Box sx={{ minHeight: '1.5rem' }}></Box>
        </Box>
      </form>
    </PageContainer>
  );
};
