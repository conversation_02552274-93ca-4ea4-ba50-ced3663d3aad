import { useRequireLoggedIn } from 'popup/hooks/useRequireLoggedIn.hook';
import React, { FC } from 'react';
// import the toggle menu icon from @mui/material/icons
// import { EndToEndRfp } from '../../../components/EndToEndRfp';
import { useRootStore } from '../../state/useRootStore';
import { AddQuestionnaire } from './pages/AddQuestionnaire';
import { DefaultPage } from './pages/Default';
import { ViewE2ELongForm } from './pages/ViewE2ELongForm';
import { QuestionPage } from './pages/ViewQuestion';
import { ViewQuestionnaire } from './pages/ViewQuestionnaire';
import { ViewQuestionnaireAddQuestions } from './pages/ViewQuestionnaireAddQuestions';

export const HomePage: FC = () => {
  useRequireLoggedIn();
  const rootStore = useRootStore();

  switch (rootStore.page.name) {
    case 'addQuestionnaire':
      return <AddQuestionnaire />;
    case 'viewQuestionnaire':
      return (
        <ViewQuestionnaire
          id={rootStore.page.id}
          scrollToId={rootStore.page.scrollToId}
          questionsAdded={rootStore.page.questionsAdded}
        />
      );
    case 'viewQuestionnaire/addQuestions':
      return (
        <ViewQuestionnaireAddQuestions
          id={rootStore.page.id}
          rfxContentId={rootStore.page.rfxContentId}
        />
      );
    case 'viewQuestion':
      return (
        <QuestionPage
          questionId={rootStore.page.questionId}
          questionnaire={rootStore.page.questionnaire}
        />
      );
    case 'e2eLongForm':
      return <ViewE2ELongForm rfp={rootStore.page.rfp} />;
    // case 'endToEndRfp':
    //   return <EndToEndRfp />;
    case 'default':
    default:
      return <DefaultPage />;
  }
};
