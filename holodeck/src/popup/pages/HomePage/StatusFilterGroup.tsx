import React, { FC, useEffect } from 'react';
import { PrettyToggleButtonGroup } from '../../../atomic-design/molecules/PrettyToggleButtonGroup';
import {
  getUserPreferences,
  saveUserPreferences,
} from '../../../utils/helpers';
import { QuestionnaireFilters, useFilterStore } from './useFilterStore';

type StatusFilterGroupProps = {};
export const StatusFilterGroup: FC<StatusFilterGroupProps> = (props) => {
  const store = useFilterStore();

  useEffect(() => {
    const loadUserPreferences = async () => {
      const userPreferences = await getUserPreferences();
      store.setFilters({
        status: userPreferences?.filters?.status ?? ['active'],
      });
    };
    loadUserPreferences();
  }, []);

  const handleChange = (
    e: React.MouseEvent<HTMLElement, MouseEvent>,
    status: QuestionnaireFilters['status'][0],
  ) => {
    e.preventDefault();
    const newValue = { status: [status] };
    store.setFilters({ status: [status] });
    saveUserPreferences({ ...store.filters, ...newValue });
  };

  const buttons = [
    { value: 'active', label: 'Active' },
    { value: 'finished', label: 'Finished' },
    { value: 'all', label: 'All' },
  ];

  return (
    <PrettyToggleButtonGroup
      buttons={buttons}
      onChange={handleChange}
      value={store.filters.status}
    />
  );
};
