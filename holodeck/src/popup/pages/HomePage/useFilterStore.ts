import { ZustandStoreCreator, buildStore } from '../storeContextBuilder';

// Generic type for a filter store
export type GenericFilterStore<Filters> = {
  filters: Filters;
  setFilters: (partial: Partial<Filters>) => void;
  clearAllFilters: () => void;
  hasAdvancedFiltersSet: () => boolean;
};

//Generic store creator
const genericStoreCreator =
  <Filters>(
    getDefaultState: () => Filters,
  ): ZustandStoreCreator<GenericFilterStore<Filters>> =>
  (set, get) => {
    return {
      filters: getDefaultState(),
      setFilters: (newFilters: Partial<Filters>) => {
        set((oldState) => {
          const filters = { ...oldState.filters, ...newFilters };
          return { ...oldState, filters };
        });
      },
      hasAdvancedFiltersSet: () => {
        return false;
      },
      clearAllFilters: () => {
        set((oldState) => {
          return { ...oldState, filters: getDefaultState() };
        });
      },
    };
  };

//Generic filterstore builder
export const buildFilterStore = <Filters>(getDefaultState: () => Filters) => {
  const { useLocalStore, StoreProvider, useSharedStore } = buildStore(
    genericStoreCreator(getDefaultState),
  );
  return { useLocalStore, StoreProvider, useSharedStore };
};

//Build your own filter stores!
export type QuestionnaireFilters = {
  // todo
  status: ('all' | 'active' | 'finished')[] | undefined;
  searchQuery: string | undefined;
  reviewed: 'all' | 'under review' | 'reviewed' | undefined;
  ownerAssigneeFilter: 'all' | 'mine' | 'other' | undefined;
  ownerAssigneeFilterOtherId: string | undefined;
  sortBy: 'created_date' | 'due_date' | 'created_by_name' | 'label' | undefined;
  sortDirection: 'asc' | 'desc';
  // currently we do not support open ended date ranges. It's either both or none.
  dueDate:
    | {
        from: Date;
        to: Date;
      }
    | undefined;
  clientId: string | undefined;
  assignedToIds: string[] | undefined;
  ownerId: string | undefined;
};

// Show user's stuff only by default
export const OWNER_ASSIGNEE_FILTER_DEFAULT = 'mine';
export const REVIEWED_FILTER_DEFAULT = 'all';

const getDefaultQuestionnaireState = (): QuestionnaireFilters => ({
  status: ['active'],
  searchQuery: undefined,
  reviewed: 'all',
  ownerAssigneeFilter: OWNER_ASSIGNEE_FILTER_DEFAULT,
  ownerAssigneeFilterOtherId: undefined,
  sortBy: 'created_date',
  sortDirection: 'desc',
  dueDate: undefined,
  clientId: undefined,
  assignedToIds: undefined,
  ownerId: undefined,
});

export const { useSharedStore: useFilterStore } =
  buildFilterStore<QuestionnaireFilters>(getDefaultQuestionnaireState);
