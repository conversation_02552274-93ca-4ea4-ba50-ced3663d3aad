import {
  Button,
  CircularProgress,
  MenuItem,
  Typography,
  useTheme,
} from '@mui/material';
import { AUTH0_CONFIG } from 'utils/constants_auth';
// Should we be using this one instead?
// import { AUTH0_CONFIG } from '../../../_config/constants_auth.dev';
import { MenuDropdown } from 'atomic-design/atoms/MenuDropdown';
import { doAuth } from 'options/options';
import { useRootStore } from 'popup/state/useRootStore';
import React, { FC } from 'react';
import { getSavedAccessToken } from 'utils/auth0';

// This wraps the old function until we can refactor it
const performAuthAndSaveTokens = async (env: keyof typeof AUTH0_CONFIG) =>
  new Promise<{ success: boolean }>(async (resolve) => {
    await doAuth(env, (result) => {
      resolve({ success: result });
    });
  });

type EnvironmentSelectButtonProps = {
  onSuccessfulLogin: Function;
};

/**
 * This component lets the user select an environment to log in to.
 */
export const EnvironmentSelectButton: FC<EnvironmentSelectButtonProps> = ({
  onSuccessfulLogin,
}) => {
  const { auth, setAuth } = useRootStore(({ auth, setAuth }) => ({
    auth,
    setAuth,
  }));
  const theme = useTheme();
  const singleEnvironment = Object.keys(AUTH0_CONFIG).length == 1;

  /**
   * This function performs the login process for the given environment.
   * @param env The environment to log in to.
   */
  const doLoginWithEnvironment = async (env: keyof typeof AUTH0_CONFIG) => {
    // start spinner
    setAuth({ status: 'loading' });

    // perform auth
    const { success } = await performAuthAndSaveTokens(env);
    const accessToken = success && (await getSavedAccessToken());

    // failure
    if (!accessToken) {
      setAuth({ status: 'error', error: 'Failed to log in.' });
      return;
    }

    // success
    setAuth({
      status: 'success',
      data: { accessToken, env },
    });
    onSuccessfulLogin();
  };

  const handleSignInClick = async (env: keyof typeof AUTH0_CONFIG) => {
    try {
      await doLoginWithEnvironment(env);
    } catch (e) {
      console.error(e);
      setAuth({ status: 'error', error: 'Failed to log in.' });
    }
  };

  /**
   * This function generates the dropdown options for the menu.
   */
  const generateDropdownOptionsArray = (handleClose: Function) =>
    Object.keys(AUTH0_CONFIG).map((env: string) => {
      const handleClickMenu = async () => {
        await handleSignInClick(env as keyof typeof AUTH0_CONFIG);
        handleClose();
      };

      return (
        <MenuItem key={env} onClick={handleClickMenu}>
          {env}
        </MenuItem>
      );
    });

  const TriggerButton = () => <Button sx={{ width: '85vw' }}>Sign In</Button>;

  const TriggerButtonSingleEnvironment = () => (
    <Button
      sx={{ width: '85vw' }}
      onClick={() => {
        handleSignInClick('prod');
      }}
    >
      Sign In
    </Button>
  );

  const ErrorMessage = () => {
    return auth.status == 'error' ? (
      <Typography
        sx={{ m: 2, textAlign: 'center', color: theme.palette.error.main }}
      >
        Error: {auth.error}
      </Typography>
    ) : (
      <></>
    );
  };

  switch (auth.status) {
    case 'idle':
    case 'error':
      return (
        <div>
          {singleEnvironment ? (
            <TriggerButtonSingleEnvironment />
          ) : (
            <MenuDropdown trigger={<TriggerButton />}>
              {(handleClose) => generateDropdownOptionsArray(handleClose)}
            </MenuDropdown>
          )}
        </div>
      );
    case 'loading':
      return <CircularProgress />;
    case 'success':
      return (
        <div>
          <Button>Log out</Button>
          {/* Todo: Logout somehow */}
        </div>
      );
  }
};
