import { Stack, Typography } from '@mui/material';
import styled from '@mui/material/styles/styled';
import tribble_logo_svg from 'assets/tribble_logo.svg';
import { useRootStore } from 'popup/state/useRootStore';
import React, { FC, useEffect } from 'react';
import { getSavedAccessToken } from 'utils/auth0';
import { EnvironmentSelectButton } from './EnvironmentSelectButton';

const PageHeader = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  fontSize: '1.5rem',
}));

const PageBodyText = styled(Typography)(({ theme }) => ({
  fontWeight: 400,
  fontSize: '1rem',
  lineHeight: '1.25rem',
  maxWidth: '300px',
  color: theme.palette.grey[600],
  textAlign: 'center',
}));

export const LoginPage: FC = () => {
  const { navigate } = useRootStore(({ navigate }) => ({ navigate }));

  const checkToken = async () => {
    const accessToken = await getSavedAccessToken();
    if (accessToken) {
      navigate({ name: 'home' });
    }
  };

  // run once on mount
  useEffect(() => {
    checkToken();
  }, []);

  return (
    <Stack
      direction="column"
      gap="1.2rem"
      justifyContent="center"
      alignItems="center"
      height="100%"
    >
      <img src={tribble_logo_svg} style={{ maxWidth: '64px' }} />
      <Stack
        direction="column"
        alignItems="center"
        justifyContent="center"
        gap="1rem"
        sx={{ mb: '1rem' }}
      >
        <PageHeader>Welcome to Tribble</PageHeader>
        <PageBodyText>
          Use this Chrome Extension to answer your questionnaires.
        </PageBodyText>
      </Stack>
      <EnvironmentSelectButton
        onSuccessfulLogin={() => navigate({ name: 'home' })}
      />
    </Stack>
  );
};
