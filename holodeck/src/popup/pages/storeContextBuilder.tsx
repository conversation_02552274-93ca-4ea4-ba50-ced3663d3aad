import {
  PropsWithChildren,
  createContext,
  useContext,
  useEffect,
  useRef,
} from 'react';
import { StoreApi, createStore, useStore } from 'zustand';

export type ZustandStoreCreator<T> = Parameters<typeof createStore<T, []>>[0];

export const buildStore = <StoreShape,>(
  storeCreator: ZustandStoreCreator<StoreShape>,
) => {
  const localStore = createStore<StoreShape>(storeCreator);

  const StoreContext = createContext<StoreApi<StoreShape>>(localStore);

  const useLocalStore = () => {
    const storeRef = useRef<StoreApi<StoreShape>>(localStore);
    useEffect(() => {
      storeRef.current = createStore(storeCreator);
    }, []);
    return useStore(storeRef.current);
  };

  const StoreProvider: React.FC<PropsWithChildren> = ({ children }) => {
    const storeRef = useRef<StoreApi<StoreShape>>(localStore);

    return (
      <StoreContext.Provider value={storeRef.current}>
        {children}
      </StoreContext.Provider>
    );
  };

  const useStoreFunction = () => {
    const store = useContext(StoreContext);
    return useStore(store);
  };

  return {
    useLocalStore,
    StoreProvider,
    useSharedStore: useStoreFunction,
  };
};
