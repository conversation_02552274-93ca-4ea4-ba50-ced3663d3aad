export type ApiResultDto<T> = Promise<{
  success: boolean;
  response: T;
}>;

type SlackWorkflowType = 'ANSWER_HELP' | 'MODIFY_ANSWER_APPROVAL';

type SlackWorkflowBase = {
  type: SlackWorkflowType;
  note: string;
  content_detail_id: number;
  collaboration_channel?: string;
};

type SlackWorkflowAnswerHelp = {
  type: 'ANSWER_HELP';
};

type SlackWorkflowModifyAnswerApproval = {
  type: 'MODIFY_ANSWER_APPROVAL';
  proposed_answer: string;
};

//Sorry for the type gymnastics!
//This bit gives us a type called SlackWorkFlow where you have to specify the subtype
//e.g. SlackWorkFlow<'MODIFY_ANSWER_APPROVAL'>
export type SlackWorkflow<T extends SlackWorkflowBase['type']> =
  T extends 'ANSWER_HELP'
    ? SlackWorkflowBase & SlackWorkflowAnswerHelp
    : T extends 'MODIFY_ANSWER_APPROVAL'
      ? SlackWorkflowBase & SlackWorkflowModifyAnswerApproval
      : SlackWorkflowBase &
          SlackWorkflowModifyAnswerApproval &
          SlackWorkflowAnswerHelp;
