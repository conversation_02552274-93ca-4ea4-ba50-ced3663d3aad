// Constants
const SALT_LENGTH = 16;
const IV_LENGTH = 12;

// Core crypto primitives
const generateSalt = () => crypto.getRandomValues(new Uint8Array(SALT_LENGTH));
const generateIV = () => crypto.getRandomValues(new Uint8Array(IV_LENGTH));

const getBaseKey = () => {
  const extensionId = chrome.runtime.id;
  return new TextEncoder().encode(extensionId);
};

const deriveKey = async (salt: Uint8Array): Promise<CryptoKey> => {
  const baseKey = getBaseKey();

  const importedKey = await crypto.subtle.importKey(
    'raw',
    baseKey,
    'PBKDF2',
    false,
    ['deriveKey'],
  );

  return crypto.subtle.deriveKey(
    {
      name: 'PBKDF2',
      salt,
      iterations: 100000,
      hash: 'SHA-256',
    },
    importedKey,
    { name: 'AES-GCM', length: 256 },
    false,
    ['encrypt', 'decrypt'],
  );
};

// Data conversion utilities
const combineArrays = (
  salt: Uint8Array,
  iv: Uint8Array,
  data: ArrayBuffer,
): Uint8Array => {
  const combined = new Uint8Array(salt.length + iv.length + data.byteLength);
  combined.set(salt, 0);
  combined.set(iv, salt.length);
  combined.set(new Uint8Array(data), salt.length + iv.length);
  return combined;
};

const splitArrays = (combined: Uint8Array) => ({
  salt: combined.slice(0, SALT_LENGTH),
  iv: combined.slice(SALT_LENGTH, SALT_LENGTH + IV_LENGTH),
  data: combined.slice(SALT_LENGTH + IV_LENGTH),
});

const arrayToBase64 = (array: Uint8Array): string =>
  btoa(String.fromCharCode(...array));

const base64ToArray = (base64: string): Uint8Array =>
  new Uint8Array(
    atob(base64)
      .split('')
      .map((c) => c.charCodeAt(0)),
  );

// Main encryption functions
export const encryptToken = async (data: string): Promise<string> => {
  const salt = generateSalt();
  const iv = generateIV();
  const key = await deriveKey(salt);

  const encrypted = await crypto.subtle.encrypt(
    { name: 'AES-GCM', iv },
    key,
    new TextEncoder().encode(data),
  );

  const combined = combineArrays(salt, iv, encrypted);
  return arrayToBase64(combined);
};

export const decryptToken = async (encryptedData: string): Promise<string> => {
  const combined = base64ToArray(encryptedData);
  const { salt, iv, data } = splitArrays(combined);

  try {
    const key = await deriveKey(salt);
    const decrypted = await crypto.subtle.decrypt(
      { name: 'AES-GCM', iv },
      key,
      data,
    );

    return new TextDecoder().decode(decrypted);
  } catch (e) {
    console.error('[decryptToken] Error decrypting token: ', e);
    return null;
  }
};
