import { AUTH0_CONFIG } from 'utils/constants_auth';
import { create } from 'zustand';
import { Rfp } from '../../components/EndToEndRfp';
import { QuestionnaireDto } from '../api/questionnaires.api';
import { SalesforceUserAuth } from '../api/salesforce.api';
import { SlackSettings } from '../api/slack.api';
import { UserDetailDto } from '../api/user.api';

type AsyncResultHolder<T> =
  | { status: 'idle' }
  | { status: 'loading' }
  | { status: 'error'; error: string }
  | { status: 'success'; data: T };

export type Page =
  | { name: 'login' }
  | { name: 'home' }
  | { name: 'default' }
  | { name: 'addQuestionnaire' }
  | {
      name: 'viewQuestionnaire';
      id: string;
      scrollToId?: string;
      questionsAdded?: boolean;
    }
  | {
      name: 'viewQuestionnaire/addQuestions';
      id: string;
      rfxContentId: string;
    }
  | {
      name: 'viewQuestion';
      questionId: number;
      questionnaire: QuestionnaireDto;
    }
  | { name: 'e2eLongForm'; rfp: Rfp }
  | { name: 'endToEndRfp' };

export type Auth = {
  accessToken: string;
  env: keyof typeof AUTH0_CONFIG;
};

export type Notification = {
  header: string;
  body: string;
  type: 'success' | 'error' | 'info' | 'warning';
  timeoutId?: number;
};

export type RootStore = {
  page: Page;
  auth: AsyncResultHolder<Auth>;
  user: UserDetailDto;
  e2eEnabled: boolean;
  notification: Notification | null;
  slackSettings: SlackSettings | null;
  salesforceUser: SalesforceUserAuth | null;
  navigate: (page: Page) => void;
  setAuth: (auth: AsyncResultHolder<Auth>) => void;
  setUser: (user: UserDetailDto) => void;
  clearNotification: () => void;
  setNotification: (notification: Notification, timeout?: number) => void;
  setSlackSettings: (settings: SlackSettings) => void;
  setE2EEnabled: (enabled: boolean) => void;
  setSalesforceUser: (salesforceUser: SalesforceUserAuth) => void;
  connectedToSalesforce: () => boolean;
};

export const useRootStore = create<RootStore>((set, get) => {
  return {
    page: { name: 'login' },
    auth: { status: 'idle' },
    user: null,
    notification: null,
    e2eEnabled: false,
    slackSettings: null,
    salesforceUser: null,
    navigate: (page: Page) => {
      set((oldState) => {
        return { ...oldState, page };
      });
    },
    setAuth: (auth: RootStore['auth']) => set({ auth }),
    setUser: (user: UserDetailDto) => set({ user }),
    setE2EEnabled: (enabled: boolean) => set({ e2eEnabled: enabled }),
    clearNotification: () => set({ notification: null }),
    setSlackSettings: (settings: SlackSettings) =>
      set({ slackSettings: settings }),
    setSalesforceUser: (salesforceUser: SalesforceUserAuth) =>
      set({ salesforceUser }),
    setNotification: (notification, timeout = 5000) => {
      // clear old timeout if necessary
      const previousTimeoutId = get().notification?.timeoutId;
      if (previousTimeoutId) {
        clearTimeout(previousTimeoutId);
      }

      // set new timeout
      const timeoutId: number = setTimeout(() => {
        clearTimeout(timeoutId);
        get().clearNotification();
      }, timeout) as unknown as number;

      // set notification
      set({
        notification: {
          ...notification,
          timeoutId,
        },
      });
    },
    connectedToSalesforce: () => get().salesforceUser?.has_auth ?? false,
  };
});
