export type Env = 'localhost' | 'test' | 'staging' | 'prod';

export const getAccessToken = async (): Promise<string | undefined> => {
  const auth = await chrome.storage.local.get('TRIBBLE_EXT_auth');
  if (!auth || !auth['TRIBBLE_EXT_auth']) return undefined;
  const accessToken = auth['TRIBBLE_EXT_auth']['TRIBBLE_EXT_access_token'];
  return accessToken;
};

export const getEnv = async (): Promise<Env | undefined> => {
  const auth = await chrome.storage.local.get('TRIBBLE_EXT_auth');
  if (!auth) return undefined;
  const env = auth['TRIBBLE_EXT_auth']['TRIBBLE_EXT_env'];
  return env;
};

// // Currently we're using this file to read from localstorage, but we should be using it to write as well.

// // TODO: Move to this system once questionnaires and a few other features are working.
// export const setAccessToken = async (accessToken: string): Promise<void> => {
//     await chrome.storage.local.set({ TRIBBLE_EXT_access_token: accessToken });
// }

// // TODO: Move to this system once questionnaires and a few other features are working.
// export const setEnv = async (env: Env): Promise<void> => {
//     await chrome.storage.local.set({ TRIBBLE_EXT_env: env });
// }
