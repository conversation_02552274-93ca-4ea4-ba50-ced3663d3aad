import { act, renderHook } from '@testing-library/react';
import { RootStore, useRootStore } from './useRootStore';

describe('useRootStore', () => {
  let rootStore = renderHook<RootStore, Record<string, never>>(
    useRootStore,
  ).result;

  beforeEach(() => {
    rootStore = renderHook<RootStore, Record<string, never>>(
      useRootStore,
    ).result;
  });

  it('starts in the login page', () => {
    expect(rootStore.current.page).toEqual({ name: 'login' });
  });

  it('can set the page', () => {
    act(() => rootStore.current.navigate({ name: 'home' }));
    expect(rootStore.current.page).toEqual({ name: 'home' });
  });
});
