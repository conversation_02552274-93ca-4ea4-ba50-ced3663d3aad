export function processUTCDate(
  inputDate: string | Date | undefined,
): Date | undefined {
  if (!inputDate) return undefined;

  let date: Date;

  if (typeof inputDate === 'string') {
    // Use the native Date constructor which handles ISO 8601 strings correctly.
    date = new Date(inputDate);
  } else if (inputDate instanceof Date) {
    // If it's already a Date object, use it directly without stripping time.
    date = inputDate;
  } else {
    // Should not be reachable with TypeScript but good practice
    return undefined;
  }

  // Check if parsing resulted in an invalid date
  if (isNaN(date.getTime())) {
    // Optional: Log a warning if needed
    // console.warn(`Could not parse date value: ${inputDate}`);
    return undefined;
  }

  return date;
}

/**
 * Displays a Date object's UTC date using the default locale formatting.
 * @param {Date | undefined} date - The input Date object.
 * @returns {string | undefined} - Formatted date string using default locale conventions (e.g., "12/25/2025", "25/12/2025", etc.) or undefined.
 */
export function displayUTCDateWithDefaultLocale(
  date: Date | undefined,
): string | undefined {
  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
    return undefined; // Handle invalid inputs
  }

  try {
    // Pass undefined for locale to use the default
    return date.toLocaleString(undefined, {
      // <-- Use undefined for default locale
      timeZone: 'UTC', // IMPORTANT: Calculate based on UTC
      year: 'numeric', // Example format options, adjust as needed
      month: 'numeric',
      day: 'numeric',
      // You might want different month/day formats depending on locale preference
      // e.g., month: 'long' or 'short', etc.
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return undefined;
  }
}

export const formatDatePretty = (date: Date) => {
  if (typeof date == 'string') date = new Date(date);
  const months = [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec',
  ];
  const day = date.getUTCDate();
  const monthIndex = date.getUTCMonth();
  const year = date.getUTCFullYear();

  return `${months[monthIndex]} ${day.toString().padStart(2, '0')}`;
};

export const formatDate = (date: Date | null) => {
  if (!date) return null;

  if (typeof date == 'string') {
    date = new Date(date);

    const day = date.getUTCDate();
    const month = date.getUTCMonth() + 1;
    const year = date.getUTCFullYear();

    return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
  } else {
    const day = date.getDate();
    const month = date.getMonth() + 1;
    const year = date.getFullYear();

    return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
  }
};
