import { PropsOf } from '@emotion/react';
import { TextField } from '@mui/material';
import { FieldErrors } from 'react-hook-form';

/**
 * Helper function to get the error and helperText props for a TextField component
 * @param field Name of the field to check for errors
 * @returns An object with the error and helperText props for the TextField component
 */
export const errorHelper = <InputType>(
  field: keyof InputType,
  errors: FieldErrors<InputType>,
): Partial<PropsOf<typeof TextField>> => {
  const err = errors?.[field];

  if (!err) {
    return {};
  }
  console.log('err', err, err?.message);

  let helperText: string;
  if (err.message) {
    if (typeof err.message === 'string') {
      helperText = err.message;
    } else if (Array.isArray(err.message)) {
      helperText = err.message.join('\n');
    } else if (typeof err.message === 'object') {
      helperText = Object.values(err.message).join('\n');
    }
  } else if (err.type === 'required') {
    helperText = 'This field is required.';
  } else {
    helperText = 'An unknown error occurred.';
  }

  return {
    helperText,
    error: true,
  };
};
