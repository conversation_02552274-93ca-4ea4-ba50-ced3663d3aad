import { CLIENT_SETTING_PRIVATE_SOURCE_WARNING } from '../../utils/constants';
import { ContentOutputOriginal } from '../../utils/model';
import { GroupingDto } from '../api/groupings.api';
import {
  MetadataFilterTypeDto,
  formatMDFValuesForUIFromQuestionnaire,
} from '../api/metadata.api';
import { QuestionDto, getDocsByIds } from '../api/question.api';
import { getClientSetting } from '../api/user.api';
import {
  getGeneratedAnswer,
  processAnswerCitations,
  reorderCitations,
} from '../pages/HomePage/pages/ViewQuestionnaire/QuestionList/answerProcessing';

export const isProcessingAnswer = (question: QuestionDto) => {
  return question.state == 'processing';
};

export const isModifiedAnswer = (question: QuestionDto) => {
  return (
    question.state == 'modified' ||
    (question?.output_modified?.text !== undefined &&
      question?.output_modified?.text !== null)
  );
};

export const isAcceptedAnswer = (question: QuestionDto) => {
  return question.state == 'accepted';
};

/*
In general, should use the translated answer if the key is present.
However, corner-case:

Case 1
- original answer
- original gets translated
- user modifies the translated
Result: should show modified.

Case 2
- original answer
- user modifies the original
- user then translates (will use modified answer)
Result: should show translated

Also need to handle the revert workflow for each (as appropriate).

Note: this is why we store the "source_text" in the translation object.
*/
export const shouldUseTranslatedAnswer = (question: QuestionDto) => {
  const translatedText = question.output_original?.translation?.translation;
  const translatedSourceText =
    question.output_original?.translation?.source_text;

  const isModified = isModifiedAnswer(question);

  // If not modified, easy: just use translated text if it's available
  if (!translatedText) {
    return false;
  } else if (!isModified) {
    return true;
  } else {
    // Handle cases commented above
    if (translatedSourceText == question?.output_modified?.text) {
      return true;
    } else {
      return false;
    }
  }
};

export const hasConfidenceScore = (question: QuestionDto) => {
  return question.output_original.confidence_score != undefined;
};

export const couldNotGenerateAnswer = (question: QuestionDto) => {
  if (isMultiVersionAnswer(question)) {
    return question.output_original.answer_versions.every((answer) => {
      // Answer RFP uses this same string...need some shared constants...
      return answer?.review_message?.includes(
        'Could not generate an answer to the question',
      );
    });
  } else {
    return question.output_original?.review_message?.includes(
      'Could not generate an answer to the question',
    );
  }
};

export const isMultiVersionAnswer = (question: QuestionDto) => {
  return (
    question.output_original?.answer_versions !== undefined &&
    question.output_original?.answer_versions?.length > 0
  );
};

export const numMultiVersionAnswers = (question: QuestionDto) => {
  return isMultiVersionAnswer(question)
    ? question.output_original?.answer_versions?.length
    : 0;
};

export const requiresRegeneration = (
  availableGroupings: GroupingDto[],
  questionnaireMDF,
  oldGroupingId: number,
  newGroupingId: number,
): boolean => {
  // Check if question requires regeneration:
  // - if grouping changed, where metadata filters are different
  let requiresRegeneration = false;
  const formattedQuestionnaireMDF =
    formatMDFValuesForUIFromQuestionnaire(questionnaireMDF);

  if (oldGroupingId != newGroupingId) {
    const oldGrouping = availableGroupings.find(
      (grouping) => grouping.id == oldGroupingId,
    );
    const newGrouping = availableGroupings.find(
      (grouping) => grouping.id == newGroupingId,
    );

    if (!oldGrouping && !newGrouping) {
      // This shouldn't happen
      requiresRegeneration = false;
    } else if (!oldGrouping) {
      // If no old grouping, it would have used the questionnaire MDF (if any)
      // Compare newGrouping MDF to questionnaire MDF
      if (!_.isEqual(newGrouping.metadata_filter, formattedQuestionnaireMDF)) {
        requiresRegeneration = true;
      }
    } else if (!newGrouping) {
      // Check if oldGrouping or the questionnaire has any MDF
      if (
        Object.keys(oldGrouping.metadata_filter).length ||
        Object.keys(formattedQuestionnaireMDF).length
      ) {
        requiresRegeneration = true;
      }
    } else {
      // Else, check if MDF is different

      // If oldGrouping has MDF, compare to newGrouping
      if (Object.keys(oldGrouping.metadata_filter).length) {
        if (
          !_.isEqual(oldGrouping.metadata_filter, newGrouping.metadata_filter)
        ) {
          requiresRegeneration = true;
        }
      } else {
        // Else, compare newGrouping to questionnaire MDF
        if (!_.isEqual(newGrouping.metadata_filter, questionnaireMDF)) {
          requiresRegeneration = true;
        }
      }
    }
  }

  return requiresRegeneration;
};

export const hasDisplayQuestion = (question: QuestionDto) => {
  return (
    question.input?.meta && question.input?.meta['displayQuestion']?.length > 0
  );
};

// Account for displayQuestion populated via E2E RFP Spreadsheets
export const questionText = (question: QuestionDto) => {
  return hasDisplayQuestion(question)
    ? question.input?.meta['displayQuestion']
    : question.input.query_string;
};

// Check if any of the sources that were RAG'd are verbatim sources
export const verbatimSources = (
  answer: ContentOutputOriginal,
  metadataFilterTypes: MetadataFilterTypeDto[],
) => {
  // Check if any MDF is even verbatim
  const verbatimMDF = metadataFilterTypes.filter((mdf) => mdf.is_verbatim);

  if (verbatimMDF.length == 0) {
    return [];
  }

  const docSources = answer?.documents?.docs || [];
  const qaSources = answer?.documents?.qa || [];

  return [...docSources, ...qaSources].filter((source) => {
    return Object.keys(source.metadata_filter ?? {})?.some((mdft_id) => {
      return verbatimMDF.some((mdf) => mdf.id == parseInt(mdft_id));
    });
  });
};

export const getUsedSourceMetadata = async (
  originalOutput: ContentOutputOriginal,
) => {
  let processedAnswer = processAnswerCitations(originalOutput?.answer, false);
  const { usedCitationIndexes } = reorderCitations(processedAnswer);

  const outputOriginal = originalOutput;
  const docSources = outputOriginal?.documents?.docs || [];
  const qaSources = outputOriginal?.documents?.qa || [];
  let combinedSources = Array(docSources.length + qaSources.length).fill(null);
  docSources.forEach((source, i) => {
    combinedSources[source.context_index - 1] = source;
  });

  qaSources.forEach((source, i) => {
    combinedSources[source.context_index - 1] = source;
  });

  // For each citation index, find the document id and query to see if
  // the document is marked private. (We can add other metadata in future.)
  const docs = await getDocsByIds(
    usedCitationIndexes.map((idx) => combinedSources[idx - 1].document_id),
  );

  return docs?.response ?? [];
};

export const getPrivateSourceWarningMessage = async () => {
  const privateSourceWarning = await getClientSetting(
    CLIENT_SETTING_PRIVATE_SOURCE_WARNING,
  );

  return privateSourceWarning?.value ?? '';
};
