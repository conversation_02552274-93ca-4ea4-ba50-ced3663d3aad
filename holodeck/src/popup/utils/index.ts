export const markdownToHtml = (markdown: string) => {
  if (!markdown) return '';

  if (Array.isArray(markdown)) {
    markdown = markdown.join(' ');
  } else if (typeof markdown === 'object') {
    try {
      markdown = JSON.stringify(markdown);
    } catch (err) {
      // If we can't stringify it, just set it to an empty string
      markdown = '';
    }
  }

  return markdown
    .replaceAll(/\\n/g, '<br>')
    .replaceAll(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replaceAll(/\\/g, '');
};

export const SUPPORTED_LANGUAGES = [
  'English',
  'Albanian',
  'Amharic',
  'Arabic',
  'Armenian',
  'Bengali',
  'Bosnian',
  'Bulgarian',
  'Burmese',
  'Catalan',
  'Chinese',
  'Croatian',
  'Czech',
  'Danish',
  'Dutch',
  'English (UK)',
  'Estonian',
  'Finnish',
  'French',
  'Georgian',
  'German',
  'Greek',
  'Gujarati',
  'Hindi',
  'Hungarian',
  'Icelandic',
  'Indonesian',
  'Italian',
  'Japanese',
  'Kannada',
  'Kazakh',
  'Korean',
  'Latvian',
  'Lithuanian',
  'Macedonian',
  'Malay',
  'Malayalam',
  'Marathi',
  'Mongolian',
  'Norwegian',
  'Persian',
  'Polish',
  'Portuguese',
  'Punjabi',
  'Romanian',
  'Russian',
  'Serbian',
  'Slovak',
  'Slovenian',
  'Somali',
  'Spanish',
  'Swahili',
  'Swedish',
  'Tagalog',
  'Tamil',
  'Telugu',
  'Thai',
  'Turkish',
  'Ukrainian',
  'Urdu',
  'Vietnamese',
];
