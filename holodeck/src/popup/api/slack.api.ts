import { ApiResultDto, SlackWorkflow } from '../types';
import { get, post } from './apiUtils';

export type SlackSettings = {
  collaboration_channel: { id: string; name: string }[];
};

export const getSlackSettings = async (): Promise<
  { id: string; name: string }[]
> => {
  const result = await get<ApiResultDto<{ id: string; name: string }[]>>(
    '/slack_collab_channel?include_names=1',
  );
  if (result.success) {
    // Temporary while backend and Chrome Ext can be out of sync
    if (typeof result.response === 'string') {
      if (result.response == '') {
        return [];
      }
      return [
        {
          id: result.response,
          name: result.response,
        },
      ];
    } else {
      return result.response;
    }
  }
  return [];
};

export const sendToSlackGetHelp = async (
  contentDetailId: number,
  note: string,
  collaboration_channel?: string,
): Promise<ApiResultDto<any>> => {
  const workflow: SlackWorkflow<'ANSWER_HELP'> = {
    type: 'ANSWER_HELP',
    content_detail_id: contentDetailId,
    note,
    collaboration_channel,
  };
  const result = await post<ApiResultDto<any>>('/slack_workflow', { workflow });
  return result;
};
