// import { AUTH0_CONFIG } from '../../_config/constants_auth.dev';
import urlJoin from 'url-join';
import { TRIBBLE_APP_URL } from '../../utils/constants_auth';
import { getAccessToken, getEnv } from '../state/auth.storage';
import { ApiResultDto } from '../types';

export const get = async <ResultBody>(path: string): Promise<ResultBody> =>
  doFetch(path);

export const post = async <ResultBody, RequestBody = any>(
  path: string,
  body: RequestBody,
): Promise<ResultBody> => {
  return doFetch(path, {
    method: 'POST',
    body: JSON.stringify(body),
  });
};

export const patch = async <ResultBody, RequestBody = any>(
  path: string,
  body: RequestBody,
): Promise<ResultBody> => {
  return doFetch(path, {
    method: 'PATCH',
    body: JSON.stringify(body),
  });
};

export const deleteCall = async <ResultBody, RequestBody = any>(
  path: string,
): Promise<ResultBody> => {
  return doFetch(path, {
    method: 'DELETE',
  });
};

/**
 * This function adapts a function that returns an ApiResultDto<T> to return the response body of the API call.
 * On success, it returns the result.response. It throws an error if result.success is false or any other error occurs.
 * In this sense, it forces the caller to handle the error case and so reduces the chance of an unhandled error.
 *
 * @params label A label for the API call, used for logging
 * @param cb The function to be adapted to return the response body of the API call
 * @returns A function that adapts the given function to return the response body of the API call
 */
export const adaptify = <
  ParamTypes,
  ParamsArr extends Array<ParamTypes>,
  ReturnType,
>(
  label: string,
  cb: (...args: ParamsArr) => Promise<ApiResultDto<ReturnType>>,
  failSilently = false,
) => {
  return async (...args: ParamsArr) => {
    try {
      const result = await cb(...args);

      if (!result.success && !failSilently) {
        throw new Error(
          `[Tribble] API response body had success set to ${result.success}.`,
        );
      } else if (!result.success && failSilently) {
        return null;
      }

      return result.response;
    } catch (e) {
      console.error(`[Tribble] API call with label [${label}] failed.`, e);
      // throw because it still needs to be handled by the caller
      throw e;
    }
  };
};

// Private

const getApiRoot = (rootDomain: string) => urlJoin(rootDomain, 'api');

const ensureAccessToken = async () => {
  const accessToken = await getAccessToken();
  if (!accessToken)
    throw new Error('[Tribble] API call failed: No access token found.');
  return accessToken;
};

const ensureRootDomain = async () => {
  const env = await getEnv();
  const domain = TRIBBLE_APP_URL[env as keyof typeof TRIBBLE_APP_URL];

  if (!domain)
    throw new Error('[Tribble] API call failed: No environments found.');

  const root = getApiRoot(domain);

  return root;
};

export const doFetch = async <ResponseBody>(
  path: Parameters<typeof fetch>[0],
  init?: Parameters<typeof fetch>[1],
): Promise<ResponseBody> => {
  if (typeof path !== 'string') {
    throw new Error('[Tribble] Only string paths are implemented in doFetch.');
  }

  const accessToken = await ensureAccessToken();
  const root = await ensureRootDomain();
  const headers = {
    Authorization: `Bearer ${accessToken}`,
    'Content-Type': 'application/json',
  };
  const finalUrl = urlJoin(root, path);
  const response = await fetch(finalUrl, { headers, ...init });
  const json = await response.json();
  return json;
};

export const doFetchNoContentType = async <ResponseBody>(
  path: Parameters<typeof fetch>[0],
  init?: Parameters<typeof fetch>[1],
): Promise<ResponseBody> => {
  if (typeof path !== 'string') {
    throw new Error('[Tribble] Only string paths are implemented in doFetch.');
  }

  const accessToken = await ensureAccessToken();
  const root = await ensureRootDomain();
  const headers = {
    Authorization: `Bearer ${accessToken}`,
  };
  const finalUrl = urlJoin(root, path);
  const response = await fetch(finalUrl, { headers, ...init });
  const json = await response.json();
  return json;
};
