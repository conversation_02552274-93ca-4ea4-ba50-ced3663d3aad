import { ApiResultDto } from '../types';
import { deleteCall, get, patch, post } from './apiUtils';

import { ContentInput, ContentOutputOriginal } from '../../utils/model';

export type ContentDetailState =
  | 'processing'
  | 'initial'
  | 'accepted'
  | 'rejected'
  | 'modified';
export type QuestionDto = {
  id?: number;
  content_id: number;
  index: number;
  input: ContentInput;
  output_original?: ContentOutputOriginal;
  output_modified?: {
    text: string;
  };
  modified_by_user_id?: number;
  modified_by_name?: string;
  modified_at?: Date;
  state: ContentDetailState;
  assignee_id?: number;
  assignee_name?: string;
  assignee_picture?: string;
  grouping_id?: number;
  grouping_name?: string;
  prev_content_detail_id?: number;
  next_content_detail_id?: number;
  cnt?: number; // Used for count_only version of getContentDetail
  conversation_id?: number;
};

export const deleteQuestion = async (content_detail_id: number) => {
  return deleteCall<ApiResultDto<any>>(
    `/questionnaire_question/${content_detail_id}`,
  );
};

interface UpdateQuestionDto {
  content_detail_id: number;
  status: ContentDetailState;
  grouping_id: number | null;
  assignee_id: number | null;
}

export const updateQuestionMeta = async (params: UpdateQuestionDto) => {
  return patch<ApiResultDto<any>>('/questionnaire_question', params);
};

export const modifyAnswer = async (
  content_detail_id: number,
  modified_text: string,
) => {
  return post<ApiResultDto<any>>('/modify_content_detail', {
    content_detail_id,
    modified_text,
  });
};

export const setQuestionStatus = async (
  content_detail_id: number,
  state: ContentDetailState,
) => {
  const params = new URLSearchParams({
    content_detail_id: `${content_detail_id}`,
    state,
  });
  return get<ApiResultDto<any>>(`/set_cd_state?${params.toString()}`);
};

export const getQuestions = async (content_id: string) => {
  return get<ApiResultDto<QuestionDto[]>>(
    `/get_content_detail?content_id=${content_id}`,
  );
};

export const getSingleQuestion = async (content_detail_id: number) => {
  return get<ApiResultDto<QuestionDto>>(`/question/${content_detail_id}`);
};

export const postModifyAnswer = async (
  content_detail_id: number,
  modified_text: string,
) => {
  return post<ApiResultDto<any>>('/modify_content_detail', {
    content_detail_id,
    modified_text,
  });
};

export const postRevertAnswer = async (content_detail_id: number) => {
  return post<ApiResultDto<any>>('/revert_content_detail', {
    content_detail_id,
  });
};

export const patchQuestion = async (
  content_detail_id: number,
  status: string,
  grouping_id: number = null,
  assignee_id: number = null,
  content_id: number,
  regenerate = false,
) => {
  return patch<ApiResultDto<any>>('/questionnaire_question', {
    content_detail_id,
    status,
    grouping_id,
    assignee_id,
    content_id,
    regenerate,
  });
};

type AddQuestionsToQuestionnaireParams = {
  content_id: string;
  job_id: number;
  questions: {
    query_string: string;
    assignee_id: number | null;
    grouping_id: number | null;
  }[];
};
export const addQuestionsToQuestionnaire = async (
  params: AddQuestionsToQuestionnaireParams,
) => {
  const { content_id, job_id, questions } = params;

  type ReqBody = {
    content_id: string;
    job_id: number;
    questions: {
      query_string: string;
      assignee_id: number | null;
      grouping_id: number | null;
    }[];
  };

  const body: ReqBody = {
    content_id,
    job_id,
    questions,
  };

  return post<ApiResultDto<any>>('/questionnaire_questions', body);
};

export const bulkUpdateQuestions = async (action: string, payload: any) => {
  return post<ApiResultDto<any>>('/questionnaire_bulk_update', {
    action,
    payload,
  });
};

export const getDocsByIds = async (documentIds: number[]) => {
  return get<ApiResultDto<any>>('/docs_by_ids?ids=' + documentIds.join(','));
};
