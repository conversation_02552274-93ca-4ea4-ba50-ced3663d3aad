import useSwr from 'swr';
import {
  getSalesforceOpportunities,
  getSalesforceOpportunity,
  getSalesforceUser,
  salesforceOpportunitiesSearch,
} from './salesforce.api';

export const useSalesforceUser = () =>
  useSwr('salesforceUser', getSalesforceUser);

export const useSalesforceOpportunities = (isConnectedToSalesforce: boolean) =>
  useSwr(
    isConnectedToSalesforce ? 'salesforceOpportunities' : null,
    getSalesforceOpportunities,
    {
      shouldRetryOnError: false,
    },
  );

export const useSalesforceOpportunity = (opportunityId: string) =>
  useSwr(
    opportunityId && opportunityId !== 'undefined'
      ? ['salesforceOpportunity', opportunityId]
      : null,
    ([url, opportunityId]) => getSalesforceOpportunity(opportunityId),
  );

export const useSalesforceOpportunitiesSearch = (searchTerm: string) =>
  useSwr(
    !searchTerm || searchTerm.length < 3
      ? null
      : ['salesforceOpportunitiesSearch', searchTerm],
    ([url, searchTerm]) => salesforceOpportunitiesSearch(searchTerm),
  );
