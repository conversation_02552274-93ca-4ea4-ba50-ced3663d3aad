import { ApiResultDto } from '../types';
import { adaptify, get, post } from './apiUtils';

export type UserDto = {
  id: number;
  name: string;
  email: string;
  picture: string;
};

export const getUsers = adaptify('GET users', () =>
  get<ApiResultDto<UserDto[]>>('/user_list'),
);

export type UserDetailDto = {
  id: string;
  client_id: string;
  name: string;
  email: string;
  status: string;
  is_admin: boolean;
  is_tribble: boolean;
  is_deleted: boolean;
  client: {
    id: string;
    name: string;
    storage_id: string;
    database_schema_id: string;
  };
};
export const getCurrentUser = async (): Promise<UserDetailDto> => {
  const response = await get<ApiResultDto<UserDetailDto>>('/user_detail');
  if (!response || !response.success) {
    throw new Error('Failed to get current user details from API.');
  }
  return response.response;
};

export const getUserSetting = async (setting: string): Promise<any> => {
  const response = await get<ApiResultDto<any>>(
    `/user_setting?name=${setting}`,
  );
  if (!response || !response.success) {
    return null;
  }
  return response.response;
};

export const updateUserSetting = async (
  settingName: string,
  settingValue: string,
): Promise<boolean> => {
  const response = await post<ApiResultDto<any>>('/user_setting', {
    settingName,
    settingValue,
  });
  return response.success;
};

export const getClientSetting = async (setting: string): Promise<any> => {
  const response = await get<ApiResultDto<any>>(
    `/client_setting_by_name?setting_name=${setting}`,
  );
  if (!response || !response.success) {
    return null;
  }
  return response.response;
};
