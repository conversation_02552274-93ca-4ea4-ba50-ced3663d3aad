import { ApiResultDto } from '../types';
import { adaptify, get } from './apiUtils';

export type MetadataFilterValueDto = {
  id: number;
  value: string;
  is_active: boolean;
  type_id?: number;
  type_name?: string;
  synonyms?: string;
  description?: string;
  rfp_default?: boolean;
};

export type MetadataFilterTypeDto = {
  id: number;
  name: string;
  distinct_answer?: boolean;
  is_verbatim?: boolean;
  filter_values: MetadataFilterValueDto[];
};

export type MetadataFiltersDto = {
  metadata_filters: MetadataFilterTypeDto[];
};

export type MetadataFilterValueDB =
  | {
      type_id: number;
      type_name: string;
      values: string[];
      type_distinct_answer?: boolean;
    }
  | {
      multiple_types: {
        type_id: number;
        type_name: string;
        values: string[];
        type_distinct_answer?: boolean;
      }[];
    };

export const getMetadataFilters = adaptify('GET metadataFilters', () =>
  get<ApiResultDto<MetadataFiltersDto>>('/metadata_filters'),
);

export const getMappedMetdataFilterValues = (
  all_mdf: MetadataFilterTypeDto[],
): Record<number, MetadataFilterValueDto> => {
  let mdf_values = {};
  all_mdf.forEach((mdf) => {
    mdf.filter_values.forEach((mdfv) => {
      mdf_values[mdfv.id] = mdfv;
    });
  });
  return mdf_values;
};

// This function returns the metadata filter value selections in the database's expected metadata_filter format.
// This will mirror the questionaire-level MDF definition:
// {
//      multiple_types: [
//          {
//              type_id: [typeId1],
//              type_name: [typeName1],
//              values: ['value1', 'value2']
//          },
//          {
//              ...
//          },  ...
// }
export const formatMDFValuesForDatabase = (
  mdf_values: MetadataFilterValueDto[],
) => {
  const mapped = {};

  mdf_values.map((mdfv) => {
    const type_key = '' + mdfv.type_id + '~!~' + mdfv.type_name;
    if (!mapped[type_key]) mapped[type_key] = [];
    mapped[type_key].push(mdfv.value);
  });

  return Object.keys(mapped).map((key) => {
    const [type_id, type_name] = key.split('~!~');
    return {
      type_id: String(type_id),
      type_name,
      values: mapped[key],
    };
  });
};

// Common enough function I think
// Output format:
// {
//      [typeName1]: ['value1', 'value2'],
//      [typeName2]: ['value1', 'value2'],
//      ...
// }
export const formatMDFValuesForUI = (
  mdf_values: MetadataFilterValueDB,
): Record<string, string[]> => {
  if (mdf_values['multiple_types']) {
    return mdf_values['multiple_types'].reduce((acc, curr) => {
      if (curr.type_id && curr.values && curr.values.length > 0) {
        acc[curr.type_name] = curr.values;
      }
      return acc;
    }, {});
  } else if (mdf_values['type_id']) {
    return {
      [mdf_values['type_name']]: mdf_values['values'],
    };
  } else {
    return {};
  }
};

// An unfortunate consequence of not supporting multi-MDF in the past, and then supporting it.
// MDF at the questionnaire level is stored like:
// {
//     values?: string[];
//     type_id?: number;
//     type_name?: string;
//     multiple_types?: {
//         values?: string[];
//         type_id?: number;
//         type_name?: string;
//     }[];
// }
// Convert that to the formatMDFValuesForUI() format in the function above
export const formatMDFValuesForUIFromQuestionnaire = (
  questionnaireMDF: Record<string, any>,
): Record<string, string[]> => {
  if (!questionnaireMDF?.multiple_types && !questionnaireMDF?.type_id) {
    return {};
  }

  if (questionnaireMDF.multiple_types) {
    return questionnaireMDF.multiple_types.reduce((acc, curr) => {
      if (curr.type_id && curr.values && curr.values.length > 0) {
        acc[String(curr.type_id)] = curr.values || [];
      }
      return acc;
    }, {});
  } else {
    return {
      [String(questionnaireMDF.type_id)]: questionnaireMDF.values,
    };
  }
};

// Check if the MDF is the "special" generic (i.e. "all" / no context) MDF:
// { type_id: 0, values: ['all'] }
export const usesAllMDF = (mdf_values: MetadataFilterValueDB): boolean => {
  return (
    mdf_values['type_id'] == '0' &&
    JSON.stringify(mdf_values['values']) == '["all"]'
  );
};

// TODO: Delete below once above adaptify version (getMetadataFilters) is tested
export const getAvailableMetadataFilters = async () =>
  await get<ApiResultDto<MetadataFiltersDto>>('/metadata_filters');
