import { ApiResultDto } from '../types';
import { adaptify, get, post } from './apiUtils';
import { MetadataFilterValueDB } from './metadata.api';

export type GroupingDto = {
  id: number;
  name: string;
  metadata_filter: Record<string, string[]>;
};
export const getGroupings = adaptify('GET groupings', () =>
  get<ApiResultDto<GroupingDto[]>>('/groupings'),
);

export type ContentGroupingDto = {
  grouping_id: number;
  grouping_name: string;
  grouping_metadata_filter: MetadataFilterValueDB;
};

export const getContentGroupings = adaptify(
  'GET contentGroupings',
  (contentId: number) =>
    get<ApiResultDto<ContentGroupingDto[]>>(
      `/questionnaire_groupings/${contentId}`,
    ),
);

export const createGrouping = adaptify(
  'POST groupings',
  (name: string, contentId: number, metadataFilter) =>
    post<ApiResultDto<never>>('/grouping', {
      name,
      content_id: contentId,
      metadata_filter: metadataFilter,
    }),
);
