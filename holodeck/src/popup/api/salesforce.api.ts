import { ApiResultDto } from '../types';
import { adaptify, get, post } from './apiUtils';

export type SalesforceUserAuth = {
  has_auth: boolean;
  user_id: string;
  instance_url: string;
};

export type SalesforceOpportunityDto = {
  Id: string;
  Name: string;
  Amount: number;
  __LineItmes?: any[];
};

export const getSalesforceLoginUrl = adaptify('GET salesforce login URL', () =>
  get<ApiResultDto<string>>('/salesforce/login'),
);

export const getSalesforceUser = adaptify(
  'GET salesforce user',
  () => get<ApiResultDto<SalesforceUserAuth>>('/salesforce/user/details'),
  true,
);

export const disconnectSalesforce = adaptify('POST disconnect salesforce', () =>
  post<ApiResultDto<any>>('/salesforce/disconnect', {}),
);

export const getSalesforceOpportunities = adaptify(
  'GET salesforce get opportunities',
  () => get<ApiResultDto<{ records: any[] }>>('/salesforce/opportunities'),
  true,
);

export const getSalesforceOpportunity = adaptify(
  'GET salesforce get opportunities',
  (opportunityId: string) =>
    get<ApiResultDto<SalesforceOpportunityDto>>(
      `/salesforce/opportunities/${opportunityId}`,
    ),
  true,
);

export const salesforceOpportunitiesSearch = adaptify(
  'GET salesforce search opportunities',
  (search: string) =>
    get<ApiResultDto<{ autoSuggestResults?: any[] }>>(
      `/salesforce/opportunities/search?q=${search}`,
    ),
);
