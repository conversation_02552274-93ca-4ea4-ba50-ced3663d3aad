import { Rfp } from '../../components/EndToEndRfp';
import { ApiResultDto } from '../types';
import { deleteCall, get, patch, post } from './apiUtils';

export type LongformStatusValue =
  | 'new'
  | 'generating_outline'
  | 'in_review_outline'
  | 'generating_response'
  | 'in_review_response'
  | 'finished'
  | 'error';

type E2eSpreadsheetStatusValue =
  | 'new'
  | 'analyzing'
  | 'long_form_go_no_go'
  | 'spreadsheet_confirm_analysis'
  | 'generating'
  | 'in_review'
  | 'finished'
  | 'error'
  | 'rejected';

export type RfpStatusValue = E2eSpreadsheetStatusValue | LongformStatusValue;

export type RfpStatusLabelValue =
  | 'New'
  | 'Analyzing Excel'
  | 'Analyzing PDF'
  | 'Awaiting Go/No-Go'
  | 'Awaiting Analysis Confirmation'
  | 'Gathering Answers'
  | 'Generating Response'
  | 'In Review'
  | 'Finished'
  | 'Error'
  | 'Rejected'
  | 'Generating Outline'
  | 'Awaiting Outline Review'
  | 'Awaiting Response Review'
  | 'Unknown';

export type QuestionnaireDto = {
  job_id: number;
  job_type: string;
  document_id: string;
  created_date: Date;
  file_name: string;
  created_by_name: string;
  content_id: string;
  source: 'EXTENSION';
  has_e2e_workbook: boolean;
  metadata_filter: {
    values?: string[];
    type_id?: number;
    type_name?: string;
    multiple_types?: {
      values?: string[];
      type_id?: number;
      type_name?: string;
    }[];
  } | null;
  groupings: { grouping_id: number; grouping_name: string }[] | null;
  assignees: Assignee[] | null;
  created_by_id?: number;
  label?: string;
  details?: {
    project_name?: string;
    project_description?: string;
    customer_name?: string;
    rfpId?: string;
    fileName?: string;
    files?: {
      url: string;
      name: string;
      mimetype: string;
    };
    parentContentId?: string;
    agentParams?: any;
    custom_instructions?: string;
    terse?: boolean;
    verbosity_words?: number;
    target_language?: string;
  };
  due_date: Date;
  num_questions?: string;
  num_questions_accepted?: string;
  num_questions_modified?: string;
  job_log_data?: any[];
  rfx_content_id: string;
  project_content_status: RfpStatusValue;
  project_content_status_label: RfpStatusLabelValue;
  owner: LimitedUser;
  opportunity_id?: string;
  project_description?: string;
};

export type Assignee = {
  assignee_id: number;
  user_name?: string;
  is_owner?: boolean | null;
};

interface LimitedUser {
  id: number;
  name: string;
  email: string;
  picture?: string;
}

export const getAllQuestionnaires = async () => {
  const responsePromise = get('/get_rfps?source=EXTENSION') as Promise<
    ApiResultDto<QuestionnaireDto[]>
  >;
  const teamsPromise = get('/get_rfps?source=TEAMS') as Promise<
    ApiResultDto<QuestionnaireDto[]>
  >;
  const slackPromise = get('/get_rfps?source=SLACK') as Promise<
    ApiResultDto<QuestionnaireDto[]>
  >;

  const results = await Promise.all([
    responsePromise,
    teamsPromise,
    slackPromise,
  ]);

  const combinedResults = results.flatMap((result) => result.response || []);

  return {
    response: combinedResults,
    success: results.every((r) => r.success),
  };
};
export type CreateQuestionnaireDto = {
  source: 'EXTENSION';
  due_date?: Date;
  assignee_list?: Assignee[];
  metadata: {
    uuid: string;
    type: 'RFP';

    label: string;
    projectDescription?: string;
    customerName: string;

    terse: boolean;
    verbosityWords: number;

    metadataFilter: {
      values?: string[];
      type_id?: number;
      type_name?: string;
      multiple_types?: {
        values?: string[];
        type_id?: number;
        type_name?: string;
      }[];
    } | null;

    privacy: 'public';

    opportunityId?: string;

    targetLanguage?: string;

    customInstructions?: string;
  };
};

export type CreateQuestionnaireResponseDto = {
  contentId: string | number; // this should be a number but postgres is weird
  jobId: number;
  rfxContentId: string | number;
};

export type PatchQuestionnaireDto = {
  content_id: string;
  job_id: number;
  document_id: string;
  status: RfpStatusValue;
  label: string;
  projectDescription?: string;
  details?: any;
  due_date?: Date | null;
  assignees: Assignee[] | null;
  metadata_filter?: {
    values?: string[];
    type_id?: number;
    type_name?: string;
    multiple_types?: {
      values?: string[];
      type_id?: number;
      type_name?: string;
    }[];
  } | null;
  customInstructions?: string;
  opportunity_id?: string;
};

export const createQuestionnaire = async (
  questionnaire: CreateQuestionnaireDto,
): Promise<CreateQuestionnaireResponseDto> => {
  const result = await post<ApiResultDto<CreateQuestionnaireResponseDto>>(
    '/questionnaire',
    questionnaire,
  );
  if (!result.success || !result.response) {
    return Promise.reject(result);
  }
  return result.response;
};

export const getQuestionnaireById = async (
  questionnaireId: string | number,
) => {
  const result = await get<ApiResultDto<QuestionnaireDto>>(
    `/questionnaire?content_id=${questionnaireId}`,
  );
  if (!result.success || !result.response) {
    return Promise.reject(result);
  }
  return result.response;
};

export const patchQuestionnaire = async (
  questionnaire: PatchQuestionnaireDto,
  rfxContentId: string | number,
) => {
  return patch<ApiResultDto<any>>(`/questionnaire`, {
    ...questionnaire,
    rfx_content_id: rfxContentId,
  });
};

export const deleteQuestionnaire = async (content_id: number) => {
  return deleteCall<ApiResultDto<any>>(`/delete_rfp?content_id=${content_id}`);
};

export const getQuestionnaireJobStatus = async (jobId: number) => {
  const result = await get<ApiResultDto<any[]>>(
    `/questionnaire_job_status?job_id=${jobId}`,
  );
  if (!result.success || !result.response) {
    return [];
  }
  return result.response;
};

export const patchE2ERfp = async (rfp: Rfp) => {
  return patch<ApiResultDto<any>>('/rfp', rfp);
};

export const deleteRfp = async (rfp: Rfp) => {
  return deleteCall<ApiResultDto<any>>(`/completed-rfp?content_id=${rfp.id}`);
};
