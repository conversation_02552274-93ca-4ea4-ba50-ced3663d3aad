import useSWR from 'swr';
import { getContentGroupings, getGroupings } from './groupings.api';

export const useGroupings = () =>
  useSWR('groupings', getGroupings, {
    fallbackData: [],
    revalidateOnFocus: false,
  });

export const useContentGroupings = (contentId: number) =>
  useSWR(
    contentId ? ['contentGroupings', contentId] : null,
    contentId ? ([_url, id]) => getContentGroupings(id) : null,
    {
      fallbackData: [],
      revalidateOnFocus: false,
    },
  );
