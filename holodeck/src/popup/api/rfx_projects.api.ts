import { Rfp } from '../../components/EndToEndRfp';
import { ApiResultDto } from '../types';
import { adaptify, get } from './apiUtils';
import {
  Assignee,
  LongformStatusValue,
  QuestionnaireDto,
  RfpStatusLabelValue,
  RfpStatusValue,
} from './questionnaires.api';

// New RFX project model types
type ProjectStatus = 'new' | 'finished' | 'rejected';

type RfxProjectDto = {
  id: number; // rfx.id - primary key in rfx table
  rfx_id: string; // rfx.rfx_id - uuid for rfx
  name: string;
  description: string | null;
  customer_name: string | null;
  project_status: ProjectStatus;
  status_message: string | null;
  due_date: string | null;
  created_at: string;
  opportunity_id: string | null;
  owner: {
    id: number;
    name: string;
    email: string;
  };
  content_records: RfxContentRecordDto[];
};

type RfxContentRecordDto = {
  id: number; // content.id - primary key in content table
  job_id: number;
  details: any;
  created_date: string;
  last_updated: string;
  num_questions: number;
  num_reviewed: number;
  owner: {
    id: number;
    name: string;
    email: string;
  };
  status: RfpStatusValue;
  status_message: string | null;
  rfx_content_id: number; // rfx_content.id - primary key in rfx_content table
  metadata_filter: any;
};

// Get all RFX projects
export const getAllRfxProjects = async (): Promise<
  ApiResultDto<RfxProjectDto[]>
> => {
  return get<ApiResultDto<RfxProjectDto[]>>('/projects');
};

// Get RFX project by rfx_content ID (the joining table ID)
export const getRfxProjectByRfxContentId = async (
  rfxContentId: number,
): Promise<ApiResultDto<RfxProjectDto>> => {
  return get<ApiResultDto<RfxProjectDto>>(
    `/projects/by-rfx-content-id/${rfxContentId}`,
  );
};

// Helper functions to map between old and new models

// Map RFX project to questionnaire DTO format for backwards compatibility
export const mapRfxProjectToQuestionnaireDto = (
  project: RfxProjectDto,
  contentRecord: RfxContentRecordDto,
): QuestionnaireDto => {
  return {
    job_id: contentRecord.job_id,
    job_type: 'rfp',
    document_id: contentRecord.details?.rfpId || '',
    content_id: contentRecord.id.toString(), // This is content.id
    created_date: new Date(contentRecord.created_date),
    file_name: contentRecord.details?.fileName || '',
    created_by_name: contentRecord.owner?.name || '',
    created_by_id: contentRecord.owner?.id,
    source: 'EXTENSION',
    has_e2e_workbook: false,
    metadata_filter: contentRecord.metadata_filter,
    groupings: null,
    assignees: [
      {
        assignee_id: contentRecord.owner?.id || 0,
        user_name: contentRecord.owner?.name || '',
        is_owner: true,
      },
    ] as Assignee[],
    label: project.name,
    details: {
      project_name: project.name,
      project_description: project.description || '',
      customer_name: project.customer_name || '',
      rfpId: project.rfx_id || '',
      fileName: contentRecord.details?.fileName || '',
      // Use rfx_content_id from the join table for parentContentId
      parentContentId: contentRecord.rfx_content_id.toString(),
      agentParams: contentRecord.details?.agentParams || {},
      custom_instructions: contentRecord.details?.custom_instructions || '',
      terse: contentRecord.details?.terse,
      verbosity_words: contentRecord.details?.verbosity_words,
      target_language: contentRecord.details?.target_language,
    },
    due_date: project.due_date ? new Date(project.due_date) : new Date(),
    num_questions: contentRecord.num_questions?.toString() || '0',
    num_questions_accepted: contentRecord.num_reviewed?.toString() || '0',
    num_questions_modified: '0',
    project_content_status_label: mapRfxContentStatusLabel(
      contentRecord.status,
    ),
    project_content_status: contentRecord.status,
    rfx_content_id: contentRecord.rfx_content_id.toString(),
    owner: contentRecord.owner,
    opportunity_id: project.opportunity_id,
    project_description: project.description || '',
  };
};

const mapRfxContentStatusLabel = (
  status: RfpStatusValue,
): RfpStatusLabelValue => {
  switch (status?.toLowerCase()) {
    case 'new':
      return 'New';
    case 'analyzing':
      return 'Analyzing Excel';
    case 'long_form_go_no_go':
      return 'Awaiting Go/No-Go';
    case 'longform_outline_review':
      return 'Awaiting Outline Review';
    case 'spreadsheet_confirm_analysis':
      return 'Awaiting Analysis Confirmation';
    case 'generating':
      return 'Gathering Answers';
    case 'in_review':
      return 'In Review';
    case 'finished':
      return 'Finished';
    case 'rejected':
      return 'Rejected';
    case 'error':
      return 'Error';
    case 'generating_outline':
      return 'Generating Outline';
    case 'in_review_outline':
      return 'Awaiting Outline Review';
    case 'generating_response':
      return 'Generating Response';
    case 'in_review_response':
      return 'Awaiting Response Review';
    default:
      // No match - set to generic "Unknown" status
      return 'Unknown';
  }
};

// Map RFX project to Rfp DTO format for E2E RFPs
export const mapRfxProjectToRfpDto = (
  project: RfxProjectDto,
  contentRecord: RfxContentRecordDto,
): Rfp => {
  return {
    id: contentRecord.id.toString(),
    type: contentRecord.details?.type || 'e2e_longform',
    status: contentRecord.status as LongformStatusValue,
    details: {
      customer_name: project.customer_name || '',
      project_name: project.name,
      project_description: project.description || '',
      custom_instructions: contentRecord.details?.custom_instructions || '',
      rfpId: project.rfx_id || '',
      fileName: contentRecord.details?.fileName || '',
      files: contentRecord.details?.files || [],
      parentContentId: contentRecord.rfx_content_id.toString(),
      agentParams: contentRecord.details?.agentParams || {},
    },
    created_date: project.created_at,
    has_e2e_workbook: false,
    due_date: project.due_date ? new Date(project.due_date) : new Date(),
    project_content_status_label: mapRfxContentStatusLabel(
      contentRecord.status,
    ),
    rfp_status: project.project_status,
    longForm: true,
    isE2E: true,
    rfx_content_id: contentRecord.rfx_content_id,
    rfx_id: project.id,
  };
};

export const getE2EWorkbook = adaptify(
  'GET e2eWorkbook',
  (contentId: number) =>
    get<ApiResultDto<any>>(`/projects/content/${contentId}/e2e-workbook`),
  true,
);
