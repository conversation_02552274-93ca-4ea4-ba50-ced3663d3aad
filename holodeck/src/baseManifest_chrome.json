{"name": "Tribble Chrome Extension (DEV)", "author": "Tribble", "version": "1.4.6", "manifest_version": 3, "description": "Integrate with the Tribble platform from within your browser.", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnGwgyf86Xt38L/lm04klKZg5oZHY7h4b+BEZAnO8wIyajhajbkjf4g/rf/JERNDF8Eu9D48Vz/2jxBcHzS6g+BDJGHLEQRZBomR19XoPcF+8CG+boPYvL3OCuZt78ij2meuBm2BnnfbTzjHMgYvOA2BpjiEfftp+YucXKln5zEhTxXO4fhsCDWGkCSy5QjRqxN2GGBa49RDDelBFJkP9QhSdryNv/5aRq0E9gdc9NgbVMp6/0HXjdzoWsbED6Tm6K7gRZ7miucPAjSimN5Y2kYIHlWma/cirbXzTFCTm9hbU7YyUvSyWRbpROSFd5+uQH1nZsg5wJAGPctRzr78G5wIDAQAB", "icons": {"16": "assets/icons/icon-16.png", "24": "assets/icons/icon-24.png", "64": "assets/icons/icon-64.png", "128": "assets/icons/icon-128.png"}, "default_locale": "en", "content_scripts": [{"matches": ["https://*/*", "http://*/*"], "js": ["content/content.js"], "resources": ["assets/*"]}], "background": {"service_worker": "background/background.js"}, "permissions": ["tabs", "activeTab", "storage", "identity", "clipboardRead", "clipboardWrite", "notifications", "downloads", "sidePanel"], "host_permissions": ["http://localhost:5173/*", "https://login-dev.tribble.ai/*", "https://login-staging.tribble.ai/*", "https://login.tribble.ai/*", "https://tribble-test.azurewebsites.net/*", "https://tribble-staging.azurewebsites.net/*", "https://my.tribble.ai/*"], "options_ui": {"page": "options/index.html"}, "action": {"default_icon": {"16": "assets/icons/icon-16.png", "48": "assets/icons/icon-48.png"}, "default_title": "Click to open Tribble Side Panel"}, "side_panel": {"default_path": "popup/index.html", "openPanelOnActionClick": true}, "web_accessible_resources": [{"resources": ["assets/*", "content/*", "options/*", "popup/*", "background/*"], "matches": ["<all_urls>"]}]}