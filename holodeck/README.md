# Holodeck - <PERSON><PERSON>'s Chrome Extension

## Quick reference:

```bash
# Installing dependencies
npm install --legacy-peer-deps

# Run the extension in dev mode
npm run dev:chrome

# Build the extension. You normally don't need this.
npm run build:chrome
```

## Chrome only

There are other targets (check `package.json`), but currently <PERSON><PERSON> only publishes to Chrome

## Local dev setup

First, install the dependencies and run the build server.

```bash
# Legacy peer deps flag comes from tech debt that needs to be fixed
npm install --legacy-peer-deps

# There are other targets, but currently <PERSON><PERSON> only publishes to Chrome
npm run dev:chrome
```

This should have run the webpack dev server.

Let the initial build complete. Then, you'll have to install it inside chrome.

- Open Chrome browser and navigate to [chrome://extensions](chrome://extensions)
- Select "Developer Mode"
- Click "Load unpacked extension..."
- From the file browser, choose [./dev/chrome](./dev/chrome)

## Storybook-based component dev

You can run storybook to develop components in isolation.

```bash
npm run storybook
```

This runs storybook in watch mode. You can then edit components and see them update in the browser.

### Theming in Storybook

We have a theme in the `src/atomic-design/theme.ts` file. This is used by the components. Storybook also uses this theme, and you can see where it's being applied in the `.storybook/preview.tsx` file. As a result, any changes you make to the theme will be reflected in both the components and in storybook.

## List of Tech Debt

Technical debt is in the [TECHDEBT.md](./TECHDEBT.md) file. This needs to be addressed over time. A lot of this will probably be great things for new devs to pick up and try to fix.

## Boilerplate source

We started this project from the following boilerplate. Including it here for reference:

https://github.com/WebExp0528/React-Extension-Boilerplate
